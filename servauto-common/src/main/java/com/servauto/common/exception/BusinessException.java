package com.servauto.common.exception;

import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.core.domain.Resultable;

import java.io.Serial;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException implements Resultable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    private String msg;

    private Object data;

    private Object[] args;

    private String detailMessage;

    private boolean convertI18n;

    public BusinessException(Throwable e) {
        super(e.getMessage(), e);
    }

    /**
     * 空构造方法，避免反序列化问题
     */
    public BusinessException() {
    }

    public BusinessException(String msg, String code) {
        this.msg = msg;
        this.code = code;
    }

    public BusinessException(String code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public BusinessException(String code, String msg, Object[] args, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.args = args;
    }

    public BusinessException(String code, Object[] args, Object data) {
        this.code = code;
        this.data = data;
        this.args = args;
    }

    public BusinessException(String code, String msg, Object[] args, Object data, boolean isConvertI18n) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.args = args;
        this.convertI18n = isConvertI18n;
    }

    public BusinessException(Resultable result, Object data) {
        this.code = result.getCode();
        this.msg = result.getMsg();
        this.data = data;
    }

    public static BusinessException of(String code, String msg, Object data) {
        return new BusinessException(code, msg, data);
    }

    public static BusinessException of(String code, String msg) {
        return new BusinessException(msg, code);
    }

    public static BusinessException of(String msg) {
        return new BusinessException(msg, ResponseCode.ERROR.getCode());
    }

    public static BusinessException of(Resultable result) {
        return new BusinessException(result, null);
    }

    public static BusinessException of(Resultable result, Object data) {
        return new BusinessException(result, data);
    }

    public static BusinessException ofLocale(Resultable result, Object[] args) {
        return ofLocale(result, args, null);
    }

    public static BusinessException ofLocale(Resultable result) {
        return ofLocale(result, null);
    }

    public static BusinessException ofLocale(Resultable result, Object[] args, Object data) {
        return new BusinessException(result.getCode(), result.getMsg(), args, data, true);
    }

    public static BusinessException ofLocale(String code, Object[] args, Object data) {
        return new BusinessException(code, null, args, data, true);
    }

    public static BusinessException ofLocale(String code, Object[] args) {
        return ofLocale(code, args, null);
    }

    public static BusinessException ofLocale(String code) {
        return ofLocale(code, null, null);
    }

    @Override
    public String getMessage() {
        return msg;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    public BusinessException setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public BusinessException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public boolean isConvertI18n() {
        return convertI18n;
    }

    public void setConvertI18n(boolean convertI18n) {
        this.convertI18n = convertI18n;
    }
}