package com.servauto.common.core.domain;

public enum ResponseCode implements Resultable {

    SUCCESS("0000", "success"),

    UNAUTHORIZED("401", "unauthorized"),

    FORBIDDEN("403", "forbidden"),

    RESOURCE_NOT_FOUND("404", "Request resource cannot found"),

    ERROR("9999", "error"),

    WARN("601", "warn"),

    ParamNonExist("0001", "Missing required parameters"),

    ParamInValid("0002", "Required parameters invalid"),

    ParamBindFailed("0003", "Required parameters bind failed"),

    RequestMethodNotSupported("0004", "Request method not supported");


    private final String code;

    private final String msg;

    ResponseCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
