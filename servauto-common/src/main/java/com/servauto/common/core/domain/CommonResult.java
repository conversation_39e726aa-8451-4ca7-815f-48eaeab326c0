package com.servauto.common.core.domain;

import java.io.Serial;
import java.io.Serializable;

public class CommonResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    private String msg;

    private T data;

    public CommonResult() {
    }

    public CommonResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public CommonResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> CommonResult<T> success() {
        return CommonResult.of(ResponseCode.SUCCESS);
    }

    public static <T> CommonResult<T> of(String code, String msg) {
        return new CommonResult<T>(code, msg, null);
    }

    public static <T> CommonResult<T> of(String code, String msg, T data) {
        return new CommonResult<T>(code, msg, data);
    }

    public static <T> CommonResult<T> success(T data) {
        return of(ResponseCode.SUCCESS, data);
    }

    public static <T> CommonResult<T> success(String msg) {
        return of(ResponseCode.SUCCESS.getCode(), msg, null);
    }

    public static <T> CommonResult<T> success(String msg, T data) {
        return of(ResponseCode.SUCCESS.getCode(), msg, data);
    }

    public static <T> CommonResult<T> of(Resultable resultable, T data) {
        return of(resultable.getCode(), resultable.getMsg(), data);
    }

    public static <T> CommonResult<T> of(Resultable resultable) {
        return of(resultable.getCode(), resultable.getMsg(), null);
    }

    public static <T> CommonResult<T> warn(String msg) {
        return of(ResponseCode.WARN.getCode(), msg, null);
    }

    public static <T> CommonResult<T> warn(String msg, T data) {
        return of(ResponseCode.WARN.getCode(), msg, data);
    }

    public static <T> CommonResult<T> error() {
        return of(ResponseCode.ERROR);
    }

    public static <T> CommonResult<T> error(String msg) {
        return error(ResponseCode.ERROR.getCode(), msg);
    }

    public static <T> CommonResult<T> error(String msg, T data) {
        return of(ResponseCode.ERROR.getCode(), msg, data);
    }

    public static <T> CommonResult<T> error(String code, String msg) {
        return new CommonResult<T>(code, msg, null);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
