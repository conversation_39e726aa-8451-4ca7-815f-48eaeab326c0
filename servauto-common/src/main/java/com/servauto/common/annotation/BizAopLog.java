package com.servauto.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface BizAopLog {

    /**
     * 日志描述
     */
    String value() default "";

    /**
     * 是否打印全部日志内容 默认TRUE
     */
    boolean isPrint() default true;

    /**
     * 是否打印入参 默认TRUE
     */
    boolean isPrintArgs() default true;

    /**
     * 是否打印返回值 默认FALSE
     */
    boolean isPrintResp() default false;

    /**
     * 是否打印异常  默认TRUE
     */
    boolean isPrintExc() default true;
}
