package com.servauto.common.utils;

import java.util.regex.Pattern;

/**
 * <p>ValidationUtils</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/28 17:58
 */
public class ValidationUtils {

    private static final Pattern PATTERN_MOBILE = Pattern.compile("^601\\d{8,9}$");

    private static final Pattern PATTERN_URL = Pattern.compile("^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");

    private static final Pattern PATTERN_XML_NCNAME = Pattern.compile("[a-zA-Z_][\\-_.0-9_a-zA-Z$]*");

    public static boolean validateMobile(String mobile) {
        return StringUtils.hasText(mobile) && PATTERN_MOBILE.matcher(mobile).matches();
    }

}
