package com.servauto.common.utils.generator;

/**
 * <p>UniqueID</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/9 13:40
 */
public class UniqueID {

    private static IdWorker idWorder;

    public UniqueID() {
    }

    public static synchronized long generateId() {
        return idWorder.nextId();
    }

    public static synchronized String generateId(String prefix) {
        return prefix+idWorder.nextId();
    }

    static {
        int machineNo = Integer.parseInt(LocalHostUtil.getLastSegment());
        idWorder = new IdWorker((long) machineNo);
    }

}
