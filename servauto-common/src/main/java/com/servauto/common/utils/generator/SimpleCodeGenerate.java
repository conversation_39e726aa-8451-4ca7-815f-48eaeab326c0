package com.servauto.common.utils.generator;

import java.util.Random;

/**
 * <p>SimpleCodeGenerate</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/14 00:03
 */
public class SimpleCodeGenerate {

    private final Random random = new Random();
    private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public SimpleCodeGenerate() {
    }

    public synchronized String generate() {
        StringBuilder sb = new StringBuilder(6);
        for (int i = 0; i < 6; i++) {
            int index = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

}
