package com.servauto.common.utils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    
    public static Date getNowDate() {
        return new Date();
    }

    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_PATTERN =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_PATTERN =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public static String format(Date date, DateTimeFormatter pattern, ZoneId zoneId) {
        return format(date.toInstant(), pattern, zoneId);
    }

    public static String format(Instant instant, DateTimeFormatter pattern, ZoneId zoneId) {
        return instant.atZone(zoneId).format(pattern);
    }

    public static Date parse(String str, String pattern, ZoneId zoneId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern).withZone(zoneId);
        Instant instant = ZonedDateTime.parse(str, formatter).toInstant();
        return Date.from(instant);
    }

}
