package com.servauto.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <p>BigDecimalUtils</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/29 18:05
 */
public class BigDecimalUtils {

    public static BigDecimal format(BigDecimal decimal) {
        return Objects.isNull(decimal) ? BigDecimal.ZERO.setScale(2, RoundingMode.HALF_EVEN) : decimal.setScale(2, RoundingMode.HALF_EVEN);
    }

    public static void main(String[] args) {
        System.out.println(format(null));
        System.out.println(format(BigDecimal.ZERO));
        System.out.println(format(new BigDecimal(100)));
    }
}
