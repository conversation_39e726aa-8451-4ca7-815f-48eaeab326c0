package com.servauto.common.utils;

/**
 * <p>GeoUtils</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/24 14:43
 */
public class GeoUtils {


    private static final double EARTH_RADIUS = 6371000; // 地球半径，单位：米

    /**
     * 计算两个经纬度之间的距离
     *
     * @param lat1 latitude-1
     * @param lon1 longitude-1
     * @param lat2 latitude-2
     * @param lon2 longitude-2
     * @return distance / m
     */
    public static double getDistance(double lat1, double lon1, double lat2, double lon2) {
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double deltaLat = Math.toRadians(lat2 - lat1);
        double deltaLon = Math.toRadians(lon2 - lon1);

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2)
                + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

        return EARTH_RADIUS * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    }

}
