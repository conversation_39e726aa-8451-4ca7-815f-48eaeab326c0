package com.servauto.common.utils.sign;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * TikTok Shop Webhook 签名验证工具类
 *
 * 根据TikTok Shop官方文档实现webhook签名验证
 * 签名算法: HMAC-SHA256
 * 签名数据: {app_key}{webhook_payload}
 * 签名密钥: app_secret
 */
@Slf4j
public class TiktokWebhookSignatureUtils {

    private static final String HMAC_SHA256 = "HmacSHA256";

    /**
     * 验证TikTok Shop webhook签名
     *
     * @param payload webhook请求体
     * @param signature 签名
     * @param appKey 应用Key
     * @param appSecret 应用Secret
     * @return 验证结果
     */
    public static boolean verifySignature(String payload, String signature, String appKey, String appSecret) {
        try {
            String expectedSignature = generateSignature(payload, appKey, appSecret);
            boolean isValid = signature.equals(expectedSignature);

            if (!isValid) {
                log.warn("TikTok webhook signature verification failed. Expected: {}, Actual: {}",
                        expectedSignature, signature);
            }

            return isValid;
        } catch (Exception e) {
            log.error("Error verifying TikTok webhook signature", e);
            return false;
        }
    }

    /**
     * 生成TikTok Shop webhook签名
     *
     * 签名生成规则（根据官方文档）:
     * 1. 将app_key和payload拼接: app_key + payload
     * 2. 使用HMAC-SHA256算法和app_secret对拼接字符串进行签名
     * 3. 将签名结果转换为十六进制字符串
     *
     * 示例:
     * app_key: abcdef
     * payload: {"type":1,"tts_notification_id":"7380066284010030890",...}
     * signature_base: abcdef{"type":1,"tts_notification_id":"7380066284010030890",...}
     * app_secret: 123
     * 结果: 5dec0f11ec2f6783b8deee53c9ffbf8d024302f7c7e7fa55a35d17629031ac05
     *
     * @param payload webhook请求体
     * @param appKey 应用Key
     * @param appSecret 应用Secret
     * @return 签名字符串
     */
    public static String generateSignature(String payload, String appKey, String appSecret)
            throws NoSuchAlgorithmException, InvalidKeyException {

        // 拼接app_key和payload
        String signatureBase = appKey + payload;

        // 创建HMAC-SHA256实例
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKeySpec = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKeySpec);

        // 计算签名
        byte[] signatureBytes = mac.doFinal(signatureBase.getBytes(StandardCharsets.UTF_8));

        // 转换为十六进制字符串
        return bytesToHex(signatureBytes);
    }

    /**
     * 字节数组转十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

}
