package com.servauto.common.constant;

public class TriggerConstants {
    public static String ORDER_PAYMENT_COMPLETED_NOTICE = "You have a new {orderType} order. Please handle it on time：";
    public static String ORDER_PAYMENT_COMPLETED_NOTICE_1 = "You have a new order. Please handle it on time：";
    public static String CONFIRM_NOTICE = "The workshop has provided feedback that the order service time needs to be changed. Please handle it on time：";


    public static String ORDER_ID_MESSAGE = "**Order ID：{Order ID}**";
    public static String ORDER_TYPE_MESSAGE = "**Order Type：{Order Type}**";
    public static String CAR_PLATE_NUMBER_MESSAGE = "**Car Plate Number：{Car Plate Number}**";
    public static String SERVICE_NAME_MESSAGE = "**Service Name：{Service Name}**";
    public static String APPOINTMENT_INFO_MESSAGE = "**Appointment Info：**";
    public static String STORE_MESSAGE = "• Store：{Store Name}";
    public static String TIME_MESSAGE = "• Time：{Appointment Time（yyyy-mm-dd hh:mm）}";

    public static String FEEDBACK_INFO_MESSAGE = "**Feedback Info：**";
    public static String WORKSHOP_PREFER_TIME_MESSAGE = "• Workshop Prefer Time：{Available Reservation Time}";

    public static String LARK_MD = "lark_md";
    public static String DIV = "div";
    public static String PLAIN_TEXT = "plain_text";


    public static String USER_NAME = "**Hello: {userName}**";
    public static String WARNING_MESSAGE = "**TikTok authorization is about to expire. To avoid order synchronization failures with Lark documents, please reauthorize.**";
    public static String EXPIRE_TIME_MESSAGE = "**Remaining expiration time: {Hours} Hours**";
    public static String REGRANT_MESSAGE = "[Click to Reauthorize](https://services.tiktokshop.com/open/authorize?service_id=7515370173727409925 \"Authorize\")";

}
