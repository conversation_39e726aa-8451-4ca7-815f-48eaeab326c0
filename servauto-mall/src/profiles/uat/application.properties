spring.profiles.active=uat
server.port=8060
system.timezone=Asia/Shanghai

logging.level.com.servauto=info
logging.level.org.springframework=warn

spring.datasource.druid.master.url=***********************************************************************************************************************************************************************
spring.datasource.druid.master.username=servauto
spring.datasource.druid.master.password=servauto@123

spring.data.redis.host=************
spring.data.redis.port=6379
spring.data.redis.database=0

mall.security.token-header=Authorization
mall.security.token-parameter=token
mall.security.permit-all-urls=

sms-code.enable=true
sms-code.begin-code=0
sms-code.end-code=999999
sms-code.expire-times=10m
sms-code.send-frequency=10s
sms-code.send-maximum-quantity-per-day=100

springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
knife4j.production=true
knife4j.enable=false

webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.confirm.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.book.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.completed.secret=2FslWuOqM6AjPxU3rvzXCc