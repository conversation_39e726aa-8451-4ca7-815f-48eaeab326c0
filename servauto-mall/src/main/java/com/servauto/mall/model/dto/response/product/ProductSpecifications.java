package com.servauto.mall.model.dto.response.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Product Specifications Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductSpecifications {
    @Schema(description = "price", example = "101.11")
    private BigDecimal price;

    @Schema(description = "carNetContent", example = "6")
    private Integer carNetContent;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "list", example = "[{}]")
    private List<ProductSpecification> list;
}
