package com.servauto.mall.model.dto.request.auth;

import com.servauto.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Schema(description = "Customer -  mobile + password login")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginReqDTO {

    @Schema(description = "mobile", requiredMode = Schema.RequiredMode.REQUIRED, example = "123123123")
    @NotEmpty(message = "mobile cannot empty")
    @Mobile
    private String mobile;

    @Schema(description = "Mobile verification code", requiredMode = Schema.RequiredMode.REQUIRED, example = "999999")
    @NotEmpty(message = "Mobile verification code cannot be empty")
    @Length(min = 4, max = 6, message = "The length of the mobile verification code should be between 4 and 6 digits.")
    @Pattern(regexp = "^[0-9]+$", message = "The mobile verification code must consist of only digits.")
    private String code;

    @Schema(description = "Sending scenario, 1 = sms code; 2= whatsapp code", example = "1")
    @NotNull(message = "Sending scenario cannot be empty")
    private Integer scene;

    @Schema(description = "source", example = "default")
    private String source;
}