package com.servauto.mall.model.dto.response.order;

import com.servauto.mall.model.dto.response.payment.PaymentResDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>CashierDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/29 14:27
 */
@Schema(description = "Order - order cashier response vo")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CashierDTO {
    @Schema(description = "orderNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "O321984763294289")
    private String orderNo;

    @Schema(description = "payNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "P321984763294289")
    private String payNo;

    @Schema(description = "payment", requiredMode = Schema.RequiredMode.REQUIRED)
    private PaymentResDTO payment;
}
