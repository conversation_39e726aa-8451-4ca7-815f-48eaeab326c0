package com.servauto.mall.model.dto.response.order;

import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Order - create order product response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateOrderDTO {

    @Schema(description = "orderNo", example = "123123123123123")
    private String orderNo;

    @Schema(description = "serviceId", example = "1")
    private Long serviceId;

    @Schema(description = "serviceName", example = "xxx")
    private String serviceName;

    @Schema(description = "service hour", example = "xxx")
    private Integer serviceHour;

    @Schema(description = "serviceFee", example = "100")
    private BigDecimal serviceFee;

    @Schema(description = "packageId", example = "1")
    private Long packageId;

    @Schema(description = "packageName", example = "xxx")
    private String packageName;

    @Schema(description = "shippingFee", example = "10")
    private BigDecimal shippingFee;

    @Schema(description = "order original amount", example = "120")
    private BigDecimal originalAmount;

    @Schema(description = "order discount amount", example = "25")
    private BigDecimal discountAmount;

    @Schema(description = "order coupon amount", example = "10")
    private BigDecimal couponAmount;

    @Schema(description = "order subtotal", example = "85")
    private BigDecimal subtotal;

    @Schema(description = "order grand total", example = "195")
    private BigDecimal grandTotal;

    @Schema(description = "order product details", example = "[]")
    private List<OrderProductDTO> products;

    @Schema(description = "delivery methods")
    private List<OrderDeliveryDTO> deliveryMethods;

    @Schema(description = "order carInfo")
    private CarInfoDTO carInfo;

    @Schema(description = "order customerInfo")
    private OrderCustomerDTO customerInfo;

    @Schema(description = "order remark")
    private String remark;

    @Schema(description = "force fixed price")
    private Boolean fixed;

    public record OrderDeliveryDTO(String code, String msg) {
        @Override
        public String toString() {
            return "OrderDeliveryDTO{" +
                    "code='" + code + '\'' +
                    ", msg='" + msg + '\'' +
                    '}';
        }
    }
}
