package com.servauto.mall.model.dto.response.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Order - order response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderDTO {

    @Schema(description = "orderNo", example = "123123123123123")
    private String orderNo;

    @Schema(description = "create order time", example = "123123123123")
    private Date orderTime;

    @Schema(description = "order name", example = "orton")
    private String orderName;

    @Schema(description = "order original amount", example = "120")
    private BigDecimal originalAmount;

    @Schema(description = "order discount amount", example = "25")
    private BigDecimal discountAmount;

    @Schema(description = "order subtotal", example = "95")
    private BigDecimal subtotal;

    @Schema(description = "order shipping fee", example = "10")
    private BigDecimal shippingFee;

    @Schema(description = "order grand total", example = "105")
    private BigDecimal grandTotal;

    @Schema(description = "order type", example = "todo")
    private String type;

    @Schema(description = "order product package id", example = "")
    private Long packageId;

    @Schema(description = "order product package name", example = "")
    private String packageName;

    @Schema(description = "order status", example = "")
    private String status;

    @Schema(description = "order status", example = "")
    private String statusName;

    @Schema(description = "customer id", example = "")
    private Long customerId;

    @Schema(description = "service id", example = "")
    private Long serviceId;

    @Schema(description = "service name", example = "")
    private String serviceName;

    @Schema(description = "service Hour", example = "")
    private Integer serviceHour;

    @Schema(description = "service fee", example = "")
    private BigDecimal serviceFee;

    @Schema(description = "workshop id", example = "")
    private Long workshopId;

    @Schema(description = "workshop name", example = "")
    private String workshopName;

    @Schema(description = "delivery type", example = "OrderDeliveryTypeEnum")
    private String deliveryType;

    @Schema(description = "delivery type smg", example = "OrderDeliveryTypeEnum")
    private String deliveryTypeMsg;

    @Schema(description = "coupon amount", example = "")
    private BigDecimal couponAmount;

    @Schema(description = "reservation time", example = "")
    private Date reservationTime;

    @Schema(description = "paid time", example = "")
    private Date paidTime;

    @Schema(description = "order finished time", example = "")
    private Date finishedTime;

    @Schema(description = "order refunded time", example = "")
    private Date refundedTime;

    @Schema(description = "order product details", example = "[]")
    List<OrderProductDTO> products;

    @Schema(description = "order expire time", example = "")
    private Date expireTime;

    @Schema(description = "order pickup code", example = "")
    private String pickupCode;

    @Schema(description = "order delivery", example = "")
    private OrderDeliveryDTO delivery;

    @Schema(description = "order pays", example = "")
    private OrderPayDTO orderPay;

    @Schema(description = "cancel button visible", example = "")
    private Boolean cancelVisible;

    @Schema(description = "payNow button visible", example = "")
    private Boolean payNowVisible;

    @Schema(description = "bookService button visible", example = "")
    private Boolean bookServiceVisible;

    @Schema(description = "reschedule button visible", example = "")
    private Boolean rescheduleVisible;

    @Schema(description = "pickup button visible", example = "")
    private Boolean pickupVisible;

    @Schema(description = "workshop info", example = "")
    private OrderWorkshopDTO workshopInfo;

    @Schema(description = "remark", example = "")
    private String remark;

    @Schema(description = "order carInfo")
    private CarInfoDTO carInfo;

    @Schema(description = "order customerInfo")
    private OrderCustomerDTO customerInfo;
}
