package com.servauto.mall.model.dto.response.packageinfo;

import com.servauto.mall.model.dto.response.serviceinfo.ServiceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Package Detail Response V0")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageDetailDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "isFixedPrice", example = "true")
    private Boolean isFixedPrice;

    @Schema(description = "price", example = "100.00")
    private BigDecimal price;

    @Schema(description = "products", example = "[{}]")
    private List<ProductSimpleDTO> products;

    @Schema(description = "serviceDTO", example = "{}")
    private ServiceDTO serviceDTO;
}
