package com.servauto.mall.model.dto.response.content;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Content - query content layout response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LayoutDTO {

    @Schema(description = "layout id")
    private Long id;

    @Schema(description = "layout title")
    private String title;

    @Schema(description = "layout sequence")
    private Long seq;

    @Schema(description = "layout type", example = "BANNER/CHANNEL")
    private String type;

    @Schema(description = "layout resources", example = "[]")
    private List<ResourceDTO> resources;

}
