package com.servauto.mall.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Car Key Value Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarBrandListDTO {

    @Schema(description = "Top Brands", example = "[]")
    private List<CarKeyValueDTO> topBrands;

    @Schema(description = "Brand List", example = "[]")
    private List<CarKeyValueDTO> brandList;


}