package com.servauto.mall.model.dto.response.order;

import com.servauto.mall.model.dto.response.product.ProductAttributeValueDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Order - order product response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderProductDTO {

    @Schema(description = "product id", example = "1")
    private Long productId;

    @Schema(description = "product name", example = "name/title")
    private String productName;

    @Schema(description = "brand id", example = "123")
    private Long brandId;

    @Schema(description = "brand name", example = "name")
    private String brandName;

    @Schema(description = "net content", example = "name")
    private Integer netContent;

    @Schema(description = "content unit", example = "name")
    private String contentUnit;

    @Schema(description = "product attribute", example = "xxx 5L")
    private List<ProductAttributeValueDTO> productAttribute;

    @Schema(description = "product category id", example = "1")
    private Long categoryId;

    @Schema(description = "product category name", example = "oil")
    private String categoryName;

    private Boolean isOil;

    @Schema(description = "product image", example = "https://xxxx.com")
    private String image;

    @Schema(description = "product reserve price", example = "1")
    private BigDecimal reservePrice;

    @Schema(description = "product actual price", example = "1")
    private BigDecimal actualPrice;

    @Schema(description = "product discount amount", example = "1")
    private BigDecimal discountAmount;

    @Schema(description = "product promotion amount", example = "1")
    private BigDecimal promotionDiscount;

    @Schema(description = "product discount price", example = "1")
    private BigDecimal discountPrice;

    @Schema(description = "product quantity", example = "1")
    private Integer quantity;

}
