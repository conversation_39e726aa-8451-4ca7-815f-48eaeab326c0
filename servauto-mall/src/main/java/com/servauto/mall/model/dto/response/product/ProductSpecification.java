package com.servauto.mall.model.dto.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "Product Specification Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductSpecification {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "price", example = "10000.00")
    private BigDecimal price;

    @Schema(description = "count", example = "1")
    private Integer count;
}
