package com.servauto.mall.model.dto.response.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "TikTok Shop Secrets Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TiktokShopSecretsRespDTO {

    @Schema(description = "Response code", example = "0")
    private Integer code;

    @Schema(description = "Response message", example = "success")
    private String message;

    @Schema(description = "Shop secrets data")
    private ShopSecretsData data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ShopSecretsData {
        
        @Schema(description = "List of shops")
        private List<ShopSecret> shops;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ShopSecret {
        
        @Schema(description = "Shop cipher/secret", example = "shop_secret_key")
        private String cipher;

        @Schema(description = "Shop code", example = "shop_001")
        private String code;

        @Schema(description = "Shop ID", example = "123456789")
        @JsonProperty("shop_id")
        private String shopId;

        @Schema(description = "Shop name", example = "My TikTok Shop")
        @JsonProperty("shop_name")
        private String shopName;

        @Schema(description = "Shop region", example = "US")
        @JsonProperty("shop_region")
        private String shopRegion;

        @Schema(description = "Shop status", example = "ACTIVE")
        @JsonProperty("shop_status")
        private String shopStatus;
    }
}
