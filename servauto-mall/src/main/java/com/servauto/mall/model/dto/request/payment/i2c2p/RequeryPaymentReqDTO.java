package com.servauto.mall.model.dto.request.payment.i2c2p;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import java.math.BigDecimal;


@Data
@JacksonXmlRootElement(localName = "TxDetailsInquiryCardInfo", namespace = "https://www.mobile88.com/epayment/webservice")
public class RequeryPaymentReqDTO {

    @JacksonXmlProperty(localName = "MerchantCode")
    private String merchantCode;

    @JacksonXmlProperty(localName = "ReferenceNo")
    private String referenceNo;

    @JacksonXmlProperty(localName = "Amount")
    private BigDecimal amount;

    @JacksonXmlProperty(localName = "Version")
    private String version = "4";
}