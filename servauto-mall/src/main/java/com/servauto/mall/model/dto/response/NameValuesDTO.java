package com.servauto.mall.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Name Values Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NameValuesDTO {
    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "values", example = "[]")
    private List<String> values;
}
