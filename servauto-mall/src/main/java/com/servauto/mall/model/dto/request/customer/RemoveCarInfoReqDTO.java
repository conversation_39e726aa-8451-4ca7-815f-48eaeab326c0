package com.servauto.mall.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(description = "Customer - carInfo Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RemoveCarInfoReqDTO {

    @Schema(description = "carID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25")
    private Long carId;
}