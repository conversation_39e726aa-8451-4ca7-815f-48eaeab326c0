package com.servauto.mall.model.dto.response.content;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Content - query content template response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateDTO {

    @Schema(description = "template code", example = "HOME_PAGE")
    private String code;

    @Schema(description = "template title", example = "title")
    private String title;

    @Schema(description = "layouts", example = "[]")
    private List<LayoutDTO> layouts;
}
