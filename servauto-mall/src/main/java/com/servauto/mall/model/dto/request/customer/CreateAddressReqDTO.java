package com.servauto.mall.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - address Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateAddressReqDTO {

    @Schema(description = "shipping name", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    @NotBlank(message = "consignee name cannot null")
    private String name;

    @Schema(description = "shipping mobile", requiredMode = Schema.RequiredMode.REQUIRED, example = "mobile")
    @NotBlank(message = "consignee mobile cannot null")
    private String mobile;

    @Schema(description = "shipping state code", requiredMode = Schema.RequiredMode.REQUIRED, example = "state code")
    @NotBlank(message = "consignee state cannot null")
    private String stateCode;

    @Schema(description = "shipping city code", requiredMode = Schema.RequiredMode.REQUIRED, example = "city code")
    @NotBlank(message = "consignee city cannot null")
    private String cityCode;

    @Schema(description = "shipping address", requiredMode = Schema.RequiredMode.REQUIRED, example = "address")
    @NotBlank(message = "consignee address cannot null")
    private String address;

    @Schema(description = "is default address", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "true/false")
    private Boolean isDefault;
}
