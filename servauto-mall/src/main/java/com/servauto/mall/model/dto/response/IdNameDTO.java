package com.servauto.mall.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "ID Name Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdNameDTO {
    @Schema(description = "id", example = "0")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;
}