package com.servauto.mall.model.dto.response.serviceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "Service Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "fee", example = "200.00")
    private BigDecimal fee;

    @Schema(description = "isRequired", example = "true")
    private Boolean isRequired;
}
