package com.servauto.mall.model.dto.response.serviceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Service Info - Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceInfoDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "hours", example = "hours")
    private Integer hours;

    @Schema(description = "fee", example = "100.00")
    private BigDecimal fee;

    @Schema(description = "supportedOn", example = "1")
    private String supportedOn;

    @Schema(description = "supportedOnName", example = "Product")
    private String supportedOnName;

    @Schema(description = "isRequired", example = "1")
    private Integer isRequired;

    @Schema(description = "isRequiredName", example = "Yes")
    private String isRequiredName;

    @Schema(description = "workshopIds", example = "[1]")
    private List<Long> workshopIds;

    @Schema(description = "productIds", example = "[1]")
    private List<Long> productIds;

    @Schema(description = "packageIds", example = "[1]")
    private List<Long> packageIds;

    @Schema(description = "updatedBy", example = "1")
    private Long updatedBy;

    @Schema(description = "updaterName", example = "updaterName")
    private String updaterName;

    @Schema(description = "createTime", example = "1")
    private Date createTime;

    @Schema(description = "updateTime", example = "1")
    private Date updateTime;
}
