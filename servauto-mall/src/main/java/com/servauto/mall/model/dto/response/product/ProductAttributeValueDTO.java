package com.servauto.mall.model.dto.response.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product Attribute Value Response V0")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductAttributeValueDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "type", example = "select/number/date/input/multiple")
    private String type;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "suffix", example = "mm")
    @JsonIgnore
    private String suffix;

    @Schema(description = "values", example = "[]")
    private List<String> values;

    @Schema(description = "labels", example = "[]")
    private List<String> labels;

    @Schema(description = "order", example = "1")
    private Integer order;
}
