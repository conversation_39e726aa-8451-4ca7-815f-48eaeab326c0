package com.servauto.mall.model.entity.packageinfo;

import java.math.BigDecimal;
import java.util.Date;

public class PackageInfo {
    private Long id;

    private String name;

    private String code;

    private Long serviceId;

    private Integer status;

    private Integer deliveryModes;

    private Integer isFixedPrice;

    private BigDecimal price;

    private Long updatedBy;

    private Date createTime;

    private Date updateTime;

    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDeliveryModes() {
        return deliveryModes;
    }

    public void setDeliveryModes(Integer deliveryModes) {
        this.deliveryModes = deliveryModes;
    }

    public Integer getIsFixedPrice() {
        return isFixedPrice;
    }

    public void setIsFixedPrice(Integer isFixedPrice) {
        this.isFixedPrice = isFixedPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}