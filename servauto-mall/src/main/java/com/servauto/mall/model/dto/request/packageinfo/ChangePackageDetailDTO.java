package com.servauto.mall.model.dto.request.packageinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Package - change detail Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChangePackageDetailDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "carId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long carId;

    @Schema(description = "productId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long productId;

    @Schema(description = "dstProductIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private List<Long> dstProductIds;
}
