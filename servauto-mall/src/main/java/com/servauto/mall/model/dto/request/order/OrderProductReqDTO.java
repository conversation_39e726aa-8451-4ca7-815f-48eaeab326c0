package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(description = "Order - create order product request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderProductReqDTO {

    @Schema(description = "product id", example = "1")
    @NotNull(message = "ProductId cannot null")
    private Long productId;

    @Schema(description = "product quantity", example = "2")
    @NotNull(message = "Product quantity cannot null")
    private Integer quantity;
}
