package com.servauto.mall.model.dto.request.payment.i2c2p;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class I2c2pPaymentCreateReqDTO {

    private String merchantID;
    private String invoiceNo;
    private String description;
    private BigDecimal amount;
    private String currencyCode;
    //yyyy-MM-dd HH:mm:ss
    private String paymentExpiry;
    //支付完成后前端跳转地址
    private String frontendReturnUrl;
    //支付完成后端接受通知地址
    private String backendReturnUrl;

}