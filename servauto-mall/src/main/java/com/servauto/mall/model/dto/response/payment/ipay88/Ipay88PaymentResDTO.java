package com.servauto.mall.model.dto.response.payment.ipay88;

import com.servauto.mall.model.dto.response.payment.PaymentResDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Payment - create Response VO")
@Data
public class Ipay88PaymentResDTO extends PaymentResDTO {

    @Schema(description = "merchantCode", requiredMode = Schema.RequiredMode.REQUIRED, example = "A8888")
    private String merchantCode;

    /*     @Schema(description = "refNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "P321984763294289")
        private String refNo;

        @Schema(description = "amount", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,278.99")
        private String amount;*/
    @Schema(description = "platFromUrl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://demo.com/pay")
    private String platFromUrl;

    @Schema(description = "paymentId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "A8888")
    private String paymentId;

    @Schema(description = "currency", requiredMode = Schema.RequiredMode.REQUIRED, example = "MYR")
    private String currency;

    @Schema(description = "prodDesc", requiredMode = Schema.RequiredMode.REQUIRED, example = "Product description")
    private String prodDesc;

    @Schema(description = "userName", requiredMode = Schema.RequiredMode.REQUIRED, example = "Tom Bob")
    private String userName;

    @Schema(description = "userEmail", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String userEmail;

    @Schema(description = "userContact", requiredMode = Schema.RequiredMode.REQUIRED, example = "+8608988898")
    private String userContact;

 /*   @Schema(description = "remark", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Tom Bob")
    private String remark;*/

    @Schema(description = "userName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Tom Bob")
    private String lang;

    @Schema(description = "signatureType", requiredMode = Schema.RequiredMode.REQUIRED, example = "HMACSHA512")
    private String signatureType;

    @Schema(description = "signature", requiredMode = Schema.RequiredMode.REQUIRED, example = "HMACSHA512 signature")
    private String signature;

    @Schema(description = "responseURL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://demo.com/mall/response")
    private String responseURL;

    @Schema(description = "backendURL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://demo.com/pay/backend")
    private String backendURL;

    @Schema(description = "optional", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Sample value: Optional = {\"carddetails\":\"Y\"}")
    private String optional;

    @Schema(description = "appdeeplink", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "https://demo.com/pay/app")
    private String appDeeplink;

    @Schema(description = "xfield1", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Tom Bob")
    private String xField1;


}
