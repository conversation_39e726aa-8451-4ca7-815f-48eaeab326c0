package com.servauto.mall.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - address Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EditAddressReqDTO {

    @Schema(description = "shipping id", hidden = true, example = "id")
    private Long id;

    @Schema(description = "shipping name", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "shipping mobile", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "mobile")
    private String mobile;

    @Schema(description = "shipping state code", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "state code")
    private String stateCode;

    @Schema(description = "shipping city code", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "city code")
    private String cityCode;

    @Schema(description = "shipping address", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "address")
    private String address;

    @Schema(description = "is default address", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "true/false")
    private Boolean isDefault;
}
