package com.servauto.mall.model.dto.response.packageinfo;

import com.servauto.mall.model.dto.response.NameValuesDTO;
import com.servauto.mall.model.dto.response.product.ProductSpecification;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Schema(description = "Product Simple DTO Response V0")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductSimpleDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "categoryId", example = "1")
    private Long categoryId;

    @Schema(description = "categoryName", example = "categoryName")
    private String categoryName;

    @Schema(description = "isOil", example = "isOil")
    private Boolean isOil;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "price", example = "100.00")
    private BigDecimal price;

    @Schema(description = "maxNetContent", example = "8")
    private Integer maxNetContent;

    @Schema(description = "carNetContent", example = "6")
    private Integer carNetContent;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "mainImage", example = "www.photo.com")
    private String mainImage;

    @Schema(description = "featuredTags", example = "[]")
    private List<String> featuredTags;

    @Schema(description = "skuAttributeValues", example = "[[]]")
    private List<NameValuesDTO> skuAttributeValues;

    @Schema(description = "list", example = "[{}]")
    private List<ProductSpecification> productSpecifications;
}

