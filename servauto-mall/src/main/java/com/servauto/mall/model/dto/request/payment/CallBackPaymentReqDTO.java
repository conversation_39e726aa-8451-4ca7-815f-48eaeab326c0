package com.servauto.mall.model.dto.request.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - edit Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CallBackPaymentReqDTO {

    @Schema(description = "Payment Number", example = "123456789000")
    private String payNo;

    @Schema(description = "Req Data", example = "Req Data")
    private String reqData;
}