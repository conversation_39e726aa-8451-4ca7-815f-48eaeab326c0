package com.servauto.mall.model.dto.request.auth;

import com.servauto.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "User - Request VO for sending mobile verification code")

@Data
@Accessors(chain = true)
public class AuthSmsSendReqDTO {

    @Schema(description = "Mobile number", example = "123123123")
    @NotBlank(message = "mobile cannot null")
    @Mobile
    private String mobile;

    @Schema(description = "Sending scenario, 1 = sms code; 2= whatsapp code", example = "1")
    @NotNull(message = "Sending scenario cannot be empty")
    private Integer scene;
}
