package com.servauto.mall.model.dto.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Package Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "mainImage", example = "https://www.toyota.com.cn/")
    private String mainImage;

    @Schema(description = "serviceName", example = "serviceName")
    private String serviceName;

    @Schema(description = "price", example = "100.00")
    private String price;

    @Schema(description = "categoryNames", example = "[]")
    private List<String> categoryNames;
}
