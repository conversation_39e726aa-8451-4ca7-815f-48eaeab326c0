package com.servauto.mall.model.dto.request.payment.ipay88;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
public class IPay88PaymentCallBackReqDTO {

    @JsonProperty("MerchantCode")
    private String merchantCode;

    @JsonProperty("PaymentId")
    private String paymentId;

    @JsonProperty("RefNo")
    private String refNo;

    @JsonProperty("Amount")
    private String amount;

    @JsonProperty("Currency")
    private String currency;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("TransId")
    private String transId;

    @JsonProperty("AuthCode")
    private String authCode;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("ErrDesc")
    private String errDesc;

    @JsonProperty("Signature")
    private String signature;

    @JsonProperty("HiddenToURL")
    private String hiddenToURL;

    @JsonProperty("ActionType")
    private String actionType;

    @JsonProperty("TokenId")
    private String tokenId;

    @JsonProperty("CCCOriTokenId")
    private String cccOriTokenId;

    @JsonProperty("PromoCode")
    private String promoCode;

    @JsonProperty("DiscountedAmount")
    private String discountedAmount;

    @JsonProperty("MTVersion")
    private String mtVersion;

    @JsonProperty("MTLogId")
    private String mtLogId;
}
