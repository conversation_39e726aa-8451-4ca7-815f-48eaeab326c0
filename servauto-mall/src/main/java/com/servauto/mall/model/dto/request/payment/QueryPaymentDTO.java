package com.servauto.mall.model.dto.request.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Customer - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryPaymentDTO {

    @Schema(description = "Payment Number", example = "123456789000")
    private Long payNo;

    @Schema(description = "Source ID", example = "0987654321")
    private String sourceId;

    @Schema(description = "Payment Time", example = "2023-10-01T12:00:00")
    private Date payTime;

    @Schema(description = "Refund Time", example = "2023-10-01T12:00:00")
    private Date refundTime;

    @Schema(description = "Payment Type", example = "ONLINE")
    private String payType;

    @Schema(description = "Payment Status", example = "SUCCESS")
    private String status;

    @Schema(description = "Creation Time", example = "2023-10-01T12:00:00")
    private Date createTime;

    @Schema(description = "Update Time", example = "2023-10-01T12:00:00")
    private Date updateTime;
}
