package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>OrderWorkshopDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/24 14:49
 */
@Schema(description = "Order - query order workshops request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderWorkshopReqDTO {

    @Schema(hidden = true)
    private Long customerId;

    @Schema(hidden = true)
    private String orderNo;

    @NotNull(message = "latitude cannot be null")
    @Schema(description = "user latitude", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.90923")
    private Double latitude;

    @NotNull(message = "longitude cannot be null")
    @Schema(description = "user longitude", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.397428")
    private Double longitude;

}
