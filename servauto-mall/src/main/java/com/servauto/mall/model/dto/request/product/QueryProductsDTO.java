package com.servauto.mall.model.dto.request.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryProductsDTO {
    @Schema(description = "current", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @NotBlank(message = "current")
    private Integer pageNo;

    @Schema(description = "pageSize", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "20")
    @NotBlank(message = "pageSize")
    private Integer pageSize;

    @Schema(description = "carId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long carId;

    @Schema(description = "name, fuzzy query", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "categoryId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long categoryId;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long serviceId;

    @Schema(description = "packageId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long packageId;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> ids;

    @Schema(description = "brandIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> brandIds;

    @Schema(description = "attributeValues", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[{}]")
    private List<IdValuesDTO> attributeValues;

    @Schema(description = "sortByPrice", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer sortByPrice;

    @JsonIgnore
    private List<String> footPadNames;
}
