package com.servauto.mall.model.dto.request.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "User - Request VO for verifying mobile verification code")
@Data
@Accessors(chain = true)
public class AuthSmsValidateReqDTO {

    @Schema(description = "Mobile phone number", example = "60123123124")
    private String mobile;

    @Schema(description = "Sending scenario", example = "1")
    @NotNull(message = "Sending scenario cannot be empty")
    private Integer scene;

    @Schema(description = "Mobile verification code", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "Mobile verification code cannot be empty")
    @Length(min = 4, max = 6, message = "The length of the mobile verification code should be between 4 and 6 digits.")
    @Pattern(regexp = "^[0-9]+$", message = "The mobile verification code must consist of only digits.")
    private String code;

}
