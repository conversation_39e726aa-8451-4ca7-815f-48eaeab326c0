package com.servauto.mall.model.dto.response.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Order - create order product request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubmitOrderDTO {

    @Schema(description = "orderNo", example = "123123123123123")
    private String orderNo;

    @Schema(description = "pay number", example = "123123123123")
    private String payNo;

    @Schema(description = "create order time", example = "123123123123")
    private Date orderTime;

    @Schema(description = "order original amount", example = "120")
    private BigDecimal originalAmount;

    @Schema(description = "order discount amount", example = "25")
    private BigDecimal discountAmount;

    @Schema(description = "order subtotal", example = "95")
    private BigDecimal subtotal;

    @Schema(description = "order shipping fee", example = "10")
    private BigDecimal shippingFee;

    @Schema(description = "order grand total", example = "105")
    private BigDecimal grandTotal;

    @Schema(description = "order expire time", example = "123123123")
    private Date expireTime;

    @Schema(description = "order product details", example = "[]")
    List<OrderProductDTO> products;
}
