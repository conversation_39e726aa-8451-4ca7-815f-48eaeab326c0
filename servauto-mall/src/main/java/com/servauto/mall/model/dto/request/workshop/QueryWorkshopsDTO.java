package com.servauto.mall.model.dto.request.workshop;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Workshop - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryWorkshopsDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "name", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "type", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "type")
    private Integer type;

    @Schema(description = "ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> ids;
}
