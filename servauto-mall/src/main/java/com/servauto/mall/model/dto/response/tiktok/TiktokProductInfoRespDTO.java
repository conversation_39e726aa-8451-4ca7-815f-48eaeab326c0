package com.servauto.mall.model.dto.response.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TiktokProductInfoRespDTO {
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("message")
    private String message;
    @JsonProperty("request_id")
    private String requestId;

    @Data
    public static class DataDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("status")
        private String status;
        @JsonProperty("title")
        private String title;
        @JsonProperty("category_chains")
        private List<CategoryChainDTO> categoryChains;
        @JsonProperty("brand")
        private BrandDTO brand;
        @JsonProperty("main_images")
        private List<ImageDTO> mainImages;
        @JsonProperty("video")
        private VideoDTO video;
        @JsonProperty("description")
        private String description;
        @JsonProperty("package_dimensions")
        private PackageDimensionsDTO packageDimensions;
        @JsonProperty("package_weight")
        private PackageWeightDTO packageWeight;
        @JsonProperty("skus")
        private List<SkuDTO> skus;
        @JsonProperty("certifications")
        private List<CertificationDTO> certifications;
        @JsonProperty("size_chart")
        private SizeChartDTO sizeChart;
        @JsonProperty("is_cod_allowed")
        private Boolean isCodAllowed;
        @JsonProperty("product_attributes")
        private List<ProductAttributeDTO> productAttributes;
        @JsonProperty("audit_failed_reasons")
        private List<AuditFailedReasonDTO> auditFailedReasons;
        @JsonProperty("update_time")
        private Long updateTime;
        @JsonProperty("create_time")
        private Long createTime;
        @JsonProperty("delivery_options")
        private List<DeliveryOptionDTO> deliveryOptions;
        @JsonProperty("external_product_id")
        private String externalProductId;
        @JsonProperty("product_types")
        private List<String> productTypes;
        @JsonProperty("is_not_for_sale")
        private Boolean isNotForSale;
        @JsonProperty("recommended_categories")
        private List<RecommendedCategoryDTO> recommendedCategories;
        @JsonProperty("manufacturer_ids")
        private List<String> manufacturerIds;
        @JsonProperty("responsible_person_ids")
        private List<String> responsiblePersonIds;
        @JsonProperty("listing_quality_tier")
        private String listingQualityTier;
        @JsonProperty("integrated_platform_statuses")
        private List<IntegratedPlatformStatusDTO> integratedPlatformStatuses;
        @JsonProperty("shipping_insurance_requirement")
        private String shippingInsuranceRequirement;
        @JsonProperty("minimum_order_quantity")
        private Integer minimumOrderQuantity;
        @JsonProperty("is_pre_owned")
        private Boolean isPreOwned;
        @JsonProperty("audit")
        private AuditDTO audit;
        @JsonProperty("global_product_association")
        private GlobalProductAssociationDTO globalProductAssociation;
        @JsonProperty("prescription_requirement")
        private PrescriptionRequirementDTO prescriptionRequirement;
        @JsonProperty("product_families")
        private List<ProductFamilyDTO> productFamilies;
    }

    @Data
    public static class CategoryChainDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("parent_id")
        private String parentId;
        @JsonProperty("local_name")
        private String localName;
        @JsonProperty("is_leaf")
        private Boolean isLeaf;
    }

    @Data
    public static class BrandDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
    }

    @Data
    public static class ImageDTO {
        @JsonProperty("height")
        private Integer height;
        @JsonProperty("width")
        private Integer width;
        @JsonProperty("thumb_urls")
        private List<String> thumbUrls;
        @JsonProperty("uri")
        private String uri;
        @JsonProperty("urls")
        private List<String> urls;
    }

    @Data
    public static class VideoDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("cover_url")
        private String coverUrl;
        @JsonProperty("format")
        private String format;
        @JsonProperty("url")
        private String url;
        @JsonProperty("width")
        private Integer width;
        @JsonProperty("height")
        private Integer height;
        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class PackageDimensionsDTO {
        @JsonProperty("length")
        private String length;
        @JsonProperty("width")
        private String width;
        @JsonProperty("height")
        private String height;
        @JsonProperty("unit")
        private String unit;
    }

    @Data
    public static class PackageWeightDTO {
        @JsonProperty("value")
        private String value;
        @JsonProperty("unit")
        private String unit;
    }

    @Data
    public static class SkuDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("seller_sku")
        private String sellerSku;
        @JsonProperty("price")
        private PriceDTO price;
        @JsonProperty("inventory")
        private List<InventoryDTO> inventory;
        @JsonProperty("identifier_code")
        private IdentifierCodeDTO identifierCode;
        @JsonProperty("sales_attributes")
        private List<SalesAttributeDTO> salesAttributes;
        @JsonProperty("external_sku_id")
        private String externalSkuId;
        @JsonProperty("combined_skus")
        private List<CombinedSkuDTO> combinedSkus;
        @JsonProperty("global_listing_policy")
        private GlobalListingPolicyDTO globalListingPolicy;
        @JsonProperty("sku_unit_count")
        private String skuUnitCount;
        @JsonProperty("external_urls")
        private List<String> externalUrls;
        @JsonProperty("extra_identifier_codes")
        private List<String> extraIdentifierCodes;
        @JsonProperty("pre_sale")
        private PreSaleDTO preSale;
        @JsonProperty("list_price")
        private ListPriceDTO listPrice;
        @JsonProperty("external_list_prices")
        private List<ExternalListPriceDTO> externalListPrices;
    }

    @Data
    public static class PriceDTO {
        @JsonProperty("tax_exclusive_price")
        private String taxExclusivePrice;
        @JsonProperty("sale_price")
        private String salePrice;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("unit_price")
        private String unitPrice;
    }

    @Data
    public static class InventoryDTO {
        @JsonProperty("warehouse_id")
        private String warehouseId;
        @JsonProperty("quantity")
        private Integer quantity;
    }

    @Data
    public static class IdentifierCodeDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("type")
        private String type;
    }

    @Data
    public static class SalesAttributeDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("value_id")
        private String valueId;
        @JsonProperty("value_name")
        private String valueName;
        @JsonProperty("sku_img")
        private ImageDTO skuImg;
        @JsonProperty("supplementary_sku_images")
        private List<ImageDTO> supplementarySkuImages;
    }

    @Data
    public static class CombinedSkuDTO {
        @JsonProperty("product_id")
        private String productId;
        @JsonProperty("sku_id")
        private String skuId;
        @JsonProperty("sku_count")
        private Integer skuCount;
    }

    @Data
    public static class GlobalListingPolicyDTO {
        @JsonProperty("price_sync")
        private Boolean priceSync;
        @JsonProperty("inventory_type")
        private String inventoryType;
        @JsonProperty("replicate_source")
        private ReplicateSourceDTO replicateSource;
    }

    @Data
    public static class ReplicateSourceDTO {
        @JsonProperty("product_id")
        private String productId;
        @JsonProperty("shop_id")
        private String shopId;
        @JsonProperty("sku_id")
        private String skuId;
    }

    @Data
    public static class PreSaleDTO {
        @JsonProperty("type")
        private String type;
        @JsonProperty("fulfillment_type")
        private FulfillmentTypeDTO fulfillmentType;
    }

    @Data
    public static class FulfillmentTypeDTO {
        @JsonProperty("handling_duration_days")
        private Integer handlingDurationDays;
        @JsonProperty("release_date")
        private Long releaseDate;
    }

    @Data
    public static class ListPriceDTO {
        @JsonProperty("amount")
        private String amount;
        @JsonProperty("currency")
        private String currency;
    }

    @Data
    public static class ExternalListPriceDTO {
        @JsonProperty("source")
        private String source;
        @JsonProperty("amount")
        private String amount;
        @JsonProperty("currency")
        private String currency;
    }

    @Data
    public static class CertificationDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("title")
        private String title;
        @JsonProperty("files")
        private List<CertificationFileDTO> files;
        @JsonProperty("images")
        private List<ImageDTO> images;
        @JsonProperty("expiration_date")
        private Long expirationDate;
    }

    @Data
    public static class CertificationFileDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("urls")
        private List<String> urls;
        @JsonProperty("name")
        private String name;
        @JsonProperty("format")
        private String format;
    }

    @Data
    public static class SizeChartDTO {
        @JsonProperty("image")
        private ImageDTO image;
        @JsonProperty("template")
        private SizeChartTemplateDTO template;
    }

    @Data
    public static class SizeChartTemplateDTO {
        @JsonProperty("id")
        private String id;
    }

    @Data
    public static class ProductAttributeDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("values")
        private List<ProductAttributeValueDTO> values;
    }

    @Data
    public static class ProductAttributeValueDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
    }

    @Data
    public static class AuditFailedReasonDTO {
        @JsonProperty("position")
        private String position;
        @JsonProperty("reasons")
        private List<String> reasons;
        @JsonProperty("suggestions")
        private List<String> suggestions;
        @JsonProperty("listing_platform")
        private String listingPlatform;
    }

    @Data
    public static class DeliveryOptionDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("is_available")
        private Boolean isAvailable;
    }

    @Data
    public static class RecommendedCategoryDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("local_name")
        private String localName;
    }

    @Data
    public static class IntegratedPlatformStatusDTO {
        @JsonProperty("platform")
        private String platform;
        @JsonProperty("status")
        private String status;
    }

    @Data
    public static class AuditDTO {
        @JsonProperty("status")
        private String status;
        @JsonProperty("pre_approved_reasons")
        private List<String> preApprovedReasons;
    }

    @Data
    public static class GlobalProductAssociationDTO {
        @JsonProperty("global_product_id")
        private String globalProductId;
        @JsonProperty("sku_mappings")
        private List<SkuMappingDTO> skuMappings;
    }

    @Data
    public static class SkuMappingDTO {
        @JsonProperty("global_sku_id")
        private String globalSkuId;
        @JsonProperty("local_sku_id")
        private String localSkuId;
        @JsonProperty("sales_attribute_mappings")
        private List<SalesAttributeMappingDTO> salesAttributeMappings;
    }

    @Data
    public static class SalesAttributeMappingDTO {
        @JsonProperty("local_attribute_id")
        private String localAttributeId;
        @JsonProperty("global_attribute_id")
        private String globalAttributeId;
        @JsonProperty("local_value_id")
        private String localValueId;
        @JsonProperty("global_value_id")
        private String globalValueId;
    }

    @Data
    public static class PrescriptionRequirementDTO {
        @JsonProperty("needs_prescription")
        private Boolean needsPrescription;
    }

    @Data
    public static class ProductFamilyDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("products")
        private List<ProductIdDTO> products;
    }

    @Data
    public static class ProductIdDTO {
        @JsonProperty("id")
        private String id;
    }
} 