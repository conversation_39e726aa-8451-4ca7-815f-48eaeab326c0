package com.servauto.mall.model.dto.response.payment.I2c2p;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class I2c2pPaymentInquiryDetailDTO {
    private String accountNo;
    private String customerToken;
    private String customerTokenExpiry;
    private String loyaltyPoints;
    private String uniqueAccountReference;
    private String childMerchantID;
    private String processBy;
    private String paymentID;
    private String schemePaymentID;
    private String merchantID;
    private String invoiceNo;
    private BigDecimal amount;
    private String monthlyPayment;
    private String userDefined1;
    private String userDefined2;
    private String userDefined3;
    private String userDefined4;
    private String userDefined5;
    private String currencyCode;
    private String recurringUniqueID;
    private String tranRef;
    private String referenceNo;
    private String approvalCode;
    private String eci;
    private String transactionDateTime;
    private String agentCode;
    private String channelCode;
    private String issuerCountry;
    private String issuerBank;
    private String installmentMerchantAbsorbRate;
    private String paymentScheme;
    private boolean displayProcessingAmount;
    private String respCode;
    private String respDesc;
}