package com.servauto.mall.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Car Brand Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarBrandsDTO {

    @Schema(description = "Car Brand ID", example = "1")
    private Long id;

    @Schema(description = "Car Brand Name", example = "Toyota")
    private String brandName;

    @Schema(description = "Car Brand Order", example = "1")
    private Integer order;

}