package com.servauto.mall.model.dto.response.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>OrderCustomerDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/27 12:59
 */
@Schema(description = "Order - order customer product response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderCustomerDTO {

    @Schema(description = "real name", example = "orton.zhang")
    private String realName;

    @Schema(description = "customer mobile", example = "18888888888")
    private String mobile;

    @Schema(description = "customer email", example = "<EMAIL>")
    private String email;
}
