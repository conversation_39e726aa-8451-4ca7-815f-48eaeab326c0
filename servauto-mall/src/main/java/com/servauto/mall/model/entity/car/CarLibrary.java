package com.servauto.mall.model.entity.car;

import java.util.Date;

public class CarLibrary {
    private Integer id;

    private String carLibId;

    private String manufacturer;

    private String brand;

    private String series;

    private String model;

    private String modelYear;

    private String grade;

    private String chassisCode;

    private String trn;

    private String transmissionType;

    private String driverPosition;

    private String vehicleType;

    private Byte doors;

    private String engineModel;

    private String displacement;

    private String powerKw;

    private String fuelType;

    private String driveModel;

    private String countryOfSale;

    private String areaOfSale;

    private String tireFront;

    private String tireRear;

    private Double oilCapacity;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCarLibId() {
        return carLibId;
    }

    public void setCarLibId(String carLibId) {
        this.carLibId = carLibId == null ? null : carLibId.trim();
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer == null ? null : manufacturer.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series == null ? null : series.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear == null ? null : modelYear.trim();
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade == null ? null : grade.trim();
    }

    public String getChassisCode() {
        return chassisCode;
    }

    public void setChassisCode(String chassisCode) {
        this.chassisCode = chassisCode == null ? null : chassisCode.trim();
    }

    public String getTrn() {
        return trn;
    }

    public void setTrn(String trn) {
        this.trn = trn == null ? null : trn.trim();
    }

    public String getTransmissionType() {
        return transmissionType;
    }

    public void setTransmissionType(String transmissionType) {
        this.transmissionType = transmissionType == null ? null : transmissionType.trim();
    }

    public String getDriverPosition() {
        return driverPosition;
    }

    public void setDriverPosition(String driverPosition) {
        this.driverPosition = driverPosition == null ? null : driverPosition.trim();
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType == null ? null : vehicleType.trim();
    }

    public Byte getDoors() {
        return doors;
    }

    public void setDoors(Byte doors) {
        this.doors = doors;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel == null ? null : engineModel.trim();
    }

    public String getDisplacement() {
        return displacement;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement == null ? null : displacement.trim();
    }

    public String getPowerKw() {
        return powerKw;
    }

    public void setPowerKw(String powerKw) {
        this.powerKw = powerKw == null ? null : powerKw.trim();
    }

    public String getFuelType() {
        return fuelType;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType == null ? null : fuelType.trim();
    }

    public String getDriveModel() {
        return driveModel;
    }

    public void setDriveModel(String driveModel) {
        this.driveModel = driveModel == null ? null : driveModel.trim();
    }

    public String getCountryOfSale() {
        return countryOfSale;
    }

    public void setCountryOfSale(String countryOfSale) {
        this.countryOfSale = countryOfSale == null ? null : countryOfSale.trim();
    }

    public String getAreaOfSale() {
        return areaOfSale;
    }

    public void setAreaOfSale(String areaOfSale) {
        this.areaOfSale = areaOfSale == null ? null : areaOfSale.trim();
    }

    public String getTireFront() {
        return tireFront;
    }

    public void setTireFront(String tireFront) {
        this.tireFront = tireFront == null ? null : tireFront.trim();
    }

    public String getTireRear() {
        return tireRear;
    }

    public void setTireRear(String tireRear) {
        this.tireRear = tireRear == null ? null : tireRear.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Double getOilCapacity() {
        return oilCapacity;
    }

    public void setOilCapacity(Double oilCapacity) {
        this.oilCapacity = oilCapacity;
    }
}