package com.servauto.mall.model.dto.response.payment.ipay88;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Ipay88RequeryResult {

    private String xmlResult;

    @JsonProperty("Body")
    private Body body;

    @Data
    public static class Body {
        @JsonProperty("TxDetailsInquiryCardInfoResponse")
        private TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse;
    }

    @Data
    public static class TxDetailsInquiryCardInfoResponse {
        @JsonProperty("TxDetailsInquiryCardInfoResult")
        private TxDetailsInquiryCardInfoResult txDetailsInquiryCardInfoResult;
    }

    @Data
    public static class TxDetailsInquiryCardInfoResult {
        @JsonProperty("PaymentId")
        private String paymentId;
        @JsonProperty("RefNo")
        private String refNo;
        @JsonProperty("Amount")
        private String amount;
        @JsonProperty("Currency")
        private String currency;
        @JsonProperty("Remark")
        private String remark;
        @JsonProperty("TransId")
        private String transId;
        @JsonProperty("AuthCode")
        private String authCode;
        @JsonProperty("Status")
        private String status;
        @JsonProperty("Errdesc")
        private String errdesc;
    }
}