package com.servauto.mall.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Car Info Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarInfoDTO {

    @Schema(description = "brand", example = "bmw")
    private String brand;

    @Schema(description = "model", example = "x3")
    private String model;

    @Schema(description = "year", example = "2108")
    private String year;

    @Schema(description = "variant", example = "M Sport")
    private String variant;

    @Schema(description = "icon", example = "icon")
    private String icon;

    @Schema(description = "licensePlate", example = "AX8898")
    private String licensePlate;

    @Schema(description = "transmissionType", example = "auto")
    private String transmissionType;

    @Schema(description = "defaultCar", example = "ture")
    private Boolean defaultCar;

    @Schema(description = "carId", example = "25")
    private Long carId;

}