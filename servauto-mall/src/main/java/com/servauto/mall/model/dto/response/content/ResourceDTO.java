package com.servauto.mall.model.dto.response.content;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Content - query content resource response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResourceDTO {

    @Schema(description = "resource id")
    private Long id;

    @Schema(description = "resource title")
    private String title;

    @Schema(description = "resource sequence")
    private Long seq;

    @Schema(description = "resource picture URI")
    private String pictUri;

    @Schema(description = "resource entry URI")
    private String entryUri;

    @Schema(description = "resource entry type")
    private String entryType;

    @Schema(description = "resource ext")
    private String ext;

}
