package com.servauto.mall.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Schema(description = "Customer - carInfo Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateCarInfoReqDTO {

    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long brandId;

    @Schema(description = "modelId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long modelId;

    @Schema(description = "yearId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long yearId;

    @Schema(description = "variantId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long variantId;

    @Schema(description = "license Plate", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "XA1024")
    private String licensePlate;

    @Schema(description = "carMileage", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "25")
    private Long carMileage;
}