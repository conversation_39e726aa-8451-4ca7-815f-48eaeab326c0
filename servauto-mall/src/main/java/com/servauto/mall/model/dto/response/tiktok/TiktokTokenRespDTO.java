package com.servauto.mall.model.dto.response.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Schema(description = "TikTok Shop Token Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class TiktokTokenRespDTO {

    @Schema(description = "Response code", example = "0")
    private Integer code;

    @Schema(description = "Response message", example = "success")
    private String message;

    @Schema(description = "Shop secrets data")
    private TokenData data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenData {

        @Schema(description = "Access token", example = "act.example12345Example12345Example")
        @JsonProperty("access_token")
        private String accessToken;

        @Schema(description = "Access token Expiration  timestamp", example = "1751610133")
        @JsonProperty("access_token_expire_in")
        private Long accessTokenExpiresIn;


        @Schema(description = "Refresh token", example = "rft.example12345Example12345Example")
        @JsonProperty("refresh_token")
        private String refreshToken;


        @Schema(description = "Refresh token Expiration  timestamp", example = "4872584492")
        @JsonProperty("refresh_token_expire_in")
        private Long refreshTokenExpiresIn;

        @JsonProperty("open_id")
        private String openId;

        @Schema(description = "seller name", example = "ServAuto Malaysia")
        @JsonProperty("seller_name")
        private String sellerName;

        @JsonProperty("seller_base_region")
        private String sellerBaseRegion;

        @JsonProperty("user_type")
        private Integer userType;


        @JsonProperty("granted_scopes")
        private List<String> grantedScopes;
    }


}
