package com.servauto.mall.model.dto.response.product;


import com.servauto.mall.model.dto.response.IdNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product Attribute Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductAttributesDTO {
    @Schema(description = "brands", example = "[{}]")
    private List<IdNameDTO> brands;

    @Schema(description = "topAttribute", example = "{}")
    private ProductAttributeDTO topAttribute;

    @Schema(description = "attributes", example = "[{}]")
    private List<ProductAttributeDTO> attributes;
}
