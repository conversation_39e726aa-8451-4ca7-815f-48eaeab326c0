package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Order - create order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateOrderReqDTO {

    @Schema(description = "carId", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "CarId cannot null")
    private Long carId;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private Long serviceId;

    @Schema(description = "packageId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private Long packageId;

    @Schema(description = "product list", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @NotEmpty(message = "please select your products")
    List<OrderProductReqDTO> products;

    @Schema(description = "remark", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "")
    private String remark;
}
