package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Order - query order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryOrderReqDTO {

    @Schema(hidden = true)
    private Long customerId;

    @Schema(description = "orderNo", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "orderNo")
    private String orderNo;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "status")
    private String status;

    @Schema(hidden = true)
    private List<String> statuses;
}
