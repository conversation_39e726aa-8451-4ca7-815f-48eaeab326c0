package com.servauto.mall.model.dto.response.product;

import com.servauto.mall.model.dto.response.NameValuesDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceDTO;
import com.servauto.mall.model.dto.response.TypeNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Product Attribute Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "oil")
    private String name;

    @Schema(description = "categoryId", example = "1")
    private Long categoryId;

    @Schema(description = "categoryName", example = "categoryName")
    private String categoryName;

    @Schema(description = "brandId", example = "1")
    private Long brandId;

    @Schema(description = "brandName", example = "brandName")
    private String brandName;

    @Schema(description = "status, 1: Listed, 2: Unlisted", example = "1")
    private Integer status;

    @Schema(description = "statusName", example = "statusName")
    private String statusName;

    @Schema(description = "serviceId", example = "1")
    private Long serviceId;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "price", example = "10000.00")
    private BigDecimal price;

    @Schema(description = "mainImage", example = "https://www.toyota.com.cn/")
    private String mainImage;

    @Schema(description = "description", example = "oil")
    private String description;

    @Schema(description = "featuredTags", example = "[{}]")
    private List<String> featuredTags;

    @Schema(description = "deliveryModes", example = "[1]")
    private List<TypeNameDTO> deliveryModes;

    @Schema(description = "attributeLabel", example = "[]")
    private List<String> attributeLabel;

    @Schema(description = "attributeValues", example = "[{attributeId: 1, attributeName: name, value: value, order: 1. isRequired: true}]")
    private List<ProductAttributeValueDTO> attributeValues;

    @Schema(description = "skuAttributeValues", example = "{}")
    private List<NameValuesDTO> skuAttributeValues;

    @Schema(description = "coverImages", example = "[https://www.toyota.com.cn]")
    private List<String> coverImages;

    @Schema(description = "detailImages", example = "[https://www.toyota.com.cn]")
    private List<String> detailImages;

    @Schema(description = "serviceDTO", example = "{}")
    private ServiceDTO serviceDTO;

    @Schema(description = "packages", example = "[{}]")
    private List<PackageDTO> packages;
}
