package com.servauto.mall.model.dto.response.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "TikTok Orders Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TiktokOrdersRespDTO {

    @Schema(description = "Response code", example = "0")
    private Integer code;

    @Schema(description = "Response message", example = "Success")
    private String message;

    @Schema(description = "Request ID", example = "202203070749000101890810281E8C70B7")
    @JsonProperty("request_id")
    private String requestId;

    @Schema(description = "Orders data")
    private OrdersData data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrdersData {
        
        @Schema(description = "Next page token")
        @JsonProperty("next_page_token")
        private String nextPageToken;

        @Schema(description = "Total count", example = "22113")
        @JsonProperty("total_count")
        private Long totalCount;

        @Schema(description = "List of orders")
        private List<Order> orders;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Order {

        @Schema(description = "Order ID", example = "578696815604172459")
        private String id;

        @Schema(description = "Buyer message", example = "Please ship asap!")
        @JsonProperty("buyer_message")
        private String buyerMessage;

        @Schema(description = "Order status", example = "COMPLETED")
        private String status;

        @Schema(description = "Create time", example = "1746277077")
        @JsonProperty("create_time")
        private Long createTime;

        @Schema(description = "Update time", example = "1749053902")
        @JsonProperty("update_time")
        private Long updateTime;

        @Schema(description = "Paid time", example = "1746277090")
        @JsonProperty("paid_time")
        private Long paidTime;

        @Schema(description = "Fulfillment type", example = "FULFILLMENT_BY_SELLER")
        @JsonProperty("fulfillment_type")
        private String fulfillmentType;

        @Schema(description = "Delivery type", example = "HOME_DELIVERY")
        @JsonProperty("delivery_type")
        private String deliveryType;

        @Schema(description = "Shipping type", example = "SELLER")
        @JsonProperty("shipping_type")
        private String shippingType;

        @Schema(description = "Payment method name", example = "Atome")
        @JsonProperty("payment_method_name")
        private String paymentMethodName;

        @Schema(description = "Tracking number", example = "XS2023103779")
        @JsonProperty("tracking_number")
        private String trackingNumber;

        @Schema(description = "User ID", example = "7494019674881296043")
        @JsonProperty("user_id")
        private String userId;

        @Schema(description = "Buyer email", example = "<EMAIL>")
        @JsonProperty("buyer_email")
        private String buyerEmail;

        @Schema(description = "Is COD order", example = "false")
        @JsonProperty("is_cod")
        private Boolean isCod;

        @Schema(description = "Is on hold order", example = "false")
        @JsonProperty("is_on_hold_order")
        private Boolean isOnHoldOrder;

        @Schema(description = "Is replacement order", example = "false")
        @JsonProperty("is_replacement_order")
        private Boolean isReplacementOrder;

        @Schema(description = "Is sample order", example = "false")
        @JsonProperty("is_sample_order")
        private Boolean isSampleOrder;

        @Schema(description = "Cancel order SLA time", example = "1747583999")
        @JsonProperty("cancel_order_sla_time")
        private Long cancelOrderSlaTime;

        @Schema(description = "Collection due time", example = "1747583999")
        @JsonProperty("collection_due_time")
        private Long collectionDueTime;

        @Schema(description = "Collection time", example = "1746437238")
        @JsonProperty("collection_time")
        private Long collectionTime;

        @Schema(description = "Commerce platform", example = "TIKTOK_SHOP")
        @JsonProperty("commerce_platform")
        private String commercePlatform;

        @Schema(description = "Delivery option ID", example = "7101328587784980225")
        @JsonProperty("delivery_option_id")
        private String deliveryOptionId;

        @Schema(description = "Delivery option name", example = "Shipped by seller")
        @JsonProperty("delivery_option_name")
        private String deliveryOptionName;

        @Schema(description = "Delivery SLA time", example = "1747313891")
        @JsonProperty("delivery_sla_time")
        private Long deliverySlaTime;

        @Schema(description = "Delivery time", example = "**********")
        @JsonProperty("delivery_time")
        private Long deliveryTime;

        @Schema(description = "Has updated recipient address", example = "false")
        @JsonProperty("has_updated_recipient_address")
        private Boolean hasUpdatedRecipientAddress;

        @Schema(description = "RTS time", example = "**********")
        @JsonProperty("rts_time")
        private Long rtsTime;

        @Schema(description = "Shipping provider", example = "Shipped by seller")
        @JsonProperty("shipping_provider")
        private String shippingProvider;

        @Schema(description = "Shipping provider ID", example = "7083316167611254529")
        @JsonProperty("shipping_provider_id")
        private String shippingProviderId;

        @Schema(description = "TTS SLA time", example = "**********")
        @JsonProperty("tts_sla_time")
        private Long ttsSlaTime;

        @Schema(description = "Warehouse ID", example = "7449716424254080775")
        @JsonProperty("warehouse_id")
        private String warehouseId;

        @Schema(description = "Payment information")
        private Payment payment;

        @Schema(description = "Recipient address")
        @JsonProperty("recipient_address")
        private RecipientAddress recipientAddress;

        @Schema(description = "Line items")
        @JsonProperty("line_items")
        private List<LineItem> lineItems;

        @Schema(description = "Packages")
        private List<Package> packages;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Payment {

        @Schema(description = "Currency", example = "MYR")
        private String currency;

        @Schema(description = "Sub total", example = "199")
        @JsonProperty("sub_total")
        private String subTotal;

        @Schema(description = "Shipping fee", example = "0")
        @JsonProperty("shipping_fee")
        private String shippingFee;

        @Schema(description = "Total amount", example = "199")
        @JsonProperty("total_amount")
        private String totalAmount;

        @Schema(description = "Seller discount", example = "759")
        @JsonProperty("seller_discount")
        private String sellerDiscount;

        @Schema(description = "Platform discount", example = "0")
        @JsonProperty("platform_discount")
        private String platformDiscount;

        @Schema(description = "Tax", example = "0")
        private String tax;

        @Schema(description = "Original shipping fee", example = "0")
        @JsonProperty("original_shipping_fee")
        private String originalShippingFee;

        @Schema(description = "Original total product price", example = "958")
        @JsonProperty("original_total_product_price")
        private String originalTotalProductPrice;

        @Schema(description = "Shipping fee cofunded discount", example = "0")
        @JsonProperty("shipping_fee_cofunded_discount")
        private String shippingFeeCofundedDiscount;

        @Schema(description = "Shipping fee platform discount", example = "0")
        @JsonProperty("shipping_fee_platform_discount")
        private String shippingFeePlatformDiscount;

        @Schema(description = "Shipping fee seller discount", example = "0")
        @JsonProperty("shipping_fee_seller_discount")
        private String shippingFeeSellerDiscount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RecipientAddress {

        @Schema(description = "Full address", example = "Malaysia, Selangor, Sepang,No 41 Jalan Abadi 1/23, Abadi heights")
        @JsonProperty("full_address")
        private String fullAddress;

        @Schema(description = "Phone number", example = "(+60)0132879566")
        @JsonProperty("phone_number")
        private String phoneNumber;

        @Schema(description = "Name", example = "muhammad nazri amri")
        private String name;

        @Schema(description = "First name", example = "")
        @JsonProperty("first_name")
        private String firstName;

        @Schema(description = "Last name", example = "")
        @JsonProperty("last_name")
        private String lastName;

        @Schema(description = "First name local script", example = "")
        @JsonProperty("first_name_local_script")
        private String firstNameLocalScript;

        @Schema(description = "Last name local script", example = "")
        @JsonProperty("last_name_local_script")
        private String lastNameLocalScript;

        @Schema(description = "Address detail", example = "No 41 Jalan Abadi 1/23, Abadi heights")
        @JsonProperty("address_detail")
        private String addressDetail;

        @Schema(description = "Address line 1", example = "No 41 Jalan Abadi 1/23,")
        @JsonProperty("address_line1")
        private String addressLine1;

        @Schema(description = "Address line 2", example = "Abadi heights")
        @JsonProperty("address_line2")
        private String addressLine2;

        @Schema(description = "Address line 3", example = "")
        @JsonProperty("address_line3")
        private String addressLine3;

        @Schema(description = "Address line 4", example = "")
        @JsonProperty("address_line4")
        private String addressLine4;

        @Schema(description = "Postal code", example = "47120")
        @JsonProperty("postal_code")
        private String postalCode;

        @Schema(description = "Region code", example = "MY")
        @JsonProperty("region_code")
        private String regionCode;

        @Schema(description = "District information")
        @JsonProperty("district_info")
        private List<DistrictInfo> districtInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DistrictInfo {

        @Schema(description = "Address level", example = "L0")
        @JsonProperty("address_level")
        private String addressLevel;

        @Schema(description = "Address level name", example = "Country")
        @JsonProperty("address_level_name")
        private String addressLevelName;

        @Schema(description = "Address name", example = "Malaysia")
        @JsonProperty("address_name")
        private String addressName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LineItem {

        @Schema(description = "Line item ID", example = "578696815604303531")
        private String id;

        @Schema(description = "SKU ID", example = "1730860631404611310")
        @JsonProperty("sku_id")
        private String skuId;

        @Schema(description = "Product ID", example = "1730860662131951342")
        @JsonProperty("product_id")
        private String productId;

        @Schema(description = "Product name", example = "[RAYA SPECIAL] ClearShield 3Pc Automotive Tinted Films")
        @JsonProperty("product_name")
        private String productName;

        @Schema(description = "Seller SKU", example = "EcoShield(S,M,L,XL)")
        @JsonProperty("seller_sku")
        private String sellerSku;

        @Schema(description = "SKU name", example = "Lalai")
        @JsonProperty("sku_name")
        private String skuName;

        @Schema(description = "SKU image URL")
        @JsonProperty("sku_image")
        private String skuImage;

        @Schema(description = "Sale price", example = "199")
        @JsonProperty("sale_price")
        private String salePrice;

        @Schema(description = "Original price", example = "958")
        @JsonProperty("original_price")
        private String originalPrice;

        @Schema(description = "Currency", example = "MYR")
        private String currency;

        @Schema(description = "Display status", example = "COMPLETED")
        @JsonProperty("display_status")
        private String displayStatus;

        @Schema(description = "Package status", example = "COMPLETED")
        @JsonProperty("package_status")
        private String packageStatus;

        @Schema(description = "Is gift", example = "false")
        @JsonProperty("is_gift")
        private Boolean isGift;

        @Schema(description = "Package ID", example = "1161390582945449643")
        @JsonProperty("package_id")
        private String packageId;

        @Schema(description = "Platform discount", example = "0")
        @JsonProperty("platform_discount")
        private String platformDiscount;

        @Schema(description = "Seller discount", example = "759")
        @JsonProperty("seller_discount")
        private String sellerDiscount;

        @Schema(description = "RTS time", example = "**********")
        @JsonProperty("rts_time")
        private Long rtsTime;

        @Schema(description = "Shipping provider ID", example = "7083316167611254529")
        @JsonProperty("shipping_provider_id")
        private String shippingProviderId;

        @Schema(description = "Shipping provider name", example = "Shipped by seller")
        @JsonProperty("shipping_provider_name")
        private String shippingProviderName;

        @Schema(description = "SKU type", example = "NORMAL")
        @JsonProperty("sku_type")
        private String skuType;

        @Schema(description = "Tracking number", example = "XS2023103779")
        @JsonProperty("tracking_number")
        private String trackingNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Package {
        
        @Schema(description = "Package ID", example = "1152321127278713123")
        private String id;
    }
}
