package com.servauto.mall.model.dto.request.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "Customer - edit Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreatePaymentReqDTO {

    @Schema(description = "userName", requiredMode = Schema.RequiredMode.REQUIRED, example = "Tom Bob")
    private String userName;

    @Schema(description = "userEmail", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String userEmail;

    @Schema(description = "userContact", requiredMode = Schema.RequiredMode.REQUIRED, example = "+8608988898")
    private String userContact;

    @Schema(description = "orderNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "P321984763294289")
    private String orderNo;

    @Schema(description = "amount", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,278.99")
    private BigDecimal amount;

    @Schema(description = "productDesc", requiredMode = Schema.RequiredMode.REQUIRED, example = "Product description")
    private String productDesc;
}