package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>ReservationOrderReqDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/24 13:30
 */
@Schema(description = "Order - reservation order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReservationOrderReqDTO {

    @Schema(hidden = true)
    private String orderNo;

    @Schema(description = "workshop id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1020000004")
    @NotNull(message = "workshop id cannot be null")
    private Long workshopId;

    @Schema(description = "reservationTime", requiredMode = Schema.RequiredMode.REQUIRED, example = "1020000004")
    @NotNull(message = "reservation time cannot be null")
    private Long reservationTime;


}
