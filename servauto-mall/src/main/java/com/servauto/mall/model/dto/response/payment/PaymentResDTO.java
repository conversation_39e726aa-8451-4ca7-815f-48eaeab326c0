package com.servauto.mall.model.dto.response.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Payment - create Response VO")
@Data
public class PaymentResDTO {

    @Schema(description = "refNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "P321984763294289")
    private String payNo;

    @Schema(description = "amount", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,278.99")
    private String amount;

    @Schema(description = "paymentUrl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://demo.com/mall/response")
    private String paymentUrl;

    @Schema(description = "remark", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Tom Bob")
    private String remark;


}
