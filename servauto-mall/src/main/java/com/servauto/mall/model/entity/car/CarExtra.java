package com.servauto.mall.model.entity.car;

import java.util.Date;

public class CarExtra {
    private Long id;

    private String carLibId;

    private Long brandId;

    private Long modelId;

    private Long yearId;

    private String variantName;

    private String transmissionType;

    private String displacementValue;

    private String carType;

    private String tireFront;

    private String tireRear;

    private Integer status;

    private Integer order;

    private Date createdTime;

    private Date updatedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCarLibId() {
        return carLibId;
    }

    public void setCarLibId(String carLibId) {
        this.carLibId = carLibId == null ? null : carLibId.trim();
    }

    public String getDisplacementValue() {
        return displacementValue;
    }

    public void setDisplacementValue(String displacementValue) {
        this.displacementValue = displacementValue == null ? null : displacementValue.trim();
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType == null ? null : carType.trim();
    }

    public String getTireFront() {
        return tireFront;
    }

    public void setTireFront(String tireFront) {
        this.tireFront = tireFront == null ? null : tireFront.trim();
    }

    public String getTireRear() {
        return tireRear;
    }

    public void setTireRear(String tireRear) {
        this.tireRear = tireRear == null ? null : tireRear.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public Long getYearId() {
        return yearId;
    }

    public void setYearId(Long yearId) {
        this.yearId = yearId;
    }

    public String getTransmissionType() {
        return transmissionType;
    }

    public void setTransmissionType(String transmissionType) {
        this.transmissionType = transmissionType;
    }

    public String getVariantName() {
        return variantName;
    }

    public void setVariantName(String variantName) {
        this.variantName = variantName;
    }
}