package com.servauto.mall.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Schema(description = "Order - submit order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubmitOrderReqDTO {

    @Schema(description = "order no", requiredMode = Schema.RequiredMode.REQUIRED, example = "123123123123")
    @NotBlank(message = "orderNo cannot null")
    private String orderNo;

    @Schema(description = "carId", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "carId cannot null")
    private Long carId;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private Long serviceId;

    @Schema(description = "packageId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private Long packageId;

    @Schema(description = "product list", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @NotEmpty(message = "please select your products")
    List<OrderProductReqDTO> products;

    @Schema(description = "delivery type", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "SHIPPING/PICKUP/IN-WORKSHOP")
    private String deliveryType;

    @Schema(description = "workshop id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123")
    private Long workshopId;

    @Schema(description = "delivery address", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123")
    private Long shippingAddressId;

    @Schema(description = "reservation time", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "Long")
    private Long reservationTime;

    @Schema(description = "submit license plate", requiredMode = Schema.RequiredMode.REQUIRED, example = "J_A888888")
    @NotBlank(message = "licensePlate cannot null")
    private String licensePlate;

    @Schema(description = "submit email", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "<EMAIL>")
    private String email;

}
