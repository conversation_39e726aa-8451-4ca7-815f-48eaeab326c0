package com.servauto.mall.model.dto.request.packageinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Package Info - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryPackagesDTO {
    @Schema(description = "name, fuzzy query", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long serviceId;

    @Schema(description = "code", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "code")
    private String code;

    @Schema(description = "ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> ids;
}
