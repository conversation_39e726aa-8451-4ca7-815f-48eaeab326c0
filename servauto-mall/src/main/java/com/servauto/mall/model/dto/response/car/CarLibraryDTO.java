package com.servauto.mall.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Car Library Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarLibraryDTO {

    @Schema(description = "Car Library ID", example = "1")
    private Integer id;

    @Schema(description = "Car Library ID", example = "CL001")
    private String carLibId;

    @Schema(description = "Manufacturer", example = "Toyota")
    private String manufacturer;

    @Schema(description = "Brand", example = "Corolla")
    private String brand;

    @Schema(description = "Series", example = "Series 1")
    private String series;

    @Schema(description = "Model", example = "Model A")
    private String model;

    @Schema(description = "Model Year", example = "2023")
    private String modelYear;

    @Schema(description = "Grade", example = "2023")
    private String grade;

    @Schema(description = "Chassis Code", example = "CC001")
    private String chassisCode;

    @Schema(description = "Transmission Type", example = "Automatic")
    private String transmissionType;

    @Schema(description = "Driver Position", example = "Right")
    private String driverPosition;

    @Schema(description = "Vehicle Type", example = "Sedan")
    private String vehicleType;

    @Schema(description = "Doors", example = "4")
    private Byte doors;

    @Schema(description = "Engine Model", example = "1.8L")
    private String engineModel;

    @Schema(description = "Displacement", example = "1800")
    private String displacement;

    @Schema(description = "Power (kW)", example = "102")
    private String powerKw;

    @Schema(description = "Fuel Type", example = "Petrol")
    private String fuelType;

    @Schema(description = "Drive Model", example = "FWD")
    private String driveModel;

    @Schema(description = "Country of Sale", example = "Japan")
    private String countryOfSale;

    @Schema(description = "Area of Sale", example = "Asia")
    private String areaOfSale;

    @Schema(description = "Tire Front", example = "225/45 R18")
    private String tireFront;

    @Schema(description = "Tire Rear", example = "225/45 R18")
    private String tireRear;

    @Schema(description = "Oil Capacity", example = "5")
    private Double oilCapacity;

    @Schema(description = "Car Library Status", example = "1")
    private Integer status;

    @Schema(description = "Car Library Created Time", example = "2023-10-01T00:00:00Z")
    private Date createTime;

    @Schema(description = "Car Library Updated Time", example = "2023-10-01T00:00:00Z")
    private Date updateTime;
}