package com.servauto.mall.model.dto.request.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product - id values Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdValuesDTO {
    @JsonIgnore
    private Long categoryId;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "values", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[]")
    private List<String> values;
}
