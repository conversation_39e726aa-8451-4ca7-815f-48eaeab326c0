package com.servauto.mall.support.security.core.handler;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import com.servauto.mall.support.security.core.utils.WebFrameworkUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import static com.servauto.common.core.domain.ResponseCode.FORBIDDEN;

@Slf4j
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) {
        log.warn("[commence][User({}) does not have sufficient permissions to access URL({})]", request.getRequestURI(), SecurityFrameworkUtils.getLoginUserId(), e);
        WebFrameworkUtils.renderString(response, JacksonSerializer.serialize(CommonResult.of(FORBIDDEN)));
    }

}
