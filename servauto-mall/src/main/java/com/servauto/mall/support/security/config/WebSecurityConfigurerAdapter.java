package com.servauto.mall.support.security.config;

import com.google.common.collect.Multimap;
import com.servauto.mall.support.security.core.filter.TokenAuthenticationFilter;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

public class WebSecurityConfigurerAdapter {

    @Resource
    private AuthenticationEntryPoint authenticationEntryPoint;

    @Resource
    private AccessDeniedHandler accessDeniedHandler;

    @Resource
    private TokenAuthenticationFilter authenticationTokenFilter;

    @Resource
    private PermitAllUrlConfiguration permitAllUrlConfiguration;

    /**
     * 配置 URL 的安全配置
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Bean
    protected SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                .cors(Customizer.withDefaults())
                .csrf(AbstractHttpConfigurer::disable)
                .sessionManagement(c -> c.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .headers(c -> c.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable))
                .exceptionHandling(c -> c.authenticationEntryPoint(authenticationEntryPoint).accessDeniedHandler(accessDeniedHandler));

        Multimap<HttpMethod, String> permitUrls = permitAllUrlConfiguration.getPermitAllUrls();
        httpSecurity.authorizeHttpRequests(c -> c
                .requestMatchers("/druid/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/favicon.ico", "/webjars/**", "/v3/api-docs/**", "/swagger-ui/**", "/*.html", "/*.css", "/*.js").permitAll()
                .requestMatchers(HttpMethod.GET, permitUrls.get(HttpMethod.GET).toArray(new String[0])).permitAll()
                .requestMatchers(HttpMethod.POST, permitUrls.get(HttpMethod.POST).toArray(new String[0])).permitAll()
                .requestMatchers(HttpMethod.PUT, permitUrls.get(HttpMethod.PUT).toArray(new String[0])).permitAll()
                .requestMatchers(HttpMethod.DELETE, permitUrls.get(HttpMethod.DELETE).toArray(new String[0])).permitAll()
                .requestMatchers(HttpMethod.HEAD, permitUrls.get(HttpMethod.HEAD).toArray(new String[0])).permitAll()
                .requestMatchers(HttpMethod.PATCH, permitUrls.get(HttpMethod.PATCH).toArray(new String[0])).permitAll()
        ).authorizeHttpRequests(c -> c.anyRequest().authenticated());

        httpSecurity.addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        return httpSecurity.build();
    }
}
