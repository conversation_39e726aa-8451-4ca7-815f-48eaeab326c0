package com.servauto.mall.support;

import com.servauto.framework.config.SwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class ApiWebConfiguration {

    @Bean
    @ConditionalOnProperty(prefix = "springdoc.api-docs", name = "enabled", havingValue = "true")
    public GroupedOpenApi sysGroupedOpenApi() {
        return SwaggerAutoConfiguration.buildGroupedOpenApi("mall", "");
    }
}
