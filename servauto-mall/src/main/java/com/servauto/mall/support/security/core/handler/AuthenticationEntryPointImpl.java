package com.servauto.mall.support.security.core.handler;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.support.security.core.utils.WebFrameworkUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;


@Slf4j
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        log.debug("[commence][When accessing URL({}), no login]", request.getRequestURI(), e);
        WebFrameworkUtils.renderString(response, JacksonSerializer.serialize(CommonResult.of(ResponseCode.UNAUTHORIZED)));
    }

}
