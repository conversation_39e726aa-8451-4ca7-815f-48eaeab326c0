package com.servauto.mall.support.oauth.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 访问令牌创建 Request DTO
 */
@Data
public class AccessTokenCreateReqDTO implements Serializable {

    /**
     * 用户编号
     */
    @NotNull(message = "userId cannot null")
    private Long userId;

    /**
     * 授权范围
     */
    private List<String> scopes;

}
