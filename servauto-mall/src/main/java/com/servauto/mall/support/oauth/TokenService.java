package com.servauto.mall.support.oauth;

import com.servauto.mall.support.oauth.dto.AccessTokenCreateReqDTO;
import com.servauto.mall.support.oauth.dto.AccessTokenRespDTO;
import jakarta.validation.Valid;


public interface TokenService {

    /**
     * 创建访问令牌
     *
     * @param accessToken 访问令牌的信息
     * @return 访问令牌的信息
     */
    AccessTokenRespDTO getAccessToken(String accessToken);

    /**
     * 创建访问令牌
     *
     * @param reqDTO 访问令牌的创建信息
     * @return 访问令牌的信息
     */
    AccessTokenRespDTO createAccessToken(@Valid AccessTokenCreateReqDTO reqDTO);

    /**
     * 校验访问令牌
     *
     * @param accessToken 访问令牌
     * @return 访问令牌的信息
     */
    AccessTokenRespDTO checkAccessToken(String accessToken);

    /**
     * 移除访问令牌
     *
     * @param accessToken 访问令牌
     */
    void removeAccessToken(String accessToken);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 访问令牌的信息
     */
    AccessTokenRespDTO refreshAccessToken(String refreshToken);

}
