package com.servauto.mall.support.payment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "mall.payment.ipay88")
public class Ipay88PaymentProperties {

    private Merchant merchant;
    private Platform platform;



    @Data
    public static class Merchant {
        private String name;
        private String code;
        private String key;
        private String responseURL;
        private String websiteURL;
        private String backendURL;
        private String appDeeplink;
    }

    @Data
    public static class Platform {
        private String createUrl;
        private String requeryUrl;
    }
}