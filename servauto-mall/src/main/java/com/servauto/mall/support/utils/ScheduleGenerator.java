package com.servauto.mall.support.utils;

import com.google.common.collect.Maps;
import com.servauto.framework.factory.SystemFactory;
import com.servauto.mall.model.dto.response.order.OrderWorkshopDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopServiceTimeDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <p>ScheduleGenerator</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 16:44
 */
public class ScheduleGenerator {


    public static List<OrderWorkshopDTO.ServiceTimeDTO> generateServicesTimes(List<WorkshopServiceTimeDTO> workshopServiceTimes) {
        if (CollectionUtils.isEmpty(workshopServiceTimes)) {
            return Lists.newArrayList();
        }

        List<WeekConfig> weekConfigs = workshopServiceTimes.stream().map(e -> {
            LocalDateTime startTime = LocalDateTime.ofInstant(e.getStartTime().toInstant(), SystemFactory.getSystemZonId());
            LocalDateTime endTime = LocalDateTime.ofInstant(e.getEndTime().toInstant(), SystemFactory.getSystemZonId());
            return new WeekConfig(e.getDay(), startTime, endTime);
        }).toList();
        Map<LocalDate, List<LocalTime>> timeslots = CollectionUtils.isNotEmpty(weekConfigs) ? generate(weekConfigs) : Maps.newHashMap();

        List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes = Lists.newArrayList();
        timeslots.forEach((date, times) -> {

            Date header = Date.from(date.atStartOfDay().atZone(SystemFactory.getSystemZonId()).toInstant());
            List<OrderWorkshopDTO.DayOfTimeDTO> table = times.stream().map(e -> {
                String time = e.format(DateTimeFormatter.ofPattern("HH:mm"));
                var visible = !date.isEqual(LocalDate.now()) || !LocalTime.now().isAfter(e);
                return new OrderWorkshopDTO.DayOfTimeDTO(visible, time);
            }).toList();

            serviceTimes.add(new OrderWorkshopDTO.ServiceTimeDTO(header, table));
        });

        return serviceTimes;
    }

    public static Map<LocalDate, List<LocalTime>> generate(List<WeekConfig> weekConfigs) {
        Map<LocalDate, List<LocalTime>> schedule = new TreeMap<>();

        LocalDate firstDay = LocalDate.now(SystemFactory.getSystemZonId()).plusDays(3);
        LocalDate lastDay = firstDay.plusDays(30);

        for (LocalDate date = firstDay; !date.isAfter(lastDay); date = date.plusDays(1)) {
            int week = (date.getDayOfWeek() == DayOfWeek.SUNDAY) ? 0 : date.getDayOfWeek().getValue();

            WeekConfig config = weekConfigs.stream().filter(e -> e.days() == week).findFirst().orElse(null);

            if (config != null) {

                LocalTime startTime = config.startTime().toLocalTime();
                LocalTime endTime = config.endTime().toLocalTime();

                if (startTime.isBefore(endTime)) {
                    schedule.put(date, splitTime(startTime, endTime));
                }
            }
        }

        return schedule;
    }

    private static List<LocalTime> splitTime(LocalTime start, LocalTime end) {

        List<LocalTime> result = Lists.newArrayList();

        LocalTime current = start;
        result.add(current);

        while (true) {
            LocalTime next;
            if (current.getMinute() != 0) {
                next = current.plusMinutes(30);
            } else {
                next = current.plusHours(1);
            }

            if (next.getHour() == 0 || next.isAfter(end)) {
                break;
            }

            result.add(next);
            current = next;
        }

        if (!current.equals(end)) {
            result.add(end);
        }

        return result;
    }
}
