package com.servauto.mall.support.payment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "mall.payment.i2c2p")
public class I2c2pPaymentProperties {

    private I2c2pPaymentProperties.Merchant merchant;
    private I2c2pPaymentProperties.Platform platform;

    @Data
    public static class Merchant {
        private String merchantID;
        private String secretKey;
        private String currencyCode;
        private Long expiryTime;
        private String responseURL;
        private String websiteURL;
        private String backendURL;
    }

    @Data
    public static class Platform {
        private String requestUrl;
    }
}