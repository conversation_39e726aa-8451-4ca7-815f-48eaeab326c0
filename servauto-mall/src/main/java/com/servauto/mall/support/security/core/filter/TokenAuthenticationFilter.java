package com.servauto.mall.support.security.core.filter;

import com.google.common.collect.Multimap;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.support.oauth.TokenService;
import com.servauto.mall.support.oauth.dto.AccessTokenRespDTO;
import com.servauto.mall.support.security.config.SecurityProperties;
import com.servauto.mall.support.security.core.LoginUser;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import com.servauto.mall.support.security.core.utils.WebFrameworkUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.hutool.core.text.AntPathMatcher;
import org.dromara.hutool.core.text.StrUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpMethod;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collection;

@Slf4j
@RequiredArgsConstructor
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;

    private final TokenService tokenService;

    private final Multimap<HttpMethod, String> permitUrls;

    private final AntPathMatcher matcher = new AntPathMatcher();

    @Override
    protected boolean shouldNotFilter(@NotNull HttpServletRequest request) {
        Collection<String> urls = permitUrls.get(HttpMethod.valueOf(request.getMethod()));
        if (CollectionUtils.isNotEmpty(urls)) {
            return urls.stream().anyMatch(e -> e.equals(request.getRequestURI()) || matcher.isPattern(e) && matcher.match(e, request.getRequestURI()));
        }
        return false;
    }

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain chain) throws ServletException, IOException {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotEmpty(token)) {
            AccessTokenRespDTO accessToken;
            try {
                accessToken = tokenService.checkAccessToken(token);
            } catch (BusinessException e) {
                throw e;
            } catch (Throwable ex) {
                WebFrameworkUtils.renderString(response, JacksonSerializer.serialize(CommonResult.of(ResponseCode.ERROR)));
                return;
            }
            LoginUser loginUser = LoginUser.builder().id(accessToken.getUserId()).expiresTime(accessToken.getExpiresTime()).build();
            SecurityFrameworkUtils.setLoginUser(loginUser, request);
        }

        chain.doFilter(request, response);
    }
}
