package com.servauto.mall.support.oauth.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.support.oauth.TokenService;
import com.servauto.mall.support.oauth.dto.AccessTokenCreateReqDTO;
import com.servauto.mall.support.oauth.dto.AccessTokenRespDTO;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.data.id.IdUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class TokenServiceImpl implements TokenService {

    private static final long ACCESS_TOKEN_VALIDITY_SECONDS = 60 * 60 * 24 * 30;
    private static final long REFRESH_TOKEN_VALIDITY_SECONDS = 60 * 60 * 12;
    private static final String OAUTH2_ACCESS_TOKEN = "OAUTH2_ACCESS_TOKEN:%s";
    private static final String OAUTH2_REFRESH_ACCESS_TOKEN = "OAUTH2_REFRESH_ACCESS_TOKEN:%s";

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public AccessTokenRespDTO createAccessToken(AccessTokenCreateReqDTO reqDTO) {
        return createOAuth2AccessToken(createOAuth2RefreshToken(reqDTO.getUserId()));
    }

    @Override
    public AccessTokenRespDTO checkAccessToken(String accessToken) {
        AccessTokenRespDTO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw BusinessException.of(ResponseCode.UNAUTHORIZED.getCode(), "Access token does not exist");
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(accessTokenDO.getExpiresTime())) {
            throw BusinessException.of(ResponseCode.UNAUTHORIZED.getCode(), "Access token has expired");
        }

        AccessTokenRespDTO refreshAccessToken = getRefreshAccessToken(accessTokenDO.getRefreshToken());
        if (now.isAfter(refreshAccessToken.getExpiresTime())) {
            return refreshAccessToken(accessTokenDO.getRefreshToken());
        }
        return accessTokenDO;
    }

    @Override
    public AccessTokenRespDTO getAccessToken(String accessToken) {
        String obj = redisTemplate.opsForValue().get(formatKey(accessToken));
        return JacksonSerializer.deSerialize(obj, AccessTokenRespDTO.class);
    }

    private AccessTokenRespDTO getRefreshAccessToken(String refreshAccessToken) {
        String obj = redisTemplate.opsForValue().get(formatRefreshKey(refreshAccessToken));
        return JacksonSerializer.deSerialize(obj, AccessTokenRespDTO.class);
    }

    @Override
    public void removeAccessToken(String accessToken) {
        AccessTokenRespDTO accessTokenDTO = getAccessToken(accessToken);
        if (Objects.isNull(accessTokenDTO)) {
            return;
        }

        if (StringUtils.isNotBlank(accessTokenDTO.getAccessToken())) {
            redisTemplate.delete(formatKey(accessToken));
        }

        if (StringUtils.isNotBlank(accessTokenDTO.getRefreshToken())) {
            redisTemplate.delete(formatRefreshKey(accessTokenDTO.getRefreshToken()));
        }
    }

    @Override
    public AccessTokenRespDTO refreshAccessToken(String refreshToken) {
        AccessTokenRespDTO refreshAccessToken = getRefreshAccessToken(refreshToken);
        if (Objects.isNull(refreshAccessToken)) {
            throw BusinessException.of(ResponseCode.UNAUTHORIZED.getCode(), "Access token has expired");
        }
        if (refreshAccessToken.getExpiresTime().isBefore(LocalDateTime.now())) {
            refreshAccessToken = createOAuth2RefreshToken(refreshAccessToken.getUserId());
        }
        return createOAuth2AccessToken(refreshAccessToken);
    }

    private AccessTokenRespDTO createOAuth2AccessToken(AccessTokenRespDTO refreshTokenDO) {
        AccessTokenRespDTO accessTokenDO = new AccessTokenRespDTO()
                .setAccessToken(generateAccessToken())
                .setUserId(refreshTokenDO.getUserId())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setExpiresTime(LocalDateTime.now().plusSeconds(ACCESS_TOKEN_VALIDITY_SECONDS));
        long time = LocalDateTimeUtil.between(LocalDateTime.now(), accessTokenDO.getExpiresTime(), ChronoUnit.SECONDS);
        redisTemplate.opsForValue().set(formatKey(accessTokenDO.getAccessToken()), JacksonSerializer.serialize(accessTokenDO), time, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(formatRefreshKey(accessTokenDO.getRefreshToken()), JacksonSerializer.serialize(accessTokenDO), time, TimeUnit.SECONDS);
        return accessTokenDO;
    }

    private static String formatKey(String accessToken) {
        return String.format(OAUTH2_ACCESS_TOKEN, accessToken);
    }

    private static String formatRefreshKey(String accessToken) {
        return String.format(OAUTH2_REFRESH_ACCESS_TOKEN, accessToken);
    }

    private AccessTokenRespDTO createOAuth2RefreshToken(Long userId) {
        return new AccessTokenRespDTO().setRefreshToken(generateRefreshToken()).setUserId(userId)
                .setExpiresTime(LocalDateTime.now().plusSeconds(REFRESH_TOKEN_VALIDITY_SECONDS));
    }

    private static String generateAccessToken() {
        return IdUtil.fastSimpleUUID();
    }

    private static String generateRefreshToken() {
        return IdUtil.fastSimpleUUID();
    }

}
