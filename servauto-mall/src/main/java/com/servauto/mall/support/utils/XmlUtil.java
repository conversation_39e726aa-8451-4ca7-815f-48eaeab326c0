package com.servauto.mall.support.utils;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.io.IOException;

public class XmlUtil {

    // 初始化 XmlMapper 实例（线程安全）
    private static final XmlMapper xmlMapper = new XmlMapper();

    /**
     * 将对象转换为 XML 字符串
     *
     * @param object 需要转换的对象
     * @return XML 格式的字符串
     * @throws IOException 序列化失败时抛出异常
     */
    public static String toXmlString(Object object) throws IOException {
        return xmlMapper.writeValueAsString(object);
    }

    /**
     * 将 XML 字符串转换为指定类型的对象
     *
     * @param xmlString XML 数据字符串
     * @param clazz 目标类的 Class 对象
     * @return 转换后的对象实例
     * @throws IOException 反序列化失败时抛出异常
     */
    public static <T> T fromXmlString(String xmlString, Class<T> clazz) throws IOException {
        return xmlMapper.readValue(xmlString, clazz);
    }

    public static String buildSoapRequest(String xmlBody) {
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
                "               xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"\n" +
                "               xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "  <soap:Body>\n" +
                xmlBody +
                "  </soap:Body>\n" +
                "</soap:Envelope>";
    }
}