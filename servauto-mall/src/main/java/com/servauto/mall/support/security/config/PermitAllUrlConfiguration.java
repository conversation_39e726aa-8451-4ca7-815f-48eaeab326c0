package com.servauto.mall.support.security.config;


import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.Getter;
import org.apache.commons.lang3.RegExUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

public class PermitAllUrlConfiguration implements InitializingBean, ApplicationContextAware {

    @Resource
    private SecurityProperties securityProperties;

    private ApplicationContext applicationContext;

    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");

    @Getter
    private final Multimap<HttpMethod, String> permitAllUrls = HashMultimap.create();

    @Override
    public void afterPropertiesSet() {
        Multimap<HttpMethod, String> result = HashMultimap.create();
        RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping) applicationContext.getBean("requestMappingHandlerMapping");
        Map<RequestMappingInfo, HandlerMethod> handlerMethodMap = requestMappingHandlerMapping.getHandlerMethods();

        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethodMap.entrySet()) {
            HandlerMethod handlerMethod = entry.getValue();
            if (!handlerMethod.hasMethodAnnotation(PermitAll.class)) {
                continue;
            }

            Set<String> urls = new HashSet<>();
            if (entry.getKey().getPatternsCondition() != null) {
                urls.addAll(entry.getKey().getPatternsCondition()
                        .getPatterns().stream().map(e -> RegExUtils.replaceAll(e, PATTERN, "*")).toList());
            }

            if (entry.getKey().getPathPatternsCondition() != null && !CollectionUtils.isEmpty(entry.getKey().getPathPatternsCondition().getPatterns())) {
                List<String> list = entry.getKey().getPathPatternsCondition()
                        .getPatterns().stream().map(e -> RegExUtils.replaceAll(e.getPatternString(), PATTERN, "*")).toList();
                urls.addAll(list);
            }

            if (urls.isEmpty()) {
                continue;
            }

            Set<RequestMethod> methods = entry.getKey().getMethodsCondition().getMethods();
            if (CollectionUtils.isEmpty(methods)) {
                result.putAll(HttpMethod.GET, urls);
                result.putAll(HttpMethod.POST, urls);
                result.putAll(HttpMethod.PUT, urls);
                result.putAll(HttpMethod.DELETE, urls);
                result.putAll(HttpMethod.HEAD, urls);
                result.putAll(HttpMethod.PATCH, urls);
                continue;
            }

            entry.getKey().getMethodsCondition().getMethods().forEach(requestMethod -> {
                switch (requestMethod) {
                    case GET:
                        result.putAll(HttpMethod.GET, urls);
                        break;
                    case POST:
                        result.putAll(HttpMethod.POST, urls);
                        break;
                    case PUT:
                        result.putAll(HttpMethod.PUT, urls);
                        break;
                    case DELETE:
                        result.putAll(HttpMethod.DELETE, urls);
                        break;
                    case HEAD:
                        result.putAll(HttpMethod.HEAD, urls);
                        break;
                    case PATCH:
                        result.putAll(HttpMethod.PATCH, urls);
                        break;
                }
            });
        }
        for (String permitUrl : securityProperties.getPermitAllUrls()) {
            result.put(HttpMethod.GET, permitUrl);
            result.put(HttpMethod.POST, permitUrl);
            result.put(HttpMethod.PUT, permitUrl);
            result.put(HttpMethod.DELETE, permitUrl);
            result.put(HttpMethod.PATCH, permitUrl);
            result.put(HttpMethod.HEAD, permitUrl);
        }
        permitAllUrls.putAll(result);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
