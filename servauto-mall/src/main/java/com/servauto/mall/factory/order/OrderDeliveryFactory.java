package com.servauto.mall.factory.order;

import com.google.common.base.Preconditions;
import com.servauto.mall.enums.DeliveryMode;
import com.servauto.mall.enums.order.OrderDeliveryTypeEnum;
import com.servauto.mall.model.dto.request.order.CreateOrderReqDTO;
import com.servauto.mall.model.dto.response.TypeNameDTO;
import com.servauto.mall.model.dto.response.order.CreateOrderDTO;
import com.servauto.mall.model.dto.response.order.OrderDeliveryDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.entity.order.OrderDelivery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <p>OrderDeliveryFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/8 11:18
 */
public class OrderDeliveryFactory {

    public static OrderDeliveryDTO convert(OrderDelivery delivery) {
        return OrderDeliveryDTO.builder()
                .orderNo(delivery.getOrderNo())
                .name(delivery.getName())
                .mobile(delivery.getMobile())
                .stateCode(delivery.getStateCode())
                .stateName(delivery.getStateName())
                .cityCode(delivery.getCityCode())
                .cityName(delivery.getCityName())
                .address(delivery.getAddress())
                .build();
    }

    public static List<CreateOrderDTO.OrderDeliveryDTO> convert(CreateOrderReqDTO createOrderReqDTO, PackageInfoDTO packageInfo, List<ProductDTO> products) {
        List<OrderDeliveryTypeEnum> deliveryMethods = Lists.newArrayList();
        if (createOrderReqDTO.getServiceId() != null && createOrderReqDTO.getServiceId() > 0) {
            deliveryMethods.add(OrderDeliveryTypeEnum.WORKSHOP);

        } else if (createOrderReqDTO.getPackageId() != null && createOrderReqDTO.getPackageId() > 0) {
            Preconditions.checkNotNull(packageInfo);
            deliveryMethods.addAll(convert(packageInfo.getDeliveryModes()));

        } else {

            ProductDTO productInfo = products.stream().findFirst().orElseThrow();
            deliveryMethods.addAll(convert(productInfo.getDeliveryModes()));
        }

        if (createOrderReqDTO.getServiceId() == null && CollectionUtils.isEmpty(deliveryMethods)) {
            deliveryMethods.add(OrderDeliveryTypeEnum.SHIPPING);
            deliveryMethods.add(OrderDeliveryTypeEnum.PICKUP);
        }

        return deliveryMethods.stream().filter(Objects::nonNull)
                .map(e -> new CreateOrderDTO.OrderDeliveryDTO(e.getCode(), e.getMsg())).distinct().toList();
    }

    public static List<OrderDeliveryTypeEnum> convert(List<TypeNameDTO> deliveryModes) {
        if (CollectionUtils.isEmpty(deliveryModes)) {
            return Lists.newArrayList();
        }

        List<OrderDeliveryTypeEnum> deliveryMethods = Lists.newArrayList();
        deliveryModes.forEach(e -> {
            if (DeliveryMode.isMailing(e.getType())) {
                deliveryMethods.add(OrderDeliveryTypeEnum.SHIPPING);
            }
            if (DeliveryMode.isSelfPickup(e.getType())) {
                deliveryMethods.add(OrderDeliveryTypeEnum.PICKUP);
            }
        });

        return deliveryMethods;
    }


}
