package com.servauto.mall.factory.packageinfo;

import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.mall.model.dto.response.packageinfo.ProductSimpleDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;

public class ProductSimpleFactory {
    public static ProductSimpleDTO convert(ProductDTO info) {
        return ProductSimpleDTO.builder().
                id(info.getId()).
                name(info.getName()).
                categoryId(info.getCategoryId()).
                categoryName(info.getCategoryName()).
                price(info.getPrice()).
                netContent(info.getNetContent()).
                contentUnit(info.getContentUnit()).
                mainImage(info.getMainImage()).
                featuredTags(info.getFeaturedTags()).
                skuAttributeValues(info.getSkuAttributeValues()).
                build();
    }
}
