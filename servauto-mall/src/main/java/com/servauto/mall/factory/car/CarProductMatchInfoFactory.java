package com.servauto.mall.factory.car;

import com.servauto.mall.enums.car.CarMatchProductTypeEnum;
import com.servauto.mall.enums.car.CarMatchTypeEnum;
import com.servauto.mall.model.dto.response.car.CarProductMatchInfoDTO;
import com.servauto.mall.model.entity.car.CarProductMatchInfo;

public class CarProductMatchInfoFactory {
    public static CarProductMatchInfoDTO convert(CarProductMatchInfo info, CarMatchProductTypeEnum productType) {
        if (info == null) {
            return null;
        }
        return CarProductMatchInfoDTO.builder()
                .carLibId(info.getCarLibId())
                .matchContent(info.getMatchContent())
                .productType(productType)
                .matchType(CarMatchTypeEnum.getByCode(info.getMatchType()))
                .build();
    }
}