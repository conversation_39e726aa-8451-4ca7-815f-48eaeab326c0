package com.servauto.mall.factory.product;

import com.servauto.mall.model.dto.response.product.ProductAttributeDTO;
import com.servauto.mall.model.entity.product.ProductAttribute;

public class ProductAttributeFactory {
    public static ProductAttributeDTO convert(ProductAttribute info) {
        return  ProductAttributeDTO.builder()
                .id(info.getId())
                .type(info.getType())
                .name(info.getName())
                .suffix(info.getSuffix())
                .categoryId(info.getCategoryId())
                .order(info.getOrder())
                .build();
    }
}