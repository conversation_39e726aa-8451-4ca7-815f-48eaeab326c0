package com.servauto.mall.factory.packageinfo;

import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.model.entity.packageinfo.PackageInfo;

public class PackageFactory {
    public static PackageInfoDTO convert(PackageInfo info) {
        return PackageInfoDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .status(info.getStatus())
                .statusName(SaleStatus.getByCode(info.getStatus()).getDesc())
                .isFixedPrice(info.getIsFixedPrice() == 1)
                .price(info.getPrice())
                .serviceId(info.getServiceId())
                .updatedBy(info.getUpdatedBy())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}
