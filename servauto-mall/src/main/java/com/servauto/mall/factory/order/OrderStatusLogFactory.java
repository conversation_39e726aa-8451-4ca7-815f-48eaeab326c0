package com.servauto.mall.factory.order;

import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.enums.order.OrderStatusEnum;
import com.servauto.mall.model.entity.order.OrderStatusLog;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <p>OrderStatusLogFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 18:31
 */
public class OrderStatusLogFactory {


    public static OrderStatusLog build(String orderNo, OrderStatusEnum enums,
                                       String updateBy, List<String> images, String remark) {
        OrderStatusLog orderStatusLog = new OrderStatusLog();
        orderStatusLog.setOrderNo(orderNo);
        orderStatusLog.setStatus(enums.getCode());
        orderStatusLog.setRemark(remark);
        orderStatusLog.setUpdateBy(updateBy);
        if (CollectionUtils.isNotEmpty(images)) {
            orderStatusLog.setImages(JacksonSerializer.serialize(images));
        }
        return orderStatusLog;
    }

    public static OrderStatusLog build(String orderNo, OrderStatusEnum enums, String remark, String updateBy) {
        return build(orderNo, enums, updateBy, null, remark);
    }

}
