package com.servauto.mall.factory.customer;

import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.mall.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.mall.model.entity.customer.CustomerShippingAddress;

import java.util.Map;

/**
 * <p>CustomerFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/7 14:29
 */
public class CustomerFactory {

    public static CustomerAddressDTO convert(CustomerShippingAddress e, Map<String, AreaDTO> areaMap, Map<String, StateDTO> stateMap) {
        AreaDTO areaDTO = areaMap.get(e.getCityCode());
        StateDTO stateDTO = stateMap.get(e.getStateCode());
        String areaName = areaDTO == null ? "" : areaDTO.getName();
        String stateName = stateDTO == null ? "" : stateDTO.getName();
        return CustomerAddressDTO.builder()
                .id(e.getId())
                .customerId(e.getCustomerId())
                .name(e.getName())
                .mobile(e.getMobile())
                .cityCode(e.getCityCode())
                .cityName(areaName)
                .stateCode(e.getStateCode())
                .stateName(stateName)
                .address(e.getAddress())
                .shippingAddress(e.getAddress() + "," + stateName + "," + areaName)
                .isDefault(e.getIsDefault())
                .build();
    }


}
