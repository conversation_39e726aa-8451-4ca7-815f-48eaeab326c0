package com.servauto.mall.factory.product;

import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.entity.product.Product;

public class ProductFactory {
    public static ProductDTO convert(Product info) {
        return ProductDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .categoryId(info.getCategoryId())
                .brandId(info.getBrandId())
                .status(info.getStatus())
                .statusName(SaleStatus.getByCode(info.getStatus()).getDesc())
                .description(info.getDescription())
                .serviceId(info.getServiceId().equals(0L)?  null :info.getServiceId())
                .netContent(info.getNetContent())
                .contentUnit(info.getContentUnit())
                .price(info.getPrice())
                .mainImage(info.getMainImage())
                .build();
    }
}
