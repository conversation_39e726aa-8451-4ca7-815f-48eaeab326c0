package com.servauto.mall.factory.car;

import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarExtra;

public class CareExtraInfoFactory {
    public static CarKeyValueDTO convertKv(CarExtra info) {
        return CarKeyValueDTO.builder()
                .id(info.getId())
                .value(info.getVariantName() + " " + info.getTransmissionType() + " " + info.getDisplacementValue())
                .order(info.getOrder())
                .build();
    }
}