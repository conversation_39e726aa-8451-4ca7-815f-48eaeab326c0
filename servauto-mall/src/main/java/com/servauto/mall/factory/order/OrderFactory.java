package com.servauto.mall.factory.order;


import com.servauto.common.utils.GeoUtils;
import com.servauto.mall.enums.WorkshopStatus;
import com.servauto.mall.enums.order.OrderDeliveryTypeEnum;
import com.servauto.mall.enums.order.OrderStatusEnum;
import com.servauto.mall.model.dto.request.order.OrderProductReqDTO;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.model.dto.response.order.*;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.model.entity.order.OrderDelivery;
import com.servauto.mall.model.entity.order.OrderPay;
import com.servauto.mall.model.entity.order.OrderProduct;
import com.servauto.mall.support.utils.ScheduleGenerator;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class OrderFactory {

    public static CreateOrderDTO buildCreateOrderResp(List<OrderProductReqDTO> reqProducts, List<ProductDTO> products, ServiceInfoDTO serviceInfo, PackageInfoDTO packageInfo) {

        BigDecimal totalCoupon = BigDecimal.ZERO;
        BigDecimal totalOriginal = BigDecimal.ZERO;
        BigDecimal totalDiscount = BigDecimal.ZERO;
        BigDecimal subtotal = BigDecimal.ZERO;
        BigDecimal shippingFee = BigDecimal.ZERO;
        BigDecimal grandTotal;

        // Priority = Package > Service
        BigDecimal serviceFee = Objects.nonNull(serviceInfo) ? serviceInfo.getFee() : BigDecimal.ZERO;

        // calc order total
        boolean forceFixedPrice = packageInfo != null && BigDecimal.ZERO.compareTo(packageInfo.getPrice()) < 0
                ? packageInfo.getIsFixedPrice() : false;

        Map<Long, ProductDTO> productMap = products.stream().collect(Collectors.toMap(ProductDTO::getId, s -> s));
        List<OrderProductDTO> orderProducts = reqProducts.stream().map(e -> OrderProductFactory.convert(e, productMap.get(e.getProductId()))).distinct().toList();

        if (forceFixedPrice) {
            BigDecimal price = packageInfo.getPrice();

//            int quantity = 0;
//            for (OrderProductReqDTO reqProduct : reqProducts) {
//                quantity = quantity + reqProduct.getQuantity();
//                totalOriginal = totalOriginal.add(productMap.get(reqProduct.getProductId()).getPrice().multiply(BigDecimal.valueOf(reqProduct.getQuantity())));
//            }
//
//            grandTotal = price;
//            if (totalOriginal.add(serviceFee).compareTo(price) > 0) {
//                subtotal = grandTotal.subtract(serviceFee);
//                totalDiscount = totalOriginal.add(serviceFee).subtract(price);
//            } else {
//                subtotal = totalOriginal;
//            }
//            BigDecimal discountPrice = subtotal.divide(new BigDecimal(quantity), RoundingMode.HALF_EVEN);
//            orderProducts = reqProducts.stream().map(e -> OrderProductFactory.convert(e, productMap.get(e.getProductId()), discountPrice)).toList();
            for (OrderProductDTO product : orderProducts) {
                totalOriginal = totalOriginal.add(product.getReservePrice().multiply(BigDecimal.valueOf(product.getQuantity())));
            }
            grandTotal = price;
            subtotal = totalOriginal;
            totalDiscount = totalOriginal.add(serviceFee).subtract(price);

        } else {

            for (OrderProductDTO product : orderProducts) {
                totalOriginal = totalOriginal.add(product.getReservePrice().multiply(BigDecimal.valueOf(product.getQuantity())));
                totalDiscount = totalDiscount.add(product.getDiscountAmount().multiply(BigDecimal.valueOf(product.getQuantity())));
                subtotal = subtotal.add(product.getDiscountPrice().multiply(BigDecimal.valueOf(product.getQuantity())));
            }
            grandTotal = subtotal.add(shippingFee).add(serviceFee).subtract(totalCoupon);
        }

        return CreateOrderDTO.builder()
                .serviceId(Objects.nonNull(serviceInfo) ? serviceInfo.getId() : null)
                .serviceName(Objects.nonNull(serviceInfo) ? serviceInfo.getName() : null)
                .serviceHour(Objects.nonNull(serviceInfo) ? serviceInfo.getHours() : null)
                .packageId(packageInfo != null ? packageInfo.getId() : null)
                .packageName(packageInfo != null ? packageInfo.getName() : null)
                .serviceFee(serviceFee)
                .shippingFee(shippingFee)
                .originalAmount(totalOriginal)
                .discountAmount(totalDiscount)
                .couponAmount(totalCoupon)
                .subtotal(subtotal)
                .grandTotal(grandTotal)
                .products(orderProducts)
                .fixed(forceFixedPrice)
                .build();
    }

    public static SubmitOrderDTO buildSubmitOrderResp(OrderDTO orderDTO) {
        return SubmitOrderDTO.builder()
                .orderNo(orderDTO.getOrderNo())
                .payNo(orderDTO.getOrderPay().getPayNo())
                .orderTime(orderDTO.getOrderTime())
                .originalAmount(orderDTO.getOriginalAmount())
                .discountAmount(orderDTO.getDiscountAmount())
                .subtotal(orderDTO.getSubtotal())
                .shippingFee(orderDTO.getShippingFee())
                .grandTotal(orderDTO.getGrandTotal())
                .expireTime(orderDTO.getExpireTime())
                .products(orderDTO.getProducts())
                .build();
    }

    public static List<OrderDTO> buildOrders(List<Order> orders, List<OrderPay> pays,
                                             List<OrderProduct> products, List<OrderDelivery> deliveries) {

        Map<String, List<OrderProduct>> orderProductMap = products.stream().collect(Collectors.groupingBy(OrderProduct::getOrderNo));
        Map<String, OrderDelivery> orderDeliveryMap = deliveries.stream().collect(Collectors.toMap(OrderDelivery::getOrderNo, k -> k));
        Map<String, OrderPay> orderPayMap = pays.stream().collect(Collectors.toMap(OrderPay::getOrderNo, k -> k));

        return orders.stream().map(e -> {
            List<OrderProduct> orderProducts = orderProductMap.get(e.getOrderNo());
            OrderDelivery orderDelivery = orderDeliveryMap.get(e.getOrderNo());
            OrderPay orderPay = orderPayMap.get(e.getOrderNo());
            return OrderFactory.buildOrder(e, orderProducts, orderDelivery, orderPay);
        }).toList();
    }

    public static OrderDTO buildOrder(Order order, List<OrderProduct> products, OrderDelivery delivery, OrderPay pay) {
        LocalDateTime expireTime = order.getOrderTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusMinutes(15);
        List<OrderProductDTO> orderProducts = products.stream().map(OrderProductFactory::convert).toList();
        OrderPayDTO orderPay = OrderPayFactory.convert(pay);

        OrderDeliveryDTO orderDelivery = new OrderDeliveryDTO();
        if (OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
            orderDelivery = OrderDeliveryFactory.convert(delivery);
        }

        OrderCustomerDTO customerDTO = OrderCustomerDTO.builder()
                .mobile(order.getCustomerMobile()).realName(order.getCustomerName())
                .email("<EMAIL>".equals(order.getCustomerEmail()) ? "" : order.getCustomerEmail())
                .build();

        CarInfoDTO carInfoDTO = CarInfoDTO.builder().licensePlate(order.getLicensePlate()).icon(order.getCarIcon())
                .brand(order.getBrand()).model(order.getModel()).variant(order.getVariant()).year(order.getYear()).transmissionType(order.getTransmissionType()).build();

        return OrderDTO.builder()
                .orderNo(order.getOrderNo())
                .orderName(orderProducts.stream().findFirst().orElseThrow().getProductName())
                .originalAmount(order.getOriginalAmount())
                .discountAmount(order.getDiscountAmount())
                .subtotal(order.getSubtotal())
                .shippingFee(order.getShippingFee())
                .grandTotal(order.getGrandTotal())
                .type(order.getType())
                .status(order.getStatus())
                .statusName(OrderStatusEnum.format(order.getStatus()))
                .packageId(order.getPackageId())
                .packageName(order.getPackageName())
                .serviceId(order.getServiceId())
                .serviceName(order.getServiceName())
                .serviceHour(order.getServiceHour())
                .serviceFee(order.getServiceFee())
                .workshopId(order.getWorkshopId())
                .workshopName(order.getWorkshopName())
                .deliveryType(order.getDeliveryType())
                .deliveryTypeMsg(OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg())
                .couponAmount(order.getCouponAmount())
                .orderTime(order.getOrderTime())
                .expireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()))
                .reservationTime(formatDate(order.getReservationTime()))
                .paidTime(formatDate(order.getPaidTime()))
                .finishedTime(formatDate(order.getFinishedTime()))
                .refundedTime(formatDate(order.getRefundedTime()))
                .products(orderProducts)
                .delivery(orderDelivery)
                .orderPay(orderPay)
                .pickupCode(order.getPickupCode())
                .remark(order.getRemark())
                .customerInfo(customerDTO)
                .carInfo(carInfoDTO)
                .build();
    }

    public static boolean isValidDate(Date date) {
        return date != null && date.getTime() > 0;
    }

    public static Date formatDate(Date date) {
        return isValidDate(date) ? date : null;
    }


    public static OrderWorkshopDTO buildWorkshop(Double latitude, Double longitude, WorkshopDTO e, List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes) {
        double distance = 0;
        if (latitude != null && longitude != null && e.getLongitude() != null && e.getLatitude() != null) {
            distance = GeoUtils.getDistance(latitude, longitude, e.getLatitude().doubleValue(), e.getLongitude().doubleValue());
        }
        return OrderWorkshopDTO.builder()
                .id(e.getId())
                .name(e.getName())
                .stateCode(e.getStateCode())
                .stateName(e.getStateName())
                .cityCode(e.getCityCode())
                .cityName(e.getCityName())
                .address(e.getAddress())
                .latitude(e.getLatitude())
                .longitude(e.getLongitude())
                .contactNumber(e.getContactNumber())
                .logo(e.getLogo())
                .locationUrl(e.getLocationUrl())
                .distance(String.format("%.2f", distance / 1000))
                .serviceTimes(serviceTimes)
                .build();
    }

    public static List<OrderWorkshopDTO> buildWorkshops(List<WorkshopDTO> workshops) {
        return workshops.stream().filter(e -> CollectionUtils.isNotEmpty(e.getServiceTimeList())
                        && WorkshopStatus.isActive(e.getStatus()))
                .map(e -> OrderFactory.buildWorkshop(null, null, e, ScheduleGenerator.generateServicesTimes(e.getServiceTimeList())))
                .filter(Objects::nonNull).toList();
    }

    public static List<OrderWorkshopDTO> buildWorkshops(Double latitude, Double longitude, List<WorkshopDTO> workshops) {
        return workshops.stream().filter(e -> CollectionUtils.isNotEmpty(e.getServiceTimeList())
                        && WorkshopStatus.isActive(e.getStatus()))
                .map(e -> OrderFactory.buildWorkshop(latitude, longitude, e, null))
                .filter(Objects::nonNull).toList();
    }

    public static OrderWorkshopDTO buildWorkshop(WorkshopDTO e, List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes) {
        return buildWorkshop(null, null, e, serviceTimes);
    }


}
