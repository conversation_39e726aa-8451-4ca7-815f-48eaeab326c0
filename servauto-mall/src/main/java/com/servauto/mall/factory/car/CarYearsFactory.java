package com.servauto.mall.factory.car;

import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.dto.response.car.CarYearsDTO;
import com.servauto.mall.model.entity.car.CarYears;

public class CarYearsFactory {
    public static CarYearsDTO convert(CarYears info) {
        return CarYearsDTO.builder()
                .id(info.getId())
                .modelId(info.getModelId())
                .yearValue(info.getYearValue())
                .order(info.getOrder())
                .build();
    }
    public static CarKeyValueDTO convertKv(CarYears info) {
        return CarKeyValueDTO.builder()
                .id(info.getId())
                .value(info.getYearValue())
                .order(info.getOrder())
                .build();
    }
}