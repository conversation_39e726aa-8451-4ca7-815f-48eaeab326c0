package com.servauto.mall.factory.order;


import com.fasterxml.jackson.core.type.TypeReference;
import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.model.dto.request.order.OrderProductReqDTO;
import com.servauto.mall.model.dto.response.order.OrderProductDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.entity.order.OrderProduct;

import java.math.BigDecimal;

import static com.servauto.mall.service.product.impl.ProductServiceImpl.CATEGORY_ENGINE_OIL;

public class OrderProductFactory {

    public static OrderProductDTO convert(OrderProductReqDTO reqDTO, ProductDTO productInfo) {
        BigDecimal reservePrice = productInfo.getPrice();
        BigDecimal actualPrice = productInfo.getPrice();
        BigDecimal promotionDiscount = BigDecimal.ZERO; // current no promotion
        BigDecimal discountAmount = reservePrice.subtract(actualPrice).add(promotionDiscount);
        BigDecimal discountPrice = reservePrice.subtract(discountAmount);
        return OrderProductDTO.builder()
                .productId(productInfo.getId())
                .productName(productInfo.getName())
                .brandId(productInfo.getBrandId())
                .brandName(productInfo.getBrandName())
                .netContent(productInfo.getNetContent())
                .contentUnit(productInfo.getContentUnit())
                .productAttribute(productInfo.getAttributeValues())
                .categoryId(productInfo.getCategoryId())
                .categoryName(productInfo.getCategoryName())
                .image(productInfo.getMainImage())
                .reservePrice(reservePrice)
                .actualPrice(productInfo.getPrice())
                .discountPrice(discountPrice)
                .discountAmount(discountAmount)
                .promotionDiscount(promotionDiscount)
                .quantity(reqDTO.getQuantity())
                .isOil(productInfo.getCategoryName().equals(CATEGORY_ENGINE_OIL))
                .build();
    }

    public static OrderProductDTO convert(OrderProductReqDTO reqDTO, ProductDTO productInfo, BigDecimal discountPrice) {
        BigDecimal reservePrice = productInfo.getPrice();
        BigDecimal actualPrice = productInfo.getPrice();
        BigDecimal promotionDiscount = BigDecimal.ZERO;
        BigDecimal discountAmount = reservePrice.subtract(discountPrice);
        return OrderProductDTO.builder()
                .productId(productInfo.getId())
                .productName(productInfo.getName())
                .brandId(productInfo.getBrandId())
                .brandName(productInfo.getBrandName())
                .netContent(productInfo.getNetContent())
                .contentUnit(productInfo.getContentUnit())
                .productAttribute(productInfo.getAttributeValues())
                .categoryId(productInfo.getCategoryId())
                .categoryName(productInfo.getCategoryName())
                .image(productInfo.getMainImage())
                .reservePrice(reservePrice)
                .actualPrice(actualPrice)
                .discountPrice(discountPrice)
                .discountAmount(discountAmount)
                .promotionDiscount(promotionDiscount)
                .quantity(reqDTO.getQuantity())
                .isOil(productInfo.getCategoryName().equals(CATEGORY_ENGINE_OIL))
                .build();
    }

    public static OrderProductDTO convert(OrderProduct product) {
        return OrderProductDTO.builder()
                .productId(product.getProductId())
                .productName(product.getProductName())
                .brandId(product.getBrandId())
                .brandName(product.getBrandName())
                .netContent(product.getNetContent())
                .contentUnit(product.getContentUnit())
                .productAttribute(StringUtils.isNotBlank(product.getProductAttribute()) ?
                        JacksonSerializer.deSerialize(product.getProductAttribute(), new TypeReference<>() {
                        }) : null)
                .categoryId(product.getCategoryId())
                .categoryName(product.getCategoryName())
                .image(product.getImage())
                .reservePrice(product.getReservePrice())
                .actualPrice(product.getActualPrice())
                .discountPrice(product.getDiscountPrice())
                .discountAmount(product.getDiscountAmount())
                .promotionDiscount(product.getPromotionDiscount())
                .quantity(product.getQuantity())
                .isOil(product.getCategoryName().equals(CATEGORY_ENGINE_OIL))
                .build();
    }
}
