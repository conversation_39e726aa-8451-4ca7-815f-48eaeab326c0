package com.servauto.mall.factory.workshop;

import com.servauto.common.utils.StringUtils;
import com.servauto.mall.enums.WorkshopStatus;
import com.servauto.mall.enums.WorkshopType;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.model.entity.workshop.Workshop;

import java.util.List;

public class WorkshopFactory {
    public static WorkshopDTO convert(Workshop info) {
        return WorkshopDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .type(info.getType().toString())
                .typeName(WorkshopType.getByCode(info.getType()).getDesc())
                .status(info.getStatus().toString())
                .statusName(WorkshopStatus.getByCode(info.getStatus()).getDesc())
                .stateCode(info.getStateCode())
                .cityCode(info.getCityCode())
                .address(info.getAddress())
                .contactNumber(info.getPhoneNumber())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .operatorId(info.getOperatorId())
                .remark(info.getRemark())
                .logo(info.getLogo())
                .longitude(info.getLongitude())
                .latitude(info.getLatitude())
                .locationUrl(info.getLocationUrl())
                .photo(StringUtils.isNotBlank(info.getPhoto()) ? List.of(info.getPhoto().split(",")) : null)
                .whatsAppNumber(info.getWhatsAppNumber())
                .build();
    }


}
