package com.servauto.mall.factory.workshop;

import com.servauto.mall.model.dto.response.workshop.WorkshopServiceTimeDTO;
import com.servauto.mall.model.entity.workshop.WorkshopServiceTime;

public class WorkshopServiceTimeFactory {
    public static WorkshopServiceTimeDTO convert(WorkshopServiceTime info) {
        return  WorkshopServiceTimeDTO.builder()
                .id(info.getId())
                .day(info.getDay())
                .startTime(info.getStartTime())
                .endTime(info.getEndTime())
                .build();
    }
}
