package com.servauto.mall.factory.car;


import com.servauto.mall.model.dto.response.car.CarBrandsDTO;
import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarBrands;

public class CarBrandsFactory {
    public static CarBrandsDTO convert(CarBrands info) {
        return CarBrandsDTO.builder()
                .id(info.getId())
                .brandName(info.getBrandName())
                .order(info.getOrder())
                .build();
    }

    public static CarKeyValueDTO convertKv(CarBrands info) {
        return CarKeyValueDTO.builder()
                .id(info.getId())
                .value(info.getBrandName())
                .icon(info.getIcon())
                .order(info.getOrder())
                .build();
    }
}