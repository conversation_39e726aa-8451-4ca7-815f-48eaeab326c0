package com.servauto.mall.factory.serviceinfo;

import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.mall.enums.ServiceSupportedOn;
import com.servauto.mall.enums.YesOrNo;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.mall.model.entity.serviceinfo.ServiceInfo;

public class ServiceFactory {
    public static ServiceInfoDTO convert(ServiceInfo info) {
        return ServiceInfoDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .hours(info.getHours())
                .fee(info.getFee())
                .supportedOn(info.getSupportedOn().toString())
                .supportedOnName(ServiceSupportedOn.getByCode(info.getSupportedOn()).getDesc())
                .isRequired(info.getIsRequired())
                .isRequiredName(YesOrNo.getByCode(info.getIsRequired()).getDesc())
                .updatedBy(info.getUpdatedBy())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}
