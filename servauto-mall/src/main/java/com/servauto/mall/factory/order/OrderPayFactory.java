package com.servauto.mall.factory.order;

import com.servauto.mall.model.dto.response.order.OrderPayDTO;
import com.servauto.mall.model.entity.order.OrderPay;

/**
 * <p>OrderPayFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 16:56
 */
public class OrderPayFactory {

    public static OrderPayDTO convert(OrderPay orderPay) {
        if (orderPay == null) {
            return OrderPayDTO.builder().build();
        }

        return OrderPayDTO.builder()
                .orderNo(orderPay.getOrderNo())
                .payNo(orderPay.getPayNo())
                .customerId(orderPay.getCustomerId())
                .grandTotal(orderPay.getGrandTotal())
                .paidAmount(orderPay.getPaidAmount())
                .errorCode(orderPay.getErrorCode())
                .errorMsg(orderPay.getErrorMsg())
                .build();
    }
}
