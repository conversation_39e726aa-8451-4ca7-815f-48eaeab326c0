package com.servauto.mall.factory.payment;

import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.entity.payment.Payment;

public class PaymentFactory {

    public static PaymentDTO convert(Payment payment) {
        if (payment == null) {
            return null;
        } else {
            return PaymentDTO.builder().id(payment.getId()).payNo(payment.getPayNo()).channel(payment.getChannel()).payType(payment.getPayType()).amount(payment.getAmount()).sourceId(payment.getSourceId()).url(payment.getUrl()).expiresAt(payment.getExpiresAt()).status(payment.getStatus()).requestData(payment.getRequestData()).build();
        }
    }

}
