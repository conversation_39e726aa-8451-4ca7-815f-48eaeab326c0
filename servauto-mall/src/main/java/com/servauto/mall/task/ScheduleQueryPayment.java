package com.servauto.mall.task;


import com.servauto.mall.enums.payment.PaymentChannel;
import com.servauto.mall.service.payment.PaymentFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
@Slf4j
@Configuration
@EnableScheduling
public class ScheduleQueryPayment {

    @Resource
    private PaymentFactory paymentFactory;
    @Scheduled(cron = "0 */4 * * * ?")
    public void queryPaymentStatus() {
        try {
            log.info("QueryPaymentStatus定时任务开始执行...");
            paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).batchQueryPaymentStatus();
            log.info("QueryPaymentStatus定时任务执行完成");
        } catch (Exception e) {
            log.error("QueryPaymentStatus定时任务执行失败", e);
        }
    }

    @Scheduled(cron = "0 */5 * * * ?")
    public void queryPaymentStatusFromI2C2P() {
        try {
            log.info("queryPaymentStatusFromI2C2P定时任务开始执行...");
            paymentFactory.createPaymentServices(PaymentChannel.I2C2P.getDesc()).batchQueryPaymentStatus();
            log.info("queryPaymentStatusFromI2C2P定时任务执行完成");
        } catch (Exception e) {
            log.error("queryPaymentStatusFromI2C2P定时任务执行失败", e);
        }
    }

}
