package com.servauto.mall.task;

import com.lark.oapi.service.contact.v3.model.BatchGetIdUserResp;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import com.servauto.common.constant.GlobalConstants;
import com.servauto.common.constant.TiktokConstants;
import com.servauto.common.constant.TriggerConstants;
import com.servauto.framework.lark.AbstractMessageTriggerStrategy;
import com.servauto.framework.lark.interactive.CardType;
import com.servauto.framework.lark.interactive.Element;
import com.servauto.mall.model.dto.response.tiktok.TiktokTokenRespDTO;
import com.servauto.mall.service.lark.LarkService;
import com.servauto.mall.service.lark.config.LarkProperties;
import com.servauto.mall.service.lark.dto.BatchSendMessageDTO;
import com.servauto.mall.service.tiktok.TiktokService;
import com.servauto.mall.support.tiktok.config.TiktokProperties;
import io.jsonwebtoken.lang.Collections;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@EnableScheduling
@Configuration
@Slf4j
public class TiktokOrderSyncTask {

    @Resource
    private TiktokService tiktokService;

    @Resource
    private LarkProperties properties;

    @Resource
    private TiktokProperties tiktokProperties;

    @Resource
    private LarkService larkService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    //30分钟同步一次新增订单
    @Scheduled(fixedRate = 1000 * 60 * 30)
    public void syncInsertOrders() {
        Long createTime = null;
        try {
            log.info("syncInsertOrders start");
            createTime = System.currentTimeMillis() / 1000 - 60 * 30;
            tiktokService.tiktokOrdersSyncToLark(properties.getFolderToken(), properties.getTableId(), createTime, null, null, null);
            log.info("syncInsertOrders success,createTime:{}", createTime);
        } catch (Exception ex) {
            log.error("syncInsertOrders fail,createTime:{}", createTime, ex);
        }
    }

    //一小时同步一次更新订单
    @Scheduled(fixedRate = 1000 * 60 * 60)
    public void syncUpdateOrders() {
        Long updateTime = null;
        try {
            log.info("syncUpdateOrders start");
            updateTime = System.currentTimeMillis() / 1000 - 60 * 60;
            tiktokService.tiktokOrdersSyncToLark(properties.getFolderToken(), properties.getTableId(), null, updateTime, null, null);
            log.info("syncUpdateOrders success,updateTime:{}", updateTime);
        } catch (Exception ex) {
            log.error("syncUpdateOrders fail,updateTime:{}", updateTime, ex);
        }
    }

    //8小时检查一次 AccessToken
    @Scheduled(fixedRate = 1000 * 60 * 60 * 8)
    public void checkTiktokAccessToken() {
        try {
            log.info("checkTiktokAccessToken start");
            Long accessTokenTtl = stringRedisTemplate.getExpire(TiktokConstants.TIKTOK_ACCESS_TOKEN, TimeUnit.SECONDS);
            if (accessTokenTtl <= 60 * 60 * 24) {
                String[] emails = tiktokProperties.getExpirationNotificationUserList().split(",");
                BatchGetIdUserResp batchGetIdUserResp = larkService.batchGetUserId(emails, null);
                if (batchGetIdUserResp.getData() != null && batchGetIdUserResp.getData().getUserList() != null) {
                    List<UserContactInfo> list = Arrays.asList(batchGetIdUserResp.getData().getUserList());
                    Set<String> listUserIds = list.stream().map(UserContactInfo::getUserId).collect(Collectors.toSet());
                    BatchUserResp batchUserResp = larkService.batchGetUserInfo(listUserIds.toArray(new String[0]));
                    Map<String, String> userIdToName = new HashMap<>();
                    Arrays.stream(batchUserResp.getData().getItems()).forEach(f -> {
                        userIdToName.put(f.getUserId(), f.getName());
                    });
                    userIdToName.forEach((k, v) -> {
                        BatchSendMessageDTO batchSendMessage = new BatchSendMessageDTO();
                        batchSendMessage.setUserIds(new String[]{k});
                        batchSendMessage.setMsgType(GlobalConstants.INTERACTIVE);
                        CardType cardtype = new CardType();
                        cardtype.setElements(Arrays.asList(AbstractMessageTriggerStrategy.getElement(TriggerConstants.USER_NAME, true, v),
                                AbstractMessageTriggerStrategy.getElement(TriggerConstants.WARNING_MESSAGE, false, null),
                                AbstractMessageTriggerStrategy.getElement(TriggerConstants.EXPIRE_TIME_MESSAGE, true, String.valueOf(accessTokenTtl / 60 / 60)),
                                AbstractMessageTriggerStrategy.getElement(TriggerConstants.REGRANT_MESSAGE, false, null)));

                        batchSendMessage.setCard(cardtype);
                        larkService.batchSendMessage(batchSendMessage);
                    });
                }
            }
            log.info("tiktok accessToken, no need to refresh");
        } catch (Exception ex) {
            log.error("checkTiktokAccessToken fail.", ex);
        }
    }
}
