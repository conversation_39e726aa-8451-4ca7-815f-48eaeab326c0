package com.servauto.mall.event.subscriber;

import com.servauto.mall.event.dto.OrderEvent;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.service.notice.NoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
public class OrderReservationSubscriber {

    @Resource
    private NoticeService noticeService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerReservationPushLarkSubscriber(OrderEvent.OrderReservationEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reservation event orderNo {} {} {} push lark message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onBookServiceSendLark(order);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerReservationPushWhatsappSubscriber(OrderEvent.OrderReservationEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reservation event orderNo {} {} {} push whatsapp message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onBookServiceSendWhatsapp(order);
    }

}
