package com.servauto.mall.event.subscriber;

import com.servauto.mall.event.dto.OrderEvent;
import com.servauto.mall.model.entity.order.Order;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <p>OrderSubmitSubscriber</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/13 17:55
 */
@Slf4j
@Component
public class OrderSubmitSubscriber {

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerSubmitMessageSubscriber(OrderEvent.OrderSubmitEvent<Order> event) {
        log.info("customer submit succeed, mock paid order {}", event.getOrderNo());
    }

}
