package com.servauto.mall.event.producer;

import com.servauto.mall.dao.order.OrderMapper;
import com.servauto.mall.dao.order.OrderPayMapper;
import com.servauto.mall.event.dto.OrderEvent;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.model.entity.order.OrderPay;
import com.servauto.mall.model.entity.order.OrderPayExample;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>OrderProducer</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/28 16:09
 */
@Slf4j
@Component
public class OrderProducer {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderPayMapper orderPayMapper;

    public void sendOrderSubmitEvent(String orderNo) {
        log.info("Send order submit message, orderNo: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        applicationContext.publishEvent(new OrderEvent.OrderSubmitEvent<>(this, orderNo, order));
    }

    public void sendPaidEvent(String orderNo) {
        log.info("Send order paid message, orderNo: {}", orderNo);
        OrderPayExample example = new OrderPayExample();
        example.createCriteria().andOrderNoEqualTo(orderNo);
        List<OrderPay> orderPays = orderPayMapper.selectByExample(example);
        applicationContext.publishEvent(new OrderEvent.OrderPaidEvent<>(this, orderNo, orderPays.stream().findFirst().orElseThrow()));
    }

    public void sendReservationEvent(String orderNo) {
        log.info("Send order reservation message, orderNo: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        applicationContext.publishEvent(new OrderEvent.OrderReservationEvent<>(this, orderNo, order));
    }

    public void sendRescheduleEvent(String orderNo) {
        log.info("Send order reschedule message, orderNo: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        applicationContext.publishEvent(new OrderEvent.OrderRescheduleEvent<>(this, orderNo, order));
    }

}
