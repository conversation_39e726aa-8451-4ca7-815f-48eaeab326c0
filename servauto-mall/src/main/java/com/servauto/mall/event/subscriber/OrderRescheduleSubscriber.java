package com.servauto.mall.event.subscriber;

import com.servauto.mall.event.dto.OrderEvent;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.service.notice.NoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <p>OrderRescheduleSubscriber</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/16 18:51
 */
@Slf4j
@Component
public class OrderRescheduleSubscriber {

    @Resource
    private NoticeService noticeService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerReschedulePushWhatsappSubscriber(OrderEvent.OrderRescheduleEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reschedule event orderNo {} {} {} push whatsapp message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onRescheduleSendWhatsapp(order);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerReschedulePushWhatsappSubscriberToCustomer(OrderEvent.OrderRescheduleEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reschedule event orderNo {} {} {} push whatsapp message to customer",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onRescheduleSendWhatsappToCustomer(order);
    }
}
