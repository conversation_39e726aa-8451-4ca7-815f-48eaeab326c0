package com.servauto.mall.event.dto;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

public class OrderEvent {

    @Getter
    public static class AbstractOrderEvent<T> extends ApplicationEvent {

        public String orderNo;

        public T t;

        public AbstractOrderEvent(Object source, String orderNo, T t) {
            super(source);
            this.orderNo = orderNo;
            this.t = t;
        }
    }

    public static class OrderSubmitEvent<T> extends AbstractOrderEvent<T> {
        public OrderSubmitEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderPaidEvent<T> extends AbstractOrderEvent<T> {
        public OrderPaidEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderCanceledEvent<T> extends AbstractOrderEvent<T> {
        public OrderCanceledEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderReservationEvent<T> extends AbstractOrderEvent<T> {
        public OrderReservationEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderRescheduleEvent<T> extends AbstractOrderEvent<T> {
        public OrderRescheduleEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }



}
