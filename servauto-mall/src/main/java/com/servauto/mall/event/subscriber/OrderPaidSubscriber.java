package com.servauto.mall.event.subscriber;

import com.servauto.mall.dao.order.OrderMapper;
import com.servauto.mall.event.dto.OrderEvent;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.model.entity.order.OrderPay;
import com.servauto.mall.service.notice.NoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <p>OrderPaidSubscriber</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/13 17:55
 */
@Slf4j
@Component
public class OrderPaidSubscriber {

    @Resource
    private NoticeService noticeService;

    @Resource
    private OrderMapper orderMapper;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerPaidPushLarkSubscriber(OrderEvent.OrderPaidEvent<OrderPay> event) {
        Order order = orderMapper.selectByOrderNo(event.getOrderNo());
        log.info("receive order paid event orderNo {} {} {}  push lark message", event.getOrderNo(), order.getWorkshopName(), event.getT().getPaidAmount());
        noticeService.onOrderPaidSendLark(order);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerPaidPushWhatsappSubscriber(OrderEvent.OrderPaidEvent<OrderPay> event) {
        Order order = orderMapper.selectByOrderNo(event.getOrderNo());
        log.info("receive order paid event orderNo {} {} {} push whatsapp message", event.getOrderNo(), order.getWorkshopName(), event.getT().getPaidAmount());
        noticeService.onOrderPaidSendWhatsapp(order);
    }

}
