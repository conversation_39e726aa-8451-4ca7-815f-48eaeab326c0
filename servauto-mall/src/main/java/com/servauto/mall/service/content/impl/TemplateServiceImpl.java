package com.servauto.mall.service.content.impl;


import com.servauto.mall.dao.content.ContentLayoutMapper;
import com.servauto.mall.dao.content.ContentLayoutResourceMapper;
import com.servauto.mall.dao.content.ContentResourceMapper;
import com.servauto.mall.dao.content.ContentTemplateMapper;
import com.servauto.mall.model.dto.response.content.LayoutDTO;
import com.servauto.mall.model.dto.response.content.ResourceDTO;
import com.servauto.mall.model.dto.response.content.TemplateDTO;
import com.servauto.mall.model.entity.content.ContentLayout;
import com.servauto.mall.model.entity.content.ContentLayoutResource;
import com.servauto.mall.model.entity.content.ContentResource;
import com.servauto.mall.model.entity.content.ContentTemplate;
import com.servauto.mall.service.content.TemplateService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private ContentTemplateMapper contentTemplateMapper;

    @Resource
    private ContentLayoutMapper contentLayoutMapper;

    @Resource
    private ContentLayoutResourceMapper contentLayoutResourceMapper;

    @Resource
    private ContentResourceMapper contentResourceMapper;

    @Override
    @Cacheable(cacheNames = "content:templates#1h", key = "#templateCode")
    public TemplateDTO getTemplateByTemplateCode(String templateCode) {
        TemplateDTO.TemplateDTOBuilder builder = TemplateDTO.builder().code(templateCode);
        ContentTemplate template = contentTemplateMapper.selectByTemplateCode(templateCode);
        if (template == null || !template.getEnabled()) {
            return builder.build();
        }

        List<ContentLayout> layouts = contentLayoutMapper.selectByTemplateCode(templateCode);
        List<Long> layoutIds = layouts.stream().filter(ContentLayout::getEnabled).map(ContentLayout::getId).toList();
        if (CollectionUtils.isEmpty(layoutIds)) {
            return builder.build();
        }

        List<ContentLayoutResource> layoutResources = contentLayoutResourceMapper.selectByLayoutIds(layoutIds);
        List<Long> resourceIds = layoutResources.stream().filter(ContentLayoutResource::getEnabled).map(ContentLayoutResource::getResourceId).toList();
        if (CollectionUtils.isEmpty(resourceIds)) {
            return builder.build();
        }

        List<ContentResource> resources = contentResourceMapper.selectByIds(resourceIds);
        Map<Long, ContentResource> resourceMap = resources.stream().collect(Collectors.toMap(ContentResource::getId, s -> s));
        Map<Long, ContentLayout> layoutMap = layouts.stream().filter(ContentLayout::getEnabled).collect(Collectors.toMap(ContentLayout::getId, s -> s));

        List<LayoutDTO> layoutList = Lists.newArrayList();
        layoutResources.stream().collect(Collectors.groupingBy(ContentLayoutResource::getLayoutId)).forEach((layoutId, lrs) -> {

            List<ResourceDTO> resourceList = Lists.newArrayList();
            lrs.forEach(r -> {
                ContentResource resource = resourceMap.get(r.getResourceId());
                resourceList.add(convertResource(resource, r.getResourceSeq()));
            });

            ContentLayout layout = layoutMap.get(layoutId);
            layoutList.add(convertLayout(layout, resourceList));
        });

        return builder.code(template.getCode()).title(template.getTitle()).layouts(layoutList).build();
    }


    private static ResourceDTO convertResource(ContentResource resource, Long resourceSeq) {
        return ResourceDTO.builder().id(resource.getId())
                .title(resource.getTitle())
                .seq(resourceSeq)
                .pictUri(resource.getPictUri())
                .entryUri(resource.getEntryUri())
                .entryType(resource.getEntryType())
                .ext(resource.getExt())
                .build();
    }

    private static LayoutDTO convertLayout(ContentLayout layout, List<ResourceDTO> resourceList) {
        return LayoutDTO.builder().id(layout.getId()).seq(layout.getSeq()).title(layout.getTitle())
                .type(layout.getType()).resources(resourceList).build();
    }
}
