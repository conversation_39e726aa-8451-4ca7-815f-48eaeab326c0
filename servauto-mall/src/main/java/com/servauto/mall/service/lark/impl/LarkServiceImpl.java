package com.servauto.mall.service.lark.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonParser;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.response.BaseResponse;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.*;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.service.lark.LarkService;
import com.servauto.mall.service.lark.config.LarkClientConfig;
import com.servauto.mall.service.lark.config.LarkProperties;
import com.servauto.mall.service.lark.dto.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class LarkServiceImpl implements LarkService {

    @Resource
    private LarkProperties larkProperties;

    @Resource
    private LarkClientConfig larkClient;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisTemplate redisTemplate;

//    private static final String L_D_R_APP_TOKEN_KEY = "tiktokDeveloper:app_access_token";

    private static final String L_D_R_TENANT_TOKEN_KEY = "tiktokDeveloper:tenant_access_token";

    @Override
    public CreateAppResp createAppAndTables(String tableName) {

        CreateAppReq req = CreateAppReq.newBuilder()
                .reqApp(ReqApp.newBuilder()
                        .name(tableName)
                        .folderToken(larkProperties.getFolderToken())
                        .build())
                .build();
        // 发起请求
        CreateAppResp resp = null;

        String tenantAccessToken = this.getAccessToken();

        try {

            resp = larkClient.client().bitable().app().create(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public UpdateAppResp updateAppAndTables(String newTableName, String appToken) {
        // 创建请求对象
        UpdateAppReq req = UpdateAppReq.newBuilder()
                .appToken(appToken)
                .updateAppReqBody(UpdateAppReqBody.newBuilder()
                        .name(newTableName)
                        .isAdvanced(true)
                        .build())
                .build();

        String tenantAccessToken = this.getAccessToken();

        // 发起请求
        UpdateAppResp resp = null;
        try {
            resp = larkClient.client().bitable().app().update(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public GetAppResp getAppAndTablesByAppToken(String appToken) {
        // 创建请求对象
        GetAppReq req = GetAppReq.newBuilder()
                .appToken(appToken)
                .build();

        String tenantAccessToken = this.getAccessToken();

        // 发起请求
        GetAppResp resp = null;
        try {
            resp = larkClient.client().bitable().app().get(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public BatchCreateAppTableRecordResp batchCreateAppTableRecord(BatchCreateTableRecordDTO dto) {

        AppTableRecord[] appTableRecords = new AppTableRecord[dto.getRecords().size()];
        for (int i = 0; i < dto.getRecords().size(); i++) {
            AppTableRecord appTableRecord = AppTableRecord.newBuilder()
                    .fields(dto.getRecords().get(i).getFields())
                    .build();
            appTableRecords[i] = appTableRecord;
        }

        // 创建请求对象
        BatchCreateAppTableRecordReq req = BatchCreateAppTableRecordReq.newBuilder()
                .appToken(dto.getAppToken())
                .tableId(dto.getTableId())
                .userIdType(dto.getUserIdType())
                .clientToken(dto.getClientToken())
                .ignoreConsistencyCheck(dto.getIgnoreConsistencyCheck())
                .batchCreateAppTableRecordReqBody(BatchCreateAppTableRecordReqBody.newBuilder()
                        .records(appTableRecords)
                        .build()).build();
        String tenantAccessToken = this.getAccessToken();

        // 发起请求
        BatchCreateAppTableRecordResp resp = null;
        try {
            resp = larkClient.client().bitable().appTableRecord().batchCreate(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public BatchUpdateAppTableRecordResp batchUpdateAppTableRecord(BatchUpdateTableRecordDTO dto) {


        AppTableRecord[] records = new AppTableRecord[dto.getRecords().size()];

        dto.getRecords().forEach(record -> {
            AppTableRecord appTableRecord = AppTableRecord.newBuilder()
                    .fields(record.getFields())
                    .recordId(record.getRecordId())
                    .build();
            records[dto.getRecords().indexOf(record)] = appTableRecord;
        });

        // 创建请求对象
        BatchUpdateAppTableRecordReq req = BatchUpdateAppTableRecordReq.newBuilder()
                .appToken(dto.getAppToken())
                .tableId(dto.getTableId())
                .userIdType(dto.getUserIdType())
                .ignoreConsistencyCheck(dto.getIgnoreConsistencyCheck())
                .batchUpdateAppTableRecordReqBody(BatchUpdateAppTableRecordReqBody.newBuilder()
                        .records(records)
                        .build())
                .build();

        String tenantAccessToken = this.getAccessToken();

        // 发起请求
        BatchUpdateAppTableRecordResp resp = null;

        // 处理服务端错误
        try {
            resp = larkClient.client().bitable().appTableRecord().batchUpdate(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public ListAppTableRecordResp queryTableRecords(QueryTableRecordsDTO query) {

        // 创建请求对象
        ListAppTableRecordReq req = ListAppTableRecordReq.newBuilder()
                .appToken(query.getAppToken())
                .tableId(query.getTableId())
                .viewId(query.getViewId())
                .filter(query.getFilter())
                .sort(String.valueOf(query.getSort()))
                .fieldNames(String.valueOf(query.getFieldNames()))
                .textFieldAsArray(query.getTextFieldAsArray())
                .userIdType(query.getUserIdType())
                .displayFormulaRef(query.getDisplayFormulaRef())
                .automaticFields(query.getAutomaticFields())
                .pageToken(query.getPageToken())
                .pageSize(query.getPageSize())
                .build();

        String tenantAccessToken = this.getAccessToken();

        // 发起请求
        ListAppTableRecordResp resp = null;

        // 处理服务端错误
        try {
            resp = larkClient.client().bitable().appTableRecord().list(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public String getAccessToken() {

        Object tenantAccessToken = redisTemplate.opsForValue().get(L_D_R_TENANT_TOKEN_KEY);

        if (null != tenantAccessToken) {
            return tenantAccessToken.toString();
        }

        Map<String, Object> data = new HashMap<>();
        data.put("app_id", larkProperties.getAppId());
        data.put("app_secret", larkProperties.getAppSecret());
        // 设置请求头，指定 Content-Type 为 JSON
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSONObject.toJSONString(data), headers);
        String response = restTemplate.postForObject(
                "https://open.larksuite.com/open-apis/auth/v3/app_access_token/internal",
                requestEntity,
                String.class
        );
        if (response != null && !response.isEmpty()) {
            AccessTokenDTO accessTokenDTO = JacksonSerializer.deSerialize(response, AccessTokenDTO.class);
            // 缓存访问令牌，设置过期时间为token的有效期
            redisTemplate.opsForValue().set(L_D_R_TENANT_TOKEN_KEY, accessTokenDTO.getTenantAccessToken(),
                    accessTokenDTO.getExpire(), TimeUnit.SECONDS);
            return accessTokenDTO.getAppAccessToken();
        }
        return "";
    }

    @Override
    public BatchGetIdUserResp batchGetUserId(String[] emails, String[] mobiles) {

        // 创建请求对象
        BatchGetIdUserReq req = BatchGetIdUserReq.newBuilder()
                .userIdType("user_id")
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                        .emails(emails != null ? emails : new String[]{})
                        .mobiles(mobiles != null ? mobiles : new String[]{})
                        .includeResigned(true)
                        .build())
                .build();
        // 发起请求
        BatchGetIdUserResp resp = null;
        String tenantAccessToken = this.getAccessToken();
        try {

            resp = larkClient.client().contact().user().batchGetId(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);

        if (resp.getCode() != 0) {
            throw new RuntimeException("获取用户信息失败");
        }
        return resp;
    }

    @Override
    public GetUserInfoByNameResp getUserInfoByName(String name) {

        Object tenantAccessToken = this.getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + tenantAccessToken);
        // 创建请求实体
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 发送请求
        ResponseEntity<GetUserInfoByNameResp> response = restTemplate.exchange(
                "https://open.larksuite.com/open-apis/search/v1/user?query=" + name + "&page_size=200",
                HttpMethod.GET,
                entity,
                GetUserInfoByNameResp.class
        );
        return response.getBody();
    }

    @Override
    public CopyAppResp copyAppTable(String appToken, String newAppName) {
        CopyAppReq req = CopyAppReq.newBuilder()
                .appToken(appToken)
                .copyAppReqBody(CopyAppReqBody.newBuilder()
                        .name(newAppName)
                        .folderToken(larkProperties.getFolderToken())
                        .withoutContent(true)
                        .build())
                .build();

        String tenantAccessToken = this.getAccessToken();
        // 发起请求
        CopyAppResp resp = null;
        try {
            resp = larkClient.client().bitable().app().copy(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public CreateMessageResp createMessage(String userId, String content, String msgType) {

        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType("user_id")
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(userId)
                        .msgType(msgType)
                        .content(content)
                        .uuid(UUID.randomUUID().toString().toLowerCase())
                        .build())
                .build();
        // 发起请求
        CreateMessageResp resp = null;
        String tenantAccessToken = this.getAccessToken();
        try {
            resp = larkClient.client().im().message().create(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;
    }

    @Override
    public BatchUserResp batchGetUserInfo(String[] userIds) {

        BatchUserReq req = BatchUserReq.newBuilder()
                .userIdType("user_id")
                .userIds(userIds)
                .departmentIdType("open_department_id")
                .build();
        // 发起请求
        BatchUserResp resp = null;
        String tenantAccessToken = this.getAccessToken();
        try {
            resp = larkClient.client().contact().user().batch(req, RequestOptions.newBuilder()
                    .tenantAccessToken(tenantAccessToken)
                    .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 处理服务端错误
        checkResponse(resp);
        return resp;


    }

    @Override
    public BatchSendMessageResp batchSendMessage(BatchSendMessageDTO batchSendMessageDTO) {

        String tenantAccessToken = this.getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + tenantAccessToken);

        String reqStr = JacksonSerializer.serialize(batchSendMessageDTO);
        // 创建请求实体
        HttpEntity<String> entity = new HttpEntity<>(reqStr,headers);
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
                "https://open.larksuite.com/open-apis/message/v4/batch_send/",
                HttpMethod.POST,
                entity,
                String.class
        );

        return JacksonSerializer.deSerialize(response.getBody(), BatchSendMessageResp.class);
    }

    private void checkResponse(BaseResponse<?> resp) {
        if (!resp.success()) {
            log.error("code:{},msg:{},reqId:{}, resp:{}", resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
        }
    }

}
