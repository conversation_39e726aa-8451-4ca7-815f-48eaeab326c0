package com.servauto.mall.service.payment.impl;

import com.servauto.mall.model.dto.response.payment.ipay88.Ipay88RequeryResult.TxDetailsInquiryCardInfoResult;
import com.servauto.mall.model.dto.response.payment.ipay88.Ipay88RequeryResult.TxDetailsInquiryCardInfoResponse;
import com.servauto.mall.model.dto.response.payment.ipay88.Ipay88RequeryResult.Body;

import com.servauto.common.utils.generator.UniqueID;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.constants.RedisKeyConstants;
import com.servauto.mall.enums.payment.PaymentChannel;
import com.servauto.mall.enums.payment.PaymentStatus;
import com.servauto.mall.enums.payment.ipay88.Ipay88Status;
import com.servauto.mall.model.dto.request.payment.CallBackPaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.CreatePaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.UpdatePaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.ipay88.IPay88PaymentCallBackReqDTO;
import com.servauto.mall.model.dto.request.payment.ipay88.IPay88ReQueryPaymentReqDTO;
import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.dto.response.payment.PaymentResDTO;
import com.servauto.mall.model.dto.response.payment.ipay88.Ipay88PaymentResDTO;
import com.servauto.mall.model.dto.response.payment.ipay88.Ipay88RequeryResult;
import com.servauto.mall.model.entity.payment.Payment;
import com.servauto.mall.service.order.TradeService;
import com.servauto.mall.service.payment.PaymentFactoryServices;
import com.servauto.mall.service.payment.PaymentServices;
import com.servauto.mall.support.payment.config.Ipay88PaymentProperties;
import com.servauto.mall.support.utils.XmlUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class Ipay88PaymentFactoryServicesImpl implements PaymentFactoryServices {

    @Resource
    private Ipay88PaymentProperties ipay88PaymentProperties;

    @Resource
    private PaymentServices paymentServices;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private Environment environment;

    @Resource
    private TradeService tradeService;

    @Resource
    private TransactionTemplate transactionTemplate;

    private static final String HMAC_SHA512 = "HmacSHA512";
    private static final String ENCODING = "UTF-8";
    private static final String CURRENCY = "MYR";
    private static final String PAYMENT_LOCK_KEY = "payment_lock_key";

    /**
     * create payment
     *
     * @param createPaymentReqDTO
     * @return
     */
    @Override
    public Ipay88PaymentResDTO createPayment(CreatePaymentReqDTO createPaymentReqDTO) {
        log.info("createPayment createPaymentReqDTO: {}", createPaymentReqDTO);
        try {
            PaymentDTO paymentDto = paymentServices.queryPayment(createPaymentReqDTO.getOrderNo());

            Ipay88PaymentResDTO paymentResDTO = initializePaymentResDTO();

            if (paymentDto != null) {
                paymentResDTO.setPayNo(paymentDto.getPayNo());
            } else {
                paymentResDTO.setPayNo(UniqueID.generateId("P"));
            }

            setupBasicPaymentInfo(paymentResDTO, createPaymentReqDTO);
            setSignatureForPayment(paymentResDTO);
            savePaymentIfNew(paymentResDTO, createPaymentReqDTO, paymentDto);

            return paymentResDTO;

        } catch (Exception e) {
            log.error("createPayment Error creating payment", e);
            return null;
        }
    }

    @Override
    public Ipay88PaymentResDTO queryPaymentByPayNo(String payNo) {
        try {
            PaymentDTO paymentDto = paymentServices.queryPaymentByPayNo(payNo);
            if (paymentDto == null || !PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
                return null;
            }
            Ipay88PaymentResDTO paymentResDTO = JacksonSerializer.deSerialize(paymentDto.getRequestData(), Ipay88PaymentResDTO.class);
            return paymentResDTO;

        } catch (Exception e) {
            log.error("Error creating payment", e);
            return null;
        }

    }

    @Override
    public PaymentResDTO queryPaymentStatusByNo(String orderNo) {
        log.info("queryPaymentStatusByNo orderNo: {}", orderNo);
        // 根据订单号查询支付表
        PaymentDTO paymentDto = paymentServices.queryPayment(orderNo);
        if (paymentDto == null) {
            return null;
        }

        if (!PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
            // 支付状态不是初始状态，直接返回
            return null;
        }
        String env = environment.getProperty("spring.profiles.active");
        if (!"prod".equals(env)) {
            processQueryPaymentStatus(paymentDto);
            return null;
        }

/*
        IPay88ReQueryPaymentReqDTO paymentReqDTO = new IPay88ReQueryPaymentReqDTO();
        paymentReqDTO.setMerchantCode(ipay88PaymentProperties.getMerchant().getCode());
        paymentReqDTO.setReferenceNo(paymentDto.getPayNo());
        paymentReqDTO.setAmount(new BigDecimal(204));

        try {
            // 发起外部请求并获取响应
            String res = sendPaymentStatusRequest(paymentReqDTO);

            // 处理响应内容
            Map map = XmlUtil.fromXmlString(res, Map.class);
            String jsonResult = JacksonSerializer.serialize(map);
            Ipay88RequeryResult ipay88RequeryResult = JacksonSerializer.deSerialize(jsonResult, Ipay88RequeryResult.class);
            ipay88RequeryResult.setXmlResult(jsonResult);
            String statusRes = ipay88RequeryResult.getBody().getTxDetailsInquiryCardInfoResponse().getTxDetailsInquiryCardInfoResult().getStatus();
            //根据不同支付状态处理
            Ipay88Status status = Ipay88Status.getByCode(statusRes);
            switch (status) {
                case SUCCESS:
                    processPaySuccess(paymentDto, ipay88RequeryResult);
                    break;
                case FAIL:
                    processPayFail(paymentDto, ipay88RequeryResult);
                    break;
                default:
                    // 未知状态或未匹配到，记录日志并保持原状
                    processUnknow(paymentDto, ipay88RequeryResult);
                    break;
            }
        } catch (IOException e) {
            log.error("Failed to query payment status for orderNo: {}", orderNo, e);
            return null;
        }*/

        return null;
    }


    @Override
    public void batchQueryPaymentStatus() {
        log.info("batchQueryPaymentStatus start");
        //查询所有初始化状态的支付，如果没有直接返回
        List<PaymentDTO> paymentDtos = paymentServices.getPaymentByStatus(PaymentStatus.INIT.getCode(),PaymentChannel.IPAY88.getCode());
        if (paymentDtos.isEmpty()) {
            return;
        }
        //多线程处理支付状态查询
        ExecutorService executorService = Executors.newFixedThreadPool(paymentDtos.size());
        paymentDtos.forEach(paymentDto -> executorService.submit(() -> {
            //查询支付状态
            log.info("batchQueryPaymentStatus start payNo:{}", paymentDto.getPayNo());
            processQueryPaymentStatus(paymentDto);
            log.info("batchQueryPaymentStatus end payNo:{}", paymentDto.getPayNo());
        }));
    }

    @Override
    public void paymentCallBack(CallBackPaymentReqDTO callBackPaymentReqDTO) {
        log.info("paymentCallBack callBackPaymentReqDTO: {}", callBackPaymentReqDTO);
        //解析回调结构体
        IPay88PaymentCallBackReqDTO ipay88CallBackReqDTO = JacksonSerializer.deSerialize(callBackPaymentReqDTO.getReqData(), IPay88PaymentCallBackReqDTO.class);
        //查询支付记录
        PaymentDTO paymentDto = paymentServices.queryPaymentByPayNo(callBackPaymentReqDTO.getPayNo());
        if (paymentDto == null) {
            log.info("paymentCallBack Payment not found for payNo: {}", callBackPaymentReqDTO.getPayNo());
            return;
        }
        if (!PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
            log.info("paymentCallBack Payment already processed for payNo: {}", callBackPaymentReqDTO.getPayNo());
            return;
        }
        //对比验签
        String callBackSignature = ipay88CallBackReqDTO.getSignature();
        try {
            String localSignature = getSignatureForCallBack(ipay88CallBackReqDTO);
            if (!callBackSignature.equals(localSignature)) {
                log.info("paymentCallBack Invalid signature for payNo: {}", callBackPaymentReqDTO.getPayNo());
                return;
            }
        } catch (Exception e) {
            log.info("paymentCallBack getSignatureForCallBack fail for payNo: {}", callBackPaymentReqDTO.getPayNo());
            return;
        }


        RLock lock = redissonClient.getLock(String.format(RedisKeyConstants.UPDATE_PAYMENT_KEY, paymentDto.getPayNo()));
        boolean isLocked = false;
        try {
            // 加锁后最多持有30秒
            isLocked = lock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("paymentCallBack isLocked for payNo: {}", callBackPaymentReqDTO.getPayNo());
                return;
            }

            Ipay88Status status = Ipay88Status.getByCode(ipay88CallBackReqDTO.getStatus());
            switch (status) {
                case SUCCESS:
                    processCallBackPaySuccess(paymentDto, ipay88CallBackReqDTO);
                    break;
                case FAIL:
                    processCallBackPayFail(paymentDto, ipay88CallBackReqDTO);
                    break;
                default:
                    break;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("paymentCallBack Error processing query payment status for payNo: {}", paymentDto.getPayNo(), e);
        } finally {
            // 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void processQueryPaymentStatus(PaymentDTO paymentDto) {
        RLock lock = redissonClient.getLock(String.format(RedisKeyConstants.UPDATE_PAYMENT_KEY, paymentDto.getPayNo()));
        boolean isLocked = false;
        try {
            // 加锁后最多持有30秒
            isLocked = lock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                throw new RuntimeException("获取锁失败");
            }
            if (!PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
                return;
            }
            // 非生产环境模拟测试成功流程
            String env = environment.getProperty("spring.profiles.active");
            if (!"prod".equals(env)) {
                Ipay88RequeryResult test = getIpay88RequeryTestResult();
                paymentDto.setPayTime(new Date());
                processPaySuccess(paymentDto, test);
                return;
            }
            IPay88ReQueryPaymentReqDTO paymentReqDTO = new IPay88ReQueryPaymentReqDTO();
            paymentReqDTO.setMerchantCode(ipay88PaymentProperties.getMerchant().getCode());
            paymentReqDTO.setReferenceNo(paymentDto.getPayNo());
            paymentReqDTO.setAmount(paymentDto.getAmount());
            //  发送查询请求
            String res = sendPaymentStatusRequest(paymentReqDTO);
            log.info("batchQueryPaymentStatus sendPaymentStatusRequest resp:{}", res);
            Map map = XmlUtil.fromXmlString(res, Map.class);
            String jsonResult = JacksonSerializer.serialize(map);
            Ipay88RequeryResult ipay88RequeryResult = JacksonSerializer.deSerialize(jsonResult, Ipay88RequeryResult.class);
            ipay88RequeryResult.setXmlResult(res);
            String statusRes = ipay88RequeryResult.getBody().getTxDetailsInquiryCardInfoResponse().getTxDetailsInquiryCardInfoResult().getStatus();
            // 根据状态码处理
            Ipay88Status status = Ipay88Status.getByCode(statusRes);
            switch (status) {
                case SUCCESS:
                    processPaySuccess(paymentDto, ipay88RequeryResult);
                    break;
                case FAIL:
                    processPayFail(paymentDto, ipay88RequeryResult);
                    break;
                default:
                    processUnknow(paymentDto, ipay88RequeryResult);
                    break;
            }

        } catch (InterruptedException | IOException e) {
            Thread.currentThread().interrupt();
            log.error("Error processing query payment status for payNo: {}", paymentDto.getPayNo(), e);
        } finally {
            // 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    //  模拟测试数据
    @NotNull
    private static Ipay88RequeryResult getIpay88RequeryTestResult() {
        Ipay88RequeryResult test = new Ipay88RequeryResult();
        test.setXmlResult("Ipay88RequeryTestResult");
        Body body = new Body();
        TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = new TxDetailsInquiryCardInfoResponse();
        TxDetailsInquiryCardInfoResult txDetailsInquiryCardInfoResult = new TxDetailsInquiryCardInfoResult();
        txDetailsInquiryCardInfoResult.setPaymentId("");
        txDetailsInquiryCardInfoResult.setRefNo("");
        txDetailsInquiryCardInfoResult.setAmount("0");
        txDetailsInquiryCardInfoResult.setCurrency("");
        txDetailsInquiryCardInfoResult.setRemark("");
        txDetailsInquiryCardInfoResult.setTransId("");
        txDetailsInquiryCardInfoResult.setAuthCode("");
        txDetailsInquiryCardInfoResult.setStatus("");
        txDetailsInquiryCardInfoResult.setErrdesc("");

        txDetailsInquiryCardInfoResponse.setTxDetailsInquiryCardInfoResult(txDetailsInquiryCardInfoResult);

        body.setTxDetailsInquiryCardInfoResponse(txDetailsInquiryCardInfoResponse);
        test.setBody(body);
        return test;
    }

    //onPaymentSuccess
    private void processCallBackPaySuccess(PaymentDTO paymentDto, IPay88PaymentCallBackReqDTO iPay88PaymentCallBackReqDTO) {
        log.info("Payment callBack PaySuccess for payNo: {},ipay88RequeryResult{}", paymentDto.getPayNo(), iPay88PaymentCallBackReqDTO);
        UpdatePaymentReqDTO updatePaymentReqDTO = getcallBackUpdatePaymentReqDTO(iPay88PaymentCallBackReqDTO, PaymentStatus.SUCCESS.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentSuccess(paymentDto.getSourceId(), paymentDto.getPayNo(), paymentDto.getAmount(), paymentDto.getPayTime());
            }
        });
    }

    //onPaymentFailed
    private void processCallBackPayFail(PaymentDTO paymentDto, IPay88PaymentCallBackReqDTO iPay88PaymentCallBackReqDTO) {
        log.info("Payment callBack PayFail for payNo: {},ipay88RequeryResult{}", paymentDto.getPayNo(), iPay88PaymentCallBackReqDTO);
        UpdatePaymentReqDTO updatePaymentReqDTO = getcallBackUpdatePaymentReqDTO(iPay88PaymentCallBackReqDTO, PaymentStatus.FAIL.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentFailed(paymentDto.getSourceId(), paymentDto.getPayNo(), PaymentStatus.FAIL.getCode(), updatePaymentReqDTO.getErrMsg());
            }
        });
    }

    //onPaymentSuccess
    private void processPaySuccess(PaymentDTO paymentDto, Ipay88RequeryResult ipay88RequeryResult) {
        log.info("Payment PaySuccess for payNo: {},ipay88RequeryResult{}", paymentDto.getPayNo(), ipay88RequeryResult);
        UpdatePaymentReqDTO updatePaymentReqDTO = getUpdatePaymentReqDTO(ipay88RequeryResult, PaymentStatus.SUCCESS.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentSuccess(paymentDto.getSourceId(), paymentDto.getPayNo(), paymentDto.getAmount(), paymentDto.getPayTime());
            }
        });
    }

    //onPaymentFailed
    private void processPayFail(PaymentDTO paymentDto, Ipay88RequeryResult ipay88RequeryResult) {
        log.info("Payment PayFail for payNo: {},ipay88RequeryResult{}", paymentDto.getPayNo(), ipay88RequeryResult);
        UpdatePaymentReqDTO updatePaymentReqDTO = getUpdatePaymentReqDTO(ipay88RequeryResult, PaymentStatus.FAIL.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentFailed(paymentDto.getSourceId(), paymentDto.getPayNo(), PaymentStatus.FAIL.getCode(), updatePaymentReqDTO.getErrMsg());
            }
        });
    }

    //onPaymentTimeout
    private void processUnknow(PaymentDTO paymentDto, Ipay88RequeryResult ipay88RequeryResult) {
        log.info("Payment Unknow for payNo: {},ipay88RequeryResult{}", paymentDto.getPayNo(), ipay88RequeryResult);
        //检查支付是否过期
        log.info("Payment Unknow for paymentDto.getExpiresAt(): {},new Date(){}", paymentDto.getExpiresAt(), new Date());

        if (paymentDto.getExpiresAt().before(new Date())) {
            UpdatePaymentReqDTO updatePaymentReqDTO = getUpdatePaymentReqDTO(ipay88RequeryResult, PaymentStatus.TIME_OUT.getCode());
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                    paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                    tradeService.onPaymentTimeout(paymentDto.getSourceId(), paymentDto.getPayNo());
                }
            });
        }
    }

    //callBack
    @NotNull
    private static UpdatePaymentReqDTO getcallBackUpdatePaymentReqDTO(IPay88PaymentCallBackReqDTO iPay88PaymentCallBackReqDTO, String status) {

        UpdatePaymentReqDTO updatePaymentReqDTO = new UpdatePaymentReqDTO();
        updatePaymentReqDTO.setThirdPayNo(iPay88PaymentCallBackReqDTO.getTransId());
        updatePaymentReqDTO.setResponseData(JacksonSerializer.serialize(iPay88PaymentCallBackReqDTO));
        updatePaymentReqDTO.setStatus(status);
        if (status.equals(PaymentStatus.SUCCESS.getCode())) {
            updatePaymentReqDTO.setPayTime(new Date());
        }
        updatePaymentReqDTO.setErrCode(iPay88PaymentCallBackReqDTO.getAuthCode());
        updatePaymentReqDTO.setErrMsg(iPay88PaymentCallBackReqDTO.getErrDesc());
        return updatePaymentReqDTO;
    }

    @NotNull
    private static UpdatePaymentReqDTO getUpdatePaymentReqDTO(Ipay88RequeryResult ipay88RequeryResult, String status) {
        Ipay88RequeryResult.TxDetailsInquiryCardInfoResult paymentResult = ipay88RequeryResult.getBody().getTxDetailsInquiryCardInfoResponse().getTxDetailsInquiryCardInfoResult();

        UpdatePaymentReqDTO updatePaymentReqDTO = new UpdatePaymentReqDTO();
        updatePaymentReqDTO.setThirdPayNo(paymentResult.getTransId());
        updatePaymentReqDTO.setResponseData(ipay88RequeryResult.getXmlResult());
        updatePaymentReqDTO.setStatus(status);
        if (status.equals(PaymentStatus.SUCCESS.getCode())) {
            updatePaymentReqDTO.setPayTime(new Date());
        }
        updatePaymentReqDTO.setErrCode(paymentResult.getAuthCode());
        updatePaymentReqDTO.setErrMsg(paymentResult.getErrdesc());
        return updatePaymentReqDTO;
    }


    /**
     * 发送查询支付状态的请求并返回响应结果
     *
     * @param paymentReqDTO 请求参数 DTO
     * @return 响应 XML 内容
     * @throws IOException 网络异常或请求失败
     */
    private String sendPaymentStatusRequest(IPay88ReQueryPaymentReqDTO paymentReqDTO) throws IOException {
        MediaType mediaType = MediaType.parse("text/xml;charset=utf-8");

        // 构建请求体
        String xml = XmlUtil.toXmlString(paymentReqDTO).replace("xmlns=\"\"", "");
        String content = XmlUtil.buildSoapRequest(xml);

        RequestBody body = RequestBody.create(mediaType, content);
        Request request = new Request.Builder().url(ipay88PaymentProperties.getPlatform().getRequeryUrl()).post(body).addHeader("Content-Type", "text/xml;charset=utf-8").build();

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return response.body().string();
            } else {
                throw new IOException("HTTP request failed with code: " + response.code());
            }
        }
    }

    /**
     * save  payment
     *
     * @param paymentResDTO
     * @param createPaymentReqDTO
     * @param paymentDto
     */
    private void savePaymentIfNew(@NotNull Ipay88PaymentResDTO paymentResDTO, @NotNull CreatePaymentReqDTO createPaymentReqDTO, PaymentDTO paymentDto) {
        if (paymentDto == null) {
            Payment payment = getPayment(createPaymentReqDTO, paymentResDTO);
            paymentServices.createPayment(payment);
        }
    }

    private String getSignatureForCallBack(@NotNull IPay88PaymentCallBackReqDTO callBackReqDTO) throws Exception {
        String key = ipay88PaymentProperties.getMerchant().getKey();
        String formattedAmount = callBackReqDTO.getAmount().replace(",", "").replace(".", "");
        String toEncrypt = key + ipay88PaymentProperties.getMerchant().getCode() + callBackReqDTO.getPaymentId() + callBackReqDTO.getRefNo() + formattedAmount + callBackReqDTO.getCurrency() + callBackReqDTO.getStatus();
        return securityHmacSha512(toEncrypt, key);
    }

    /**
     * set signature
     *
     * @param paymentResDTO
     * @throws Exception
     */
    private void setSignatureForPayment(@NotNull Ipay88PaymentResDTO paymentResDTO) throws Exception {
        String key = ipay88PaymentProperties.getMerchant().getKey();
        String formattedAmount = paymentResDTO.getAmount().replace(",", "").replace(".", "");
        String toEncrypt = key + ipay88PaymentProperties.getMerchant().getCode() + paymentResDTO.getPayNo() + formattedAmount + paymentResDTO.getCurrency() + paymentResDTO.getXField1();

        String signature = securityHmacSha512(toEncrypt, key);
        paymentResDTO.setSignature(signature);
    }

    /**
     * set basic info
     *
     * @param paymentResDTO
     * @param createPaymentReqDTO
     */
    private void setupBasicPaymentInfo(@NotNull Ipay88PaymentResDTO paymentResDTO, @NotNull CreatePaymentReqDTO createPaymentReqDTO) {
        paymentResDTO.setMerchantCode(ipay88PaymentProperties.getMerchant().getCode());
        paymentResDTO.setPlatFromUrl(ipay88PaymentProperties.getPlatform().getCreateUrl());

        BigDecimal amount = createPaymentReqDTO.getAmount();
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        String formattedAmount = decimalFormat.format(amount);
        paymentResDTO.setAmount(formattedAmount);

        paymentResDTO.setCurrency(CURRENCY);
        paymentResDTO.setProdDesc(createPaymentReqDTO.getProductDesc());
        paymentResDTO.setUserName(createPaymentReqDTO.getUserName());
        paymentResDTO.setUserEmail(createPaymentReqDTO.getUserEmail());
        paymentResDTO.setUserContact(createPaymentReqDTO.getUserContact());
        paymentResDTO.setSignatureType(HMAC_SHA512);

        paymentResDTO.setResponseURL(ipay88PaymentProperties.getMerchant().getResponseURL() + "?orderNo=" + createPaymentReqDTO.getOrderNo());
        paymentResDTO.setBackendURL(ipay88PaymentProperties.getMerchant().getBackendURL());
    }

    /**
     * initialize payment resDTO
     *
     * @return
     */
    @NotNull
    private Ipay88PaymentResDTO initializePaymentResDTO() {
        Ipay88PaymentResDTO paymentResDTO = new Ipay88PaymentResDTO();
        paymentResDTO.setPaymentId("");
        paymentResDTO.setLang("");
        paymentResDTO.setOptional("");
        paymentResDTO.setAppDeeplink("");
        paymentResDTO.setXField1("");
        return paymentResDTO;
    }

    /**
     * get payment
     *
     * @param createPaymentReqDTO
     * @param paymentResDTO
     * @return
     */
    @NotNull
    private static Payment getPayment(CreatePaymentReqDTO createPaymentReqDTO, Ipay88PaymentResDTO paymentResDTO) {
        Payment payment = new Payment();
        payment.setPayNo(paymentResDTO.getPayNo());
        payment.setSourceId(createPaymentReqDTO.getOrderNo());
        payment.setChannel(PaymentChannel.IPAY88.getCode());
        payment.setPayMethod(1);
        payment.setAmount(createPaymentReqDTO.getAmount());
        payment.setCurrency(CURRENCY);
        //ExpiresAt当前时间十五分钟后失效
        Date expiresAt = new Date();
        expiresAt.setTime(expiresAt.getTime() + 15 * 60 * 1000);
        payment.setExpiresAt(expiresAt);
        payment.setPayType("");
        payment.setUrl("");
        payment.setRequestData(JacksonSerializer.serialize(paymentResDTO));
        payment.setStatus(PaymentStatus.INIT.getCode());
        return payment;
    }

    /**
     * securityHmacSha512
     *
     * @param toEncrypt
     * @param key
     * @return
     * @throws Exception
     */
    private String securityHmacSha512(String toEncrypt, String key) throws Exception {
        // Convert key to byte array
        byte[] keyBytes = key.getBytes(ENCODING);

        // Create an instance of HMACSHA512 with the key
        Mac hmacSHA512 = Mac.getInstance(HMAC_SHA512);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, HMAC_SHA512);
        hmacSHA512.init(secretKeySpec);

        // Compute the hash
        byte[] hashBytes = hmacSHA512.doFinal(toEncrypt.getBytes(ENCODING));

        // Convert the byte array to a hexadecimal string
        return byteArrayToHex(hashBytes);
    }

    private String byteArrayToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}