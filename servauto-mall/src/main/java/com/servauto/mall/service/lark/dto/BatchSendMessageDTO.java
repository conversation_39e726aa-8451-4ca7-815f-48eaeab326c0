package com.servauto.mall.service.lark.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 批量发送消息DTO
 */
@Data
public class BatchSendMessageDTO {

    /**
     * 消息类型，支持多种消息类型，详见本文“消息类型及内容示例”部分
     */
    @JsonProperty("msg_type")
    private String msgType;

    /**
     * 消息内容，支持除卡片消息外的多种消息内容，详见本文“消息类型及内容示例”部分
     */
    @JsonProperty("content")
    private Object content;

    /**
     * 卡片消息内容
     * 注意：card和content字段必须二选一
     */
    @JsonProperty("card")
    private Object card;

    @JsonProperty("user_ids")
    private String[] userIds;

    @JsonProperty("open_ids")
    private String[] openIds;

    @JsonProperty("union_ids")
    private String[] unionIds;

    @JsonProperty("department_ids")
    private String[] departmentIds;
}
