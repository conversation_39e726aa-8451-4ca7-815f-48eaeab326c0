package com.servauto.mall.service.lark.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BatchSendMessageResp {

    @JsonProperty("code")
    private Integer code;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("data")
    private Data data;

    @lombok.Data
    public static class Data {
        @JsonProperty("messageId")
        private String messageId;
        @JsonProperty("invalid_department_ids")
        private List<String> invalidDepartmentIds;
        @JsonProperty("invalid_open_ids")
        private List<String> invalidOpenIds;
        @JsonProperty("invalid_user_ids")
        private List<String> invalidUserIds;
        @JsonProperty("invalid_union_ids")
        private List<String> invalidUnionIds;

    }

}
