package com.servauto.mall.service.lark.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetUserInfoByNameResp {
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;

    @lombok.Data
    public static class Data {
        @JsonProperty("has_more")
        private String hasMore;
        @JsonProperty("page_token")
        private String pageToken;
        @JsonProperty("users")
        private List<users> users;
    }

    @lombok.Data
    public static class users {
        @JsonProperty("department_ids")
        private List<String> departmentIds;
        @JsonProperty("name")
        private String name;
        @JsonProperty("open_id")
        private String openId;
        @JsonProperty("user_id")
        private String userId;
    }
}