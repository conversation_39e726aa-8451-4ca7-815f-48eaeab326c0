package com.servauto.mall.service.packageinfo;

import com.servauto.mall.model.dto.request.packageinfo.ChangePackageDetailDTO;
import com.servauto.mall.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageDetailDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;

import java.util.List;

public interface PackageService {

    /**
     * get package
     *
     * @param id id
     * @return PackageInfoDTO
     */
    PackageInfoDTO getPackage(long id);

    /**
     * get packages
     *
     * @param reqDTO reqDTO
     * @return List<PackageInfoDTO>
     */
    List<PackageInfoDTO> getPackages(QueryPackagesDTO reqDTO);

    /**
     * get package detail list
     *
     * @param id id
     * @param carId carId
     * @param productId productId
     * @return PackageInfoDTO
     */
    List<PackageDetailDTO> getPackageDetails(Long id, Long carId, Long productId, String code);

    /**
     * change package detail
     *
     * @param reqDTO reqDTO
     * @return PackageInfoDTO
     */
    PackageDetailDTO changePackageDetail(ChangePackageDetailDTO reqDTO);
}
