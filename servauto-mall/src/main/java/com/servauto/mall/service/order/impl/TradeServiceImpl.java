package com.servauto.mall.service.order.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.DateUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.common.utils.generator.SimpleCodeGenerate;
import com.servauto.common.utils.generator.UniqueID;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.SpringUtils;
import com.servauto.framework.utils.bean.BeanUtils;
import com.servauto.mall.dao.order.*;
import com.servauto.mall.enums.DeliveryMode;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.enums.ServiceSupportedOn;
import com.servauto.mall.enums.YesOrNo;
import com.servauto.mall.enums.order.OrderDeliveryTypeEnum;
import com.servauto.mall.enums.order.OrderStatusEnum;
import com.servauto.mall.enums.order.OrderTypeEnum;
import com.servauto.mall.event.producer.OrderProducer;
import com.servauto.mall.factory.order.OrderDeliveryFactory;
import com.servauto.mall.factory.order.OrderFactory;
import com.servauto.mall.factory.order.OrderStatusLogFactory;
import com.servauto.mall.model.dto.request.order.*;
import com.servauto.mall.model.dto.request.payment.CreatePaymentReqDTO;
import com.servauto.mall.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.mall.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.mall.model.dto.response.TypeNameDTO;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.mall.model.dto.response.order.*;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.model.dto.response.payment.PaymentResDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.model.entity.car.CarInfo;
import com.servauto.mall.model.entity.customer.CustomerInfo;
import com.servauto.mall.model.entity.order.*;
import com.servauto.mall.service.car.CarInfoService;
import com.servauto.mall.service.customer.CustomerService;
import com.servauto.mall.service.order.TradeService;
import com.servauto.mall.service.packageinfo.PackageService;
import com.servauto.mall.service.payment.PaymentFactory;
import com.servauto.mall.service.product.ProductService;
import com.servauto.mall.service.serviceinfo.ServiceInfoService;
import com.servauto.mall.service.workshop.WorkshopService;
import com.servauto.mall.support.utils.ScheduleGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.servauto.mall.enums.payment.PaymentChannel.IPAY88;

@Slf4j
@Service
public class TradeServiceImpl implements TradeService {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ProductService productService;

    @Resource
    private ServiceInfoService serviceInfoService;

    @Resource
    private PackageService packageService;

    @Resource
    private CustomerService customerService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderProductMapper orderProductMapper;

    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;

    @Resource
    private WorkshopService workshopService;

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private OrderStatusLogMapper orderStatusLogMapper;

    @Resource
    private OrderPayMapper orderPayMapper;

    @Resource
    private OrderProducer orderProducer;

    @Resource
    private PaymentFactory paymentFactory;

    @Override
    @CachePut(cacheNames = "create:orders#3h", key = "#customerId + #result.orderNo")
    public CreateOrderDTO createOrder(Long customerId, CreateOrderReqDTO createOrderReqDTO) {
        CustomerInfo customerInfo = customerService.getCustomerById(customerId);
        Preconditions.checkNotNull(customerInfo);

        CreateOrderDTO createOrder = validateCreateOrder(customerId, createOrderReqDTO);
        createOrder.setOrderNo(UniqueID.generateId(""));
        createOrder.setCustomerInfo(OrderCustomerDTO.builder().mobile(customerInfo.getMobile()).realName(customerInfo.getRealName()).build());
        createOrder.setRemark(createOrderReqDTO.getRemark());
        return createOrder;
    }

    private CreateOrderDTO validateCreateOrder(Long customerId, CreateOrderReqDTO createOrderReqDTO) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(createOrderReqDTO.getProducts()));

        List<CarInfo> carInfos = carInfoService.selectByCustomerId(customerId);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(carInfos));
        Preconditions.checkArgument(carInfos.stream().anyMatch(e -> e.getId().equals(createOrderReqDTO.getCarId()) && e.getCustomerId().equals(customerId)));

        CarInfoDTO carInfoDTO = carInfoService.selectCarInfoById(createOrderReqDTO.getCarId());
        log.info("create order validate carId:{} carInfo {}", createOrderReqDTO.getCarId(), carInfoDTO);
        Preconditions.checkNotNull(carInfoDTO);

        List<Long> productIds = createOrderReqDTO.getProducts().stream().map(OrderProductReqDTO::getProductId).distinct().toList();
        List<ProductDTO> products = productService.getProducts(productIds);
        List<ProductDTO> listedProducts = products.stream().filter(e -> SaleStatus.isListed(e.getStatus())).collect(Collectors.toList());
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(listedProducts) && products.size() == listedProducts.size());

        ServiceInfoDTO serviceInfo = null;
        PackageInfoDTO packageInfo = null;

        if (createOrderReqDTO.getServiceId() == null) {

            // The package must contains the products and cannot contain the service
            if (createOrderReqDTO.getPackageId() != null) {
                packageInfo = packageService.getPackage(createOrderReqDTO.getPackageId());

                // The package cannot contain the service
                Preconditions.checkArgument(Objects.isNull(packageInfo.getServiceId()) || packageInfo.getServiceId() == 0);
                Preconditions.checkArgument(new HashSet<>(packageInfo.getProductIds()).containsAll(productIds), String.format("The package [%s] not contain this products", packageInfo.getName()));
            } else {

                // Check the service is required or not (all products)
                List<Long> serviceIds = products.stream()
                        .map(ProductDTO::getServiceId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .filter(serviceId -> serviceId > 0)
                        .toList();
                if (CollectionUtils.isNotEmpty(serviceIds)) {
                    List<ServiceInfoDTO> serviceInfos = serviceInfoService.getServices(QueryServicesDTO.builder().ids(serviceIds).build());
                    Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfos));
                    Preconditions.checkArgument(serviceInfos.stream().noneMatch(e -> YesOrNo.yes(e.getIsRequired())));
                }
            }

        } else {

            serviceInfo = serviceInfoService.getService(createOrderReqDTO.getServiceId());
            Preconditions.checkNotNull(serviceInfo, "Cannot find service");

            // The package contains the product + service
            if (createOrderReqDTO.getPackageId() != null) {

                Preconditions.checkArgument(ServiceSupportedOn.isSupportPackage(serviceInfo.getSupportedOn()));
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfo.getPackageIds()));
                Preconditions.checkArgument(new HashSet<>(serviceInfo.getPackageIds()).contains(createOrderReqDTO.getPackageId()));

                packageInfo = packageService.getPackage(createOrderReqDTO.getPackageId());
                Preconditions.checkNotNull(packageInfo, "The package cannot be used without service");
                Preconditions.checkArgument(SaleStatus.isListed(packageInfo.getStatus()));
                Preconditions.checkArgument(packageInfo.getServiceId().equals(createOrderReqDTO.getServiceId()));
                Preconditions.checkArgument(new HashSet<>(packageInfo.getProductIds()).containsAll(productIds), String.format("The service [%s] not contain this products", serviceInfo.getName()));

            } else {

                // Must be product + service
                Preconditions.checkArgument(ServiceSupportedOn.isSupportProduct(serviceInfo.getSupportedOn()));
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfo.getProductIds()));
                Preconditions.checkArgument(new HashSet<>(serviceInfo.getProductIds()).containsAll(productIds));
            }
        }

        CreateOrderDTO createOrderDTO = OrderFactory.buildCreateOrderResp(createOrderReqDTO.getProducts(), products, serviceInfo, packageInfo);
        createOrderDTO.setCarInfo(carInfoDTO);
        createOrderDTO.setDeliveryMethods(OrderDeliveryFactory.convert(createOrderReqDTO, packageInfo, products));
        Preconditions.checkArgument(BigDecimal.ZERO.compareTo(createOrderDTO.getGrandTotal()) < 0);
        return createOrderDTO;
    }

    @Cacheable(cacheNames = "create:orders", key = "#customerId + #orderNo", unless = "#result == null")
    public CreateOrderDTO queryCreateOrder(Long customerId, String orderNo) {
        return null;
    }


    @Override
    @CacheEvict(cacheNames = "create:orders", key = "#customerId + #reqDTO.orderNo")
    public SubmitOrderDTO submitOrder(Long customerId, SubmitOrderReqDTO reqDTO) {

        CreateOrderDTO createOrderDTO = getBean().queryCreateOrder(customerId, reqDTO.getOrderNo());
        if (createOrderDTO == null) {
            List<OrderDTO> orders = this.queryOrders(QueryOrderReqDTO.builder().customerId(customerId).orderNo(reqDTO.getOrderNo()).build());
            if (CollectionUtils.isEmpty(orders)) {
                throw BusinessException.of("The order has timed out, please Re-order");
            }

            return OrderFactory.buildSubmitOrderResp(orders.stream().findFirst().orElseThrow());
        }

        Preconditions.checkArgument(StringUtils.isNotBlank(reqDTO.getLicensePlate()), "licensePlate cannot null");

        // re-validate order request
        CreateOrderReqDTO createOrderReqDTO = new CreateOrderReqDTO();
        BeanUtils.copyProperties(reqDTO, createOrderReqDTO);
        CreateOrderDTO validated = validateCreateOrder(customerId, createOrderReqDTO);
        validated.setOrderNo(createOrderDTO.getOrderNo());
        Preconditions.checkArgument(validated.getGrandTotal().equals(createOrderDTO.getGrandTotal()));

        CustomerInfo customerInfo = customerService.getCustomerById(customerId);
        Preconditions.checkNotNull(customerInfo);

        WorkshopDTO workshopInfo = null;
        if (Objects.nonNull(reqDTO.getWorkshopId()) && reqDTO.getWorkshopId() > 0) {
            workshopInfo = workshopService.getWorkshopDetail(reqDTO.getWorkshopId());
            Preconditions.checkNotNull(workshopInfo);
        }

        PackageInfoDTO packageInfo = null;
        if (Objects.nonNull(createOrderDTO.getPackageId()) && createOrderDTO.getPackageId() > 0) {
            packageInfo = packageService.getPackage(createOrderDTO.getPackageId());
            Preconditions.checkNotNull(packageInfo);
        }

        // allow workshop is null
        if (reqDTO.getServiceId() != null && reqDTO.getServiceId() > 0) {

            ServiceInfoDTO serviceInfo = serviceInfoService.getService(createOrderDTO.getServiceId());
            Preconditions.checkNotNull(serviceInfo);
            Preconditions.checkArgument(Objects.isNull(reqDTO.getShippingAddressId()));

            if (reqDTO.getDeliveryType() == null) {
                reqDTO.setDeliveryType(OrderDeliveryTypeEnum.WORKSHOP.getCode());
            }
            Preconditions.checkArgument(OrderDeliveryTypeEnum.isInWorkshop(reqDTO.getDeliveryType())
                    || OrderDeliveryTypeEnum.isPickup(reqDTO.getDeliveryType()));

            if (reqDTO.getWorkshopId() != null && reqDTO.getWorkshopId() > 0) {
                Preconditions.checkArgument(serviceInfo.getWorkshopIds().contains(reqDTO.getWorkshopId()));
            }

            if (packageInfo != null) {
                Preconditions.checkArgument(packageInfo.getServiceId().equals(createOrderDTO.getServiceId()));
            }

        } else {

            Preconditions.checkArgument(OrderDeliveryTypeEnum.isShipping(reqDTO.getDeliveryType())
                    || OrderDeliveryTypeEnum.isPickup(reqDTO.getDeliveryType()));

            if (OrderDeliveryTypeEnum.isShipping(reqDTO.getDeliveryType())) {
                Preconditions.checkArgument(Objects.isNull(reqDTO.getWorkshopId()));
                Preconditions.checkArgument(Objects.isNull(reqDTO.getReservationTime()));
                Preconditions.checkArgument(Objects.nonNull(reqDTO.getShippingAddressId()) && reqDTO.getShippingAddressId() > 0, "ShippingAddressId cannot be null");
            } else {
                Preconditions.checkArgument(Objects.isNull(reqDTO.getShippingAddressId()));
                Preconditions.checkArgument(Objects.nonNull(reqDTO.getWorkshopId()) && reqDTO.getWorkshopId() > 0, "WorkshopId cannot be null");
            }

            if (packageInfo != null) {
                Preconditions.checkArgument(Objects.isNull(packageInfo.getServiceId()));
                if (CollectionUtils.isNotEmpty(packageInfo.getDeliveryModes())) {
                    Preconditions.checkArgument(validateDeliveryModes(packageInfo.getDeliveryModes(), reqDTO.getDeliveryType()));
                }
            } else {
                OrderProductDTO orderProduct = createOrderDTO.getProducts().stream().findAny().orElseThrow();
                ProductDTO product = productService.getProduct(orderProduct.getProductId());
                if (CollectionUtils.isNotEmpty(product.getDeliveryModes())) {
                    Preconditions.checkArgument(validateDeliveryModes(product.getDeliveryModes(), reqDTO.getDeliveryType()));
                }
            }
        }

        OrderDeliveryTypeEnum deliveryType = OrderDeliveryTypeEnum.getByCode(reqDTO.getDeliveryType());
        Preconditions.checkNotNull(deliveryType);

        Order tblOrder = new Order();
        tblOrder.setOrderNo(reqDTO.getOrderNo());
        tblOrder.setCustomerId(customerId);
        tblOrder.setCustomerName(customerInfo.getRealName());
        tblOrder.setCustomerMobile(customerInfo.getMobile());
        tblOrder.setCustomerEmail(StringUtils.isNotEmpty(reqDTO.getEmail()) ? reqDTO.getEmail() : "<EMAIL>");
        tblOrder.setInStoreMobile(customerInfo.getMobile());
        tblOrder.setInStoreName(customerInfo.getRealName());
        tblOrder.setCreateBy(customerInfo.getRealName());
        tblOrder.setUpdateBy(customerInfo.getRealName());

        CarInfoDTO carInfoDTO = createOrderDTO.getCarInfo();
        tblOrder.setLicensePlate(reqDTO.getLicensePlate());
        tblOrder.setBrand(carInfoDTO.getBrand());
        tblOrder.setModel(carInfoDTO.getModel());
        tblOrder.setYear(carInfoDTO.getYear());
        tblOrder.setTransmissionType(carInfoDTO.getTransmissionType());
        tblOrder.setVariant(carInfoDTO.getVariant());
        tblOrder.setCarIcon(carInfoDTO.getIcon());

        tblOrder.setStatus(OrderStatusEnum.PENDING_PAY.getCode());
        tblOrder.setOrderTime(DateUtils.getNowDate());
        tblOrder.setShippingFee(createOrderDTO.getShippingFee());
        tblOrder.setDeliveryType(deliveryType.getCode());
        tblOrder.setForceFixedPrice(packageInfo == null ? null : packageInfo.getIsFixedPrice());
        tblOrder.setWorkshopId(workshopInfo == null ? null : workshopInfo.getId());
        tblOrder.setWorkshopName(workshopInfo == null ? null : workshopInfo.getName());
        tblOrder.setType(Objects.nonNull(tblOrder.getPackageId()) && tblOrder.getPackageId() > 0 ? OrderTypeEnum.PACKAGE.getCode() : OrderTypeEnum.PRODUCT.getCode());
        tblOrder.setAppliedCoupon(false);
        tblOrder.setOriginalAmount(createOrderDTO.getOriginalAmount());
        tblOrder.setDiscountAmount(createOrderDTO.getDiscountAmount());
        tblOrder.setCouponAmount(createOrderDTO.getCouponAmount());
        tblOrder.setSubtotal(createOrderDTO.getSubtotal());
        tblOrder.setGrandTotal(createOrderDTO.getGrandTotal());
        tblOrder.setServiceId(createOrderDTO.getServiceId());
        tblOrder.setServiceName(createOrderDTO.getServiceName());
        tblOrder.setServiceFee(createOrderDTO.getServiceFee());
        tblOrder.setServiceHour(createOrderDTO.getServiceHour());
        tblOrder.setPackageId(createOrderDTO.getPackageId());
        tblOrder.setPackageName(createOrderDTO.getPackageName());
        tblOrder.setReservationTime(Objects.nonNull(reqDTO.getReservationTime()) ? new Date(reqDTO.getReservationTime()) : null);
        tblOrder.setRemark(createOrderDTO.getRemark());

        // new order products
        List<OrderProduct> products = createOrderDTO.getProducts().stream().map(e -> {
            OrderProduct orderProduct = new OrderProduct();
            BeanUtils.copyProperties(e, orderProduct);
            orderProduct.setOrderNo(createOrderDTO.getOrderNo());
            orderProduct.setProductAttribute(JacksonSerializer.serialize(e.getProductAttribute()));
            orderProduct.setSubOrderNo(UniqueID.generateId(""));
            return orderProduct;
        }).toList();

        // new order pay
        OrderPay orderPay = new OrderPay();
        orderPay.setOrderNo(tblOrder.getOrderNo());
        orderPay.setCustomerId(customerId);
        orderPay.setGrandTotal(tblOrder.getGrandTotal());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {

                // create payment
                PaymentResDTO payResult = createPayment(tblOrder, products.stream().findFirst().orElseThrow().getProductName());
                if (payResult == null || StringUtils.isBlank(payResult.getPayNo())) {
                    throw BusinessException.of("Submit order failed, Please try again later");
                }
                orderPay.setPayNo(payResult.getPayNo());
                orderPayMapper.insertSelective(orderPay);

                // generate pickup code if necessary
                if (OrderDeliveryTypeEnum.isPickup(deliveryType.getCode())) {
                    tblOrder.setPickupCode(generate());
                }

                // create order
                orderMapper.insertSelective(tblOrder);

                // create order product
                orderProductMapper.batchInsert(products);

                // create order delivery
                if (OrderDeliveryTypeEnum.isShipping(deliveryType.getCode())) {
                    orderDeliveryMapper.insertSelective(getOrderDelivery());
                }

                // create order status log
                orderStatusLogMapper.insertSelective(OrderStatusLogFactory.
                        build(tblOrder.getOrderNo(), OrderStatusEnum.PENDING_PAY, "", tblOrder.getCreateBy()));

                // update user license plate
                carInfoService.updateLicenseByCustomerIdAndCarId(customerId, reqDTO.getCarId(), tblOrder.getLicensePlate());

                // send order submit event
                orderProducer.sendOrderSubmitEvent(tblOrder.getOrderNo());
            }

            @NotNull
            private OrderDelivery getOrderDelivery() {
                CustomerAddressDTO address = customerService.getAddressesById(reqDTO.getShippingAddressId());
                Preconditions.checkNotNull(address, "Could not find address");
                Preconditions.checkArgument(address.getCustomerId().equals(customerId));
                OrderDelivery orderDelivery = new OrderDelivery();
                BeanUtils.copyProperties(address, orderDelivery);
                orderDelivery.setId(null);
                orderDelivery.setOrderNo(tblOrder.getOrderNo());
                return orderDelivery;
            }
        });

        return OrderFactory.buildSubmitOrderResp(queryOrderByOrderNo(customerId, createOrderDTO.getOrderNo()));
    }

    private PaymentResDTO createPayment(Order tblOrder, String productDesc) {
        CreatePaymentReqDTO createPaymentReqDTO = new CreatePaymentReqDTO();
        createPaymentReqDTO.setUserName(tblOrder.getCustomerName());
        createPaymentReqDTO.setUserEmail(tblOrder.getCustomerEmail());
        createPaymentReqDTO.setUserContact(tblOrder.getCustomerMobile());
        createPaymentReqDTO.setOrderNo(tblOrder.getOrderNo());
        createPaymentReqDTO.setAmount(tblOrder.getGrandTotal());
        createPaymentReqDTO.setProductDesc(productDesc);
        log.info("Create payment request params orderNo:{} payment request:{}", tblOrder.getOrderNo(), createPaymentReqDTO);
        PaymentResDTO paymentResult = paymentFactory.createPaymentServices(IPAY88.getDesc()).createPayment(createPaymentReqDTO);
//        PaymentResDTO paymentResult = paymentFactory.createPaymentServices(I2C2P.getDesc()).createPayment(createPaymentReqDTO);
        log.info("Create payment request params orderNo:{} payment result:{}", tblOrder.getOrderNo(), paymentResult);
        return paymentResult;
    }


    @Override
    public PageInfo<OrderDTO> pageOrders(QueryOrderReqDTO queryOrderReqDTO) {
        PageSupport.startPage();
        List<Order> orders = orderMapper.selectByConditions(queryOrderReqDTO);
        if (CollectionUtils.isEmpty(orders)) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<Order> pageInfo = PageInfo.of(orders);

        List<String> orderNos = orders.stream().map(Order::getOrderNo).toList();
        List<OrderProduct> orderProducts = orderProductMapper.selectByOrderNos(orderNos);

        OrderPayExample example = new OrderPayExample();
        example.createCriteria().andOrderNoIn(orderNos);
        List<OrderPay> orderPays = orderPayMapper.selectByExample(example);

        List<OrderDelivery> orderDeliveries = orderDeliveryMapper.selectByOrderNos(orderNos);
        List<OrderDTO> list = OrderFactory.buildOrders(orders, orderPays, orderProducts, orderDeliveries);

        Map<Long, OrderWorkshopDTO> workshopMap = this.buildWorkshops(orders);
        list.forEach(e -> {
            e.setWorkshopInfo(e.getWorkshopId() != null && e.getWorkshopId() > 0 ? workshopMap.get(e.getWorkshopId()) : null);
            e.setPayNowVisible(OrderStatusEnum.isUnpaid(e.getStatus()) && e.getExpireTime().after(DateUtils.getNowDate()));
            e.setCancelVisible(OrderStatusEnum.isUnpaid(e.getStatus()));
            e.setBookServiceVisible(OrderStatusEnum.isPendingDelivery(e.getStatus())
                    && OrderDeliveryTypeEnum.isInWorkshop(e.getDeliveryType())
                    && (e.getWorkshopId() == null || e.getWorkshopId() == 0));
            e.setRescheduleVisible(OrderStatusEnum.isPendingDelivery(e.getStatus())
                    && OrderDeliveryTypeEnum.isInWorkshop(e.getDeliveryType())
                    && e.getWorkshopId() != null && e.getWorkshopId() > 0);
            e.setPickupVisible(OrderStatusEnum.hasPaid(e.getStatus()) && OrderDeliveryTypeEnum.isPickup(e.getDeliveryType()) && StringUtils.isNotBlank(e.getPickupCode()));
        });

        return PageSupport.copyProperties(pageInfo, list);
    }

    @Override
    public List<OrderDTO> queryOrders(QueryOrderReqDTO queryOrderReqDTO) {
        List<Order> orders = orderMapper.selectByConditions(queryOrderReqDTO);
        if (CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }

        List<String> orderNos = orders.stream().map(Order::getOrderNo).toList();
        List<OrderProduct> orderProducts = orderProductMapper.selectByOrderNos(orderNos);

        List<OrderDelivery> orderDeliveries = orderDeliveryMapper.selectByOrderNos(orderNos);

        OrderPayExample example = new OrderPayExample();
        example.createCriteria().andOrderNoIn(orderNos);
        List<OrderPay> orderPays = orderPayMapper.selectByExample(example);

        Map<Long, OrderWorkshopDTO> workshopMap = this.buildWorkshops(orders);
        List<OrderDTO> result = OrderFactory.buildOrders(orders, orderPays, orderProducts, orderDeliveries);
        result.forEach(e -> e.setWorkshopInfo(e.getWorkshopId() != null && e.getWorkshopId() > 0 ? workshopMap.get(e.getWorkshopId()) : null));
        return result;
    }

    private Map<Long, OrderWorkshopDTO> buildWorkshops(List<Order> orders) {
        List<Long> workshopIds = orders.stream().filter(e -> e.getWorkshopId() != null && e.getWorkshopId() > 0).map(Order::getWorkshopId).toList();
        Map<Long, OrderWorkshopDTO> workshopMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(workshopIds)) {
            List<WorkshopDTO> workshops = workshopService.getWorkshopList(QueryWorkshopsDTO.builder().ids(workshopIds).build());
            List<OrderWorkshopDTO> orderWorkshops = OrderFactory.buildWorkshops(workshops);
            workshopMap = orderWorkshops.stream().collect(Collectors.toMap(OrderWorkshopDTO::getId, k -> k));
        }
        return workshopMap;
    }

    @Override
    public OrderDTO queryOrderByOrderNo(Long customerId, String orderNo) {
        List<OrderDTO> orders = this.queryOrders(QueryOrderReqDTO.builder().customerId(customerId).orderNo(orderNo).build());
        return orders.stream().findFirst().orElseThrow();
    }

    @Override
    public void onPaymentSuccess(String orderNo, String payNo, BigDecimal paidAmount, Date paidTime) {

        Order order = orderMapper.selectByOrderNo(orderNo);
        Preconditions.checkNotNull(order);

        OrderPay pay = queryOrderPay(orderNo, payNo);
        Preconditions.checkNotNull(pay);

        OrderPay orderPay = new OrderPay();
        orderPay.setId(pay.getId());
        orderPay.setPaidAmount(paidAmount);
        orderPay.setPaidTime(paidTime);

        Order tblOrder = new Order();
        tblOrder.setPaidTime(paidTime);
        tblOrder.setUpdateBy(order.getCustomerName());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {

                if (OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
                    boolean reserved = order.getWorkshopId() != null && order.getWorkshopId() > 0
                            && OrderFactory.isValidDate(order.getReservationTime());
                    OrderStatusEnum statusEnum = reserved ? OrderStatusEnum.PENDING_WORKSHOP_CONFIRM : OrderStatusEnum.PAYMENT_SUCCESS;
                    tblOrder.setStatus(statusEnum.getCode());
                    if (reserved) {
                        orderStatusLogMapper.insertSelective(OrderStatusLogFactory.build(orderNo, OrderStatusEnum.PAYMENT_SUCCESS, "Payment success", tblOrder.getUpdateBy()));
                    }
                    orderStatusLogMapper.insertSelective(OrderStatusLogFactory.build(orderNo, statusEnum, "Payment success", tblOrder.getUpdateBy()));
                } else {
                    tblOrder.setStatus(OrderStatusEnum.PENDING_DELIVERY.getCode());
                }

                int rows = orderPayMapper.updateByPrimaryKeySelective(orderPay);
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                rows = orderMapper.casByOrderNoAndStatusList(orderNo, tblOrder,
                        Arrays.asList(OrderStatusEnum.PENDING_PAY.getCode(), OrderStatusEnum.CANCELED.getCode(), OrderStatusEnum.CANCELED_TIMEOUT.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                orderProducer.sendPaidEvent(order.getOrderNo());
            }
        });
    }

    private OrderPay queryOrderPay(String orderNo, String payNo) {
        OrderPayExample example = new OrderPayExample();
        example.createCriteria().andOrderNoEqualTo(orderNo).andPayNoEqualTo(payNo);
        List<OrderPay> orderPays = orderPayMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(orderPays)) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        return orderPays.stream().findAny().orElseThrow();
    }

    @Override
    public void onPaymentFailed(String orderNo, String payNo, String errorCode, String errorMsg) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        Preconditions.checkNotNull(order);

        OrderPay pay = queryOrderPay(orderNo, payNo);
        Preconditions.checkNotNull(pay);

        OrderPay orderPay = new OrderPay();
        orderPay.setId(pay.getId());
        orderPay.setCanceledTime(DateUtils.getNowDate());
        orderPay.setErrorCode(errorCode);
        orderPay.setErrorMsg(errorMsg);

        Order tblOrder = new Order();
        tblOrder.setStatus(OrderStatusEnum.CANCELED.getCode());
        tblOrder.setUpdateBy(order.getCustomerName());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                orderStatusLogMapper.insertSelective(OrderStatusLogFactory.build(orderNo, OrderStatusEnum.PENDING_DELIVERY, "Payment canceled", tblOrder.getUpdateBy()));

                int rows = orderPayMapper.updateByPrimaryKeySelective(orderPay);
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                rows = orderMapper.casByOrderNoAndStatusList(orderNo, tblOrder,
                        Arrays.asList(OrderStatusEnum.PENDING_PAY.getCode(), OrderStatusEnum.COMPLETED.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
            }
        });
    }

    @Override
    public void onPaymentTimeout(String orderNo, String payNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        Preconditions.checkNotNull(order);

        OrderPay pay = queryOrderPay(orderNo, payNo);
        Preconditions.checkNotNull(pay);

        OrderPay orderPay = new OrderPay();
        orderPay.setId(pay.getId());
        orderPay.setCanceledTime(DateUtils.getNowDate());

        Order tblOrder = new Order();
        tblOrder.setStatus(OrderStatusEnum.CANCELED_TIMEOUT.getCode());
        tblOrder.setUpdateBy(order.getCustomerName());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                orderStatusLogMapper.insertSelective(OrderStatusLogFactory.build(orderNo, OrderStatusEnum.PENDING_DELIVERY, "Payment canceled timeout", tblOrder.getUpdateBy()));

                int rows = orderPayMapper.updateByPrimaryKeySelective(orderPay);
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                rows = orderMapper.casByOrderNoAndStatusList(orderNo, tblOrder, Collections.singletonList(OrderStatusEnum.PENDING_PAY.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
            }
        });
    }

    @Override
    public List<OrderWorkshopDTO> queryOrderWorkshops(OrderWorkshopReqDTO reqDTO) {

        CreateOrderDTO createOrderDTO = getBean().queryCreateOrder(reqDTO.getCustomerId(), reqDTO.getOrderNo());
        Long serviceId;
        if (createOrderDTO != null) {
            serviceId = createOrderDTO.getServiceId();
        } else {
            List<OrderDTO> orders = this.queryOrders(QueryOrderReqDTO.builder().customerId(reqDTO.getCustomerId()).orderNo(reqDTO.getOrderNo()).build());
            if (CollectionUtils.isEmpty(orders)) {
                throw BusinessException.of(ResponseCode.ERROR);
            }
            serviceId = orders.stream().findAny().orElseThrow().getServiceId();
        }

        List<WorkshopDTO> workshops;
        if (serviceId != null && serviceId > 0) {
            ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceId);
            Preconditions.checkNotNull(serviceInfo);
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfo.getWorkshopIds()));
            workshops = workshopService.getWorkshopList(QueryWorkshopsDTO.builder().ids(serviceInfo.getWorkshopIds()).build());
        } else {
            workshops = workshopService.getWorkshopList(QueryWorkshopsDTO.builder().build());
        }
        Preconditions.checkNotNull(workshops, "Cannot find workshops");
        return OrderFactory.buildWorkshops(reqDTO.getLatitude(), reqDTO.getLongitude(), workshops);
    }

    @Override
    public OrderWorkshopDTO queryOrderWorkshopOpeningHours(Long customerId, String orderNo, Long workshopId) {

        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(workshopId);
        Preconditions.checkNotNull(workshopInfo);

        List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(workshopInfo.getServiceTimeList())) {
            serviceTimes = ScheduleGenerator.generateServicesTimes(workshopInfo.getServiceTimeList());
        }

        return OrderFactory.buildWorkshop(workshopInfo, serviceTimes);
    }

    @Override
    public void reservationOrder(Long customerId, ReservationOrderReqDTO reqDTO) {
        Order order = this.getOrder(customerId, reqDTO.getOrderNo());
        if (order.getWorkshopId() != null && order.getWorkshopId() > 0) {
            return;
        }
        Preconditions.checkArgument(!OrderDeliveryTypeEnum.isShipping(order.getDeliveryType()));
        Preconditions.checkArgument(Objects.nonNull(order.getServiceId()));

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(order.getServiceId());
        Preconditions.checkNotNull(serviceInfo);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfo.getWorkshopIds()));
        Preconditions.checkArgument(new HashSet<>(serviceInfo.getWorkshopIds()).contains(reqDTO.getWorkshopId()));

        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(reqDTO.getWorkshopId());
        Preconditions.checkNotNull(workshopInfo);

        CustomerInfo customerInfo = customerService.getCustomerById(customerId);
        Preconditions.checkNotNull(customerInfo);

        Order tblOrder = new Order();
        tblOrder.setWorkshopId(reqDTO.getWorkshopId());
        tblOrder.setWorkshopName(workshopInfo.getName());
        tblOrder.setReservationTime(new Date(reqDTO.getReservationTime()));
        tblOrder.setUpdateBy(customerInfo.getRealName());

        boolean afterFirstReservation = OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType()) && OrderStatusEnum.PAYMENT_SUCCESS.getCode().equals(order.getStatus())
                && (order.getWorkshopId() == null || order.getWorkshopId() == 0 || order.getReservationTime() == null);

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                if (afterFirstReservation) {
                    tblOrder.setStatus(OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode());
                    orderStatusLogMapper.insertSelective(OrderStatusLogFactory.build(order.getOrderNo(), OrderStatusEnum.PENDING_WORKSHOP_CONFIRM, "customer reservation order", tblOrder.getUpdateBy()));
                }

                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, OrderStatusEnum.allowReservation());
                if (rows == 0) {
                    throw BusinessException.of("reservation order failed");
                }

                orderProducer.sendReservationEvent(order.getOrderNo());
            }
        });
    }

    private Order getOrder(Long customerId, String orderNo) {
        OrderExample example = new OrderExample();
        example.createCriteria().andCustomerIdEqualTo(customerId).andOrderNoEqualTo(orderNo);
        List<Order> orders = orderMapper.selectByExample(example);
        return orders.stream().findFirst().orElseThrow();
    }

    @Override
    public void rescheduleOrder(Long customerId, RescheduleOrderReqDTO reqDTO) {
        CustomerInfo customerInfo = customerService.getCustomerById(customerId);
        Preconditions.checkNotNull(customerInfo);

        Order order = this.getOrder(customerId, reqDTO.getOrderNo());
        if (order.getWorkshopId() == null || order.getWorkshopId() == 0) {
            return;
        }
        Preconditions.checkArgument(!OrderDeliveryTypeEnum.isShipping(order.getDeliveryType()));

        Order tblOrder = new Order();
        tblOrder.setReservationTime(new Date(reqDTO.getReservationTime()));
        tblOrder.setUpdateBy(customerInfo.getRealName());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, OrderStatusEnum.allowReservation());
                if (rows == 0) {
                    throw BusinessException.of("reschedule reservation time failed");
                }

                orderProducer.sendRescheduleEvent(order.getOrderNo());
            }
        });
    }

    @Override
    public void cancelOrder(Long customerId, String orderNo) {
        OrderDTO orderDTO = queryOrderByOrderNo(customerId, orderNo);
        Preconditions.checkNotNull(orderDTO);

        if (OrderStatusEnum.isCanceled(orderDTO.getStatus())) {
            return;
        }

        if (!OrderStatusEnum.isUnpaid(orderDTO.getStatus())) {
            throw BusinessException.of("order status has changed");
        }

        Order order = new Order();
        order.setStatus(OrderStatusEnum.CANCELED.getCode());
        order.setUpdateBy(customerService.getCustomerById(customerId).getRealName());

        int rows = orderMapper.casByOrderNoAndStatusList(orderDTO.getOrderNo(), order, Collections.singletonList(OrderStatusEnum.PENDING_PAY.getCode()));
        if (rows == 0) {
            throw BusinessException.of("cancel order failed");
        }
    }

    @Override
    public CashierDTO cashier(Long customerId, String orderNo, String payNo) {

        OrderDTO orderDTO = queryOrderByOrderNo(customerId, orderNo);
        Preconditions.checkNotNull(orderDTO);
        Preconditions.checkNotNull(orderDTO.getOrderPay());
        Preconditions.checkArgument(payNo.equals(orderDTO.getOrderPay().getPayNo()));

        if (OrderStatusEnum.hasPaid(orderDTO.getStatus())) {
            throw BusinessException.of("order has paid");
        }

        PaymentResDTO paymentResult = paymentFactory.createPaymentServices(IPAY88.getDesc()).queryPaymentByPayNo(orderDTO.getOrderPay().getPayNo());
//        PaymentResDTO paymentResult = paymentFactory.createPaymentServices(I2C2P.getDesc()).queryPaymentByPayNo(orderDTO.getOrderPay().getPayNo());
        if (paymentResult == null) {
            throw BusinessException.of("System abnormality, please try again later");
        }
        return CashierDTO.builder().orderNo(orderNo).payNo(payNo).payment(paymentResult).build();
    }

    private TradeService getBean() {
        return SpringUtils.getBean("tradeServiceImpl");
    }

    private String generate() {

        String code;
        do {
            code = new SimpleCodeGenerate().generate();
        } while (orderMapper.checkPickupCodeExist(code) > 0);

        return code;
    }

    private static boolean validateDeliveryModes(List<TypeNameDTO> deliveryModes, String deliveryType) {
        return deliveryModes.stream().anyMatch(e -> {
            if (OrderDeliveryTypeEnum.isShipping(deliveryType)) {
                return DeliveryMode.MAILING.getCode().equals(e.getType());
            }
            if (OrderDeliveryTypeEnum.isPickup(deliveryType)) {
                return DeliveryMode.SELF_PICKUP.getCode().equals(e.getType());
            }
            return false;
        });
    }
}

