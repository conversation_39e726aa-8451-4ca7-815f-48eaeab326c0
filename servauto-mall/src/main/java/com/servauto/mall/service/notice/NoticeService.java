package com.servauto.mall.service.notice;

import com.servauto.mall.model.entity.order.Order;

/**
 * <p>NoticeService</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/13 18:25
 */
public interface NoticeService {

    /**
     * order created
     *
     * @param order order
     */
    void onOrderPaidSendWhatsapp(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void onRescheduleSendWhatsappToCustomer(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void onRescheduleSendWhatsapp(Order order);

    /**
     * order created
     *
     * @param order order
     */
    void onBookServiceSendWhatsapp(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void onBookServiceSendLark(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void onOrderPaidSendLark(Order order);
}
