package com.servauto.mall.service.workshop;

import com.github.pagehelper.PageInfo;
import com.servauto.mall.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;

import java.util.List;

public interface WorkshopService {

    /**
     * workshops
     *
     * @param pageNo   pageNo
     * @param pageSize pageSize
     * @return workshops
     */
    PageInfo<WorkshopDTO> pageWorkshops(Integer pageNo, Integer pageSize);

    /**
     * get workshops by conditions
     *
     * @param queryWorkshopsDTO reqDTO
     * @return List<WorkshopDTO>
     */
    List<WorkshopDTO> getWorkshopList(QueryWorkshopsDTO queryWorkshopsDTO);

    /**
     * get workshop detail by id
     *
     * @param id id
     * @return WorkshopDetailDTO
     */
    WorkshopDTO getWorkshopDetail(long id);
}
