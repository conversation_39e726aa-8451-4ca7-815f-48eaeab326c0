package com.servauto.mall.service.customer;

import com.servauto.mall.model.dto.request.customer.CreateAddressReqDTO;
import com.servauto.mall.model.dto.request.customer.EditAddressReqDTO;
import com.servauto.mall.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.mall.model.entity.customer.CustomerInfo;

import java.util.List;

public interface CustomerService {

    /**
     * query by mobile
     *
     * @param mobile mobile
     * @return CustomerInfo
     */
    CustomerInfo getUserByMobile(String mobile);

    /**
     * query by mobile if absent create
     *
     * @param mobile mobile
     * @param source source
     * @return CustomerInfo
     */
    CustomerInfo createCustomerIfAbsent(String mobile, String source);

    /**
     * query customer address
     *
     * @param customerId customer Id
     * @return List<CustomerAddressDTO>
     */
    List<CustomerAddressDTO> getAddressesByCustomerId(Long customerId);

    /**
     * query customer address
     *
     * @param addressId addressId
     * @return CustomerAddressDTO
     */
    CustomerAddressDTO getAddressesById(Long addressId);

    /**
     * createAddress
     *
     * @param createAddressReqDTO create address request
     */
    void createAddress(Long customerId, CreateAddressReqDTO createAddressReqDTO);

    /**
     * updateAddress
     *
     * @param editAddressReqDTO update address request
     */
    void updateAddress(Long customerId, EditAddressReqDTO editAddressReqDTO);


    /**
     * delete user address
     *
     * @param customerId customerId
     * @param addressId  addressId
     */
    void deleteAddress(Long customerId, Long addressId);

    /**
     * get customer info by id
     *
     * @param customerId id
     * @return CustomerInfoDTO
     */
    CustomerInfo getCustomerById(Long customerId);
}
