package com.servauto.mall.service.workshop.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.utils.PageSupport;
import com.servauto.mall.dao.workshop.WorkshopMapper;
import com.servauto.mall.dao.workshop.WorkshopServiceTimeMapper;
import com.servauto.mall.factory.workshop.WorkshopFactory;
import com.servauto.mall.factory.workshop.WorkshopServiceTimeFactory;
import com.servauto.mall.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.model.entity.workshop.Workshop;
import com.servauto.mall.model.entity.workshop.WorkshopServiceTime;
import com.servauto.mall.service.workshop.WorkshopService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkshopServiceImpl implements WorkshopService {

    @Resource
    private WorkshopMapper workshopMapper;

    @Resource
    private WorkshopServiceTimeMapper workshopServiceTimeMapper;

    @Resource
    private LocationService locationService;

    @Override
    public PageInfo<WorkshopDTO> pageWorkshops(Integer pageNo, Integer pageSize) {
        PageSupport.startPage(pageNo, pageSize);
        List<Workshop> list = workshopMapper.selectByConditions(QueryWorkshopsDTO.builder().build());
        PageInfo<Workshop> pageInfo = PageInfo.of(list);
        return PageSupport.copyProperties(pageInfo, this.convert(list));
    }

    public List<WorkshopDTO> getWorkshopList(QueryWorkshopsDTO queryWorkshopsDTO) {
        List<Workshop> list = workshopMapper.selectByConditions(queryWorkshopsDTO);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return convert(list);
    }

    private List<WorkshopDTO> convert(List<Workshop> list) {
        List<WorkshopDTO> workshops = new ArrayList<>();
        for (var v : list) {
            var dto = WorkshopFactory.convert(v);
            dto.setFeaturedTags(Arrays.stream(v.getFeaturedTags().split(",")).toList());
            workshops.add(dto);
        }

        Map<String, AreaDTO> areaMap = locationService.getAreaMap();
        Map<String, StateDTO> stateMap = locationService.getStateMap();
        Map<Long, List<WorkshopServiceTime>> wsMap = workshopServiceTimeMapper.selectByWorkshopIds(list.stream().map(Workshop::getId).toList())
                .stream().collect(Collectors.groupingBy(WorkshopServiceTime::getWorkshopId));
        for (var v : workshops) {
            List<WorkshopServiceTime> workshopServiceTimes = wsMap.get(v.getId());
            if (CollectionUtils.isNotEmpty(workshopServiceTimes)) {
                v.setServiceTimeList(workshopServiceTimes.stream().map(WorkshopServiceTimeFactory::convert).toList());
            }
            if (StringUtils.isNotEmpty(v.getStateCode())) {
                v.setStateName(stateMap.get(v.getStateCode()).getName());
            }
            if (StringUtils.isNotEmpty(v.getCityCode())) {
                v.setCityName(areaMap.get(v.getCityCode()).getName());
            }
        }
        return workshops;
    }

    public WorkshopDTO getWorkshopDetail(long id) {
        return this.getWorkshopList(QueryWorkshopsDTO.builder().ids(List.of(id)).build()).get(0);
    }
}
