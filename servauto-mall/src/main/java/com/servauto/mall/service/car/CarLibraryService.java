package com.servauto.mall.service.car;

public interface CarLibraryService {
   /* CarLibraryDTO getCarLibraryByLibId(String carLibId);
    CarLibraryDTO getCarLibraryById(Integer id);

    void insertCarLibrary(CarLibrary carLibrary);

    void updateCarLibrary(CarLibrary carLibrary);

    void deleteCarLibrary(Integer id);
    //获取所有去重后的待同步CarLibrary的Brands
    List<CarLibrary> selectWaitSyncDistinctBrands();
    //获取所有去重后的Brands
    List<String> selectAllBrandsDistinct();

    //根据 brand 获取model
    List<String> selectModelsByBrandDistinct(String brand);

    //根据brand model 获取model_year
    List<String> selectModelYearsByBrandAndModelDistinct(String brand, String model);

    //根据brand、model和model_year获取grade
    List<String> selectVariantByBrandModelAndYearDistinct(String brand, String model, String modelYear);

    //根据brand、model、model_year和grade获取carlibrary的集合
    List<CarLibrary> selectCarLibrariesByBrandModelYearAndGrade(String brand, String model, String modelYear, String grade);


    void updateCarLibraryStatusByIds(List<Integer> libraryIds);*/
}