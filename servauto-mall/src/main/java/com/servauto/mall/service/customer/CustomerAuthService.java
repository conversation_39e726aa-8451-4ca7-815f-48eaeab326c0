package com.servauto.mall.service.customer;

import com.servauto.mall.model.dto.request.auth.AuthLoginReqDTO;
import com.servauto.mall.model.dto.request.auth.AuthSmsSendReqDTO;
import com.servauto.mall.model.dto.response.auth.AuthLoginRespDTO;

public interface CustomerAuthService {

    /**
     * send sms code to user
     *
     * @param userId userId
     * @param reqDTO request by website
     */
    void sendSmsCode(Long userId, AuthSmsSendReqDTO reqDTO);

    /**
     * user login
     *
     * @param reqVO login request params
     * @return token
     */
    AuthLoginRespDTO login(AuthLoginReqDTO reqVO);

    /**
     * logout by token
     *
     * @param token token
     */
    void logout(String token);
}
