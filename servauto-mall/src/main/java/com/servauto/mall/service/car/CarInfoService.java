package com.servauto.mall.service.car;

import com.servauto.mall.model.dto.request.customer.CreateCarInfoReqDTO;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.model.entity.car.CarInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CarInfoService {

    List<CarInfo> selectByCustomerId(@Param("customerId") Long customerId);

    int deleteByCustomerId(@Param("customerId") Long customerId);

    void addNewCar(Long customerId, CreateCarInfoReqDTO createCarInfoReqDTO);

    List<CarInfoDTO> carInfoList(@Param("customerId") Long customerId);

    CarInfoDTO selectCarInfoById(Long id);

    void deleteByCustomerIdAndCarId(Long customerId, Long carId);

    void updateLicenseByCustomerIdAndCarId(Long customerId, Long carId, String licensePlate);


    void setDefaultCar(Long customerId, Long carId);

    CarInfoDTO getDefaultCar(Long customerId);


    Double getCarOilCapacity(Long carId);

}