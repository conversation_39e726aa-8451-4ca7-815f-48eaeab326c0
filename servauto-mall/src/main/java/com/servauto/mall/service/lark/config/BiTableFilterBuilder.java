package com.servauto.mall.service.lark.config;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * 多维表格记录过滤条件构建工具类
 * 用于生成符合多维表格API要求的过滤表达式
 */
public class BiTableFilterBuilder {
    private List<String> filterConditions = new ArrayList<>();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 添加空值检查条件
     * @param fieldName 字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder isEmpty(String fieldName) {
        filterConditions.add("CurrentValue.[" + fieldName + "] =\"\"");
        return this;
    }

    /**
     * 添加非空值检查条件
     * @param fieldName 字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder isNotEmpty(String fieldName) {
        filterConditions.add("NOT(CurrentValue.[" + fieldName + "] =\"\")");
        return this;
    }

    /**
     * 添加等于条件
     * @param fieldName 字段名称
     * @param value 比较值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder equalTo(String fieldName, String value) {
        filterConditions.add("CurrentValue.[" + fieldName + "] = \"" + escapeValue(value) + "\"");
        return this;
    }

    /**
     * 添加不等于条件
     * @param fieldName 字段名称
     * @param value 比较值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder notEqualTo(String fieldName, String value) {
        filterConditions.add("CurrentValue.[" + fieldName + "] != \"" + escapeValue(value) + "\"");
        return this;
    }

    /**
     * 添加大于等于条件
     * @param fieldName 字段名称
     * @param value 比较值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder greaterThanOrEqualTo(String fieldName, Number value) {
        filterConditions.add("CurrentValue.[" + fieldName + "] >= " + value);
        return this;
    }

    /**
     * 添加小于等于条件
     * @param fieldName 字段名称
     * @param value 比较值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder lessThanOrEqualTo(String fieldName, String value) {
        filterConditions.add("CurrentValue.[" + fieldName + "] <= \"" + escapeValue(value) + "\"");
        return this;
    }

    /**
     * 添加包含条件
     * @param fieldName 字段名称
     * @param value 包含的值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder contains(String fieldName, String value) {
        filterConditions.add("CurrentValue.[" + fieldName + "].contains(\"" + escapeValue(value) + "\")");
        return this;
    }

    /**
     * 添加不包含条件
     * @param fieldName 字段名称
     * @param value 不包含的值
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder notContains(String fieldName, String value) {
        filterConditions.add("NOT(CurrentValue.[" + fieldName + "].contains(\"" + escapeValue(value) + "\"))");
        return this;
    }

    /**
     * 添加AND组合条件
     * @param conditions 要组合的条件构建器
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder and(Supplier<BiTableFilterBuilder>... conditions) {
        List<String> andConditions = new ArrayList<>();
        for (Supplier<BiTableFilterBuilder> condition : conditions) {
            BiTableFilterBuilder builder = condition.get();
            andConditions.add(builder.build());
        }
        filterConditions.add("AND(" + String.join(", ", andConditions) + ")");
        return this;
    }

    /**
     * 添加OR组合条件
     * @param conditions 要组合的条件构建器
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder or(Supplier<BiTableFilterBuilder>... conditions) {
        List<String> orConditions = new ArrayList<>();
        for (Supplier<BiTableFilterBuilder> condition : conditions) {
            BiTableFilterBuilder builder = condition.get();
            orConditions.add(builder.build());
        }
        filterConditions.add("OR(" + String.join(", ", orConditions) + ")");
        return this;
    }

    /**
     * 今天的日期条件
     * @param fieldName 日期字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder today(String fieldName) {
        filterConditions.add("CurrentValue.[" + fieldName + "] = TODAY()");
        return this;
    }

    /**
     * 昨天的日期条件
     * @param fieldName 日期字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder yesterday(String fieldName) {
        filterConditions.add("CurrentValue.[" + fieldName + "] = TODAY() - 1");
        return this;
    }

    /**
     * 明天的日期条件
     * @param fieldName 日期字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder tomorrow(String fieldName) {
        filterConditions.add("CurrentValue.[" + fieldName + "] = TODAY() %2B1");
        return this;
    }

    /**
     * 本周的日期条件
     * @param fieldName 日期字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder thisWeek(String fieldName) {
        filterConditions.add("AND(TODAY() - (WEEKDAY(TODAY(), 2) -1) <= CurrentValue.[" + fieldName + "], " +
                "CurrentValue.[" + fieldName + "] <= TODAY() %2B (7-WEEKDAY(TODAY(), 2)))");
        return this;
    }

    /**
     * 本月的日期条件
     * @param fieldName 日期字段名称
     * @return 当前过滤构建器实例
     */
    public BiTableFilterBuilder thisMonth(String fieldName) {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        int month = today.getMonthValue();

        String startOfMonth = "DATE(" + year + ", " + month + ", 1)";
        String endOfMonth = "DATE(" + year + ", " + (month + 1) + ", 0)";

        filterConditions.add("AND(" + startOfMonth + " <= CurrentValue.[" + fieldName + "], " +
                "CurrentValue.[" + fieldName + "] <= " + endOfMonth + ")");
        return this;
    }

    /**
     * 构建过滤表达式
     * @return 过滤表达式字符串
     */
    public String build() {
        return String.join(" ", filterConditions);
    }

    /**
     * 构建并编码过滤表达式
     * @return URL编码后的过滤表达式
     */
    public String buildAndEncode() {
        String filter = build();
        return URLEncoder.encode(filter, StandardCharsets.UTF_8);
    }

    /**
     * 转义值中的特殊字符
     * @param value 原始值
     * @return 转义后的值
     */
    private String escapeValue(String value) {
        return value.replace("\"", "\\\"");
    }

    /**
     * 示例用法
     */
    public static void main(String[] args) {
        // 示例1: 简单条件组合
        BiTableFilterBuilder builder1 = new BiTableFilterBuilder();
        String filter1 = builder1
                .contains("order No.", "003")
                .and(() -> new BiTableFilterBuilder().today("Order Date"))
                .buildAndEncode();
        System.out.println("过滤表达式1: " + filter1);

        // 示例2: 复杂条件组合
        BiTableFilterBuilder builder2 = new BiTableFilterBuilder();
        String filter2 = builder2
                .or(
                        () -> new BiTableFilterBuilder().contains("order No.", "004"),
                        () -> new BiTableFilterBuilder().contains("order No.", "009")
                )
                .and(() -> new BiTableFilterBuilder().thisMonth("Order Date"))
                .buildAndEncode();
        System.out.println("过滤表达式2: " + filter2);
    }
}