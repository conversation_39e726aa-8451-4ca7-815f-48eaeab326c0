package com.servauto.mall.service.car.impl;

import com.servauto.mall.dao.car.CarInfoMapper;
import com.servauto.mall.dao.car.CarProductMatchInfoMapper;
import com.servauto.mall.enums.car.CarMatchProductTypeEnum;
import com.servauto.mall.factory.car.CarProductMatchInfoFactory;
import com.servauto.mall.model.dto.request.car.CarProductMatchInfoReqDTO;
import com.servauto.mall.model.dto.response.car.CarProductMatchInfoDTO;
import com.servauto.mall.model.entity.car.CarInfo;
import com.servauto.mall.model.entity.car.CarProductMatchInfo;
import com.servauto.mall.service.car.CarProductMatchService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CarProductMatchServiceImpl implements CarProductMatchService {

    @Resource
    private CarProductMatchInfoMapper carProductMatchInfoMapper;


    @Resource
    private CarInfoMapper carInfoMapper;

    @Override
    public List<CarProductMatchInfoDTO> selectMatchInfo(CarProductMatchInfoReqDTO matchInfoReqDTO) {
        if (matchInfoReqDTO == null || matchInfoReqDTO.getCarId() == null || matchInfoReqDTO.getProductType() == null) {
            return null;
        }

        // 提前保存 productType，避免多次调用 get 方法
        CarMatchProductTypeEnum productType = matchInfoReqDTO.getProductType();

        // 获取车辆信息
        CarInfo carInfo = carInfoMapper.selectByPrimaryKey(matchInfoReqDTO.getCarId());
        if (carInfo == null) {
            return null;
        }
        // 类型映射处理
        switch (productType) {
            case PPF, WRAP_FILM, WINDOW_FILM:
                productType = CarMatchProductTypeEnum.FILM;
                break;
            default:
                break;
        }

        String dbProductType = CarMatchProductTypeEnum.ALL.equals(productType) ? null : productType.getCode();

        List<CarProductMatchInfo> productMatchInfoList = carProductMatchInfoMapper.selectMatchInfo(carInfo.getCarLibId(), dbProductType);
        if (productMatchInfoList == null || productMatchInfoList.isEmpty()) {
            return null;
        }
        if (CarMatchProductTypeEnum.ALL.equals(productType)) {
            List<CarProductMatchInfoDTO> addOnFilmList = new ArrayList<>();
            //拿到并移除productType 等于FILM 的数据 将type改为PPF 并且复制出WRAP_FILM 和 WINDOW_FILM
            List<CarProductMatchInfo> filmMatchInfoList = productMatchInfoList.stream().filter(info -> CarMatchProductTypeEnum.FILM.getCode().equals(info.getProductType())).toList();
            productMatchInfoList.removeIf(info -> CarMatchProductTypeEnum.FILM.getCode().equals(info.getProductType()));
            //修改product type 为PPF并且复制出 WRAP_FILM 和 WINDOW_FILM
            for (CarProductMatchInfo filmMatchInfo : filmMatchInfoList) {
                addOnFilmList.add(CarProductMatchInfoFactory.convert(filmMatchInfo, CarMatchProductTypeEnum.PPF));
                addOnFilmList.add(CarProductMatchInfoFactory.convert(filmMatchInfo, CarMatchProductTypeEnum.WRAP_FILM));
                addOnFilmList.add(CarProductMatchInfoFactory.convert(filmMatchInfo, CarMatchProductTypeEnum.WINDOW_FILM));
            }
            List<CarProductMatchInfoDTO> resultList = new ArrayList<>(productMatchInfoList.stream().map(info -> CarProductMatchInfoFactory.convert(info, CarMatchProductTypeEnum.getByCode(info.getProductType()))).toList());
            resultList.addAll(addOnFilmList);
            return resultList;
        } else {
            // 使用 stream 简化转换过程
            return productMatchInfoList.stream().map(info -> CarProductMatchInfoFactory.convert(info, matchInfoReqDTO.getProductType())).toList();
        }
    }
}