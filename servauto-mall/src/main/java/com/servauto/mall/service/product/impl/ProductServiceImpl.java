package com.servauto.mall.service.product.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.dictionary.model.SysDictData;
import com.servauto.framework.dictionary.service.SysDictionaryService;
import com.servauto.framework.utils.PageSupport;
import com.servauto.mall.dao.packageinfo.PackageInfoMapper;
import com.servauto.mall.dao.packageinfo.PackageProductMapper;
import com.servauto.mall.dao.product.*;
import com.servauto.mall.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.mall.enums.DeliveryMode;
import com.servauto.mall.enums.ProductImageType;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.enums.YesOrNo;
import com.servauto.mall.enums.car.CarMatchProductTypeEnum;
import com.servauto.mall.enums.car.CarMatchTypeEnum;
import com.servauto.mall.factory.product.ProductAttributeFactory;
import com.servauto.mall.factory.product.ProductFactory;
import com.servauto.mall.model.dto.request.car.CarProductMatchInfoReqDTO;
import com.servauto.mall.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.mall.model.dto.request.product.IdValuesDTO;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.dto.response.IdNameDTO;
import com.servauto.mall.model.dto.response.NameValuesDTO;
import com.servauto.mall.model.dto.response.TypeNameDTO;
import com.servauto.mall.model.dto.response.product.*;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceDTO;
import com.servauto.mall.model.entity.packageinfo.PackageProduct;
import com.servauto.mall.model.entity.product.*;
import com.servauto.mall.model.entity.serviceinfo.ServiceInfo;
import com.servauto.mall.service.car.CarInfoService;
import com.servauto.mall.service.car.CarProductMatchService;
import com.servauto.mall.service.product.ProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {

    public static final long CATEGORY_ID_ENGINE_OIL = 1;

    private static final long CATEGORY_ID_FOOT_PAD = 10;

    public static final String CATEGORY_ENGINE_OIL = "Engine Oil";

    public static final String ATTRIBUTE_MODEL = "Model";

    public static final String ATTRIBUTE_OIL_TYPE = "Oil Type";

    public static final String ATTRIBUTE_SAE_GRADE = "SAE Grade";

    private static final String ATTRIBUTE_DISPLAY_TYPE_PRODUCTS_LABEL = "customer/products/label";

    private static final String ATTRIBUTE_DISPLAY_TYPE_PRODUCTS_SKU_ATTR = "customer/products/sku-attr";

    public static final Integer MAX_NET_CONTENT = 8;

    public static final long NET_CONTENT_ATTRIBUTE_ID = 0;

    private static final String PRODUCT_FEATURED_TAGS = "product_tags";

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Resource
    private ProductAttributeMapper productAttributeMapper;

    @Resource
    private ProductAttributeValueMapper productAttributeValueMapper;

    @Resource
    private ProductBrandMapper productBrandMapper;

    @Resource
    private ProductDetailImageMapper productDetailImageMapper;

    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private ProductAttributeOptionMapper productAttributeOptionMapper;

    @Resource
    private ProductAttributeDisplayMapper productAttributeDisplayMapper;

    @Resource
    private PackageInfoMapper packageInfoMapper;

    @Resource
    private PackageProductMapper packageProductMapper;

    @Resource
    private SysDictionaryService sysDictionaryService;

    @Resource
    private CarProductMatchService carProductMatchService;

    @Resource
    private CarInfoService carInfoService;

    public ProductDTO getProduct(long id) {
        if (id == 0) {
            throw new RuntimeException("id is 0");
        }

        // build product base info
        var productInfo = productMapper.selectByPrimaryKey(id);
        if (productInfo == null || productInfo.getDeleted()) {
            throw new RuntimeException("product not found");
        }
        var productDTO = ProductFactory.convert(productInfo);

        // build product details
        setProductDetails(List.of(productDTO));

        // build package detail
        setPackageDetails(productDTO);

        // build product detail images
        setDetailImage(productDTO);

        // build product attribute values
        setAttributeValues(List.of(productDTO));

        // build other info
        productDTO.setFeaturedTags(getFeaturedTags(productInfo));
        productDTO.setDeliveryModes(getDeliveryModes(productInfo));
        return productDTO;
    }

    public List<ProductDTO> getProducts(QueryProductsDTO reqDTO) {
        if (buildFilteredProductIds(reqDTO)) {
            return new ArrayList<>();
        }

        // build product base info
        var products = productMapper.selectByConditions(reqDTO);
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }
        List<ProductDTO> dos = products.stream().map(ProductFactory::convert).collect(Collectors.toList());

        // build product details
        setProductDetails(dos);

        // build product attribute values
        setAttributeValues(dos);

        // build other info
        HashMap<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, product -> product, (existing, replacement) -> existing, HashMap::new));
        for (var dst : dos) {
            var src = productMap.get(dst.getId());
            dst.setFeaturedTags(getFeaturedTags(src));
            dst.setDeliveryModes(getDeliveryModes(src));
        }
        return dos;
    }

    @Override
    public List<ProductDTO> getProducts(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        var products = productMapper.selectByConditions(QueryProductsDTO.builder().ids(productIds).build());
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }
        List<ProductDTO> productList = products.stream().map(ProductFactory::convert).collect(Collectors.toList());
        setProductDetails(productList);
        setAttributeValues(productList);
        return productList;
    }

    public PageInfo<ProductDTO> pageProducts(QueryProductsDTO reqDTO) {
        if (reqDTO.getPageNo() == null || reqDTO.getPageNo() == 0) {
            reqDTO.setPageNo(1);
            reqDTO.setPageSize(20);
        }

        if (buildFilteredProductIds(reqDTO)) {
            PageInfo<ProductDTO> result = new PageInfo<>();
            result.setPageNum(reqDTO.getPageNo());
            result.setPages(reqDTO.getPageSize());
            result.setTotal(0);
            return result;
        }

        if (reqDTO.getPageNo() == null || reqDTO.getPageNo() == 0) {
            reqDTO.setPageNo(1);
            reqDTO.setPageSize(20);
        }
        PageSupport.startPage(reqDTO.getPageNo(), reqDTO.getPageSize());
        var products = productMapper.selectByConditions(reqDTO);
        PageInfo<Product> pageInfo = PageInfo.of(products);

        if (CollectionUtils.isEmpty(products)) {
            PageInfo<ProductDTO> result = new PageInfo<>();
            result.setPageNum(pageInfo.getPageNum());
            result.setPages(pageInfo.getPages());
            result.setTotal(pageInfo.getTotal());
            return result;
        }

        // build product base info
        List<ProductDTO> dos = products.stream().map(ProductFactory::convert).toList();

        // build product details
        setProductDetails(dos);

        // build product attribute values
        setAttributeValues(dos);

        // build other info
        HashMap<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, product -> product, (existing, replacement) -> existing, HashMap::new));
        for (var dst : dos) {
            var src = productMap.get(dst.getId());
            dst.setFeaturedTags(getFeaturedTags(src));
            dst.setDeliveryModes(getDeliveryModes(src));
        }

        // build page info
        PageInfo<ProductDTO> result = PageInfo.of(dos);
        result.setPageNum(pageInfo.getPageNum());
        result.setPages(pageInfo.getPages());
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    /**
     * 根据查询条件构建过滤后的商品 ID 列表
     * 此方法主要用于根据用户选择的汽车型号、商品类别、属性值等条件来过滤商品
     * 它通过计算不同条件下商品ID的交集和并集来实现精确的商品筛选
     *
     * @param reqDTO 查询商品的条件封装对象，包含商品类别ID、汽车ID、属性值列表等信息
     * @return 返回一个布尔值，表示是否成功构建了过滤后的商品ID列表
     *         如果返回true，表示没有找到符合条件的商品；如果返回false，表示找到了符合条件的商品
     */
    private boolean buildFilteredProductIds(QueryProductsDTO reqDTO) {
        // 检查是否在未指定类别ID的情况下尝试通过属性值过滤商品，如果如此，则抛出异常
        // 目前杜绝这种情况，后续改的话就反查 attribute 填 category id
        if (Objects.equals(reqDTO.getCategoryId(), 0L) && CollectionUtils.isNotEmpty(reqDTO.getAttributeValues())) {
            throw new RuntimeException("can not filter by attribute values when categoryId is 0");
        } else if (CollectionUtils.isNotEmpty(reqDTO.getAttributeValues())) {
            // 为每个属性值设置类别ID，以便后续处理
            for (var attrValue : reqDTO.getAttributeValues()) {
                attrValue.setCategoryId(reqDTO.getCategoryId());
            }
        }

        // 打印调试日志，输出过滤后的属性值列表
        log.debug("buildFilteredProductIds origin reqDTO.attributeValues: {}, reqDTO.footPadNames: {}, reqDTO.ids: {}", reqDTO.getAttributeValues(), reqDTO.getFootPadNames(), reqDTO.getIds());

        // 根据商品类别ID获取对应的汽车匹配类型枚举
        var pt = CarMatchProductTypeEnum.getCarMatchProductTypeEnumByProductCategoryId(reqDTO.getCategoryId());
        // 如果指定了汽车ID且当前商品类别支持汽车匹配，则进行汽车匹配处理
        if (reqDTO.getCarId() != null && CarMatchProductTypeEnum.canMatch(pt)) {
            // 创建请求DTO并设置汽车ID和产品类型，以便查询匹配信息
            var carProductMatchInfoReqDTO = new CarProductMatchInfoReqDTO();
            carProductMatchInfoReqDTO.setCarId(reqDTO.getCarId());
            carProductMatchInfoReqDTO.setProductType(pt);
            // 查询汽车匹配信息，并根据结果进行处理
            var carProductMatchInfoDTO = carProductMatchService.selectMatchInfo(carProductMatchInfoReqDTO);
            if (CollectionUtils.isNotEmpty(carProductMatchInfoDTO)) {
                // 初始化合并后的属性值列表
                List<IdValuesDTO> mergedList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(reqDTO.getAttributeValues())) {
                    mergedList.addAll(reqDTO.getAttributeValues());
                }
                // 初始化属性值映射和属性ID列表
                Map<Long, List<String>> attrMap = new HashMap<>();
                List<Long> attrIds = new ArrayList<>();
                // 遍历匹配信息，构建属性值映射和属性ID列表
                for (var info : carProductMatchInfoDTO) {
                    var attrId = CarMatchTypeEnum.getProductAttributeIdByCode(info.getProductType(), info.getMatchType());
                    if (attrId == 0) {
                        log.warn("carProductMatchInfoDTO info is null, productType: {}, matchType: {}", info.getProductType(), info.getMatchType());
                        continue;
                    }
                    if (info.getMatchContent() == null || info.getMatchContent().isEmpty()) {
                        log.warn("MatchContent info is empty, productType: {}, matchType: {}, matchContent: {}", info.getProductType(), info.getMatchType(), info.getMatchContent());
                        continue;
                    }
                    attrIds.add(attrId);
                    var labels = attrMap.getOrDefault(attrId, new ArrayList<>());
                    labels.add(info.getMatchContent());
                    attrMap.put(attrId, labels);
                }
                // 根据属性ID查询属性选项，并构建选项映射
                Map<Long, Map<String, String>> optionMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(attrIds)) {
                    var options = productAttributeOptionMapper.selectByAttributeIds(attrIds);
                    if (CollectionUtils.isNotEmpty(options)) {
                        for (var option : options) {
                            var map = optionMap.getOrDefault(option.getAttributeId(), new HashMap<>());
                            map.put(option.getLabel(), option.getValue());
                            optionMap.put(option.getAttributeId(), map);
                        }
                    }
                }
                // 遍历属性值映射，构建合并后的属性值列表
                for (var attrId : attrMap.keySet()) {
                    if (attrId > 0) {
                        var attribute = productAttributeMapper.selectByPrimaryKey(attrId);
                        if (attribute == null) {
                            throw BusinessException.of("attribute id is invalid");
                        }
                        List<String> values = new ArrayList<>();
                        if (optionMap.containsKey(attrId)) {
                            var optionMap2 = optionMap.get(attrId);
                            for (var label : attrMap.get(attrId)) {
                                if (!optionMap2.containsKey(label)) {
                                    log.warn("optionMap2 cannot find label: {}, attribute id: {}", label, attrId);
                                } else {
                                    values.add(optionMap2.get(label));
                                }
                            }
                        } else {
                            values = attrMap.get(attrId);
                        }
                        mergedList.add(IdValuesDTO.builder().
                                categoryId(attribute.getCategoryId()).
                                id(attrId).values(values).build());
                    } else {
                        reqDTO.setFootPadNames(attrMap.get(attrId));
                    }
                }
                reqDTO.setAttributeValues(mergedList);
            }
        }

        // 打印调试日志，输出过滤后的属性值列表
        log.debug("buildFilteredProductIds reqDTO.attributeValues: {}, reqDTO.footPadNames: {}", reqDTO.getAttributeValues(), reqDTO.getFootPadNames());

        // 判断是否需要筛选所有商品
        boolean isAll = CollectionUtils.isEmpty(reqDTO.getIds());
        // 根据类别ID对商品ID进行分组
        Map<Long, List<Long>> idMap = groupByCategoryIds(reqDTO.getIds(), reqDTO.getCategoryId());
        // 初始化忽略的类别ID集合
        HashSet<Long> ignoreCategoryIds = new HashSet<>();
        // 初始化所有商品ID列表
        List<Long> allIds = new ArrayList<>();
        // 如果存在属性值过滤条件，则对同类别的商品ID求交集
        if (CollectionUtils.isNotEmpty(reqDTO.getAttributeValues())) {
            for (var attrValue : reqDTO.getAttributeValues()) {
                if (ignoreCategoryIds.contains(attrValue.getCategoryId())) {
                    continue;
                }

                List<Long> originIds = idMap.getOrDefault(attrValue.getCategoryId(), new ArrayList<>());
                if (isAll && CollectionUtils.isEmpty(originIds)) {  // 第一次筛选
                    originIds.add(0L);  // 0 表示所有商品
                }
                if (!isAll && CollectionUtils.isEmpty(originIds)) {
                    continue;
                }

                var ids = productAttributeValueMapper.selectProductIdsByAttributeIdAndValues(attrValue.getId(), attrValue.getValues());
                if (CollectionUtils.isNotEmpty(ids)) {
                    originIds = retain(originIds, ids);
                    if (originIds.isEmpty()) {
                        ignoreCategoryIds.add(attrValue.getCategoryId());   // 忽略该类别的商品
                    }
                    idMap.put(attrValue.getCategoryId(), originIds);
                } else {
                    ignoreCategoryIds.add(attrValue.getCategoryId());   // 忽略该类别的商品
                }
            }
        }
        // 如果存在脚垫名称过滤条件，则对脚垫商品ID求交集
        if (CollectionUtils.isNotEmpty(reqDTO.getFootPadNames()) && !ignoreCategoryIds.contains(CATEGORY_ID_FOOT_PAD)) {
            List<Long> originIds = idMap.getOrDefault(CATEGORY_ID_FOOT_PAD, new ArrayList<>());
            if (isAll && CollectionUtils.isEmpty(originIds)) {  // 第一次筛选
                originIds.add(0L);  // 0 表示所有商品
            }
            if (!(!isAll && CollectionUtils.isEmpty(originIds))) {
                var ids = productMapper.selectByConditions(QueryProductsDTO.builder().
                        categoryId(CATEGORY_ID_FOOT_PAD).
                        footPadNames(reqDTO.getFootPadNames()).
                        status(SaleStatus.ACTIVE.getCode()).
                        build()).stream().map(Product::getId).toList();
                if (CollectionUtils.isNotEmpty(ids)) {
                    originIds = retain(originIds, ids);
                    if (originIds.isEmpty()) {
                        ignoreCategoryIds.add(CATEGORY_ID_FOOT_PAD);   // 忽略该类别的商品
                    }
                    idMap.put(CATEGORY_ID_FOOT_PAD, originIds);
                } else {
                    ignoreCategoryIds.add(CATEGORY_ID_FOOT_PAD);   // 忽略该类别的商品
                }
            }
        }

        // 对不同类别的商品ID求并集
        for (var categoryId : idMap.keySet()) {
            if (ignoreCategoryIds.contains(categoryId)) {
                continue;
            }
            allIds.addAll(idMap.get(categoryId));
        }
        reqDTO.setIds(allIds);

        // 匹配不到商品
        if ((!isAll || CollectionUtils.isNotEmpty(reqDTO.getAttributeValues()) || CollectionUtils.isNotEmpty(reqDTO.getFootPadNames())) && allIds.isEmpty()) {
            return true;
        }
        reqDTO.setFootPadNames(null);

        // 打印调试日志，输出过滤后的商品ID列表
        log.debug("buildFilteredProductIds reqDTO.ids: {}", reqDTO.getIds());

        // 如果存在套餐ID过滤条件，则根据套餐产品进行过滤
        if (reqDTO.getPackageId() != null && reqDTO.getPackageId() > 0) {
            var packageProducts = packageProductMapper.selectByPackageId(reqDTO.getPackageId());
            if (CollectionUtils.isEmpty(packageProducts)) {
                throw new RuntimeException("package product not found");
            }
            var ids = packageProducts.stream().map(PackageProduct::getProductId).toList();
            if (CollectionUtils.isEmpty(reqDTO.getIds())) {
                reqDTO.setIds(ids);
            } else {
                reqDTO.getIds().retainAll(ids);
                if (reqDTO.getIds().isEmpty()) {
                    return true;
                }
            }

            log.debug("buildFilteredProductIds reqDTO.ids: {}", reqDTO.getIds());
        }

        return false;
    }


    public List<Long> retain(List<Long> ids1, List<Long> ids2) {
        if (CollectionUtils.isNotEmpty(ids1) && ids1.get(0).equals(0L)) {
            return ids2;
        }
        ids1.retainAll(ids2);
        return ids1;
    }

    public Map<Long, List<Long>> groupByCategoryIds(List<Long> productIds, Long categoryId) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new HashMap<>();
        }

        var products = productMapper.selectByConditions(QueryProductsDTO.builder().ids(productIds).build());
        if (CollectionUtils.isEmpty(products)) {
            log.info("No products found for the given productIds");
            return new HashMap<>();
        }

        Map<Long, List<Long>> productMap = new HashMap<>();
        for (var product : products) {
            if (categoryId != null && categoryId > 0 && !product.getCategoryId().equals(categoryId)) {
                continue;
            }
            productMap.computeIfAbsent(product.getCategoryId(), k -> new ArrayList<>()).add(product.getId());
        }
        return productMap;
    }

    public List<IdNameDTO> getProductCategories() {
        List<ProductCategory> productCategories = productCategoryMapper.selectAll();
        List<IdNameDTO> idNameDTOList = new ArrayList<>();
        if (productCategories != null) {
            idNameDTOList = productCategories.stream().map(e -> IdNameDTO.builder().id(e.getId()).name(e.getName()).build()).toList();
        }
        return idNameDTOList;
    }

    public List<ProductAttributeDTO> getProductAttributes(String displayType, long categoryId) {
        List<ProductAttribute> attributes = productAttributeMapper.selectByCategoryIds(List.of(categoryId));
        List<ProductAttributeDTO> attributeDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(attributes)) {
            return attributeDTOS;
        }
        if (displayType == null || displayType.isEmpty()) {
            var totalAttributes = attributes.stream().map(ProductAttributeFactory::convert).toList();
            setAttributeOptions(totalAttributes);
            return totalAttributes;
        }

        HashMap<Long, ProductAttribute> attrHashMap = attributes.stream().collect(Collectors.toMap(ProductAttribute::getId, attribute -> attribute, (existing, replacement) -> existing, HashMap::new));
        var attributeDisplays = productAttributeDisplayMapper.selectByTypeAndCategoryIds(displayType, List.of(categoryId));
        if (CollectionUtils.isEmpty(attributeDisplays)) {
            return attributeDTOS;
        }
        var attributeDisplay = attributeDisplays.get(0);

        List<Long> attributeIds = Arrays.stream(attributeDisplay.getAttributeIds().split(",")).map(Long::parseLong).toList();
        for (var id : attributeIds) {
            ProductAttribute attr = attrHashMap.get(id);
            if (attr == null) {
                throw BusinessException.of("product attribute not found: ", id.toString());
            }
            attributeDTOS.add(ProductAttributeFactory.convert(attr));
        }
        setAttributeOptions(attributeDTOS);
        return attributeDTOS;
    }

    public List<IdNameDTO> getProductBrands() {
        List<ProductBrand> productBrands = productBrandMapper.selectAll();
        List<IdNameDTO> idNameDTOList = new ArrayList<>();
        if (productBrands != null) {
            idNameDTOList = productBrands.stream().map(e -> IdNameDTO.builder().id(e.getId()).name(e.getName()).build()).toList();
        }
        return idNameDTOList;
    }

    public ProductSpecifications getRecommendProductsByProductId(Long carId, long productId) {
        var product = getProduct(productId);
        if (!product.getCategoryName().equals(CATEGORY_ENGINE_OIL)) {
            throw BusinessException.of("product is not engine oil");
        }

        List<String> attrNames = Arrays.asList(ATTRIBUTE_MODEL, ATTRIBUTE_OIL_TYPE, ATTRIBUTE_SAE_GRADE);
        List<ProductAttribute> attributes = productAttributeMapper.selectByNames(attrNames);
        List<IdValuesDTO> attributeValues = new ArrayList<>();
        for (var attr : attributes) {
            for (var v : product.getAttributeValues()) {
                if (v.getId().equals(attr.getId())) {
                    attributeValues.add(IdValuesDTO.builder().id(attr.getId()).values(v.getValues()).build());
                    break;
                }
            }
        }
        QueryProductsDTO filter = QueryProductsDTO.builder().
                categoryId(product.getCategoryId()).
                status(SaleStatus.ACTIVE.getCode()).
                brandIds(List.of(product.getBrandId())).
                attributeValues(attributeValues).
                build();
        return this.getRecommendProductsByProducts(carId, getProducts(filter));
    }

    // dp[i]: 容量在 i 的时候的最小价格, 0 < i <= MAX_NET_CONTENT
    // dp[i] = min(dp[i], dp[i - list[j].netContent*k] + list[j].price*k)
    public ProductSpecifications getRecommendProductsByProducts(Long carId, List<ProductDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw BusinessException.of("product list is empty");
        }
        list.sort(Comparator.comparingInt(ProductDTO::getNetContent).reversed());

        var oilCapacity = carInfoService.getCarOilCapacity(carId);
        double ceilingValue = Math.ceil(oilCapacity);
        int carNetContent = (int) ceilingValue;
        if (carNetContent > MAX_NET_CONTENT) {
            throw BusinessException.of("car net content is too large");
        }
        ProductSpecifications ans = null;
        ProductSpecifications[] dp = new ProductSpecifications[MAX_NET_CONTENT + 1];
        dp[0] = ProductSpecifications.builder().price(BigDecimal.ZERO).list(new ArrayList<>()).build();
        for (int i = 1; i <= MAX_NET_CONTENT; i++) {
            for (var curProduct : list) {
                for (int k = 1; i >= curProduct.getNetContent() * k; k++) {
                    var nc = curProduct.getNetContent() * k;
                    var last = dp[i - nc];
                    if (last == null) { // 无法构建 last
                        continue;
                    }
                    // 计算当前策略的价格
                    var price = curProduct.getPrice().multiply(new BigDecimal(k)).add(last.getPrice());
                    // 是否更新了 dp[i] 的价格和容量
                    boolean isChange = false;
                    if (dp[i] == null || dp[i].getPrice().compareTo(price) > 0) {
                        dp[i] = ProductSpecifications.builder().price(price).netContent(i).contentUnit(curProduct.getContentUnit()).list(new ArrayList<>()).build();
                        isChange = true;
                    }
                    if (isChange) {
                        boolean isJoin = false;
                        // 记录 last 的 products
                        for (var spec : last.getList()) {
                            var spec2 = ProductSpecification.builder().
                                    id(spec.getId()).
                                    count(spec.getCount()).
                                    netContent(spec.getNetContent()).
                                    contentUnit(spec.getContentUnit()).
                                    price(spec.getPrice()).
                                    build();
                            // 合并 products
                            if (spec2.getId().equals(curProduct.getId())) {
                                spec2.setCount(spec2.getCount() + k);
                                isJoin = true;
                            }
                            dp[i].getList().add(spec2);
                        }
                        // 记录新的 products
                        if (!isJoin) {
                            dp[i].getList().add(
                                    ProductSpecification.builder().
                                            id(curProduct.getId()).
                                            count(k).
                                            netContent(curProduct.getNetContent()).
                                            contentUnit(curProduct.getContentUnit()).
                                            price(curProduct.getPrice()).
                                            build()
                            );
                        }
                    }
                }
            }
            if (i >= carNetContent && dp[i] != null) {
                ans = ans == null ? dp[i] : ans.getPrice().compareTo(dp[i].getPrice()) > 0 ? dp[i] : ans;
            }
        }

        // 填充数量是 0 的机油
        for (var product : list) {
            boolean exist = false;
            assert ans != null;
            for (var ps : ans.getList()) {
                if (ps.getId().equals(product.getId())) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                ans.getList().add(
                        ProductSpecification.builder().
                                id(product.getId()).
                                count(0).
                                netContent(product.getNetContent()).
                                contentUnit(product.getContentUnit()).
                                price(product.getPrice()).
                                build()
                );
            }
        }
        assert ans != null;
        ans.setCarNetContent(carNetContent);
        ans.setPrice(ans.getPrice());
        return ans;
    }

    private void setProductDetails(List<ProductDTO> dos) {

        List<Long> categoryIds = dos.stream().map(ProductDTO::getCategoryId).distinct().toList();
        HashMap<Long, ProductCategory> categoryMap = productCategoryMapper.selectByIds(categoryIds).stream().collect(Collectors.toMap(ProductCategory::getId, category -> category, (existing, replacement) -> existing, HashMap::new));

        List<Long> brandIds = dos.stream().map(ProductDTO::getBrandId).distinct().toList();
        HashMap<Long, ProductBrand> brandMap = productBrandMapper.selectByIds(brandIds).stream().collect(Collectors.toMap(ProductBrand::getId, brand -> brand, (existing, replacement) -> existing, HashMap::new));

        List<Long> serviceIds = dos.stream()
                .map(ProductDTO::getServiceId)
                .filter(Objects::nonNull)
                .distinct()
                .filter(serviceId -> serviceId > 0)
                .toList();
        HashMap<Long, ServiceInfo> serviceInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            serviceInfoMap = serviceInfoMapper.selectByIds(serviceIds).stream().collect(Collectors.toMap(ServiceInfo::getId, serviceInfo -> serviceInfo, (existing, replacement) -> existing, HashMap::new));
        }

        for (var v : dos) {
            // build product category name
            v.setCategoryName(categoryMap.get(v.getCategoryId()).getName());
            // build product brand name
            v.setBrandName(brandMap.get(v.getBrandId()).getName());
            // build service detail
            if (v.getServiceId() != null && v.getServiceId() > 0) {
                var serviceDTO = new ServiceDTO();
                serviceDTO.setId(v.getServiceId());
                serviceDTO.setName(serviceInfoMap.get(v.getServiceId()).getName());
                serviceDTO.setFee(serviceInfoMap.get(v.getServiceId()).getFee());
                serviceDTO.setIsRequired(YesOrNo.yes(serviceInfoMap.get(v.getServiceId()).getIsRequired()));
                v.setServiceDTO(serviceDTO);
            }
        }
    }

    private void setPackageDetails(ProductDTO productDTO) {
        var packageProducts = packageProductMapper.selectByProductId(productDTO.getId());
        if (CollectionUtils.isNotEmpty(packageProducts)) {
            var packages = packageInfoMapper.selectByConditions(QueryPackagesDTO.builder().
                    ids(packageProducts.stream().map(PackageProduct::getPackageId).toList()).
                    status(SaleStatus.ACTIVE.getCode()).
                    build());
            if (CollectionUtils.isEmpty(packages)) {
                return;
            }
            List<PackageDTO> PackageDTOs = new ArrayList<>();
            for (var pkg : packages) {
                PackageDTO packageDTO = new PackageDTO();
                packageDTO.setId(pkg.getId());
                packageDTO.setName(pkg.getName());
                packageDTO.setMainImage(productDTO.getMainImage());

                var totalPrice = productDTO.getPrice();

                // build service name
                if (pkg.getServiceId() > 0) {
                    var serviceDTO = serviceInfoMapper.selectByPrimaryKey(pkg.getServiceId());
                    packageDTO.setServiceName(serviceDTO.getName());
                    totalPrice = totalPrice.add(serviceDTO.getFee());
                }

                // query products
                packageProducts = packageProductMapper.selectByPackageId(pkg.getId());
                var products = productMapper.selectByConditions(QueryProductsDTO.builder().
                        ids(packageProducts.stream().map(PackageProduct::getProductId).toList()).
                        status(SaleStatus.ACTIVE.getCode()).
                        build());
                if (CollectionUtils.isEmpty(products)) {
                    log.warn("Package product not found, package id: {}", pkg.getId());
                    return;
                }

                // group by category
                Map<Long, List<Product>> productMap = products.stream().collect(Collectors.groupingBy(Product::getCategoryId));

                // build price and category ids
                List<Long> categoryIds = new ArrayList<>(List.of(productDTO.getCategoryId()));
                packageDTO.setCategoryNames(new ArrayList<>());
                for (var category : productMap.entrySet()) {
                    Long categoryId = category.getKey();
                    List<Product> list = category.getValue().stream().sorted(Comparator.comparing(Product::getPrice)).toList();
                    if (!productDTO.getCategoryId().equals(categoryId)) {
                        totalPrice = totalPrice.add(list.get(0).getPrice());
                        categoryIds.add(categoryId);
                    }
                }
                packageDTO.setPrice(totalPrice.toString());

                // build product category names
                List<ProductCategory> productCategories = productCategoryMapper.selectByIds(categoryIds);
                for (var category : productCategories) {
                    packageDTO.getCategoryNames().add(category.getName());
                }
                PackageDTOs.add(packageDTO);
            }
            productDTO.setPackages(PackageDTOs.stream().sorted(Comparator.comparing(PackageDTO::getPrice)).toList());
        }
    }

    private void setDetailImage(ProductDTO productDTO) {
        var detailImages = productDetailImageMapper.selectByProductId(productDTO.getId());
        if (CollectionUtils.isNotEmpty(detailImages)) {
            productDTO.setCoverImages(new ArrayList<>());
            productDTO.setDetailImages(new ArrayList<>());
            for (var image : detailImages) {
                switch (ProductImageType.getByCode(image.getType())) {
                    case COVER_IMAGE:
                        productDTO.getCoverImages().add(image.getImage());
                        break;
                    case H5_DETAIL_IMAGE:
                        productDTO.getDetailImages().add(image.getImage());
                        break;
                    default:
                        log.warn("unknown product image type: {}", image.getType());
                        break;
                }
            }
        }
    }

    private void setAttributeValues(List<ProductDTO> dos) {
        if (CollectionUtils.isEmpty(dos)) {
            return;
        }
        // Collect all product IDs
        List<Long> productIds = dos.stream()
                .map(ProductDTO::getId)
                .distinct()
                .toList();

        // Batch query product attributes
        List<Long> categoryIds = dos.stream()
                .map(ProductDTO::getCategoryId)
                .distinct()
                .toList();
        List<ProductAttribute> attributes = productAttributeMapper.selectByCategoryIds(categoryIds);

        if (CollectionUtils.isEmpty(attributes)) {
            return;
        }

        // Batch query product attribute values
        List<ProductAttributeValue> attributeValues = productAttributeValueMapper.selectByProductIds(productIds);

        // Batch query product attribute options
        List<Long> attributeIds = attributes.stream()
                .map(ProductAttribute::getId)
                .distinct()
                .toList();
        List<ProductAttributeOption> attributeOptions = productAttributeOptionMapper.selectByAttributeIds(attributeIds);

        // Build attribute map
        HashMap<Long, ProductAttribute> attributeMap = attributes.stream()
                .collect(Collectors.toMap(ProductAttribute::getId, attr -> attr, (existing, replacement) -> existing, HashMap::new));

        // Build attribute value map
        HashMap<Long, List<ProductAttributeValue>> attributeValueMap = new HashMap<>();
        for (ProductAttributeValue value : attributeValues) {
            attributeValueMap.computeIfAbsent(value.getProductId(), k -> new ArrayList<>()).add(value);
        }

        // Build attribute option map
        HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap = new HashMap<>();
        for (ProductAttributeOption option : attributeOptions) {
            attributeOptionMap.computeIfAbsent(option.getAttributeId(), k -> new HashMap<>()).put(option.getValue(), option);
        }

        Map<Long, ProductAttributeDisplay> attrDisplayLabelMap = productAttributeDisplayMapper
                .selectByTypeAndCategoryIds(ATTRIBUTE_DISPLAY_TYPE_PRODUCTS_LABEL, categoryIds)
                .stream()
                .collect(Collectors.toMap(ProductAttributeDisplay::getCategoryId, display -> display));

        Map<Long, ProductAttributeDisplay> attrSkuAttrMap = productAttributeDisplayMapper
                .selectByTypeAndCategoryIds(ATTRIBUTE_DISPLAY_TYPE_PRODUCTS_SKU_ATTR, categoryIds)
                .stream()
                .collect(Collectors.toMap(ProductAttributeDisplay::getCategoryId, display -> display));

        // Set attribute values to product DTOs
        for (ProductDTO productDTO : dos) {
            var attrDisplayLabel = attrDisplayLabelMap.get(productDTO.getCategoryId());
            var attrSkuAttr = attrSkuAttrMap.get(productDTO.getCategoryId());
            setAttributeValue(productDTO, attributeMap, attributeValueMap, attributeOptionMap, attrDisplayLabel, attrSkuAttr);
        }
    }

    private void setAttributeValue(
            ProductDTO product,
            HashMap<Long, ProductAttribute> attributeMap,
            HashMap<Long, List<ProductAttributeValue>> attributeValueMap,
            HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap,
            ProductAttributeDisplay attrDisplayLabel, ProductAttributeDisplay attrSkuAttr) {
        var productId = product.getId();
        List<ProductAttributeValueDTO> valueDTOs = new ArrayList<>();
        List<ProductAttributeValue> attrValues = attributeValueMap.getOrDefault(productId, new ArrayList<>());
        HashMap<Long, List<ProductAttributeValue>> attrIdToValues = new HashMap<>();
        for (ProductAttributeValue v : attrValues) {
            attrIdToValues.computeIfAbsent(v.getAttributeId(), k -> new ArrayList<>()).add(v);
        }

        for (var list : attrIdToValues.values()) {
            ProductAttribute attr = attributeMap.get(list.get(0).getAttributeId());
            if (attr == null) {
                continue;
            }
            HashMap<String, ProductAttributeOption> optionSubMap = attributeOptionMap.get(attr.getId());
            List<String> labels = new ArrayList<>();
            List<String> values = new ArrayList<>();
            for (ProductAttributeValue value : list) {
                String label = value.getValue();
                if (!attr.getSuffix().isEmpty()) {
                    label += attr.getSuffix();
                }
                if (optionSubMap != null && optionSubMap.get(value.getValue()) != null) {
                    label = optionSubMap.get(value.getValue()).getLabel();
                }
                labels.add(label);
                values.add(value.getValue());
            }

            valueDTOs.add(ProductAttributeValueDTO.builder()
                    .id(attr.getId())
                    .type(attr.getType())
                    .name(attr.getName())
                    .suffix(attr.getSuffix())
                    .values(values)
                    .labels(labels)
                    .order(attr.getOrder())
                    .build());
        }
        valueDTOs.sort((o1, o2) -> o1.getOrder() - o2.getOrder());
        product.setAttributeValues(valueDTOs);

        if (attrDisplayLabel != null) {
            var nameValue = getAttributeLabels(product, attrDisplayLabel, attributeMap, attrIdToValues, attributeOptionMap, false).get(0);
            product.setAttributeLabel(nameValue.getValues());
        }

        if (attrSkuAttr != null) {
            product.setSkuAttributeValues(getAttributeLabels(product, attrSkuAttr, attributeMap, attrIdToValues, attributeOptionMap, true));
        }
    }

    private List<NameValuesDTO> getAttributeLabels(ProductDTO product, ProductAttributeDisplay attrDisplay,
                                                   HashMap<Long, ProductAttribute> attributeMap,
                                                   HashMap<Long, List<ProductAttributeValue>> attrIdToValues,
                                                   HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap,
                                                   boolean isSkuAttr) {
        List<NameValuesDTO> displayLabels = new ArrayList<>();
        if (attrDisplay != null && !attrDisplay.getAttributeIds().isEmpty()) {
            List<Long> attrDisplayId = Arrays.stream(attrDisplay.getAttributeIds().split(",")).map(Long::parseLong).toList();
            for (var attrId : attrDisplayId) {
                if (attrId == NET_CONTENT_ATTRIBUTE_ID) {
                    displayLabels.add(NameValuesDTO.builder().name("Net Content").
                            values(List.of(String.format("%d %s", product.getNetContent(), product.getContentUnit()))).build());
                    continue;
                }
                ProductAttribute attr = attributeMap.get(attrId);
                if (attr == null) {
                    continue;
                }

                var list = attrIdToValues.get(attrId);
                if (list == null) {
                    continue;
                }
                HashMap<String, ProductAttributeOption> optionSubMap = attributeOptionMap.get(attrId);
                NameValuesDTO nameValues = NameValuesDTO.builder().name(attr.getName()).values(new ArrayList<>()).build();
                if (isSkuAttr && attr.getType().equals("input") && list.get(0).getValue().contains(",")) {
                    String label = list.get(0).getValue();
                    String[] ss = label.split(",");
                    for (String s : ss) {
                        nameValues.getValues().add(s.trim());
                    }
                } else {
                    for (ProductAttributeValue value : list) {
                        String label = value.getValue();
                        if (!attr.getSuffix().isEmpty()) {
                            label += attr.getSuffix();
                        }
                        if (optionSubMap != null && optionSubMap.get(value.getValue()) != null) {
                            label = optionSubMap.get(value.getValue()).getLabel();
                        }
                        nameValues.getValues().add(label);
                    }
                }
                displayLabels.add(nameValues);
            }
        }
        return displayLabels;
    }

    private List<String> getFeaturedTags(Product src) {
        List<String> res = new ArrayList<>();
        if (StringUtils.isEmpty(src.getFeaturedTags())) {
            return res;
        }
        Map<String, String> tagMap = sysDictionaryService.selectDictDataByType(PRODUCT_FEATURED_TAGS).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        List<String> tags = Arrays.stream(src.getFeaturedTags().split(",")).toList();
        for (var tag : tags) {
            var label = tag;
            if (tagMap.containsKey(tag)) {
                label = tagMap.get(tag);
            }
            res.add(label);
        }
        return res;
    }

    private List<TypeNameDTO> getDeliveryModes(Product src) {
        List<TypeNameDTO> list = new ArrayList<>();
        for (int i = 0; (1 << i) < DeliveryMode.MAX_ENUM.getCode(); i++) {
            int j = 1 << i;
            if ((src.getDeliveryModes() & j) > 0) {
                list.add(TypeNameDTO.builder().type(j).name(DeliveryMode.getByCode(j).getDesc()).build());
            }
        }
        return list;
    }

    private void setAttributeOptions(List<ProductAttributeDTO> attributes) {
        List<ProductAttributeOption> attributeOptions = productAttributeOptionMapper.selectByAttributeIds(attributes.stream().map(ProductAttributeDTO::getId).distinct().toList());

        HashMap<Long, List<ProductAttributeOption>> attributeOptionMap = new HashMap<>();
        for (ProductAttributeOption option : attributeOptions) {
            attributeOptionMap.computeIfAbsent(option.getAttributeId(), k -> new ArrayList<>()).add(option);
        }

        for (ProductAttributeDTO attr : attributes) {
            List<ProductAttributeOptionDTO> options = attributeOptionMap.getOrDefault(attr.getId(), new ArrayList<>())
                    .stream()
                    .map(option -> ProductAttributeOptionDTO.builder()
                            .value(option.getValue())
                            .label(option.getLabel())
                            .image(option.getImage())
                            .order(option.getSortOrder())
                            .build())
                    .sorted((o1, o2) -> Integer.compare(o1.getOrder(), o2.getOrder())) // Sort by order
                    .collect(Collectors.toList());

            attr.setOptions(options);
        }
    }

    public void resetValueByLabel(List<IdValuesDTO> idValuesDTOS) {
        var attributeOptions = productAttributeOptionMapper.selectByAttributeIds(idValuesDTOS.stream().map(IdValuesDTO::getId).toList());
        Map<Long, Map<String, String>> optionMap = attributeOptions.stream().collect(Collectors.groupingBy(ProductAttributeOption::getAttributeId, Collectors.toMap(ProductAttributeOption::getValue, ProductAttributeOption::getLabel)));
        for (IdValuesDTO idValuesDTO : idValuesDTOS) {
            List<String> values = new ArrayList<>();
            for (String value : idValuesDTO.getValues()) {
                var label = optionMap.getOrDefault(idValuesDTO.getId(), new HashMap<>()).get(value);
                if (label != null) {
                    values.add(label);
                } else {
                    values.add(value);
                }
            }
            idValuesDTO.setValues(values);
        }
    }
}
