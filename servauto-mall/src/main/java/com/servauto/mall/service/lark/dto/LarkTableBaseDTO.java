package com.servauto.mall.service.lark.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class LarkTableBaseDTO implements Serializable {

    /**
     *
     * 多维表格的唯一标识符
     */
    private String appToken;

    /**
     * 多维表格数据表的唯一标识符
     */
    private String tableId;

    /**
     * 用户 ID 类型
     * open_id：标识一个用户在某个应用中的身份。同一个用户在不同应用中的 Open ID 不同。了解更多：如何获取 Open ID
     * union_id：标识一个用户在某个应用开发商下的身份。同一用户在同一开发商下的应用中的 Union ID 是相同的，在不同开发商下的应用中的 Union ID 是不同的。通过 Union ID，应用开发商可以把同个用户在多个应用中的身份关联起来。了解更多：如何获取 Union ID？
     * user_id：标识一个用户在某个租户内的身份。同一个用户在租户 A 和租户 B 内的 User ID 是不同的。在同一个租户内，一个用户的 User ID 在所有应用（包括商店应用）中都保持一致。User ID 主要用于在不同的应用间打通用户数据。了解更多：如何获取 User ID？
     */
    private String userIdType = "user_id";

    /**
     * 是否忽略一致性读写检查，默认为 false，即在进行读写操作时，系统将确保读取到的数据和写入的数据是一致的。可选值：
     *
     * true：忽略读写一致性检查，提高性能，但可能会导致某些节点的数据不同步，出现暂时不一致
     * false：开启读写一致性检查，确保数据在读写过程中一致
     */
    private Boolean ignoreConsistencyCheck = true;

}
