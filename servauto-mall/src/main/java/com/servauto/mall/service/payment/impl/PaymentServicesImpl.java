package com.servauto.mall.service.payment.impl;

import com.servauto.mall.dao.payment.PaymentLogMapper;
import com.servauto.mall.dao.payment.PaymentMapper;
import com.servauto.mall.factory.payment.PaymentFactory;
import com.servauto.mall.model.dto.request.payment.QueryPaymentDTO;
import com.servauto.mall.model.dto.request.payment.UpdatePaymentReqDTO;
import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.entity.payment.Payment;
import com.servauto.mall.model.entity.payment.PaymentLog;
import com.servauto.mall.service.payment.PaymentServices;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

@Slf4j
@Service
public class PaymentServicesImpl implements PaymentServices {
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentLogMapper paymentLogMapper;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public PaymentDTO queryPayment(String sourceId) {
        Payment payment = paymentMapper.selectBySourceId(sourceId);

        return PaymentFactory.convert(payment);
    }

    @Override
    public PaymentDTO queryPaymentByPayNo(String payNo) {
        Payment payment = paymentMapper.selectByPayNo(payNo);

        return PaymentFactory.convert(payment);
    }

    @Override
    public List<PaymentDTO> getPaymentByStatus(String status,Integer channel) {
        List<Payment> payments = paymentMapper.getPaymentByStatus(status,channel);
        return payments.stream().map(PaymentFactory::convert).toList();
    }

    @Override
    public List<PaymentDTO> getPaymentByConditions(QueryPaymentDTO queryPaymentDTO) {
        List<Payment> payments = paymentMapper.selectByConditions(queryPaymentDTO);
        return payments.stream().map(PaymentFactory::convert).toList();
    }

    @Override
    public void createPayment(Payment payment) {

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentMapper.createPayment(payment);
                PaymentLog paymentLog = new PaymentLog();
                paymentLog.setPayId(payment.getId());
                paymentLog.setSrcStatus(payment.getStatus());
                paymentLog.setTargetStatus(payment.getStatus());
                paymentLog.setInfo("");
                paymentLogMapper.insertSelective(paymentLog);
            }
        });
    }

    @Override
    public void updatePaymentStatus(Long paymentId, UpdatePaymentReqDTO updatePaymentReqDTO) {
        // 更新支付状态
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
                String srcStatus = payment.getStatus();
                payment.setStatus(updatePaymentReqDTO.getStatus());
                payment.setThirdPayNo(updatePaymentReqDTO.getThirdPayNo());
                payment.setPayTime(updatePaymentReqDTO.getPayTime());
                paymentMapper.updatePaymentStatus(payment);
                PaymentLog paymentLog = new PaymentLog();
                paymentLog.setPayId(paymentId);
                paymentLog.setSrcStatus(srcStatus);
                paymentLog.setTargetStatus(updatePaymentReqDTO.getStatus());
                paymentLog.setInfo(updatePaymentReqDTO.getResponseData());
                paymentLogMapper.insertSelective(paymentLog);
            }
        });
    }
}
