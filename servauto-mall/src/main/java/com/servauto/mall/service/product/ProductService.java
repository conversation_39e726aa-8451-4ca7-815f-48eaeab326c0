package com.servauto.mall.service.product;

import com.github.pagehelper.PageInfo;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.dto.response.IdNameDTO;
import com.servauto.mall.model.dto.response.product.ProductAttributeDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.dto.response.product.ProductSpecifications;

import java.util.List;

public interface ProductService {
    /**
     * get product by id
     *
     * @param id id
     * @return ProductDTO
     */
    ProductDTO getProduct(long id);

    /**
     * get base info list of product by reqDTO
     *
     * @param reqDTO reqDTO
     * @return List<ProductDTO>
     */
    List<ProductDTO> getProducts(QueryProductsDTO reqDTO);

    List<ProductDTO> getProducts(List<Long> productIds);

    /**
     * page base info list of product by reqDTO
     *
     * @param reqDTO reqDTO
     * @return PageInfo<ProductDTO>
     */
    PageInfo<ProductDTO> pageProducts(QueryProductsDTO reqDTO);

    /**
     * get product categories
     *
     * @return List<IdNameDTO>
     */
    List<IdNameDTO> getProductCategories();

    /**
     * get product attributes
     *
     * @return List<ProductAttributeDTO>
     */
    List<ProductAttributeDTO> getProductAttributes(String displayType, long categoryId);

    /**
     * get product brands
     *
     * @return List<IdNameDTO>
     */
    List<IdNameDTO> getProductBrands();

    /**
     * get product specifications
     *
     * @return ProductSpecifications
     */
    ProductSpecifications getRecommendProductsByProductId(Long carId, long productId);

    /**
     * get recommend products
     *
     * @return ProductSpecifications
     */
    ProductSpecifications getRecommendProductsByProducts(Long carId, List<ProductDTO> products);
}
