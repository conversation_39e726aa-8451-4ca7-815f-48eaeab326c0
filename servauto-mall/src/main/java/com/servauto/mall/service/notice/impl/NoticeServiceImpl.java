package com.servauto.mall.service.notice.impl;

import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.enums.TriggerType;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.DateUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.lark.MessageTriggerStrategy;
import com.servauto.framework.lark.MessageTriggerStrategyFactory;
import com.servauto.framework.lark.TriggerMessageReq;
import com.servauto.framework.sms.api.dto.SmsSendContentReqDTO;
import com.servauto.framework.sms.api.service.SmsSendApi;
import com.servauto.framework.sms.enums.SceneTemplateEnum;
import com.servauto.framework.sms.enums.UserTypeEnum;
import com.servauto.mall.enums.order.OrderDeliveryTypeEnum;
import com.servauto.mall.factory.order.OrderFactory;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.model.entity.customer.CustomerInfo;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.service.customer.CustomerService;
import com.servauto.mall.service.notice.NoticeService;
import com.servauto.mall.service.workshop.WorkshopService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import static com.servauto.common.utils.DateUtils.YYYY_MM_DD_HH_MM_PATTERN;

/**
 * <p>NoticeServiceImpl</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/13 18:26
 */
@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private SmsSendApi noticeApi;

    @Resource
    private WorkshopService workshopService;

    @Resource
    private CustomerService customerService;

    @Resource
    private Environment environment;

    /**
     * You have a new order. Please handle it on time：
     * #Order ID：{Order ID}
     * #Order Type：{Order Type}
     * #Car Plate Number：{Car Plate Number}
     * #Service Name：{Service Name}
     * #Appointment Info：
     * - Store：{Store Name}
     * - Time：{Appointment Time（yyyy-mm-dd hh：mm）}
     *
     * @param order order
     */
    @Override
    public void onOrderPaidSendWhatsapp(Order order) {
        log.info("the order {} paid pending send whatsapp message", order.getOrderNo());
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            log.info("the order {} created delivery type is {} skip send whatsapp message", order.getOrderNo(), order.getDeliveryType());
            return;
        }

        if (order.getWorkshopId() == null || order.getReservationTime().getTime() <= 0) {
            log.info("the order {} paid workshop {} is null || order reservation {} is null skip send whatsapp message", order.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
            return;
        }

        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());
        if (workshopInfo.getWhatsAppNumber() == null) {
            return;
        }

        for (String number : workshopInfo.getWhatsAppNumber().split(";")) {

            // ["orderType","orderNo","orderType1","licensePlate","serviceName","workshopName","time"]
            Map<String, Object> templateParams = new LinkedHashMap<>();
            templateParams.put("orderType", OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
            templateParams.put("orderNo", order.getOrderNo());
            templateParams.put("orderType1", OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
            templateParams.put("licensePlate", order.getLicensePlate());
            templateParams.put("serviceName", order.getServiceName());
            templateParams.put("workshopName", order.getWorkshopName());
            templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));

            try {
                noticeApi.sendContent(SmsSendContentReqDTO.builder()
                        .mobile(number)
                        .templateCode(SceneTemplateEnum.PAYMENT_COMPLETED.getTemplateCode())
                        .userId(order.getCustomerId())
                        .userType(UserTypeEnum.MEMBER.getValue())
                        .templateParams(templateParams)
                        .build());
            } catch (Exception ignored) {
            }
        }
        log.info("the order {} paid send whatsapp message succeed", order.getOrderNo());
    }

    /**
     * Dear Customer, your service appointment has been rescheduled.We sincerely apologize for any inconvenience caused by the adjustment to your appointment. We’ve updated your service booking details as follows:
     * - New Date & Time: [Month Day, Year]  [HH:MM AM/PM]
     * - Service Store: [Store Name][Store Address]
     *
     * @param order order
     */
    @Override
    public void onRescheduleSendWhatsappToCustomer(Order order) {
        CustomerInfo customerInfo = customerService.getCustomerById(order.getCustomerId());
        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());

        LocalDateTime ldt = order.getReservationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        String date = ldt.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH));
        String time = ldt.format(DateTimeFormatter.ofPattern("hh:mm a", Locale.ENGLISH));

        // ["date","time","workshopName","address"]
        Map<String, Object> templateParams = new LinkedHashMap<>();
        templateParams.put("date", date);
        templateParams.put("time", time);
        templateParams.put("workshopName", order.getWorkshopName());
        templateParams.put("address", String.format("%s %s %s ", workshopInfo.getCityName(), workshopInfo.getStateName(), workshopInfo.getAddress()));

        String mobile = "test".equals(environment.getProperty("spring.profiles.active")) ? "8618210378280" : customerInfo.getMobile();

        noticeApi.sendContent(SmsSendContentReqDTO.builder()
                .mobile(mobile)
                .templateCode(SceneTemplateEnum.RESCHEDULE_CUSTOMER.getTemplateCode())
                .userId(order.getCustomerId())
                .userType(UserTypeEnum.MEMBER.getValue())
                .templateParams(templateParams)
                .build());
    }

    /**
     * You have an order service time that has been changed. Please handle it on time：
     * #Order ID：{Order ID}
     * #Order Type：{Order Type}
     * #Car Plate Number：{Car Plate Number}
     * #Service Name：{Service Name}
     * #Appointment Info：
     * - Store：{Store Name}
     * - Time：{Appointment Time（yyyy-mm-dd hh：mm）}
     *
     * @param order order
     */
    @Override
    public void onRescheduleSendWhatsapp(Order order) {
        log.info("the order {} reschedule pending send whatsapp message", order.getOrderNo());
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            log.info("the order {} reschedule delivery type is {} skip send whatsapp message", order.getOrderNo(), order.getDeliveryType());
            return;
        }

        if (order.getWorkshopId() == null || order.getReservationTime().getTime() <= 0) {
            log.info("the order {} reschedule workshop {} is null || order reservation {} is null skip send whatsapp message", order.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
            return;
        }

        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());
        if (workshopInfo.getWhatsAppNumber() == null) {
            return;
        }

        for (String number : workshopInfo.getWhatsAppNumber().split(";")) {

            // ["orderNo","orderType","licensePlate","serviceName","workshopName","time"]
            Map<String, Object> templateParams = new LinkedHashMap<>();
            templateParams.put("orderNo", order.getOrderNo());
            templateParams.put("orderType", OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
            templateParams.put("licensePlate", order.getLicensePlate());
            templateParams.put("serviceName", order.getServiceName());
            templateParams.put("workshopName", order.getWorkshopName());
            templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));

            try {
                noticeApi.sendContent(SmsSendContentReqDTO.builder()
                        .mobile(number)
                        .templateCode(SceneTemplateEnum.RESCHEDULE.getTemplateCode())
                        .userId(order.getCustomerId())
                        .userType(UserTypeEnum.MEMBER.getValue())
                        .templateParams(templateParams)
                        .build());
            } catch (Exception ignored) {
            }
        }

        log.info("the order {} reschedule send whatsapp message succeed", order.getOrderNo());
    }

    @Override
    public void onBookServiceSendWhatsapp(Order order) {
        log.info("the order {} book service pending send whatsapp message", order.getOrderNo());
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            log.info("the order {} book service delivery type is {} skip send whatsapp message", order.getOrderNo(), order.getDeliveryType());
            return;
        }

        if (order.getWorkshopId() == null || order.getReservationTime().getTime() <= 0) {
            log.info("the order {} book service  workshop {} is null || order reservation {} is null skip send whatsapp message", order.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
            return;
        }

        WorkshopDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());
        if (workshopInfo.getWhatsAppNumber() == null) {
            return;
        }

        for (String number : workshopInfo.getWhatsAppNumber().split(";")) {

            // ["orderNo","orderType","licensePlate","serviceName","workshopName","time"]
            Map<String, Object> templateParams = new LinkedHashMap<>();
            templateParams.put("orderNo", order.getOrderNo());
            templateParams.put("orderType", OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
            templateParams.put("licensePlate", order.getLicensePlate());
            templateParams.put("serviceName", order.getServiceName());
            templateParams.put("workshopName", order.getWorkshopName());
            templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));

            try {
                noticeApi.sendContent(SmsSendContentReqDTO.builder()
                        .mobile(number)
                        .templateCode(SceneTemplateEnum.BOOK_SERVICE.getTemplateCode())
                        .userId(order.getCustomerId())
                        .userType(UserTypeEnum.MEMBER.getValue())
                        .templateParams(templateParams)
                        .build());
            } catch (Exception ignored) {
            }
        }

        log.info("the order {} book service send whatsapp message succeed", order.getOrderNo());
    }

    @Override
    public void onBookServiceSendLark(Order order) {
        log.info("the order {} pending  book-service  send lark message", order.getOrderNo());
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.BOOK_SERVICE);

        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId(order.getOrderNo());
        req.setOrderType(OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
        req.setCarPlateNumber(order.getLicensePlate());
        req.setServiceName(order.getServiceName());

        if (OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            if (StringUtils.isEmpty(order.getWorkshopName()) || !OrderFactory.isValidDate(order.getReservationTime())) {
                throw BusinessException.of(ResponseCode.ERROR);
            }
            req.setStoreName(order.getWorkshopName());
            req.setTime(DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));
        }

        strategy.doProcess(req);
        log.info("the order {} pending book-service send lark succeed", order.getOrderNo());
    }

    @Override
    public void onOrderPaidSendLark(Order order) {
        log.info("the order {} paid completed send lark message", order.getOrderNo());
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.ORDER_PAYMENT_COMPLETED);

        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId(order.getOrderNo());
        req.setOrderType(OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
        req.setCarPlateNumber(order.getLicensePlate());
        req.setServiceName(order.getServiceName());

        if (OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            req.setStoreName(StringUtils.isNotEmpty(order.getWorkshopName()) ? order.getWorkshopName() : "");
            req.setTime(OrderFactory.isValidDate(order.getReservationTime()) ? DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()) : "");
        }
        strategy.doProcess(req);
        log.info("the order {} paid completed send lark succeed", order.getOrderNo());
    }

}
