package com.servauto.mall.service.car;

import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarBrands;

import java.util.List;
import java.util.Map;

public interface CarBrandsService {
    // 新增方法：查询所有有效的brands
    List<CarKeyValueDTO> selectAllValidBrandsKv();

    List<CarKeyValueDTO> selectAllValidTopBrandsKv();

    Map<Long, CarBrands> selectCarBrandsMap(List<Long> brandIds);
}
