package com.servauto.mall.service.tiktok;

import com.servauto.mall.model.dto.response.tiktok.TiktokTokenRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokShopSecretsRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokOrdersRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokProductInfoRespDTO;

import java.util.List;

public interface TiktokService {
    /**
     * 使用授权码获取访问令牌
     *
     * @param authCode 回调code
     * @return 令牌响应
     */
    TiktokTokenRespDTO getAccessToken(String authCode);


    /**
     * 获取门店密钥
     *
     * @return 门店密钥信息
     */
    TiktokShopSecretsRespDTO getShopSecrets();

    /**
     * 获取订单列表
     *
     * @param pageToken 分页token（可选）
     * @param pageSize 页面大小（可选，默认20）
     * @param orderStatus 订单状态（可选）
     * @param createTimeGe 创建时间之后

     * @return 订单列表
     */
    TiktokOrdersRespDTO getOrders(String pageToken,String pageSize,String orderStatus,Long createTimeGe, Long updateTimeGe,String shippingType);




    Boolean tiktokOrdersSyncToLark(String appToken,String tableId,Long createTimeGe ,Long updateTimeGe,String shippingType,String orderStatus);

    /**
     * 获取产品信息
     *
     * @param productIds 产品IDs
     * @return 产品信息
     */
    List<TiktokProductInfoRespDTO> getProductsInfo(List<String> productIds);
}
