package com.servauto.mall.service.car;


import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarExtra;
import com.servauto.mall.model.entity.car.CarYears;

import java.util.List;
import java.util.Map;

public interface CarExtraService {


    List<CarKeyValueDTO> getCarExtraByYearIdKv(Long yearId);

    Map<Long, CarExtra> selectVariantsMap(List<Long> variantIds);

}