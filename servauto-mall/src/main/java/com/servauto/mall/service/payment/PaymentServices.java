package com.servauto.mall.service.payment;

import com.servauto.mall.model.dto.request.payment.QueryPaymentDTO;
import com.servauto.mall.model.dto.request.payment.UpdatePaymentReqDTO;
import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.entity.payment.Payment;

import java.util.List;

public interface PaymentServices {


    PaymentDTO queryPayment(String sourceId);
    PaymentDTO queryPaymentByPayNo(String sourceId);

    List<PaymentDTO> getPaymentByStatus(String status,Integer channel);


    List<PaymentDTO> getPaymentByConditions(QueryPaymentDTO queryPaymentDTO);


    void createPayment(Payment payment);
    void updatePaymentStatus(Long paymentId, UpdatePaymentReqDTO updatePaymentReqDTO);

}
