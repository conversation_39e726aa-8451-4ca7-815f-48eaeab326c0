package com.servauto.mall.service.car;


import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarModels;
import com.servauto.mall.model.entity.car.CarYears;

import java.util.List;
import java.util.Map;

public interface CarYearsService {
    // 根据modelId查询CarYearsDTO

    List<CarKeyValueDTO> getCarYearsByModelIdKv(Long modelId);

    Map<Long, CarYears> selectCarYearsMap(List<Long> yearsIds);

}