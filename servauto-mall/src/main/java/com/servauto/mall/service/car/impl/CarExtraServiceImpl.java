package com.servauto.mall.service.car.impl;

import com.servauto.mall.dao.car.CarExtraMapper;

import com.servauto.mall.factory.car.CareExtraInfoFactory;
import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarBrands;
import com.servauto.mall.model.entity.car.CarExtra;
import com.servauto.mall.service.car.CarExtraService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CarExtraServiceImpl implements CarExtraService {

    @Resource
    private CarExtraMapper carExtraMapper;


    @Override
    public List<CarKeyValueDTO> getCarExtraByYearIdKv(Long yearId) {
        return carExtraMapper.selectByYearId(yearId).stream().map(CareExtraInfoFactory::convertKv).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CarExtra> selectVariantsMap(List<Long> variantIds) {
        // 如果输入为空，直接返回空Map
        if (variantIds == null || variantIds.isEmpty()) {
            // 使用不可变的空Map，避免不必要的对象创建
            return Collections.emptyMap();
        }

        // 调用数据库查询方法，并检查返回值是否为null
        List<CarExtra> carExtras = carExtraMapper.selectByPrimaryKeys(variantIds);
        if (carExtras == null) {
            // 数据库查询结果为空时，返回空Map
            return Collections.emptyMap();
        }

        // 使用Stream API构建Map，确保id不为null 过滤掉无效的CarBrands
        return carExtras.stream().filter(carExtra -> carExtra != null && carExtra.getId() != null).collect(Collectors.toMap(CarExtra::getId, carExtra -> carExtra, (existing, replacement) -> existing));

    }
}