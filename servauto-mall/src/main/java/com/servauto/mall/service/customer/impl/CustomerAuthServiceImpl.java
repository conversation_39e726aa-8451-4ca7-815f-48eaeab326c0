package com.servauto.mall.service.customer.impl;

import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.sms.api.dto.SmsCodeSendReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeUseReqDTO;
import com.servauto.framework.sms.api.service.SmsCodeApi;
import com.servauto.framework.sms.enums.UserTypeEnum;
import com.servauto.framework.utils.ip.IpUtils;
import com.servauto.mall.model.dto.request.auth.AuthLoginReqDTO;
import com.servauto.mall.model.dto.request.auth.AuthSmsSendReqDTO;
import com.servauto.mall.model.dto.response.auth.AuthLoginRespDTO;
import com.servauto.mall.model.entity.customer.CustomerInfo;
import com.servauto.mall.service.customer.CustomerAuthService;
import com.servauto.mall.service.customer.CustomerService;
import com.servauto.mall.support.oauth.TokenService;
import com.servauto.mall.support.oauth.dto.AccessTokenCreateReqDTO;
import com.servauto.mall.support.oauth.dto.AccessTokenRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CustomerAuthServiceImpl implements CustomerAuthService {

    @Resource
    private CustomerService customerService;

    @Resource
    private TokenService tokenService;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Override
    public void sendSmsCode(Long userId, AuthSmsSendReqDTO reqDTO) {
        SmsCodeSendReqDTO sendReqDTO = SmsCodeSendReqDTO.builder().userId(userId).userType(UserTypeEnum.MEMBER.getValue())
                .mobile(reqDTO.getMobile()).scene(reqDTO.getScene()).createIp(IpUtils.getIpAddr()).build();
        smsCodeApi.sendSmsCode(sendReqDTO);
    }

    @Override
    public AuthLoginRespDTO login(AuthLoginReqDTO reqVO) {

        // use sms code
        String userIp = IpUtils.getIpAddr();
        smsCodeApi.useSmsCode(SmsCodeUseReqDTO.builder().mobile(reqVO.getMobile()).scene(reqVO.getScene()).code(reqVO.getCode()).usedIp(userIp).build());

        // register customer if absent
        CustomerInfo customer = customerService.createCustomerIfAbsent(reqVO.getMobile(), reqVO.getSource());
        if (customer == null) {
            throw BusinessException.of("Incorrect password");
        }

        // create login token
        return createTokenAfterLoginSuccess(customer);
    }

    private AuthLoginRespDTO createTokenAfterLoginSuccess(CustomerInfo customer) {
        AccessTokenCreateReqDTO accessTokenCreateReqDTO = new AccessTokenCreateReqDTO();
        accessTokenCreateReqDTO.setUserId(customer.getId());
        AccessTokenRespDTO accessTokenRespDTO = tokenService.createAccessToken(accessTokenCreateReqDTO);
        if (accessTokenRespDTO == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        return AuthLoginRespDTO.builder().customerId(customer.getId()).accessToken(accessTokenRespDTO.getAccessToken()).build();
    }

    @Override
    public void logout(String token) {
        tokenService.removeAccessToken(token);
    }
}
