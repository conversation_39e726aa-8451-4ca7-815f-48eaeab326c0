package com.servauto.mall.service.payment.impl;

import com.servauto.common.utils.DateUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.common.utils.generator.UniqueID;
import com.servauto.framework.factory.SystemFactory;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.constants.RedisKeyConstants;
import com.servauto.mall.enums.payment.PaymentChannel;
import com.servauto.mall.enums.payment.PaymentStatus;
import com.servauto.mall.enums.payment.i2c2p.I2c2pStatus;
import com.servauto.mall.model.dto.request.payment.CallBackPaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.CreatePaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.UpdatePaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.i2c2p.I2c2pPaymentCreateReqDTO;
import com.servauto.mall.model.dto.request.payment.i2c2p.I2c2pPaymentInquiryReqDTO;
import com.servauto.mall.model.dto.response.payment.I2c2p.I2c2pPaymentCreateDetailDTO;
import com.servauto.mall.model.dto.response.payment.I2c2p.I2c2pPaymentCreateResDTO;
import com.servauto.mall.model.dto.response.payment.I2c2p.I2c2pPaymentInquiryDetailDTO;
import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.dto.response.payment.PaymentResDTO;
import com.servauto.mall.model.entity.payment.Payment;
import com.servauto.mall.service.order.TradeService;
import com.servauto.mall.service.payment.PaymentFactoryServices;
import com.servauto.mall.service.payment.PaymentServices;
import com.servauto.mall.support.payment.config.I2c2pPaymentProperties;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.TextCodec;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
public class I2c2pPaymentFactoryServicesImpl implements PaymentFactoryServices {

    @Resource
    public I2c2pPaymentProperties i2c2pPaymentProperties;
    @Resource
    public PaymentServices paymentServices;

    @Resource
    private TradeService tradeService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private RedissonClient redissonClient;


    // 在类中定义 HttpClient 单例
    private static final HttpClient httpClient = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(30)).build();

    private static final String PAYMENT_TOKEN = "/paymentToken";
    private static final String PAYMENT_INQUIRY = "/paymentInquiry";


    @Override
    public PaymentResDTO createPayment(CreatePaymentReqDTO createPaymentReqDTO) {

        // 实现2C2P支付逻辑
        PaymentDTO paymentDto = paymentServices.queryPayment(createPaymentReqDTO.getOrderNo());

        I2c2pPaymentCreateReqDTO i2c2pPaymentCreateReqDTO = initializePaymentCreateReqDTO();
        if (paymentDto != null) {
            i2c2pPaymentCreateReqDTO.setInvoiceNo(paymentDto.getPayNo());
            PaymentResDTO result = new PaymentResDTO();
            result.setPayNo(paymentDto.getPayNo());
            result.setAmount(paymentDto.getAmount().toString());
            result.setPaymentUrl(paymentDto.getUrl());
            return result;
        } else {
            i2c2pPaymentCreateReqDTO.setInvoiceNo(UniqueID.generateId("P"));
        }

        i2c2pPaymentCreateReqDTO.setDescription(createPaymentReqDTO.getProductDesc());
        i2c2pPaymentCreateReqDTO.setAmount(parseFormattedAmount(createPaymentReqDTO.getAmount()));
        i2c2pPaymentCreateReqDTO.setBackendReturnUrl(i2c2pPaymentProperties.getMerchant().getBackendURL());
        i2c2pPaymentCreateReqDTO.setFrontendReturnUrl(i2c2pPaymentProperties.getMerchant().getResponseURL()+ "?orderNo=" + createPaymentReqDTO.getOrderNo());

        Map<String, Object> payload = JacksonSerializer.deSerialize(JacksonSerializer.serialize(i2c2pPaymentCreateReqDTO), Map.class);
        String token = generateToken(payload);
        // 构建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("payload", token);

        String jsonBody = JacksonSerializer.serialize(requestBody);
        // 调用封装好的 HTTP 方法
        String responseBody = sendRequest(PAYMENT_TOKEN, jsonBody);
        if (responseBody == null) {
            return null;
        }
        // 输出响应结果
        I2c2pPaymentCreateResDTO paymentResDTO = JacksonSerializer.deSerialize(responseBody, I2c2pPaymentCreateResDTO.class);
        if (paymentResDTO.getPayload().isBlank()) {
            log.info("createPayment - Failed to create payment{}: ", paymentResDTO.getRespDesc());
            return null;
        }
        String tokenRes = decodeToken(paymentResDTO);
        I2c2pPaymentCreateDetailDTO paymentDetailDTO = JacksonSerializer.deSerialize(tokenRes, I2c2pPaymentCreateDetailDTO.class);
        if (paymentDetailDTO.getRespCode().equals(I2c2pStatus.SUCCESS.getCode())) {
            PaymentResDTO result = new PaymentResDTO();
            result.setPayNo(i2c2pPaymentCreateReqDTO.getInvoiceNo());
            result.setAmount(i2c2pPaymentCreateReqDTO.getAmount().toString());
            result.setPaymentUrl(paymentDetailDTO.getWebPaymentUrl());
            savePaymentIfNew(result, createPaymentReqDTO);
            return result;
        } else {
            return null;
        }

    }

    @Override
    public PaymentResDTO queryPaymentByPayNo(String payNo) {
        try {
            PaymentDTO paymentDto = paymentServices.queryPaymentByPayNo(payNo);
            if (paymentDto == null) {
                return null;
            }
            PaymentResDTO result = new PaymentResDTO();
            result.setPayNo(paymentDto.getPayNo());
            result.setAmount(paymentDto.getAmount().toString());
            result.setPaymentUrl(paymentDto.getUrl());
            return result;
        } catch (Exception e) {
            log.error("Error creating payment", e);
            return null;
        }
    }

    @Override
    public PaymentResDTO queryPaymentStatusByNo(String payNo) {
        //根据订单号查询支付表
        PaymentDTO paymentDto = paymentServices.queryPaymentByPayNo(payNo);
        if (paymentDto == null) {
            return null;
        }
//        if (!PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
//            //支付状态不是初始状态，直接返回
//            log.info("queryPaymentStatusByNo - PaymentStatus{} ", paymentDto.getStatus());
//            return null;
//        }

        I2c2pPaymentInquiryReqDTO i2c2pPaymentInquiryReqDTO = new I2c2pPaymentInquiryReqDTO();
        i2c2pPaymentInquiryReqDTO.setMerchantID(i2c2pPaymentProperties.getMerchant().getMerchantID());
        i2c2pPaymentInquiryReqDTO.setInvoiceNo(payNo);
        i2c2pPaymentInquiryReqDTO.setLocal("en");
        Map<String, Object> payload = JacksonSerializer.deSerialize(JacksonSerializer.serialize(i2c2pPaymentInquiryReqDTO), Map.class);
        String token = generateToken(payload);
        // 构建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("payload", token);

        String jsonBody = JacksonSerializer.serialize(requestBody);
        // 调用封装好的 HTTP 方法
        String responseBody = sendRequest(PAYMENT_INQUIRY, jsonBody);
        if (responseBody == null) {
            return null;
        }
        // 输出响应结果
        I2c2pPaymentCreateResDTO paymentResDTO = JacksonSerializer.deSerialize(responseBody, I2c2pPaymentCreateResDTO.class);

        if (paymentResDTO.getPayload().isBlank()) {
            log.info("queryPaymentStatusByNo - Failed to query payment{}: ", paymentResDTO.getRespDesc());
            return null;
        }
        String tokenRes = decodeToken(paymentResDTO);
        System.out.println("tokenRes:"+tokenRes);
        I2c2pPaymentInquiryDetailDTO paymentDetailDTO = JacksonSerializer.deSerialize(tokenRes, I2c2pPaymentInquiryDetailDTO.class);
        if (paymentDetailDTO.getRespCode().equals(I2c2pStatus.SUCCESS.getCode())) {
            PaymentResDTO result = new PaymentResDTO();
            return result;
        }
        log.info("responseBody{}:", responseBody);
        return null;
    }

    @Override
    public void batchQueryPaymentStatus() {

        //查询所有初始化状态的支付，如果没有直接返回
        List<PaymentDTO> paymentDtos = paymentServices.getPaymentByStatus(PaymentStatus.INIT.getCode(), PaymentChannel.I2C2P.getCode());
        if (paymentDtos.isEmpty()) {
            return;
        }
        //多线程处理支付状态查询
        ExecutorService executorService = Executors.newFixedThreadPool(3);
        paymentDtos.forEach(paymentDto -> executorService.submit(() -> {
            //更新支付状态
            log.info("batchQueryPaymentStatus(2C2P) start payNo:{}", paymentDto.getPayNo());
            handlerPaymentStatus(paymentDto);
            log.info("batchQueryPaymentStatus(2C2P) end payNo:{}", paymentDto.getPayNo());
        }));
    }


    @Override
    public void paymentCallBack(CallBackPaymentReqDTO callBackPaymentReqDTO) {
        log.info("paymentCallBack callBackPaymentReqDTO: {}", callBackPaymentReqDTO);
        //解析回调结构体
        I2c2pPaymentCreateResDTO paymentResDTO = JacksonSerializer.deSerialize(callBackPaymentReqDTO.getReqData(), I2c2pPaymentCreateResDTO.class);
        String tokenRes = decodeToken(paymentResDTO);
        I2c2pPaymentInquiryDetailDTO paymentDetailDTO = JacksonSerializer.deSerialize(tokenRes, I2c2pPaymentInquiryDetailDTO.class);
        //查询支付记录
        PaymentDTO paymentDto = paymentServices.queryPaymentByPayNo(paymentDetailDTO.getInvoiceNo());
        if (paymentDto == null) {
            log.info("paymentCallBack Payment not found for payNo: {}", paymentDetailDTO.getInvoiceNo());
            return;
        }
        if (!PaymentStatus.INIT.getCode().equals(paymentDto.getStatus())) {
            log.info("paymentCallBack Payment already processed for payNo: {}", callBackPaymentReqDTO.getPayNo());
            return;
        }

        if (StringUtils.isNotBlank(paymentDetailDTO.getRespCode()) && "0000".equals(paymentDetailDTO.getRespCode())) {
            RLock lock = redissonClient.getLock(String.format(RedisKeyConstants.UPDATE_PAYMENT_KEY, paymentDto.getPayNo()));
            boolean isLocked = false;
            try {
                // 加锁后最多持有30秒
                isLocked = lock.tryLock(30, TimeUnit.SECONDS);
                if (!isLocked) {
                    log.info("paymentCallBack isLocked for payNo: {}", paymentDto.getPayNo());
                    return;
                }
                processPaySuccess(paymentDto, paymentDetailDTO);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("paymentCallBack Error processing query payment status for payNo: {}", paymentDto.getPayNo(), e);
            } finally {
                // 释放锁
                if (isLocked && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @NotNull
    private I2c2pPaymentCreateReqDTO initializePaymentCreateReqDTO() {
        I2c2pPaymentCreateReqDTO paymentResDTO = new I2c2pPaymentCreateReqDTO();
        paymentResDTO.setMerchantID(i2c2pPaymentProperties.getMerchant().getMerchantID());
        paymentResDTO.setCurrencyCode(i2c2pPaymentProperties.getMerchant().getCurrencyCode());
        //当前时间往后+15分钟 格式是：yyyy-MM-dd HH:mm:ss
        LocalDateTime expiryTime = LocalDateTime.now(SystemFactory.getSystemZonId()).plusMinutes(i2c2pPaymentProperties.getMerchant().getExpiryTime());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        paymentResDTO.setPaymentExpiry(expiryTime.format(formatter));
        return paymentResDTO;
    }


    public static BigDecimal parseFormattedAmount(BigDecimal amount) {
        return amount.stripTrailingZeros().setScale(2, RoundingMode.HALF_UP);
    }

    private String generateToken(Map<String, Object> payload) {
        // i2c2pCreatePaymentReqDTO转为map
        payload.put("iat", System.currentTimeMillis() / 1000);
        //设置 Header
        //payload.put("paymentChannel", Arrays.asList("APPLEPAY"));
        Map<String, Object> header = new HashMap<>();
        header.put("typ", "JWT");
        header.put("alg", "HS256");
        // 构建 JWT Token
        return Jwts.builder().setClaims(payload).setHeader(header).signWith(SignatureAlgorithm.HS256, TextCodec.BASE64.encode(i2c2pPaymentProperties.getMerchant().getSecretKey())).compact();
    }

    private String decodeToken(I2c2pPaymentCreateResDTO i2C2PPaymentCreateResDTO) {
        // 解析 JWT Token
        return JacksonSerializer.serialize(Jwts.parser().setSigningKey(TextCodec.BASE64.encode(i2c2pPaymentProperties.getMerchant().getSecretKey())).parseClaimsJws(i2C2PPaymentCreateResDTO.getPayload()).getBody());
    }


    /**
     * 发送支付请求到 2C2P 的 Payment API，并返回封装的响应对象
     *
     * @param jsonBody 请求体 JSON 字符串
     * @return 响应结果封装对象
     */
    private String sendRequest(String method, String jsonBody) {
        HttpRequest request = HttpRequest.newBuilder().uri(URI.create(i2c2pPaymentProperties.getPlatform().getRequestUrl() + method)).header("Content-Type", "application/json").header("User-Agent", "ServAuto-Mall-Backend/1.0").POST(HttpRequest.BodyPublishers.ofString(jsonBody)).timeout(Duration.ofSeconds(30)).build();

        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            if (response.statusCode() != 200) {
                return null;
            }

            return response.body();

        } catch (IOException | InterruptedException e) {
            log.error("Failed to send payment request", e);
            return null;
        }
    }

    private void savePaymentIfNew(@NotNull PaymentResDTO paymentResDTO, @NotNull CreatePaymentReqDTO createPaymentReqDTO) {
        Payment payment = getPayment(createPaymentReqDTO, paymentResDTO);
        paymentServices.createPayment(payment);
    }

    @NotNull
    private Payment getPayment(CreatePaymentReqDTO createPaymentReqDTO, PaymentResDTO paymentResDTO) {
        Payment payment = new Payment();
        payment.setPayNo(paymentResDTO.getPayNo());
        payment.setSourceId(createPaymentReqDTO.getOrderNo());
        payment.setChannel(PaymentChannel.I2C2P.getCode());
        payment.setPayMethod(1);
        payment.setAmount(createPaymentReqDTO.getAmount());
        payment.setCurrency(i2c2pPaymentProperties.getMerchant().getCurrencyCode());
        //ExpiresAt当前时间十五分钟后失效
        Date expiresAt = new Date();
        expiresAt.setTime(expiresAt.getTime() + i2c2pPaymentProperties.getMerchant().getExpiryTime() * 60 * 1000);
        payment.setExpiresAt(expiresAt);
        payment.setPayType("");
        payment.setUrl(paymentResDTO.getPaymentUrl());
        payment.setRequestData(JacksonSerializer.serialize(createPaymentReqDTO));
        payment.setStatus(PaymentStatus.INIT.getCode());
        return payment;
    }

    public void handlerPaymentStatus(PaymentDTO paymentDto) {
        //根据订单号查询支付表
        I2c2pPaymentInquiryReqDTO i2c2pPaymentInquiryReqDTO = new I2c2pPaymentInquiryReqDTO();
        i2c2pPaymentInquiryReqDTO.setMerchantID(i2c2pPaymentProperties.getMerchant().getMerchantID());
        i2c2pPaymentInquiryReqDTO.setInvoiceNo(paymentDto.getPayNo());
        i2c2pPaymentInquiryReqDTO.setLocal("en");
        Map<String, Object> payload = JacksonSerializer.deSerialize(JacksonSerializer.serialize(i2c2pPaymentInquiryReqDTO), Map.class);
        String token = generateToken(payload);
        // 构建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("payload", token);
        String jsonBody = JacksonSerializer.serialize(requestBody);
        // 调用封装好的 HTTP 方法
        String responseBody = sendRequest(PAYMENT_INQUIRY, jsonBody);
        if (responseBody == null) {
            return;
        }
        // 输出响应结果
        I2c2pPaymentCreateResDTO paymentResDTO = JacksonSerializer.deSerialize(responseBody, I2c2pPaymentCreateResDTO.class);
        if (paymentResDTO.getPayload().isBlank()) {
            log.info("handlerPaymentStatus - Failed to create payment{}: ", paymentResDTO);
            return;
        }
        String tokenRes = decodeToken(paymentResDTO);
        I2c2pPaymentInquiryDetailDTO paymentDetailDTO = JacksonSerializer.deSerialize(tokenRes, I2c2pPaymentInquiryDetailDTO.class);
        RLock lock = redissonClient.getLock(String.format(RedisKeyConstants.UPDATE_PAYMENT_KEY, paymentDto.getPayNo()));
        boolean isLocked = false;
        try {
            // 加锁后最多持有30秒
            isLocked = lock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("paymentCallBack isLocked for payNo: {}", paymentDto.getPayNo());
                return;
            }
            //根据不同支付状态处理
            I2c2pStatus status = I2c2pStatus.getByCode(paymentDetailDTO.getRespCode());
            switch (status) {
                case SUCCESS:
                    processPaySuccess(paymentDto, paymentDetailDTO);
                    break;
                case CANCEL:
                    processPayFail(paymentDto, paymentDetailDTO);
                    break;
                case UNKNOWN:
                    break;
                default:
                    // 未知状态或未匹配到，记录日志并保持原状
                    log.warn("Unknown or unsupported response code: {} for payNo: {}", paymentDetailDTO.getRespCode(), paymentDto.getPayNo());
                    break;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("paymentCallBack Error processing query payment status for payNo: {}", paymentDto.getPayNo(), e);
        } finally {
            // 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    //onPaymentSuccess
    private void processPaySuccess(PaymentDTO paymentDto, I2c2pPaymentInquiryDetailDTO paymentDetailDTO) {
        log.info("Payment PaySuccess for payNo: {},I2c2pPaymentInquiryDetailDTO{}", paymentDto.getPayNo(), paymentDetailDTO);
        UpdatePaymentReqDTO updatePaymentReqDTO = getUpdatePaymentReqDTO(paymentDetailDTO, PaymentStatus.SUCCESS.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentSuccess(paymentDto.getSourceId(), paymentDto.getPayNo(), paymentDto.getAmount(), paymentDto.getPayTime());
            }
        });
    }

    //onPaymentFailed
    private void processPayFail(PaymentDTO paymentDto, I2c2pPaymentInquiryDetailDTO paymentDetailDTO) {
        log.info("Payment PayFail for payNo: {},I2c2pPaymentInquiryDetailDTO{}", paymentDto.getPayNo(), paymentDetailDTO);
        UpdatePaymentReqDTO updatePaymentReqDTO = getUpdatePaymentReqDTO(paymentDetailDTO, PaymentStatus.FAIL.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                paymentServices.updatePaymentStatus(paymentDto.getId(), updatePaymentReqDTO);
                tradeService.onPaymentFailed(paymentDto.getSourceId(), paymentDto.getPayNo(), PaymentStatus.FAIL.getCode(), updatePaymentReqDTO.getErrMsg());
            }
        });
    }

    @NotNull
    private static UpdatePaymentReqDTO getUpdatePaymentReqDTO(I2c2pPaymentInquiryDetailDTO paymentDetailDTO, String status) {

        UpdatePaymentReqDTO updatePaymentReqDTO = new UpdatePaymentReqDTO();
        updatePaymentReqDTO.setThirdPayNo(paymentDetailDTO.getInvoiceNo());
        updatePaymentReqDTO.setResponseData(JacksonSerializer.serialize(paymentDetailDTO));
        updatePaymentReqDTO.setStatus(status);
        if (status.equals(PaymentStatus.SUCCESS.getCode())) {
            if (paymentDetailDTO.getTransactionDateTime() != null) {
                updatePaymentReqDTO.setPayTime(DateUtils.parse(paymentDetailDTO.getTransactionDateTime(), "yyyyMMddHHmmss", ZoneId.systemDefault()));
            } else {
                updatePaymentReqDTO.setPayTime(new Date());
            }
        }
        updatePaymentReqDTO.setErrCode(paymentDetailDTO.getRespCode());
        updatePaymentReqDTO.setErrMsg(paymentDetailDTO.getRespDesc());
        return updatePaymentReqDTO;
    }

}