package com.servauto.mall.service.lark.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryTableRecordsDTO extends LarkTableBaseDTO {

    private String viewId;
    /**
     * 筛选参数，用于指定本次查询的筛选条件
     */
    private String filter;
    /**
     * 排序参数，用于指定本次查询返回结果的顺序
     * 注意：
     * 1.不支持对带“公式”和“关联字段”的表的使用。
     * 2.指定排序条件时，参数长度不超过1000字符。
     * 3.当存在多个排序条件时，数据将根据条件顺序逐层排序
     * 示例值 "字段1 DESC","字段2 ASC"
     */
    private List<String> sort = Lists.newArrayList();
    /**
     * 字段名称，用于指定本次查询返回记录中包含的字段
     *
     * 示例值："["字段1","字段2"]"
     */
    private List<String> fieldNames = Lists.newArrayList();
    private Boolean textFieldAsArray = false;
    private Boolean displayFormulaRef = false;
    private Boolean automaticFields = true;
    /**
     * 第一次调用时返回，用于分页查询
     */
    private String pageToken;
    private Integer pageSize = 10;

}
