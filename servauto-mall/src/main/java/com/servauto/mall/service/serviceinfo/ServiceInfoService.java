package com.servauto.mall.service.serviceinfo;

import com.servauto.mall.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;

import java.util.List;

public interface ServiceInfoService {

    /**
     * get service info by id
     *
     * @param id id
     * @return ServiceInfoDTO
     */
    ServiceInfoDTO getService(long id);

    /**
     * get services
     *
     * @param reqDTO reqDTO
     * @return List<ServiceInfoDTO>
     */
    List<ServiceInfoDTO> getServices(QueryServicesDTO reqDTO);
}
