package com.servauto.mall.service.customer.impl;

import com.google.common.base.Preconditions;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.utils.bean.BeanUtils;
import com.servauto.mall.dao.customer.CustomerInfoMapper;
import com.servauto.mall.dao.customer.CustomerShippingAddressMapper;
import com.servauto.mall.enums.customer.CustomerSourceEnum;
import com.servauto.mall.factory.customer.CustomerFactory;
import com.servauto.mall.model.dto.request.customer.CreateAddressReqDTO;
import com.servauto.mall.model.dto.request.customer.EditAddressReqDTO;
import com.servauto.mall.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.mall.model.entity.customer.CustomerInfo;
import com.servauto.mall.model.entity.customer.CustomerShippingAddress;
import com.servauto.mall.service.customer.CustomerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {


    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private CustomerShippingAddressMapper customerShippingAddressMapper;

    @Resource
    private LocationService locationService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public CustomerInfo getUserByMobile(String mobile) {
        return customerInfoMapper.selectByMobile(mobile);
    }

    @Override
    public CustomerInfo createCustomerIfAbsent(String mobile, String source) {
        CustomerInfo customerInfo = getUserByMobile(mobile);
        if (customerInfo != null) {
            return customerInfo;
        }
        return createCustomer(mobile, null, source);
    }

    @Override
    public List<CustomerAddressDTO> getAddressesByCustomerId(Long customerId) {
        List<CustomerShippingAddress> shippingAddresses = customerShippingAddressMapper.selectByCustomerId(customerId);
        if (CollectionUtils.isEmpty(shippingAddresses)) {
            return Lists.newArrayList();
        }
        return buildAddress(shippingAddresses);
    }

    private List<CustomerAddressDTO> buildAddress(List<CustomerShippingAddress> shippingAddresses) {
        Map<String, AreaDTO> areaMap = locationService.getAreaMap();
        Map<String, StateDTO> stateMap = locationService.getStateMap();
        return shippingAddresses.stream().map(e -> CustomerFactory.convert(e, areaMap, stateMap))
                .toList();
    }

    @Override
    public CustomerAddressDTO getAddressesById(Long addressId) {
        CustomerShippingAddress shippingAddress = customerShippingAddressMapper.selectByPrimaryKey(addressId);
        if (shippingAddress == null) {
            return null;
        }
        List<CustomerAddressDTO> addresses = buildAddress(Collections.singletonList(shippingAddress));
        if (CollectionUtils.isEmpty(addresses)) {
            return CustomerAddressDTO.builder().build();
        }
        return addresses.stream().findFirst().orElse(CustomerAddressDTO.builder().build());
    }

    @Override
    public void createAddress(Long customerId, CreateAddressReqDTO createAddressReqDTO) {
        CustomerShippingAddress csa = new CustomerShippingAddress();
        BeanUtils.copyProperties(createAddressReqDTO, csa);
        csa.setCustomerId(customerId);
        customerShippingAddressMapper.insertSelective(csa);
    }

    @Override
    public void updateAddress(Long customerId, EditAddressReqDTO editAddressReqDTO) {
        CustomerShippingAddress address = customerShippingAddressMapper.selectByPrimaryKey(editAddressReqDTO.getId());
        if (address == null) {
            throw BusinessException.of("could not find address");
        }
        if (!address.getCustomerId().equals(customerId)) {
            throw BusinessException.of("address not belong to current customer");
        }

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                CustomerShippingAddress update = new CustomerShippingAddress();
                BeanUtils.copyProperties(editAddressReqDTO, update);
                customerShippingAddressMapper.updateByPrimaryKeySelective(update);

                List<Long> defaultIds = customerShippingAddressMapper.selectByCustomerId(customerId).stream()
                        .filter(e -> !e.getId().equals(editAddressReqDTO.getId()) && e.getIsDefault())
                        .map(CustomerShippingAddress::getId).toList();

                if (CollectionUtils.isNotEmpty(defaultIds)) {
                    defaultIds.forEach(e -> {
                        CustomerShippingAddress tblAddress = new CustomerShippingAddress();
                        tblAddress.setId(e);
                        tblAddress.setIsDefault(false);
                        customerShippingAddressMapper.updateByPrimaryKeySelective(tblAddress);
                    });
                }
            }
        });


    }

    @Override
    public void deleteAddress(Long customerId, Long addressId) {
        CustomerShippingAddress address = customerShippingAddressMapper.selectByPrimaryKey(addressId);
        if (address == null) {
            throw BusinessException.of("could not find address");
        }
        if (!address.getCustomerId().equals(customerId)) {
            throw BusinessException.of("address not belong to current customer");
        }
        CustomerShippingAddress tblAddress = new CustomerShippingAddress();
        tblAddress.setId(addressId);
        tblAddress.setDeleted(true);
        customerShippingAddressMapper.updateByPrimaryKeySelective(tblAddress);
    }

    @Override
    public CustomerInfo getCustomerById(Long customerId) {
        return customerInfoMapper.selectByPrimaryKey(customerId);
    }

    private CustomerInfo createCustomer(String mobile, String realName, String source) {
        CustomerSourceEnum customerSourceEnum = CustomerSourceEnum.getByCode(StringUtils.isBlank(source)
                ? CustomerSourceEnum.DEFAULT.getCode() : source);
        Preconditions.checkArgument(Objects.nonNull(customerSourceEnum));

        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setMobile(mobile);
        if (StringUtils.isEmpty(realName)) {
            realName = mobile;
        }
        customerInfo.setRealName(realName);
        customerInfo.setSource(customerSourceEnum.getCode());
        customerInfoMapper.insertSelective(customerInfo);
        return customerInfo;
    }
}
