package com.servauto.mall.service.lark.dto;

import com.servauto.common.utils.uuid.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BatchCreateTableRecordDTO extends LarkTableBaseDTO {


    private String clientToken = UUID.randomUUID().toString().toLowerCase();

    private List<CreateTableRecordsDTO>  records;


}
