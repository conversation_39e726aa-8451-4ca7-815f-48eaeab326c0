package com.servauto.mall.service.packageinfo.impl;

import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.mall.dao.product.ProductCategoryMapper;
import com.servauto.mall.dao.product.ProductMapper;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.enums.YesOrNo;
import com.servauto.mall.factory.packageinfo.ProductSimpleFactory;
import com.servauto.mall.model.dto.request.packageinfo.ChangePackageDetailDTO;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageDetailDTO;
import com.servauto.mall.model.dto.response.packageinfo.ProductSimpleDTO;
import com.servauto.mall.model.dto.response.product.ProductDTO;
import com.servauto.mall.model.dto.response.product.ProductSpecifications;
import com.servauto.mall.model.entity.product.Product;
import com.servauto.mall.model.entity.serviceinfo.ServiceInfo;
import com.servauto.mall.enums.DeliveryMode;
import com.servauto.mall.model.dto.response.TypeNameDTO;
import com.servauto.mall.model.entity.packageinfo.PackageInfo;
import com.servauto.mall.model.entity.packageinfo.PackageProduct;
import com.servauto.common.exception.BusinessException;
import com.servauto.mall.dao.packageinfo.PackageInfoMapper;
import com.servauto.mall.dao.packageinfo.PackageProductMapper;
import com.servauto.mall.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.mall.factory.packageinfo.PackageFactory;
import com.servauto.mall.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.service.packageinfo.PackageService;
import com.servauto.mall.service.product.ProductService;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.math.BigDecimal;
import java.util.stream.Collectors;

import static com.servauto.mall.service.product.impl.ProductServiceImpl.*;

@Slf4j
@Service
public class PackageServiceImpl implements PackageService {

    private static final int MAX_THREAD_NUMBER = 16;

    @Resource
    private PackageInfoMapper packageInfoMapper;

    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private PackageProductMapper packageProductMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Resource
    private ProductService productService;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(MAX_THREAD_NUMBER);

    public PackageInfoDTO getPackage(long id) {
        if (id == 0) {
            throw BusinessException.of("Invalid package id");
        }
        var packageInfo = packageInfoMapper.selectByPrimaryKey(id);
        if (packageInfo == null) {
            throw BusinessException.of("Invalid package id");
        }
        var packageInfoDTO = PackageFactory.convert(packageInfo);

        if (packageInfo.getServiceId() > 0) {
            var serviceInfo = serviceInfoMapper.selectByPrimaryKey(packageInfo.getServiceId());
            if (serviceInfo == null) {
                throw BusinessException.of("Invalid service id");
            }
            packageInfoDTO.setServiceName(serviceInfo.getName());
        }

        List<Long> productIds = packageProductMapper.selectByPackageId(id).stream().map(PackageProduct::getProductId).toList();
        if (CollectionUtils.isNotEmpty(productIds)) {
            var products = productMapper.selectByConditions(QueryProductsDTO.builder().ids(productIds).status(SaleStatus.ACTIVE.getCode()).build());
            if (CollectionUtils.isNotEmpty(products)) {
                packageInfoDTO.setProductIds(products.stream().map(Product::getId).toList());
            }
        }
        packageInfoDTO.setDeliveryModes(getDeliveryModes(packageInfo));
        return packageInfoDTO;
    }

    public List<PackageInfoDTO> getPackages(QueryPackagesDTO reqDTO) {
        var packageInfos = packageInfoMapper.selectByConditions(reqDTO);
        List<PackageInfoDTO> resp = packageInfos.stream().map(PackageFactory::convert).toList();
        List<Long> serviceIds = packageInfos.stream().map(PackageInfo::getServiceId).toList();
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            HashMap<Long, ServiceInfo> serviceInfoMap = serviceInfoMapper.selectByIds(serviceIds).stream().collect(Collectors.toMap(ServiceInfo::getId, serviceInfo -> serviceInfo, (existing, replacement) -> existing, HashMap::new));
            for (var info : resp) {
                if (info.getServiceId() > 0) {
                    info.setServiceName(serviceInfoMap.get(info.getServiceId()).getName());
                    info.setServiceFee(serviceInfoMap.get(info.getServiceId()).getFee().toString());
                }
            }
        }
        return resp;
    }

    public PackageDetailDTO changePackageDetail(ChangePackageDetailDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getDstProductIds())) {
            throw BusinessException.of("Invalid dst product id");
        }

        PackageDetailDTO resp;
        List<ProductDTO> dstProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reqDTO.getDstProductIds())) {
            dstProducts = productService.getProducts(QueryProductsDTO.builder().
                    ids(reqDTO.getDstProductIds()).status(SaleStatus.ACTIVE.getCode()).build());
            if (CollectionUtils.isEmpty(dstProducts)) {
                throw BusinessException.of("Invalid dst product id, {}", reqDTO.getDstProductIds().toString());
            }
        }



        // 查询具体的 package
        resp = getPackageDetailIgnoreEngineOil(reqDTO.getId(), reqDTO.getProductId(), reqDTO.getCarId(), dstProducts);
        for (var product : dstProducts) {
            if (product.getCategoryName().equals(CATEGORY_ENGINE_OIL)) {
                // 这里需要推荐机油
                var productSimpleDTO = getProductSimpleByProductSpecifications(product.getId(), productService.getRecommendProductsByProductId(reqDTO.getCarId(), product.getId()));
                List<ProductSimpleDTO> products2 = new ArrayList<>(resp.getProducts());
                products2.add(0, productSimpleDTO);
                resp.setProducts(products2);
                break;
            }
        }
        return resp;
    }

    public List<PackageDetailDTO> getPackageDetails(Long id, Long carId, Long productId, String code) {
        List<PackageDetailDTO> resp = new ArrayList<>();
        if (id != null && id > 0) { // 通过商品详情页进去
            // 查询具体的 package
            var detail = getPackageDetailIgnoreEngineOil(id, productId, carId, null);
            resp.add(detail);
        } else {  // 首页进去推荐机油
            var list = productMapper.selectByConditions(QueryProductsDTO.builder().categoryId(CATEGORY_ID_ENGINE_OIL).build());
            if (CollectionUtils.isEmpty(list)) {
                throw BusinessException.of("Product not found");
            }
            var packageProduct = packageProductMapper.selectByProductId(list.get(0).getId());
            if (CollectionUtils.isEmpty(packageProduct)) {
                throw BusinessException.of("Package not found");
            }
            List<Long> packageIds = packageProduct.stream().map(PackageProduct::getPackageId).toList();
            var packages = packageInfoMapper.selectByConditions(QueryPackagesDTO.builder().ids(packageIds).
                    status(SaleStatus.ACTIVE.getCode()).code(code).build());
            if (CollectionUtils.isEmpty(packages)) {
                throw BusinessException.of("Package not found");
            }
            var packageInfo = packages.get(0);
            var detail = getPackageDetailIgnoreEngineOil(packageInfo.getId(), 0L, carId, null);

            List<Long> productIds = packageProductMapper.selectByPackageId(packageInfo.getId()).stream().map(PackageProduct::getProductId).toList();
            var products = productService.getProducts(QueryProductsDTO.builder().
                    ids(productIds).
                    categoryId(CATEGORY_ID_ENGINE_OIL).
                    carId(carId).
                    status(SaleStatus.ACTIVE.getCode()).
                    build());
            HashMap<Long, HashMap<Long, HashMap<Long, HashMap<Long, List<ProductDTO>>>>> hashMap = new HashMap<>();
            for (var product : products) {
                if (!product.getCategoryName().equals(CATEGORY_ENGINE_OIL)) {
                    continue;
                }
                Long modeId = 0L, oilTypeId = 0L, saeGradeId = 0L;
                for (var attr : product.getAttributeValues()) {
                    switch (attr.getName()) {
                        case ATTRIBUTE_MODEL -> modeId = attr.getId();
                        case ATTRIBUTE_OIL_TYPE -> oilTypeId = attr.getId();
                        case ATTRIBUTE_SAE_GRADE -> saeGradeId = attr.getId();
                        default -> {
                        }
                    }
                }
                if (modeId == 0 || oilTypeId == 0 || saeGradeId == 0) {
                    throw BusinessException.of("Invalid product attribute");
                }
                hashMap.computeIfAbsent(product.getBrandId(), k -> new HashMap<>())
                        .computeIfAbsent(modeId, k -> new HashMap<>())
                        .computeIfAbsent(oilTypeId, k -> new HashMap<>())
                        .computeIfAbsent(saeGradeId, k -> new ArrayList<>()).add(product);
            }
            ProductSimpleDTO productSimpleDTO = null;
            List<ProductSimpleDTO> productSimpleDTOS = getProductSimplesByProductSpecifications(carId, getLists(hashMap));
            for (ProductSimpleDTO simple : productSimpleDTOS) {
                if (productSimpleDTO == null || productSimpleDTO.getPrice().compareTo(simple.getPrice()) > 0) {
                    productSimpleDTO = simple;
                    productSimpleDTO.setProductSpecifications(simple.getProductSpecifications());
                }
            }
            if (productSimpleDTO == null) {
                throw BusinessException.of("Not found engine oil");
            }
            List<ProductSimpleDTO> products2 = new ArrayList<>(detail.getProducts());
            products2.add(0, productSimpleDTO);
            detail.setProducts(products2);
            resp.add(detail);
        }
        for (var v : resp) {
            setPriceToPackageDetailDTO(v);
        }
        return resp;
    }

    @NotNull
    private static List<List<ProductDTO>> getLists(HashMap<Long, HashMap<Long, HashMap<Long, HashMap<Long, List<ProductDTO>>>>> hashMap) {
        List<List<ProductDTO>> groups = new ArrayList<>();
        for (var brandMap : hashMap.entrySet()) {
            for (var modeMap : brandMap.getValue().entrySet()) {
                for (var oilTypeMap : modeMap.getValue().entrySet()) {
                    for (var saeGradeMap : oilTypeMap.getValue().entrySet()) {
                        List<ProductDTO> group = saeGradeMap.getValue();
                        groups.add(group);
                    }
                }
            }
        }
        return groups;
    }

    private List<ProductSimpleDTO> getProductSimplesByProductSpecifications(Long carId, List<List<ProductDTO>> groups) {
        List<CompletableFuture<ProductSimpleDTO>> futures = groups.stream()
                .map(group -> CompletableFuture.supplyAsync(() -> {
                    ProductSpecifications productSpecifications = productService.getRecommendProductsByProducts(carId, group);
                    return getProductSimpleByProductSpecifications(0L, productSpecifications);
                }, executorService)).toList();

        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    private ProductSimpleDTO getProductSimpleByProductSpecifications(Long mainProductId, ProductSpecifications ps) {
        if (CollectionUtils.isEmpty(ps.getList())) {
            throw BusinessException.of("Invalid product specifications");
        }
        if (mainProductId == null || mainProductId == 0) {
            mainProductId = ps.getList().get(0).getId();
        }
        var product = productService.getProduct(mainProductId);
        return ProductSimpleDTO.builder().
                id(product.getId()).
                categoryId(product.getCategoryId()).
                categoryName(product.getCategoryName()).
                isOil(product.getCategoryName().equals(CATEGORY_ENGINE_OIL)).
                name(product.getName()).
                mainImage(product.getMainImage()).
                featuredTags(product.getFeaturedTags()).
                price(ps.getPrice()).
                maxNetContent(MAX_NET_CONTENT).
                carNetContent(ps.getCarNetContent()).
                netContent(ps.getNetContent()).
                contentUnit(ps.getContentUnit()).
                productSpecifications(ps.getList()).
                build();
    }

    private PackageDetailDTO getPackageDetailIgnoreEngineOil(long id, Long productId,
                                                             Long carId, List<ProductDTO> dstProducts) {
        var packageInfo = packageInfoMapper.selectByPrimaryKey(id);
        if (packageInfo == null) {
            throw BusinessException.of("Invalid package id");
        }
        PackageDetailDTO resp = PackageDetailDTO.builder().
                id(packageInfo.getId()).
                name(packageInfo.getName()).
                isFixedPrice(YesOrNo.yes(packageInfo.getIsFixedPrice())).
                price(packageInfo.getPrice()).
                products(new ArrayList<>()).
                build();
        buildProductSimpleIgnoreEngineOil(carId, packageInfo, resp, productId, dstProducts);
        buildService(packageInfo, resp);
        return resp;
    }

    private void setPriceToPackageDetailDTO(PackageDetailDTO dst) {
        if (dst.getIsFixedPrice()) {
            return;
        }
        BigDecimal totalPrice = BigDecimal.valueOf(0);
        if (dst.getServiceDTO() != null && dst.getServiceDTO().getFee() != null) {
            totalPrice = totalPrice.add(dst.getServiceDTO().getFee());
        }
        for (var v : dst.getProducts()) {
            BigDecimal price = v.getPrice();
            totalPrice = totalPrice.add(price);
        }
        dst.setPrice(totalPrice);
    }

    private void buildProductSimpleIgnoreEngineOil(Long carId, PackageInfo src, PackageDetailDTO dst,
                                                   Long productId, List<ProductDTO> dstProducts) {
        List<Long> productIds = packageProductMapper.selectByPackageId(src.getId()).stream()
                .map(PackageProduct::getProductId)
                .collect(Collectors.toCollection(ArrayList::new));

        List<ProductDTO> products = productService.getProducts(QueryProductsDTO.builder().
                ids(productIds).carId(carId).status(SaleStatus.ACTIVE.getCode()).build());
        if (CollectionUtils.isEmpty(products)) {
            return;
        }

        ProductDTO product = new ProductDTO();
        if (!Objects.equals(productId, 0L)) {
            for (var v : products) {
                if (v.getId().equals(productId)) {
                    product = v;
                    break;
                }
            }
        }

        List<ProductSimpleDTO> productSimpleDTOS = new ArrayList<>();

        // group by category
        Map<Long, List<ProductDTO>> productMap = products.stream().collect(Collectors.groupingBy(ProductDTO::getCategoryId));

        // build price and category ids
        for (var category : productMap.entrySet()) {
            List<ProductDTO> v = category.getValue().stream().sorted(Comparator.comparing(ProductDTO::getPrice)).toList();
            var product2 = v.get(0);
            if (product2.getCategoryName().equals(CATEGORY_ENGINE_OIL)) {   // 忽略机油
                continue;
            }
            Long categoryId2 = category.getKey();
            if (categoryId2.equals(product.getCategoryId())) {  // 不做推荐
                productSimpleDTOS.add(ProductSimpleFactory.convert(product));
            } else if (CollectionUtils.isEmpty(dstProducts)) {    // 根据价格推荐
                productSimpleDTOS.add(ProductSimpleFactory.convert(product2));
            } else {    // 走商品替换的逻辑
                boolean success = false;
                for (var product3 : dstProducts) {
                    if (categoryId2.equals(product3.getCategoryId())) {
                        productSimpleDTOS.add(ProductSimpleFactory.convert(product3));
                        success = true;
                    }
                }
                if (!success) {
                    throw BusinessException.of("Invalid dst product id, {}", dstProducts.stream().map(ProductDTO::getId).toString());
                }
            }
            dst.setProducts(productSimpleDTOS.stream().sorted(Comparator.comparing(ProductSimpleDTO::getCategoryId)).toList());
        }
    }

    private void buildService(PackageInfo src, PackageDetailDTO dst) {
        if (src.getServiceId().equals(0L)) {
            return;
        }
        var serviceInfo = serviceInfoMapper.selectByPrimaryKey(src.getServiceId());
        if (serviceInfo == null) {
            throw BusinessException.of("Invalid service id");
        }
        dst.setServiceDTO(ServiceDTO.builder().
                id(serviceInfo.getId()).
                name(serviceInfo.getName()).
                fee(serviceInfo.getFee()).
                isRequired(YesOrNo.yes(serviceInfo.getIsRequired())).
                build());
    }

    private List<TypeNameDTO> getDeliveryModes(PackageInfo src) {
        List<TypeNameDTO> list = new ArrayList<>();
        for (int i = 0; (1 << i) < DeliveryMode.MAX_ENUM.getCode(); i++) {
            int j = 1 << i;
            if ((src.getDeliveryModes() & j) > 0) {
                list.add(TypeNameDTO.builder().type(j).name(DeliveryMode.getByCode(j).getDesc()).build());
            }
        }
        return list;
    }

    @PreDestroy
    public void destroy() {
        if (!executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }
}
