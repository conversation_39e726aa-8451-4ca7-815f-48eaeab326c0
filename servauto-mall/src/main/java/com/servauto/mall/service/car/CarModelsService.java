package com.servauto.mall.service.car;


import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarModels;

import java.util.List;
import java.util.Map;

public interface CarModelsService {

    // 新增方法：根据品牌ID查询所有有效的车型
    List<CarKeyValueDTO> selectAllModelsByBrandIdKv(Long brandId);

    Map<Long, CarModels> selectCarModelsMap(List<Long> modelIds);

}