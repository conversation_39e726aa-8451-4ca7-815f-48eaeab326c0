package com.servauto.mall.service.payment;

import com.servauto.mall.model.dto.request.payment.CallBackPaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.CreatePaymentReqDTO;
import com.servauto.mall.model.dto.response.payment.PaymentResDTO;

public interface PaymentFactoryServices {
    PaymentResDTO createPayment(CreatePaymentReqDTO createPaymentReqDTO);

    PaymentResDTO queryPaymentByPayNo(String payNo);

    PaymentResDTO queryPaymentStatusByNo(String payNo);

    void batchQueryPaymentStatus();

    void paymentCallBack(CallBackPaymentReqDTO callBackPaymentReqDTO);
}