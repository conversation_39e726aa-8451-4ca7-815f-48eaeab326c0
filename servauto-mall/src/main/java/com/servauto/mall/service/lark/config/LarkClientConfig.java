package com.servauto.mall.service.lark.config;

import com.lark.oapi.Client;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LarkClientConfig {

    @Resource
    private LarkProperties larkProperties;

    @Bean
    public Client client(){
        return  Client.newBuilder(larkProperties.getAppId(), larkProperties.getAppSecret()).build();
    }

}
