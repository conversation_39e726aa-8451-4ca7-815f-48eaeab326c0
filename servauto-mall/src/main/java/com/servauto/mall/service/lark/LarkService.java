package com.servauto.mall.service.lark;

import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserResp;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.servauto.mall.service.lark.dto.*;

public interface LarkService {

    /**
     * 创建多维表格
     * @param tableName
     * @return
     */
    CreateAppResp createAppAndTables(String tableName);

    /**
     * 多维表格名字修改
     * @param newTableName
     * @param appToken
     * @return
     */
    UpdateAppResp updateAppAndTables(String newTableName,String appToken);


    /**
     * 获取指定多维表格的元数据信息，包括多维表格名称，多维表格版本号，多维表格是否开启高级权限等
     * @param appToken
     */
    GetAppResp getAppAndTablesByAppToken(String appToken);

    /**
     * 新增多条记录
     * @deprecated 该接口用于在数据表中新增多条记录，单次调用最多新增 500 条记录
     */
    BatchCreateAppTableRecordResp batchCreateAppTableRecord(BatchCreateTableRecordDTO batchCreateTableRecordDTO);



    /**
     * 该接口用于更新数据表中的多条记录，单次调用最多更新 500 条记录。
     *
     */
    BatchUpdateAppTableRecordResp batchUpdateAppTableRecord(BatchUpdateTableRecordDTO batchUpdateTableRecordDTO);


    /**
     * 该接口用于列出数据表中的现有记录，单次最多列出 500 行记录，支持分页获取。
     */
    ListAppTableRecordResp queryTableRecords(QueryTableRecordsDTO query);

    /**
     * 获取自建应用访问令牌
     * @return
     */
    String  getAccessToken();

    /**
     * 根据手机号或邮箱批量获取用户 ID
     * @param emails
     * @param mobiles
     * @return
     */
    BatchGetIdUserResp batchGetUserId(String[] emails,String[] mobiles);

    /**
     * 根据用户名获取用户信息
     * @param name
     * @return
     */
    GetUserInfoByNameResp getUserInfoByName(String name);

    CopyAppResp copyAppTable(String appToken,String newAppName);

    /**
     * 向单个用户发送消息
     * @param userId
     * @param content
     *  @param msgType 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考发送消息内容
     * @return
     */
    CreateMessageResp createMessage(String userId, String content , String msgType);

    /**
     * 批量获取用户信息
     * @param userIds
     * @return
     */
    BatchUserResp batchGetUserInfo(String[] userIds);

    /**
     * 批量发送消息
     * @param batchSendMessageDTO
     * @return
     */
    BatchSendMessageResp batchSendMessage(BatchSendMessageDTO batchSendMessageDTO);

}
