package com.servauto.mall.service.order;

import com.github.pagehelper.PageInfo;
import com.servauto.mall.model.dto.request.order.*;
import com.servauto.mall.model.dto.response.order.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface TradeService {

    /**
     * create order
     *
     * @param customerId        customerId
     * @param createOrderReqDTO createOrder request
     * @return CreateOrderDTO
     */
    CreateOrderDTO createOrder(Long customerId, CreateOrderReqDTO createOrderReqDTO);

    /**
     * query create order
     *
     * @param customerId customerId
     * @param orderNo    orderNo
     * @return CreateOrderDTO
     */
    CreateOrderDTO queryCreateOrder(Long customerId, String orderNo);

    /**
     * submit order
     *
     * @param customerId        customerId
     * @param submitOrderReqDTO submitOrderReqDTO
     * @return SubmitOrderDTO
     */
    SubmitOrderDTO submitOrder(Long customerId, SubmitOrderReqDTO submitOrderReqDTO);

    /**
     * query orders
     *
     * @param queryOrderReqDTO queryOrderReqDTO
     * @return List<OrderDTO>
     */
    PageInfo<OrderDTO> pageOrders(QueryOrderReqDTO queryOrderReqDTO);

    /**
     * query orders
     *
     * @param queryOrderReqDTO queryOrderReqDTO
     * @return List<OrderDTO>
     */
    List<OrderDTO> queryOrders(QueryOrderReqDTO queryOrderReqDTO);

    /**
     * query orders
     *
     * @param orderNo orderNo
     * @return OrderDTO
     */
    OrderDTO queryOrderByOrderNo(Long customerId, String orderNo);

    /**
     * on payment success
     *
     * @param orderNo    orderNo
     * @param payNo      payNo
     * @param paidAmount paidAmount
     * @param paidTime   paidTime
     */
    void onPaymentSuccess(String orderNo, String payNo, BigDecimal paidAmount, Date paidTime);

    /**
     * on payment failed
     *
     * @param orderNo   orderNo
     * @param payNo     payNo
     * @param errorMsg  errorMsg
     * @param errorCode errorCode
     */
    void onPaymentFailed(String orderNo, String payNo, String errorCode, String errorMsg);

    /**
     * on payment timeout
     *
     * @param orderNo orderNo
     * @param payNo   payNo
     */
    void onPaymentTimeout(String orderNo, String payNo);

    /**
     * query order workshops
     *
     * @param reqDTO reqDTO
     * @return Support Workshops
     */
    List<OrderWorkshopDTO> queryOrderWorkshops(OrderWorkshopReqDTO reqDTO);

    /**
     * query order workshop opening-hours
     *
     * @param customerId user
     * @param orderNo    orderNo
     * @param workshopId workshop
     * @return workshop
     */
    OrderWorkshopDTO queryOrderWorkshopOpeningHours(Long customerId, String orderNo, Long workshopId);

    /**
     * reservation order
     *
     * @param customerId customerId
     * @param reqDTO     reqDTO
     */
    void reservationOrder(Long customerId, ReservationOrderReqDTO reqDTO);

    /**
     * reschedule order
     *
     * @param customerId customerId
     * @param reqDTO     reqDTO
     */
    void rescheduleOrder(Long customerId, RescheduleOrderReqDTO reqDTO);

    /**
     * cancel order
     *
     * @param customerId customerId
     * @param orderNo    orderNo
     */
    void cancelOrder(Long customerId, String orderNo);

    /**
     * order cashier
     *
     * @param customerId customerId
     * @param orderNo    orderNo
     * @param payNo      payNo
     * @return cashier result vo
     */
    CashierDTO cashier(Long customerId, String orderNo, String payNo);
}
