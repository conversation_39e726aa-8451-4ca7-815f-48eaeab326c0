package com.servauto.mall.service.payment.impl;

import com.servauto.mall.service.payment.PaymentFactory;
import com.servauto.mall.service.payment.PaymentFactoryServices;
import com.servauto.mall.support.payment.config.Ipay88PaymentProperties;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class PaymentFactoryImpl implements PaymentFactory {

    @Resource
    private Ipay88PaymentProperties ipay88PaymentProperties;

    // 新增：注入 ApplicationContext 用于手动注入依赖
    @Resource
    private ApplicationContext applicationContext;

    @Override
    public PaymentFactoryServices createPaymentServices(String paymentChannel) {
        PaymentFactoryServices service = getFactoryByReflection(paymentChannel);
        if (service != null) {
            return service;
        } else {
            throw new IllegalArgumentException("Unsupported payment channel: " + paymentChannel);
        }
    }

    /**
     * 通过反射获取支付渠道的具体实现类
     *
     * @param paymentChannel 支付渠道名称
     * @return 支付渠道的具体实现类实例
     */
    public PaymentFactoryServices getFactoryByReflection(String paymentChannel) {
        try {
            // 构造实现类的全限定类名
            String className = "com.servauto.mall.service.payment.impl." + 
                               paymentChannel.substring(0, 1).toUpperCase() + 
                               paymentChannel.substring(1).toLowerCase() + "PaymentFactoryServicesImpl";
            // 通过反射加载类
            Class<?> clazz = Class.forName(className);
            // 创建实例
            PaymentFactoryServices service = (PaymentFactoryServices) clazz.getDeclaredConstructor().newInstance();

            // 新增：手动注入依赖
            applicationContext.getAutowireCapableBeanFactory().autowireBean(service);

            return service;
        } catch (Exception e) {
            throw new IllegalArgumentException("Unsupported payment channel or reflection failed: " + paymentChannel, e);
        }
    }
}