package com.servauto.mall.service.lark.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AccessTokenDTO {

    @JsonProperty("code")
    private  Integer code;

    @JsonProperty("expire")
    private Integer expire;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("app_access_token")
    private String appAccessToken;

    @JsonProperty("tenant_access_token")
    private String tenantAccessToken;

}
