package com.servauto.mall.service.car.impl;

import com.servauto.mall.dao.car.CarExtraMapper;
import com.servauto.mall.dao.car.CarInfoMapper;
import com.servauto.mall.dao.car.CarLibraryMapper;
import com.servauto.mall.model.dto.request.customer.CreateCarInfoReqDTO;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.model.entity.car.*;
import com.servauto.mall.service.car.*;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

@Service
public class CarInfoServiceImpl implements CarInfoService {

    @Resource
    private CarInfoMapper carInfoMapper;
    @Resource
    private CarExtraMapper carExtraMapper;
    @Resource
    private CarLibraryMapper carLibraryMapper;

    @Resource
    private CarBrandsService carBrandsService;
    @Resource
    private CarModelsService carModelsService;
    @Resource
    private CarYearsService carYearsService;
    @Resource
    private CarExtraService carExtraService;
    @Resource
    private TransactionTemplate transactionTemplate;
    // 2. 定义字段名常量，减少硬编码
    final String BRAND_IDS = "brandIds";
    final String MODEL_IDS = "modelIds";
    final String YEAR_IDS = "yearIds";
    final String VARIANT_IDS = "variantIds";

    @Override
    public List<CarInfo> selectByCustomerId(Long customerId) {
        return carInfoMapper.selectByCustomerId(customerId);
    }

    @Override
    public int deleteByCustomerId(Long customerId) {
        return carInfoMapper.deleteByCustomerId(customerId);
    }

    @Override
    public void addNewCar(Long customerId, CreateCarInfoReqDTO createCarInfoReqDTO) {
        // 根据 variantID 查询 carExtra 并根据 VariantId 放到 map 中
        CarExtra carExtra = carExtraMapper.selectCarExtraById(createCarInfoReqDTO.getVariantId());

        CarInfoWithBLOBs carInfo = new CarInfoWithBLOBs();
        carInfo.setCustomerId(customerId);
        carInfo.setCarLibId(carExtra.getCarLibId());
        carInfo.setBrandId(carExtra.getBrandId());
        carInfo.setModelId(carExtra.getModelId());
        carInfo.setYearId(carExtra.getYearId());
        carInfo.setVariantId(carExtra.getId());
        carInfo.setLicensePlate(createCarInfoReqDTO.getLicensePlate());
        carInfo.setCarMileage(createCarInfoReqDTO.getCarMileage());
        carInfo.setStatus((byte) 1);
        carInfo.setDefaultCar(Boolean.TRUE);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                carInfoMapper.clearDefaultCar(customerId);
                carInfoMapper.insertSelective(carInfo);
            }
        });

    }

    @Override
    public List<CarInfoDTO> carInfoList(Long customerId) {
        // 查询 carInfo 列表
        List<CarInfo> carInfoList = carInfoMapper.selectByCustomerId(customerId);
        if (carInfoList == null || carInfoList.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 提取 idMaps 和构建 CarInfoDTO
            return buildCarInfoDTOList(carInfoList);
        } catch (Exception e) {
            // 异常处理
            return Collections.emptyList();
        }
    }

    @Override
    public CarInfoDTO selectCarInfoById(Long id) {
        CarInfo carInfo = carInfoMapper.selectByPrimaryKey(id);
        if (carInfo == null) {
            throw new IllegalArgumentException("CarInfo with id " + id + " does not exist.");
        }

        List<CarInfo> carInfoList = Collections.singletonList(carInfo);
        try {
            // 提取 idMaps 和构建 CarInfoDTO
            List<CarInfoDTO> carInfoDTOList = buildCarInfoDTOList(carInfoList);
            if (carInfoDTOList.isEmpty()) {
                throw new IllegalStateException("Failed to build CarInfoDTO for id " + id);
            }
            return carInfoDTOList.get(0);
        } catch (Exception e) {
            // 异常处理
            throw new RuntimeException("Error building CarInfoDTO for id " + id, e);
        }
    }

    @Override
    public void deleteByCustomerIdAndCarId(Long customerId, Long carId) {
        carInfoMapper.deleteByCustomerIdAndCarId(customerId, carId);
    }

    @Override
    public void updateLicenseByCustomerIdAndCarId(Long customerId, Long carId, String licensePlate) {
        CarInfoWithBLOBs carInfo = new CarInfoWithBLOBs();
        carInfo.setLicensePlate(licensePlate);
        carInfo.setId(carId);
        carInfo.setCustomerId(customerId);
        carInfoMapper.updateByPrimaryKeySelective(carInfo);
    }

    @Override
    public void setDefaultCar(Long customerId, Long carId) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus transactionStatus) {
                //清空用户当前默认车
                carInfoMapper.clearDefaultCar(customerId);
                carInfoMapper.setDefaultCar(customerId, carId);
            }
        });

    }

    @Override
    public CarInfoDTO getDefaultCar(Long customerId) {
        CarInfo carInfo = carInfoMapper.getDefaultCar(customerId);
        if (carInfo == null) {
           return null;
        }

        List<CarInfo> carInfoList = Collections.singletonList(carInfo);
        try {
            // 提取 idMaps 和构建 CarInfoDTO
            List<CarInfoDTO> carInfoDTOList = buildCarInfoDTOList(carInfoList);
            if (carInfoDTOList.isEmpty()) {
                return null;
            }
            return carInfoDTOList.get(0);
        } catch (Exception e) {
            // 异常处理
            return null;
        }
    }

    @Override
    public Double getCarOilCapacity(Long carId) {
        CarInfo carInfo = carInfoMapper.selectByPrimaryKey(carId);
        if (carInfo == null) {
            return 6.00;
        }

        CarLibrary carLibrary = carLibraryMapper.selectByByLibId(carInfo.getCarLibId());
        if (carLibrary == null) {
            return 6.00;
        }

        Double oilCapacity = carLibrary.getOilCapacity();
        if (oilCapacity == null || oilCapacity <= 0) {
            return 6.00;
        }

        return oilCapacity;
    }

    private Map<String, List<Long>> extractIds(List<CarInfo> carInfoList) {
        // 1. 非空校验，防止 NullPointerException
        if (carInfoList == null) {
            throw new IllegalArgumentException("Input carInfoList cannot be null");
        }

        // 3. 初始化 Map 和字段对应的 Set（用于去重）
        Map<String, List<Long>> idMaps = new HashMap<>();
        Set<Long> brandIdsSet = new HashSet<>();
        Set<Long> modelIdsSet = new HashSet<>();
        Set<Long> yearIdsSet = new HashSet<>();
        Set<Long> variantIdsSet = new HashSet<>();

        // 4. 使用 Stream API 提高代码简洁性和性能
        carInfoList.forEach(carInfo -> {
            if (carInfo.getBrandId() != null) {
                brandIdsSet.add(carInfo.getBrandId());
            }
            if (carInfo.getModelId() != null) {
                modelIdsSet.add(carInfo.getModelId());
            }
            if (carInfo.getYearId() != null) {
                yearIdsSet.add(carInfo.getYearId());
            }
            if (carInfo.getVariantId() != null) {
                variantIdsSet.add(carInfo.getVariantId());
            }
        });

        // 5. 将 Set 转换为 List 并添加到 Map 中
        idMaps.put(BRAND_IDS, new ArrayList<>(brandIdsSet));
        idMaps.put(MODEL_IDS, new ArrayList<>(modelIdsSet));
        idMaps.put(YEAR_IDS, new ArrayList<>(yearIdsSet));
        idMaps.put(VARIANT_IDS, new ArrayList<>(variantIdsSet));

        return idMaps;
    }

    // 辅助方法：获取 ID 列表并处理空值
    private List<Long> getIdList(Map<String, List<Long>> idMaps, String key) {
        List<Long> idList = idMaps.get(key);
        return (idList != null) ? idList : Collections.emptyList();
    }

    // 辅助方法：构建 CarInfoDTO 列表
    private List<CarInfoDTO> buildCarInfoDTOList(List<CarInfo> carInfoList) {
        // 提取 carLibIds 和 brandIds
        Map<String, List<Long>> idMaps = extractIds(carInfoList);
        List<Long> brandIds = getIdList(idMaps, BRAND_IDS);
        List<Long> modelIds = getIdList(idMaps, MODEL_IDS);
        List<Long> yearIds = getIdList(idMaps, YEAR_IDS);
        List<Long> variantIds = getIdList(idMaps, VARIANT_IDS);

        Map<Long, CarBrands> carBrandsMap = carBrandsService.selectCarBrandsMap(brandIds);
        Map<Long, CarModels> carModelsMap = carModelsService.selectCarModelsMap(modelIds);
        Map<Long, CarYears> carYearsMap = carYearsService.selectCarYearsMap(yearIds);
        Map<Long, CarExtra> carExtrasMap = carExtraService.selectVariantsMap(variantIds);

        // 拼装数据
        return carInfoList.stream().map(carInfo -> {
            CarBrands carBrand = carBrandsMap.get(carInfo.getBrandId());
            CarModels carModels = carModelsMap.get(carInfo.getModelId());
            CarYears carYears = carYearsMap.get(carInfo.getYearId());
            CarExtra carExtra = carExtrasMap.get(carInfo.getVariantId());

            CarInfoDTO carInfoDTO = new CarInfoDTO();
            carInfoDTO.setBrand(carBrand.getBrandName());
            carInfoDTO.setModel(carModels.getModelName());
            carInfoDTO.setYear(carYears.getYearValue());
            carInfoDTO.setVariant(carExtra.getVariantName());
            carInfoDTO.setIcon(carBrand.getIcon());
            carInfoDTO.setCarId(carInfo.getId());
            carInfoDTO.setLicensePlate(carInfo.getLicensePlate());
            carInfoDTO.setTransmissionType(carExtra.getTransmissionType());
            carInfoDTO.setDefaultCar(carInfo.getDefaultCar());
            return carInfoDTO;
        }).toList();
    }
}