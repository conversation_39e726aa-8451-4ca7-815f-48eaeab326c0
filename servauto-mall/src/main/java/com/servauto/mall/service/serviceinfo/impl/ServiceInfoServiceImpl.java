package com.servauto.mall.service.serviceinfo.impl;

import com.servauto.common.exception.BusinessException;
import com.servauto.mall.dao.packageinfo.PackageInfoMapper;
import com.servauto.mall.dao.product.ProductMapper;
import com.servauto.mall.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.mall.dao.serviceinfo.ServiceWorkshopMapper;
import com.servauto.mall.enums.ServiceSupportedOn;
import com.servauto.mall.factory.serviceinfo.ServiceFactory;
import com.servauto.mall.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.mall.model.entity.packageinfo.PackageInfo;
import com.servauto.mall.model.entity.product.Product;
import com.servauto.mall.model.entity.serviceinfo.ServiceWorkshop;
import com.servauto.mall.service.serviceinfo.ServiceInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ServiceInfoServiceImpl implements ServiceInfoService {

    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private ServiceWorkshopMapper serviceWorkshopMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private PackageInfoMapper packageInfoMapper;

    public ServiceInfoDTO getService(long id) {
        if (id == 0) {
            throw BusinessException.of("Invalid service id");
        }
        var serviceInfo = serviceInfoMapper.selectByPrimaryKey(id);
        if (serviceInfo == null) {
            throw BusinessException.of("Invalid service id");
        }
        var serviceInfoDTO = ServiceFactory.convert(serviceInfo);

        serviceInfoDTO.setWorkshopIds(serviceWorkshopMapper.selectByServiceId(id).stream().map(ServiceWorkshop::getWorkshopId).toList());

        switch (ServiceSupportedOn.getByCode(serviceInfo.getSupportedOn())) {
            case UNKNOWN:
                break;
            case PRODUCT:
                var products = productMapper.selectByConditions(QueryProductsDTO.builder().serviceId(id).build());
                if (CollectionUtils.isNotEmpty(products)) {
                    serviceInfoDTO.setProductIds(products.stream().map(Product::getId).toList());
                }
                break;
            case PACKAGE:
                var packages = packageInfoMapper.selectByConditions(QueryPackagesDTO.builder().serviceId(id).build());
                if (CollectionUtils.isNotEmpty(packages)) {
                    serviceInfoDTO.setPackageIds(packages.stream().map(PackageInfo::getId).toList());
                }
                break;
            default:
                log.warn("unknown service supported on: {}", serviceInfo.getSupportedOn());
                break;
        }
        return serviceInfoDTO;
    }

    public List<ServiceInfoDTO> getServices(QueryServicesDTO reqDTO) {
        if (reqDTO.getWorkshopId() != null && reqDTO.getWorkshopId() > 0) {
            var serviceWorkshops = serviceWorkshopMapper.selectByServiceId(reqDTO.getWorkshopId());
            if (CollectionUtils.isNotEmpty(serviceWorkshops)) {
                reqDTO.setIds(serviceWorkshops.stream().map(ServiceWorkshop::getServiceId).distinct().toList());
            }
        }
        return serviceInfoMapper.selectByConditions(reqDTO).stream().map(ServiceFactory::convert).toList();
    }
}
