package com.servauto.mall.service.car.impl;


import com.servauto.mall.dao.car.CarModelsMapper;
import com.servauto.mall.factory.car.CarModelsFactory;
import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarBrands;
import com.servauto.mall.model.entity.car.CarModels;
import com.servauto.mall.service.car.CarModelsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CarModelsServiceImpl implements CarModelsService {

    @Resource
    private CarModelsMapper carModelsMapper;


    @Override
    public List<CarKeyValueDTO> selectAllModelsByBrandIdKv(Long brandId) {
        List<CarModels> carModelsList = carModelsMapper.selectAllModelsByBrandId(brandId);
        return carModelsList.stream().map(CarModelsFactory::convertKv).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CarModels> selectCarModelsMap(List<Long> modelIds) {
        // 如果输入为空，直接返回空Map
        if (modelIds == null || modelIds.isEmpty()) {
            // 使用不可变的空Map，避免不必要的对象创建
            return Collections.emptyMap();
        }

        // 调用数据库查询方法，并检查返回值是否为null
        List<CarModels> carModels = carModelsMapper.selectByPrimaryKeys(modelIds);
        if (carModels == null) {
            // 数据库查询结果为空时，返回空Map
            return Collections.emptyMap();
        }

        // 使用Stream API构建Map，确保id不为null 过滤掉无效的carModel
        return carModels.stream().filter(carModel -> carModel != null && carModel.getId() != null).collect(Collectors.toMap(CarModels::getId, carModel -> carModel, (existing, replacement) -> existing));

    }

}