package com.servauto.mall.service.car.impl;


import com.servauto.mall.dao.car.CarYearsMapper;
import com.servauto.mall.factory.car.CarYearsFactory;
import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.model.entity.car.CarModels;
import com.servauto.mall.model.entity.car.CarYears;
import com.servauto.mall.service.car.CarYearsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CarYearsServiceImpl implements CarYearsService {

    @Resource
    private CarYearsMapper carYearsMapper;

    @Override
    public List<CarKeyValueDTO> getCarYearsByModelIdKv(Long modelId) {
        List<CarYears> carYearsList = carYearsMapper.selectByModelId(modelId);
        return carYearsList.stream().map(CarYearsFactory::convertKv).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CarYears> selectCarYearsMap(List<Long> yearsIds) {
        // 如果输入为空，直接返回空Map
        if (yearsIds == null || yearsIds.isEmpty()) {
            // 使用不可变的空Map，避免不必要的对象创建
            return Collections.emptyMap();
        }

        // 调用数据库查询方法，并检查返回值是否为null
        List<CarYears> carYears = carYearsMapper.selectByPrimaryKeys(yearsIds);
        if (carYears == null) {
            // 数据库查询结果为空时，返回空Map
            return Collections.emptyMap();
        }

        // 使用Stream API构建Map，确保id不为null 过滤掉无效的carYear
        return carYears.stream().filter(carYear -> carYear != null && carYear.getId() != null).collect(Collectors.toMap(CarYears::getId, carYear -> carYear, (existing, replacement) -> existing));
    }
}