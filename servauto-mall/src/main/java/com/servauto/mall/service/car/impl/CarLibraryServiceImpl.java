package com.servauto.mall.service.car.impl;

import com.servauto.mall.dao.car.CarBrandsMapper;
import com.servauto.mall.dao.car.CarLibraryMapper;
import com.servauto.mall.service.car.CarLibraryService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class CarLibraryServiceImpl implements CarLibraryService {

    @Resource
    private CarLibraryMapper carLibraryMapper;

    @Resource
    private CarBrandsMapper carBrandsMapper;
/*
    @Override
    public CarLibraryDTO getCarLibraryByLibId(String carLibId) {
        CarLibrary carLibrary = carLibraryMapper.selectByByLibId(carLibId);
        return CarLibraryFactory.convert(carLibrary);
    }

    @Override
    public CarLibraryDTO getCarLibraryById(Integer id) {
        CarLibrary carLibrary = carLibraryMapper.selectByPrimaryKey(id);
        return CarLibraryFactory.convert(carLibrary);
    }

    @Override
    public void insertCarLibrary(CarLibrary carLibrary) {
        carLibraryMapper.insert(carLibrary);
    }

    @Override
    public void updateCarLibrary(CarLibrary carLibrary) {
        carLibraryMapper.updateByPrimaryKeySelective(carLibrary);
    }

    @Override
    public void deleteCarLibrary(Integer id) {
        carLibraryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<CarLibrary> selectWaitSyncDistinctBrands() {
        return carLibraryMapper.selectWaitSyncDistinctBrands();
    }

    // 实现获取所有去重后的品牌的方法
    @Override
    public List<String> selectAllBrandsDistinct() {
        return carLibraryMapper.selectAllBrandsDistinct();
    }

    // 实现根据 brand 获取所有去重后的 model 的方法
    @Override
    public List<String> selectModelsByBrandDistinct(String brand) {
        return carLibraryMapper.selectModelsByBrandDistinct(brand);
    }

    // 实现新增的方法
    @Override
    public List<String> selectModelYearsByBrandAndModelDistinct(String brand, String model) {
        return carLibraryMapper.selectModelYearsByBrandAndModelDistinct(brand, model);
    }

    //根据brand、model、model_year和grade获取carlibrary的集合
    @Override
    public List<CarLibrary> selectCarLibrariesByBrandModelYearAndGrade(String brand, String model, String modelYear, String grade) {
        return carLibraryMapper.selectCarLibrariesByBrandModelYearAndGrade(brand, model, modelYear, grade);
    }

    @Override
    public void updateCarLibraryStatusByIds(List<Integer> ids) {
        carLibraryMapper.updateCarLibraryStatusByIds(ids);
    }


    // 根据brand、model和model_year获取去重后的grade集合
    @Override
    public List<String> selectVariantByBrandModelAndYearDistinct(String brand, String model, String modelYear) {
        return carLibraryMapper.selectVariantByBrandModelAndYearDistinct(brand, model, modelYear);
    }*/
}