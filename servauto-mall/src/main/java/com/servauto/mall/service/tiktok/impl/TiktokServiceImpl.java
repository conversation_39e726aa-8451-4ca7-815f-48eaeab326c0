package com.servauto.mall.service.tiktok.impl;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.ListAppTableRecordResp;
import com.servauto.common.constant.TiktokConstants;
import com.servauto.common.utils.StringUtils;
import com.servauto.mall.model.dto.response.tiktok.TiktokOrdersRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokProductInfoRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokShopSecretsRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokTokenRespDTO;
import com.servauto.mall.service.lark.LarkService;
import com.servauto.mall.service.lark.dto.*;
import com.servauto.mall.service.tiktok.TiktokService;
import com.servauto.mall.support.tiktok.config.TiktokProperties;
import com.servauto.mall.support.utils.SignatureUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TiktokServiceImpl implements TiktokService {

    @Resource
    private TiktokProperties tiktokProperties;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private LarkService larkService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ThreadPoolTaskExecutor httpRequestThreadExecutor;

    @Resource
    private ThreadPoolTaskExecutor messageSendExecutor;

    private final OkHttpClient httpClient = new OkHttpClient();

    private static final String TOKEN_URL = "https://auth.tiktok-shops.com/api/v2/token/get";
    private static final String SHOP_SECRETS_URL = "https://open-api.tiktokglobalshop.com/authorization/202309/shops";
    private static final String ORDERS_URL = "https://open-api.tiktokglobalshop.com/order/202309/orders/search";
    private static final String PRODUCT_DETAIL_URL = "https://open-api.tiktokglobalshop.com/product/202309/products";

    @Override
    public TiktokTokenRespDTO getAccessToken(String authCode) {
        try {
            log.info("Getting TikTok access token with code: {}", authCode);
            Request request = getAccessTokenRequest(authCode);
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("TikTok API response: {}", responseBody);
                    TiktokTokenRespDTO result = objectMapper.readValue(responseBody, TiktokTokenRespDTO.class);
                    if (result.getCode() != null && result.getCode() != 0) {
                        log.error("TikTok token API error: {}", result.getMessage());
                        throw new RuntimeException("TikTok token error: " + result.getMessage());
                    }
                    return result;
                } else {
                    log.error("Failed to get access token, status: {}, response: {}",
                            response.code(), response.body() != null ? response.body().string() : "null");
                    throw new RuntimeException("Failed to get TikTok access token");
                }
            }
        } catch (Exception e) {
            log.error("Error getting TikTok access token", e);
            throw new RuntimeException("Failed to get TikTok access token: " + e.getMessage());
        }
    }

    @Override
    public TiktokShopSecretsRespDTO getShopSecrets() {
        try {
            log.info("Getting TikTok shop secrets");

            // 从Redis获取access_token
            String accessToken = redisTemplate.opsForValue().get(TiktokConstants.TIKTOK_ACCESS_TOKEN);
            if (accessToken == null || accessToken.isEmpty()) {
                throw new RuntimeException("Access token not found in Redis");
            }

            // 构建初始请求URL（不含签名）
            String baseUrl = SHOP_SECRETS_URL +
                    "?app_key=" + tiktokProperties.getAppKey() +
                    "&timestamp=" + System.currentTimeMillis() / 1000;

            // 1. 构建初始请求（不含签名）
            Request.Builder requestBuilder = new Request.Builder()
                    .url(baseUrl)
                    .get()
                    .addHeader("content-type", "application/json")
                    .addHeader("x-tts-access-token", accessToken);
            // 2. 生成签名
            String sign = SignatureUtil.generateSignature(requestBuilder.build(), tiktokProperties.getAppSecret());
            log.info("Generated signature: {}", sign);

            // 3. 将签名添加到URL参数中
            String finalUrl = baseUrl + "&sign=" + sign;

            // 4. 更新请求URL并构建最终请求
            Request finalRequest = requestBuilder
                    .url(finalUrl)
                    .build();
            try (Response response = httpClient.newCall(finalRequest).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    TiktokShopSecretsRespDTO result = objectMapper.readValue(responseBody, TiktokShopSecretsRespDTO.class);
                    if (result.getCode() != null && result.getCode() != 0) {
                        log.error("TikTok shop secrets API error: code={}, message={}", result.getCode(), result.getMessage());
                        throw new RuntimeException("TikTok shop secrets API error: " + result.getMessage());
                    }
                    return result;
                } else {
                    log.error("Failed to get shop secrets, status: {}, response: {}",
                            response.code(), response.body() != null ? response.body().string() : "null");
                    throw new RuntimeException("Failed to get TikTok shop secrets");
                }
            }
        } catch (Exception e) {
            log.error("Error getting TikTok shop secrets", e);
            throw new RuntimeException("Failed to get TikTok shop secrets: " + e.getMessage());
        }
    }

    @Override
    public TiktokOrdersRespDTO getOrders(String pageToken, String pageSize, String orderStatus, Long createTimeGe, Long updateTimeGe, String shippingType) {
        try {
            log.info("Getting TikTok orders  ");
            // 从Redis获取access_token
            String accessTokenKey = TiktokConstants.TIKTOK_ACCESS_TOKEN;
            String accessToken = redisTemplate.opsForValue().get(accessTokenKey);

            if (accessToken == null || accessToken.isEmpty()) {
                throw new RuntimeException("Access token not found in Redis ");
            }

            String shopSecretsKey = TiktokConstants.TIKTOK_SHOP_SECRETS;
            Object shopSecretsData = redisTemplate.opsForValue().get(shopSecretsKey);
            TiktokShopSecretsRespDTO.ShopSecretsData shopSecret = objectMapper.convertValue(shopSecretsData, TiktokShopSecretsRespDTO.ShopSecretsData.class);

            if (shopSecret == null || shopSecret.getShops().isEmpty()) {
                throw new RuntimeException("shopSecret  not found in Redis ");
            }

            // 构建请求URL和参数
            StringBuilder urlBuilder = new StringBuilder(ORDERS_URL);
            urlBuilder.append("?app_key=").append(tiktokProperties.getAppKey());
            urlBuilder.append("&shop_cipher=").append(shopSecret.getShops().get(0).getCipher());
            urlBuilder.append("&timestamp=").append(System.currentTimeMillis() / 1000);

            // 添加可选参数
            if (pageToken != null && !pageToken.isEmpty()) {
                urlBuilder.append("&page_token=").append(pageToken);
            }
            if (pageSize != null) {
                urlBuilder.append("&page_size=").append(pageSize);
            } else {
                urlBuilder.append("&page_size=20");
            }
            String url = urlBuilder.toString();
            String requestBody = builderRequestBody(orderStatus, createTimeGe, updateTimeGe, shippingType);
            RequestBody body = RequestBody.create(requestBody, MediaType.get("application/json; charset=utf-8"));

            // 1. 构建初始请求
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("content-type", "application/json")
                    .addHeader("x-tts-access-token", accessToken);
            // 2. 生成签名
            String sign = SignatureUtil.generateSignature(requestBuilder.build(), tiktokProperties.getAppSecret());
            // 4. 将签名添加到URL参数中
            String finalUrl = url + "&sign=" + sign;
            // 5. 更新请求URL并构建最终请求
            Request finalRequest = requestBuilder
                    .url(finalUrl)
                    .build();
            try (Response response = httpClient.newCall(finalRequest).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    TiktokOrdersRespDTO result = objectMapper.readValue(responseBody, TiktokOrdersRespDTO.class);
                    if (result.getCode() != null && result.getCode() != 0) {
                        log.error("TikTok orders API error: code={}, message={}", result.getCode(), result.getMessage());
                        throw new RuntimeException("TikTok orders API error: " + result.getMessage());
                    }
                    return result;
                } else {
                    log.error("Failed to get orders, status: {}, response: {}",
                            response.code(), response.body() != null ? response.body().string() : "null");
                    throw new RuntimeException("Failed to get TikTok orders");
                }
            }
        } catch (Exception e) {
            log.error("Error getting TikTok orders", e);
            throw new RuntimeException("Failed to get TikTok orders: " + e.getMessage());
        }
    }


    @Override
    public Boolean tiktokOrdersSyncToLark(String appToken, String tableId, Long createTimeGe, Long updateTimeGe, String shippingType, String orderStatus) {
        String pageToken = null;
        String pageSize = "20";
        String lockKey = TiktokConstants.TIKTOK_ORDERS_SYNC_LARK_LOCK;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(2, TimeUnit.MINUTES)) {
                do {
                    // 获取TikTok订单数据
                    TiktokOrdersRespDTO dto = getOrders(pageToken, pageSize, orderStatus, createTimeGe, updateTimeGe, StringUtils.isEmpty(shippingType) ? TiktokConstants.SELLER : shippingType);
                    if (dto == null || dto.getData() == null || dto.getCode() != 0) {
                        log.warn("获取TikTok订单失败: code={}, msg={}",
                                dto != null ? dto.getCode() : null,
                                dto != null ? dto.getMessage() : "返回数据为空");
                        break;
                    }

                    List<TiktokOrdersRespDTO.Order> orders = dto.getData().getOrders();
                    if (CollectionUtils.isEmpty(orders)) {
                        log.info("当前页无订单数据，pageToken={}", pageToken);
                        break;
                    }
                    //构建商品id列表
                    List<String> productIds = new ArrayList<>();
                    dto.getData().getOrders().forEach(f -> {
                        List<TiktokOrdersRespDTO.LineItem> lineItems = f.getLineItems();
                        lineItems.forEach(t -> {
                            productIds.add(t.getProductId());
                        });
                    });
                    // 构建飞书表格查询条件
                    String filter = buildOrderIdFilter(orders);
                    // 查询飞书表格中已存在的订单记录
                    QueryTableRecordsDTO queryDto = new QueryTableRecordsDTO();
                    queryDto.setTableId(tableId);
                    queryDto.setAppToken(appToken);
                    queryDto.setPageSize(Integer.valueOf(pageSize));
                    queryDto.setFilter(filter);


                    CompletableFuture<ListAppTableRecordResp> larkCompletableFuture = CompletableFuture.supplyAsync(() -> larkService.queryTableRecords(queryDto), messageSendExecutor);
                    CompletableFuture<List<TiktokProductInfoRespDTO>> productCompletableFuture = CompletableFuture.supplyAsync(() -> getProductsInfo(productIds), messageSendExecutor);
                    CompletableFuture.allOf(larkCompletableFuture, productCompletableFuture);

                    ListAppTableRecordResp result = larkCompletableFuture.join();
                    List<TiktokProductInfoRespDTO> productResult = productCompletableFuture.join();
                    Map<String, String> packageMap = null;
                    Map<String, String> categoryMap = null;
                    if (!CollectionUtils.isEmpty(productResult)) {
                        packageMap = new HashMap<>();
                        categoryMap = new HashMap<>();
                        getProductsCategoryAndPackage(productResult, packageMap, categoryMap);
                    }
                    Map<String, String> orderIdToRecordId = new HashMap<>();
                    if (result != null && result.getCode() == 0 && result.getData() != null && result.getData().getItems() != null) {
                        for (AppTableRecord record : result.getData().getItems()) {
                            String orderId = extractOrderId(record);
                            if (orderId != null) {
                                orderIdToRecordId.put(orderId, record.getRecordId());
                            }
                        }
                    } else {
                        log.warn("查询飞书表格记录失败: code={}, msg={}",
                                result != null ? result.getCode() : null,
                                result != null ? result.getMsg() : "返回数据为空");
                    }

                    // 区分需要更新和新增的订单
                    Map<Boolean, List<TiktokOrdersRespDTO.Order>> partitionedOrders = orders.stream()
                            .collect(Collectors.partitioningBy(order -> orderIdToRecordId.containsKey(order.getId())));

                    List<TiktokOrdersRespDTO.Order> updateOrders = partitionedOrders.get(true);
                    List<TiktokOrdersRespDTO.Order> insertOrders = partitionedOrders.get(false);

                    // 执行批量更新操作
                    if (!CollectionUtils.isEmpty(updateOrders)) {
                        executeBatchUpdate(appToken, tableId, updateOrders, orderIdToRecordId, packageMap, categoryMap);
                    }

                    // 执行批量新增操作
                    if (!CollectionUtils.isEmpty(insertOrders)) {
                        executeBatchInsert(appToken, tableId, insertOrders, packageMap, categoryMap);
                    }

                    log.info("处理完成: 更新={}条, 新增={}条, pageToken={}",
                            updateOrders.size(), insertOrders.size(), pageToken);

                    // 获取下一页Token并继续处理
                    pageToken = dto.getData().getNextPageToken();
                } while (!StringUtils.isEmpty(pageToken));
            }
            log.info("TikTok订单同步到飞书表格完成");
            return true;
        } catch (Exception e) {
            log.error("TikTok订单同步异常", e);
            return false;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void getProductsCategoryAndPackage(List<TiktokProductInfoRespDTO> productsInfo, Map<String, String> packageMap, Map<String, String> categoryMap) {
        productsInfo.stream().filter(Objects::nonNull).forEach(t -> {
            List<TiktokProductInfoRespDTO.CategoryChainDTO> categoryChains = t.getData().getCategoryChains();
            TiktokProductInfoRespDTO.PackageWeightDTO packageWeight = t.getData().getPackageWeight();
            packageMap.put(t.getData().getId(), packageWeight.getValue() + "/" + packageWeight.getUnit());
            categoryMap.put(t.getData().getId(), categoryChains.stream()
                    .filter(TiktokProductInfoRespDTO.CategoryChainDTO::getIsLeaf)
                    .map(TiktokProductInfoRespDTO.CategoryChainDTO::getLocalName).findFirst().orElse(""));
        });
    }

    @Override
    public List<TiktokProductInfoRespDTO> getProductsInfo(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            throw new RuntimeException("productIds is empty");
        }

        // 从Redis获取access_token
        String accessToken = redisTemplate.opsForValue().get(TiktokConstants.TIKTOK_ACCESS_TOKEN);
        if (accessToken == null || accessToken.isEmpty()) {
            throw new RuntimeException("Access token not found in Redis");
        }

        // 从Redis获取shop_secrets
        String shopSecretsKey = TiktokConstants.TIKTOK_SHOP_SECRETS;
        Object shopSecretsData = redisTemplate.opsForValue().get(shopSecretsKey);
        TiktokShopSecretsRespDTO.ShopSecretsData shopSecret = objectMapper.convertValue(shopSecretsData, TiktokShopSecretsRespDTO.ShopSecretsData.class);

        if (shopSecret == null || shopSecret.getShops().isEmpty()) {
            throw new RuntimeException("Shop secrets not found in Redis");
        }
        // 提交所有产品ID的请求任务
        List<CompletableFuture<TiktokProductInfoRespDTO>> futures = new ArrayList<>(20);
        List<TiktokProductInfoRespDTO> result = new ArrayList<>();
        for (String productId : productIds) {
            futures.add(CompletableFuture.supplyAsync(() -> fetchProductDetail(productId, accessToken, shopSecret), httpRequestThreadExecutor));
            if (futures.size() >= 20) {

                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                result.addAll(futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .toList());
                futures.clear();
            }
        }
        if (!CollectionUtils.isEmpty(futures)) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            result.addAll(futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .toList());
            futures.clear();
        }
        return result;
    }

    private TiktokProductInfoRespDTO fetchProductDetail(String productId, String accessToken, TiktokShopSecretsRespDTO.ShopSecretsData shopSecret) {
        try {
            log.info("Fetching detail for productId: {}", productId);

            // 构建请求URL
            String baseUrl = PRODUCT_DETAIL_URL + "/" + productId +
                    "?app_key=" + tiktokProperties.getAppKey() +
                    "&shop_cipher=" + shopSecret.getShops().get(0).getCipher() +
                    "&timestamp=" + System.currentTimeMillis() / 1000;

            // 1. 构建初始请求
            Request.Builder requestBuilder = new Request.Builder()
                    .url(baseUrl)
                    .get()
                    .addHeader("content-type", "application/json")
                    .addHeader("x-tts-access-token", accessToken);

            // 2. 生成签名
            String sign = SignatureUtil.generateSignature(requestBuilder.build(), tiktokProperties.getAppSecret());

            // 3. 将签名添加到URL参数中
            String finalUrl = baseUrl + "&sign=" + sign;

            // 4. 更新请求URL并构建最终请求
            Request finalRequest = requestBuilder
                    .url(finalUrl)
                    .build();

            try (Response response = httpClient.newCall(finalRequest).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    TiktokProductInfoRespDTO result = objectMapper.readValue(responseBody, TiktokProductInfoRespDTO.class);
                    if (result.getCode() != null && result.getCode() != 0) {
                        log.error("TikTok product detail API error for productId {}: code={}, message={}",
                                productId, result.getCode(), result.getMessage());
                        throw new RuntimeException("TikTok product detail API error: " + result.getMessage());
                    }
                    return result;
                } else {
                    log.error("Failed to get product detail for productId {}, status: {}, response: {}",
                            productId, response.code(), response.body() != null ? response.body().string() : "null");
                    throw new RuntimeException("Failed to get TikTok product detail");
                }
            }
        } catch (Exception e) {
            log.error("Error getting TikTok product detail for productId {}", productId, e);
            throw new RuntimeException("Failed to get TikTok product detail: " + e.getMessage());
        }
    }

    private String builderRequestBody(String orderStatus, Long createTimeGe, Long updateTimeGe, String shippingType) {
        JSONObject requestJson = new JSONObject();
        if (createTimeGe != null) {
            requestJson.put("create_time_ge", createTimeGe);
        }
        if (updateTimeGe != null) {
            requestJson.put("update_time_ge", updateTimeGe);
        }
        if (shippingType != null && !shippingType.isEmpty()) {
            requestJson.put("shipping_type", shippingType);
        }
        if (orderStatus != null && !orderStatus.isEmpty()) {
            requestJson.put("order_status", orderStatus);
        }
        return requestJson.toString();

    }

    private Request getAccessTokenRequest(String authCode) {
        String url = TOKEN_URL +
                "?app_key=" + tiktokProperties.getAppKey() +
                "&app_secret=" + tiktokProperties.getAppSecret() +
                "&auth_code=" + authCode +
                "&grant_type=authorized_code";
        return new Request.Builder()
                .url(url)
                .addHeader("content-type", "application/json")
                .get()
                .build();
    }

    // 构建订单ID过滤条件
    private String buildOrderIdFilter(List<TiktokOrdersRespDTO.Order> orders) {
        StringBuilder filterBuilder = new StringBuilder("OR(");
        for (TiktokOrdersRespDTO.Order order : orders) {
            filterBuilder.append("CurrentValue.[ORDER ID]=\"").append(order.getId()).append("\",");
        }
        filterBuilder.deleteCharAt(filterBuilder.length() - 1);
        filterBuilder.append(")");
        return filterBuilder.toString();
    }

    // 安全提取订单ID
    private String extractOrderId(AppTableRecord record) {
        if (record == null || record.getFields() == null) {
            return null;
        }

        Object orderIdObj = record.getFields().get("ORDER ID");
        return orderIdObj != null ? orderIdObj.toString() : null;
    }

    // 执行批量更新
    private void executeBatchUpdate(String appToken, String tableId, List<TiktokOrdersRespDTO.Order> updateOrders,
                                    Map<String, String> orderIdToRecordId, Map<String, String> packageMap, Map<String, String> categoryMap) {

        try {
            List<Map<String, Object>> updateContents = getLarkDate(updateOrders, packageMap, categoryMap);
            BatchUpdateTableRecordDTO batchUpdateDto = new BatchUpdateTableRecordDTO();
            batchUpdateDto.setTableId(tableId);
            batchUpdateDto.setAppToken(appToken);

            List<UpdateTableRecordsDTO> records = updateContents.stream()
                    .filter(content -> content.containsKey("ORDER ID"))
                    .map(content -> {
                        UpdateTableRecordsDTO updateDto = new UpdateTableRecordsDTO();
                        updateDto.setFields(content);
                        updateDto.setRecordId(orderIdToRecordId.get(content.get("ORDER ID").toString()));
                        return updateDto;
                    })
                    .collect(Collectors.toList());

            batchUpdateDto.setRecords(records);
            larkService.batchUpdateAppTableRecord(batchUpdateDto);

            log.info("批量更新成功: 更新记录数={}", records.size());
        } catch (Exception e) {
            log.error("批量更新飞书表格失败", e);
        }
    }

    // 执行批量新增
    private void executeBatchInsert(String appToken, String tableId, List<TiktokOrdersRespDTO.Order> insertOrders,
                                    Map<String, String> packageMap, Map<String, String> categoryMap) {
        try {
            List<Map<String, Object>> insertContents = getLarkDate(insertOrders, packageMap, categoryMap);
            BatchCreateTableRecordDTO batchCreateDto = new BatchCreateTableRecordDTO();
            batchCreateDto.setTableId(tableId);
            batchCreateDto.setAppToken(appToken);
            List<CreateTableRecordsDTO> records = insertContents.stream()
                    .map(content -> {
                        CreateTableRecordsDTO createDto = new CreateTableRecordsDTO();
                        createDto.setFields(content);
                        return createDto;
                    })
                    .collect(Collectors.toList());

            batchCreateDto.setRecords(records);
            larkService.batchCreateAppTableRecord(batchCreateDto);

            log.info("批量新增成功: 新增记录数={}", records.size());
        } catch (Exception e) {
            log.error("批量新增飞书表格失败", e);
        }
    }

    private List<Map<String, Object>> getLarkDate(List<TiktokOrdersRespDTO.Order> orders,
                                                  Map<String, String> packageMap, Map<String, String> categoryMap) {
        List<Map<String, Object>> larkContents = new ArrayList<>();
        if (CollectionUtils.isEmpty(orders)) {
            return larkContents;
        }

        for (TiktokOrdersRespDTO.Order order : orders) {
            Map<String, Object> larkMap = new HashMap<>();
            larkMap.put("ORDER ID", order.getId());

            // 时间戳转换为毫秒
            larkMap.put("Create time", getTimeStamp(order.getCreateTime()));
            larkMap.put("Paid time", getTimeStamp(order.getPaidTime()));
            larkMap.put("Cancel order sla time", getTimeStamp(order.getCancelOrderSlaTime()));
            larkMap.put("collection_due_time", getTimeStamp(order.getCollectionDueTime()));
            larkMap.put("Collection time", getTimeStamp(order.getCollectionTime()));
            larkMap.put("Delivery sla time", getTimeStamp(order.getDeliverySlaTime()));
            larkMap.put("Delivery time", getTimeStamp(order.getDeliveryTime()));
            larkMap.put("Update time", getTimeStamp(order.getUpdateTime()));

            // 收件人信息（保持原逻辑）
            larkMap.put("Buyer Username", getSafeString(order.getRecipientAddress(), TiktokOrdersRespDTO.RecipientAddress::getName));
            larkMap.put("Phone number", getSafeString(order.getRecipientAddress(), TiktokOrdersRespDTO.RecipientAddress::getPhoneNumber));
            larkMap.put("Zipcode", getSafeString(order.getRecipientAddress(), TiktokOrdersRespDTO.RecipientAddress::getPostalCode));
            List<TiktokOrdersRespDTO.DistrictInfo> districtInfo = order.getRecipientAddress() != null
                    ? order.getRecipientAddress().getDistrictInfo() : null;
            larkMap.put("Country", getDistrictName(districtInfo, 0));
            larkMap.put("State", getDistrictName(districtInfo, 1));
            larkMap.put("Post Town", getDistrictName(districtInfo, 2));
            larkMap.put("Detail Address", getSafeString(order.getRecipientAddress(), TiktokOrdersRespDTO.RecipientAddress::getAddressDetail));
            larkMap.put("Additional address information", getSafeString(order.getRecipientAddress(), TiktokOrdersRespDTO.RecipientAddress::getFullAddress));
            larkMap.put("Payment Method", order.getPaymentMethodName());

            // 支付信息（保持原逻辑，但修复商品信息获取）
            larkMap.put("Shipping fee after discounts", getSafeString(order.getPayment(), TiktokOrdersRespDTO.Payment::getShippingFee));
            larkMap.put("Subtotal after discounts", getSafeString(order.getPayment(), TiktokOrdersRespDTO.Payment::getSubTotal));
            larkMap.put("Total amount", getSafeString(order.getPayment(), TiktokOrdersRespDTO.Payment::getTotalAmount));

            // 修复：从lineItems中获取商品信息
            if (!CollectionUtils.isEmpty(order.getLineItems())) {
                StringBuilder sb = new StringBuilder();
                StringBuilder categorySb = new StringBuilder();
                StringBuilder weightSb = new StringBuilder();
                order.getLineItems().stream().filter(Objects::nonNull).forEach(t -> {
                    sb.append(t.getProductName()).append("&");
                    sb.append(t.getSellerSku()).append("&");
                    sb.append(t.getSkuName()).append("&");
                    sb.append(t.getSalePrice()).append("&");
                    sb.append(t.getSkuId()).append("&");
                    sb.append(t.getIsGift());
                    sb.append("\n");
                    categorySb.append(categoryMap.get(t.getProductId())).append("\n");
                    weightSb.append(packageMap.get(t.getProductId())).append("\n");

                });
                sb.deleteCharAt(sb.length() - 1);
                categorySb.deleteCharAt(categorySb.length() - 1);
                weightSb.deleteCharAt(weightSb.length() - 1);
                larkMap.put("Product name & Item seller sku & Item sku name & Item sale price & SKU ID & Is a gift", sb.toString());
                larkMap.put("Weight(kg)", weightSb.toString());
                larkMap.put("Product Category", categorySb.toString());
            }

            // 其他信息（保持原逻辑）
            larkMap.put("Order status", order.getStatus());
            larkMap.put("Tracking number", order.getTrackingNumber());
            larkMap.put("Shipping provider", order.getShippingProvider());
            larkMap.put("Shipping type", order.getShippingType());
            larkMap.put("Buyer message", order.getBuyerMessage());
            larkMap.put("Commerce platform", order.getCommercePlatform());
            larkMap.put("Delivery option", order.getDeliveryOptionName());
            larkMap.put("Delivery type", order.getDeliveryType());
            larkMap.put("Fulfillment type", order.getFulfillmentType());

            larkContents.add(larkMap);
        }
        return larkContents;
    }

    // 10位转13位
    private Long getTimeStamp(Long timeStamp) {
        return timeStamp != null ? timeStamp * 1000 : null;
    }

    // 安全获取地址信息
    private String getSafeString(TiktokOrdersRespDTO.RecipientAddress address, Function<TiktokOrdersRespDTO.RecipientAddress, String> getter) {
        return address != null ? getter.apply(address) : "";
    }

    // 安全获取支付信息
    private Float getSafeString(TiktokOrdersRespDTO.Payment payment, Function<TiktokOrdersRespDTO.Payment, String> getter) {
        return payment != null ? Float.parseFloat(getter.apply(payment)) : 0.00F;
    }

    // 安全获取地址信息
    private String getDistrictName(List<TiktokOrdersRespDTO.DistrictInfo> districtInfo, int index) {
        if (districtInfo == null || index >= districtInfo.size()) {
            return "";
        }
        TiktokOrdersRespDTO.DistrictInfo info = districtInfo.get(index);
        return info != null ? info.getAddressName() : "";
    }
}
