package com.servauto.mall.controller.payment;

import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.enums.payment.PaymentChannel;
import com.servauto.mall.model.dto.request.payment.CallBackPaymentReqDTO;
import com.servauto.mall.model.dto.request.payment.ipay88.IPay88PaymentCallBackReqDTO;
import com.servauto.mall.model.dto.response.payment.I2c2p.I2c2pPaymentCreateResDTO;
import com.servauto.mall.model.dto.response.payment.I2c2p.I2c2pPaymentInquiryDetailDTO;
import com.servauto.mall.service.payment.PaymentFactory;
import com.servauto.mall.support.payment.config.I2c2pPaymentProperties;
import com.servauto.mall.support.payment.config.Ipay88PaymentProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@Tag(name = "Car Search Keys")
@Controller
@RequestMapping("/payment")
@Validated
@Slf4j
public class PaymentCallBackController {

    @Resource
    private Ipay88PaymentProperties ipay88PaymentProperties;
    @Resource
    private I2c2pPaymentProperties i2c2pPaymentProperties;
    @Resource
    private PaymentFactory paymentFactory;
    @Resource
    private Environment environment;

    private static final String RECEIVE_OK = "RECEIVEOK";

    private static final String SUCCESS = "SUCCESS";


    @PostMapping("/response")
    @Operation(summary = "add new car")
    @PermitAll
    public String response(@RequestParam("orderNo") String orderNo, @RequestParam Map<String, String> formData) {
        // 打印所有接收到的表单字段
        String jsonFormData = JacksonSerializer.serialize(formData);
        IPay88PaymentCallBackReqDTO callBackReqDTO = JacksonSerializer.deSerialize(jsonFormData, IPay88PaymentCallBackReqDTO.class);
        log.info("callBack -response callBackReqDTO{}", callBackReqDTO);
        String env = environment.getProperty("spring.profiles.active");
        if (!"prod".equals(env)) {
            paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).queryPaymentStatusByNo(orderNo);
        }else{
            CallBackPaymentReqDTO callBackPaymentReqDTO  = new CallBackPaymentReqDTO();
            callBackPaymentReqDTO.setPayNo(callBackReqDTO.getRefNo());
            callBackPaymentReqDTO.setReqData(jsonFormData);
            paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).paymentCallBack(callBackPaymentReqDTO);
        }
        log.info("callBack - redirect{}", ipay88PaymentProperties.getMerchant().getWebsiteURL() + "?orderNo=" + orderNo);

        return "redirect:" + ipay88PaymentProperties.getMerchant().getWebsiteURL() + "?orderNo=" + orderNo;
    }
    @GetMapping("/response")
    @Operation(summary = "add new car")
    @PermitAll
    public String responseGet(@RequestParam("orderNo") String orderNo) {
        log.info("callBack -responseGet redirect{}", ipay88PaymentProperties.getMerchant().getWebsiteURL() + "?orderNo=" + orderNo);

        return "redirect:" + ipay88PaymentProperties.getMerchant().getWebsiteURL() + "?orderNo=" + orderNo;
    }
    @PostMapping("/backend_response")
    @Operation(summary = "add new car")
    @ResponseBody
    @PermitAll
    public String backendResponse(@RequestParam Map<String, String> formData) {
        // 打印所有接收到的表单字段
        String jsonFormData = JacksonSerializer.serialize(formData);
        IPay88PaymentCallBackReqDTO callBackReqDTO = JacksonSerializer.deSerialize(jsonFormData, IPay88PaymentCallBackReqDTO.class);
        log.info("backendResponse - callBackReqDTO{}", callBackReqDTO);
        CallBackPaymentReqDTO callBackPaymentReqDTO  = new CallBackPaymentReqDTO();
        callBackPaymentReqDTO.setPayNo(callBackReqDTO.getRefNo());
        callBackPaymentReqDTO.setReqData(jsonFormData);
        paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).paymentCallBack(callBackPaymentReqDTO);
        return RECEIVE_OK;
    }

    @PostMapping("/backend_2c2p_response")
    @Operation(summary = "add new car")
    @ResponseBody
    @PermitAll
    public String backendI2C2PResponse(@RequestBody String callBackStr) {
        // 打印所有接收到的表单字段
        log.info("backendI2C2PResponse - formData{}", callBackStr);
        String jsonFormData = JacksonSerializer.serialize(callBackStr);
        CallBackPaymentReqDTO callBackPaymentReqDTO  = new CallBackPaymentReqDTO();
        callBackPaymentReqDTO.setReqData(jsonFormData);
        paymentFactory.createPaymentServices(PaymentChannel.I2C2P.getDesc()).paymentCallBack(callBackPaymentReqDTO);
        return SUCCESS;
    }


    @PostMapping("/frontendResponse")
    @Operation(summary = "add new car")
    @PermitAll
    public String frontendResponse(@RequestParam("orderNo")  String orderNo) {
        // 打印所有接收到的表单字段
        log.info("callBack -2c2p response orderNo  {}", orderNo);
        return "redirect:" + i2c2pPaymentProperties.getMerchant().getWebsiteURL() + "?orderNo=" + orderNo;
    }


}

