package com.servauto.mall.controller.location;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "Location")
@RestController(value = "/locations")
@Validated
@Slf4j
public class LocationController {

    @Resource
    private LocationService locationService;

    @GetMapping("/areas")
    @Operation(summary = "area list")
    public CommonResult<List<AreaDTO>> queryAreas(@RequestParam("parentCode") String parentCode) {
        return CommonResult.success(locationService.getAreasByParentCode(parentCode));
    }

    @GetMapping("/states")
    @Operation(summary = "state list")
    public CommonResult<List<StateDTO>> queryStates() {
        return CommonResult.success(locationService.getStates());
    }
}
