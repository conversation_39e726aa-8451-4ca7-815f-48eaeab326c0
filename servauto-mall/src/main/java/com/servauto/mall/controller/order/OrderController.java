package com.servauto.mall.controller.order;

import com.github.pagehelper.PageInfo;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.constants.RedisKeyConstants;
import com.servauto.mall.enums.order.OrderStatusEnum;
import com.servauto.mall.factory.order.OrderFactory;
import com.servauto.mall.model.dto.request.order.*;
import com.servauto.mall.model.dto.response.order.*;
import com.servauto.mall.service.order.TradeService;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestClient;

import java.util.Arrays;
import java.util.List;

@Tag(name = "Customer - Order")
@RestController
@Validated
@Slf4j
public class OrderController {

    @Resource
    private TradeService tradeService;

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private RestClient.Builder builder;

    @PostMapping("/customer/orders/create")
    public CommonResult<CreateOrderDTO> createOrder(@Valid @RequestBody CreateOrderReqDTO reqDTO) {
        return CommonResult.success(tradeService.createOrder(SecurityFrameworkUtils.getLoginUserId(), reqDTO));
    }

    @PostMapping("/customer/orders/submit")
    public CommonResult<SubmitOrderDTO> submitOrder(@Valid @RequestBody SubmitOrderReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        RLock rLock = redissonClient.getLock(String.format(RedisKeyConstants.SUBMIT_ORDER_KEY, customerId, reqDTO.getOrderNo()));
        SubmitOrderDTO submitOrder;
        try {
            rLock.lock();
            submitOrder = tradeService.submitOrder(customerId, reqDTO);
        } finally {
            rLock.unlock();
        }
        return CommonResult.success(submitOrder);
    }

    @GetMapping("/customer/orders")
    public CommonResult<PageInfo<OrderDTO>> queryOrders(@RequestParam(value = "status", required = false) String status) {
        QueryOrderReqDTO.QueryOrderReqDTOBuilder builder = QueryOrderReqDTO.builder().
                customerId(SecurityFrameworkUtils.getLoginUserId());
        if (OrderStatusEnum.PENDING_DELIVERY.getCode().equals(status)) {
            builder.statuses(OrderStatusEnum.parse());
        } else {
            builder.status(status);
        }
        return CommonResult.success(tradeService.pageOrders(builder.build()));
    }

    @GetMapping("/customer/orders/{orderNo}")
    public CommonResult<OrderDTO> queryOrder(@PathVariable String orderNo) {
        QueryOrderReqDTO queryOrderReqDTO = QueryOrderReqDTO.builder().customerId(SecurityFrameworkUtils.getLoginUserId()).orderNo(orderNo).build();
        List<OrderDTO> orders = tradeService.queryOrders(queryOrderReqDTO);
        return CommonResult.success(orders.stream().findFirst().orElse(null));
    }

    @PostMapping("/customer/orders/{orderNo}/workshops")
    public CommonResult<List<OrderWorkshopDTO>> queryOrderWorkshops(@PathVariable String orderNo, @Valid @RequestBody OrderWorkshopReqDTO reqDTO) {
        reqDTO.setCustomerId(SecurityFrameworkUtils.getLoginUserId());
        reqDTO.setOrderNo(orderNo);
        return CommonResult.success(tradeService.queryOrderWorkshops(reqDTO));
    }

    @GetMapping("/customer/orders/{orderNo}/workshops/{workshopId}/opening-hours")
    public CommonResult<OrderWorkshopDTO> queryOrderWorkshopOpeningHours(@PathVariable String orderNo, @PathVariable Long workshopId) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        return CommonResult.success(tradeService.queryOrderWorkshopOpeningHours(customerId, orderNo, workshopId));
    }

    @PutMapping("/customer/orders/{orderNo}/reservation")
    public CommonResult<Boolean> reservation(@PathVariable String orderNo, @RequestBody ReservationOrderReqDTO reqDTO) {
        reqDTO.setOrderNo(orderNo);
        tradeService.reservationOrder(SecurityFrameworkUtils.getLoginUserId(), reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/customer/orders/{orderNo}/reschedule")
    public CommonResult<Boolean> reschedule(@PathVariable String orderNo, @RequestBody RescheduleOrderReqDTO reqDTO) {
        reqDTO.setOrderNo(orderNo);
        tradeService.rescheduleOrder(SecurityFrameworkUtils.getLoginUserId(), reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/customer/orders/{orderNo}/cancel")
    public CommonResult<Boolean> cancelOrder(@PathVariable String orderNo) {
        tradeService.cancelOrder(SecurityFrameworkUtils.getLoginUserId(), orderNo);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/customer/orders/{orderNo}/payments/{payNo}/cashier")
    public CommonResult<CashierDTO> cashier(@PathVariable String orderNo, @PathVariable String payNo) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        return CommonResult.success(tradeService.cashier(customerId, orderNo, payNo));
    }


}
