package com.servauto.mall.controller.product;

import com.github.pagehelper.PageInfo;
import com.servauto.mall.enums.SaleStatus;
import com.servauto.mall.model.dto.response.product.*;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.dto.response.IdNameDTO;
import com.servauto.mall.service.product.ProductService;
import com.servauto.common.core.domain.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "Customer - Product")
@RestController
@Validated
@Slf4j
public class ProductController {

    private static final String DISPLAY_TYPE_TOP_FILTER = "customer/product-top-filter";

    @Resource
    private ProductService productService;

    /**
     * 查询 product detail
     *
     * @param productId productId
     * @return ProductDTO
     */
    @GetMapping("/customer/products/{productId}")
    @Operation(summary = "get product detail")
    public CommonResult<ProductDTO> queryProduct(@PathVariable(name = "productId") Long productId) {
        var info = productService.getProduct(productId);
        return CommonResult.success(info);
    }

    /**
     * 分页查询 product 基础信息列表
     *
     * @param reqDTO reqDTO
     * @return PageInfo<ProductBaseDTO>
     */
    @PostMapping("/customer/products/list")
    @Operation(summary = "get product list")
    public CommonResult<PageInfo<ProductDTO>> queryProducts(@RequestBody QueryProductsDTO reqDTO) {
        // 默认只查询上架的
        if (reqDTO.getStatus() == null || reqDTO.getStatus() == 0) {
            reqDTO.setStatus(SaleStatus.ACTIVE.getCode());
        }
        var list = productService.pageProducts(reqDTO);
        return CommonResult.success(list);
    }

    /**
     * 查询 product categories
     *
     * @return List<IdNameDTO>
     */
    @GetMapping("/customer/product-categories")
    @Operation(summary = "get product categories")
    public CommonResult<List<IdNameDTO>> queryProductCategories() {
        var list = productService.getProductCategories();
        return CommonResult.success(list);
    }

    /**
     * 查询 product attributes
     *
     * @param categoryId  categoryId
     * @param displayType displayType
     * @return List<ProductAttributesDTO>
     */
    @GetMapping("/customer/product-attributes")
    @Operation(summary = "get product attributes")
    public CommonResult<ProductAttributesDTO> queryProductAttributes(
            @RequestParam(name = "categoryId") Long categoryId,
            @RequestParam(name = "displayType", required = false) String displayType) {
        ProductAttributesDTO resp = new ProductAttributesDTO();
        resp.setBrands(productService.getProductBrands());
        resp.setAttributes(productService.getProductAttributes(displayType, categoryId));
        var attrs = productService.getProductAttributes(DISPLAY_TYPE_TOP_FILTER, categoryId);
        if (CollectionUtils.isNotEmpty(attrs)) {
            resp.setTopAttribute(attrs.get(0));
        }
        return CommonResult.success(resp);
    }

    /**
     * 查询 product brand
     *
     * @return List<IdNameDTO>
     */
    @GetMapping("/customer/product-brands")
    @Operation(summary = "get product brands")
    public CommonResult<List<IdNameDTO>> queryProductBrands() {
        var list = productService.getProductBrands();
        return CommonResult.success(list);
    }
}
