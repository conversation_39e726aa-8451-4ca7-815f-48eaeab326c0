package com.servauto.mall.controller.customer;

import cn.hutool.core.util.StrUtil;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.request.auth.AuthLoginReqDTO;
import com.servauto.mall.model.dto.request.auth.AuthSmsSendReqDTO;
import com.servauto.mall.model.dto.response.auth.AuthLoginRespDTO;
import com.servauto.mall.service.customer.CustomerAuthService;
import com.servauto.mall.support.security.config.SecurityProperties;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "Customer - Auth")
@RestController
@RequestMapping("/customer/auth")
@Validated
@Slf4j
public class CustomerAuthController {

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private CustomerAuthService customerAuthService;

    @PostMapping("/send-sms-code")
    @Operation(summary = "send sms code")
    @PermitAll
    public CommonResult<Boolean> sendSmsCode(@RequestBody @Valid AuthSmsSendReqDTO reqVO) {
        customerAuthService.sendSmsCode(getLoginUserId(), reqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PostMapping("/login")
    @Operation(summary = "mobile + sms login")
    @PermitAll
    public CommonResult<AuthLoginRespDTO> login(@RequestBody @Valid AuthLoginReqDTO reqVO) {
        return CommonResult.success(customerAuthService.login(reqVO));
    }

    @PostMapping("/logout")
    @Operation(summary = "logout")
    @PermitAll
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            customerAuthService.logout(token);
        }
        return CommonResult.success();
    }


}
