package com.servauto.mall.controller.payment;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "Car Search Keys")
@RestController
@RequestMapping("/payment")
@Validated
@Slf4j
public class PaymentController {
/*
    @Resource
    private PaymentFactory paymentFactory;

    @Resource
    private CarProductMatchService carProductMatchService;


    @GetMapping("/pay")
    @Operation(summary = "brand list")
    @PermitAll
    public CommonResult<List<CarProductMatchInfoDTO>> queryValidBrands() {
        CreatePaymentReqDTO createPaymentReqDTO = new CreatePaymentReqDTO();
        createPaymentReqDTO.setUserName("test");
        createPaymentReqDTO.setUserEmail("<EMAIL>");
        createPaymentReqDTO.setUserContact("123");
        createPaymentReqDTO.setOrderNo(UniqueID.generateId("O"));
        createPaymentReqDTO.setAmount(new BigDecimal("1278.99"));
        createPaymentReqDTO.setProductDesc("11");
        CarProductMatchInfoReqDTO reqDTO = new CarProductMatchInfoReqDTO();
        reqDTO.setCarId(37L);
        reqDTO.setProductType(CarMatchProductTypeEnum.WIPER);
        List<CarProductMatchInfoDTO> list= carProductMatchService.selectMatchInfo(reqDTO);
      //  PaymentResDTO result1= paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).createPayment(createPaymentReqDTO);
       // PaymentResDTO result2= paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).queryPaymentStatusByNo("XS2023103414");

       //PaymentResDTO result= paymentFactory.createPaymentServices(PaymentChannel.I2C2P.getDesc()).createPayment(createPaymentReqDTO);
        return CommonResult.success(list);
    }*/
}

