package com.servauto.mall.controller.workshop;

import com.github.pagehelper.PageInfo;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.service.workshop.WorkshopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>WorkshopController</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/12 10:50
 */
@Tag(name = "mall workshop")
@RestController
@Validated
@Slf4j
public class WorkshopController {


    @Resource
    private WorkshopService workshopService;

    @GetMapping("/mall/workshops")
    @Operation(summary = "workshop list")
    public CommonResult<PageInfo<WorkshopDTO>> pageWorkshops(@RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        PageInfo<WorkshopDTO> workshops = workshopService.pageWorkshops(pageNo, pageSize);
        return CommonResult.success(workshops);
    }

    @GetMapping("/mall/workshops/{workshopId}")
    @Operation(summary = "get workshop")
    public CommonResult<WorkshopDTO> queryWorkshop(@PathVariable Long workshopId) {
        WorkshopDTO workshops = workshopService.getWorkshopDetail(workshopId);
        return CommonResult.success(workshops);
    }


}
