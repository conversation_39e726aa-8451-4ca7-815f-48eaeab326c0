package com.servauto.mall.controller.content;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.response.content.TemplateDTO;
import com.servauto.mall.service.content.TemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>TemplateController</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/31 16:25
 */
@Tag(name = "Content")
@RestController
@RequestMapping("/content")
@Validated
@Slf4j
public class TemplateController {

    @Resource
    private TemplateService templateService;

    @GetMapping("/templates/{templateCode}")
    @Operation(summary = "query content")
    @PermitAll
    public CommonResult<TemplateDTO> queryTemplate(@PathVariable String templateCode) {
        return CommonResult.success(templateService.getTemplateByTemplateCode(templateCode));
    }




}
