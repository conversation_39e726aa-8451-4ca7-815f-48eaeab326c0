package com.servauto.mall.controller.tiktok;

import com.servauto.common.constant.TiktokConstants;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.response.tiktok.TiktokShopSecretsRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokTokenRespDTO;
import com.servauto.mall.service.tiktok.TiktokService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@Tag(name = "TikTok Shop - callback and webhooks")
@RestController
@Validated
@Slf4j
@RequestMapping("/tiktok")
public class TiktokController {

    @Resource
    private TiktokService tiktokOAuthService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @GetMapping("/callback")
    @Operation(summary = "TikTok OAuth callback")
    @PermitAll
    public CommonResult<String> callback(@RequestParam("code") String code,
                                         @RequestParam(name = "state", required = false) String state) {
        try {
            TiktokTokenRespDTO tokenResp = tiktokOAuthService.getAccessToken(code);
            if (tokenResp.getData() != null) {
                // 获取当前时间
                long currentTimeSeconds = System.currentTimeMillis() / 1000;

                // 计算访问令牌的剩余有效时间（秒）
                long accessTokenTTL = tokenResp.getData().getAccessTokenExpiresIn() - currentTimeSeconds;
                if (accessTokenTTL > 0) {
                    redisTemplate.opsForValue().set(TiktokConstants.TIKTOK_ACCESS_TOKEN, tokenResp.getData().getAccessToken(),
                            accessTokenTTL, TimeUnit.SECONDS);
                }

                // 计算刷新令牌的剩余有效时间（秒）
                long refreshTokenTTL = tokenResp.getData().getRefreshTokenExpiresIn() - currentTimeSeconds;
                if (refreshTokenTTL > 0) {
                    redisTemplate.opsForValue().set(TiktokConstants.TIKTOK_REFRESH_TOKEN, tokenResp.getData().getRefreshToken(),
                            refreshTokenTTL, TimeUnit.SECONDS);
                }
            }
            return CommonResult.success("");
        } catch (Exception e) {
            log.error("Error processing TikTok OAuth callback", e);
            return CommonResult.error("Failed to process OAuth callback: " + e.getMessage());
        }
    }

    @GetMapping("/shop-secrets")
    @Operation(summary = "Get TikTok shop secrets")
    @PermitAll
    public CommonResult<String> getShopSecrets() {
        try {
            TiktokShopSecretsRespDTO result = tiktokOAuthService.getShopSecrets();
            if (result != null && result.getData() != null) {
                redisTemplate.opsForValue().set(TiktokConstants.TIKTOK_SHOP_SECRETS, result.getData(), -1, TimeUnit.SECONDS);
            }
            return CommonResult.success("successfully");

        } catch (Exception e) {
            log.error("Error getting TikTok shop secrets", e);
            return CommonResult.error("Failed to get shop secrets: " + e.getMessage());
        }
    }

    @GetMapping("/trigger")
    @Operation(summary = "TikTok Shop webhook trigger callback")
    @PermitAll
    public CommonResult<String> trigger(@RequestHeader(value = "Authorization", required = false) String authorization,
                                        @RequestBody String payload) {

        try {
            log.info("TikTok Shop webhook trigger received. Authorization: {} Payload: {}", authorization, payload);
            return CommonResult.success("Webhook processed successfully");
        } catch (Exception e) {
            log.error("Error processing TikTok webhook", e);
            return CommonResult.error("Error processing webhook: " + e.getMessage());
        }
    }

    @GetMapping("/sync-create-order-to-lark")
    @Operation(summary = "Tiktok orders sync to lark")
    @PermitAll
    public CommonResult<String> syncCreateOrderToLark(@RequestParam(value = "app_token") String appToken,
                                                      @RequestParam("table_id") String tableId,
                                                      @RequestParam("createTimeGe") Long createTimeGe,
                                                      @RequestParam(value = "shippingType", required = false) String shippingType,
                                                      @RequestParam(value = "orderStatus", required = false) String orderStatus) {
        try {
            tiktokOAuthService.tiktokOrdersSyncToLark(appToken, tableId, createTimeGe, null, shippingType, orderStatus);
            return CommonResult.success("create order sync  successfully");
        } catch (Exception e) {
            log.error("Error processing sync lark", e);
            return CommonResult.error("Error processing sync lark: " + e.getMessage());
        }
    }

    @GetMapping("/sync-update-order-to-lark")
    @Operation(summary = "Tiktok orders sync to lark")
    @PermitAll
    public CommonResult<Boolean> syncUpdateOrderToLark(@RequestParam("app_token") String appToken,
                                                       @RequestParam("table_id") String tableId,
                                                       @RequestParam("updateTimeGe") Long updateTimeGe,
                                                       @RequestParam(value = "shippingType", required = false) String shippingType,
                                                       @RequestParam(value = "orderStatus", required = false) String orderStatus) {
        try {
            tiktokOAuthService.tiktokOrdersSyncToLark(appToken, tableId, null, updateTimeGe, shippingType, orderStatus);
            return CommonResult.success("update order sync  successfully");
        } catch (Exception e) {
            log.error("Error processing sync lark", e);
            return CommonResult.error("Error processing sync lark: " + e.getMessage());
        }
    }
}