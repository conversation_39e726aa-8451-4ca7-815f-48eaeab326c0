package com.servauto.mall.controller.customer;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.request.customer.CreateCarInfoReqDTO;
import com.servauto.mall.model.dto.request.customer.DefaultCarInfoReqDTO;
import com.servauto.mall.model.dto.request.customer.RemoveCarInfoReqDTO;
import com.servauto.mall.model.dto.response.car.CarInfoDTO;
import com.servauto.mall.service.car.CarInfoService;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "Customer - CarInfo")
@RestController
@RequestMapping("/customer")
@Validated
@Slf4j
public class CustomerCarInfoController {

    @Resource
    private CarInfoService carInfoService;

    @PostMapping("/carInfo")
    @Operation(summary = "add new car")
    public CommonResult<Boolean> addNewCar(@RequestBody CreateCarInfoReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        carInfoService.addNewCar(customerId, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @DeleteMapping("/carInfo")
    @Operation(summary = "remove Car")
    public CommonResult<Boolean> removeCar(@RequestBody RemoveCarInfoReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        carInfoService.deleteByCustomerIdAndCarId(customerId, reqDTO.getCarId());
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/carInfo")
    @Operation(summary = "carInfo list")
    public CommonResult<List<CarInfoDTO>> listCarInfo() {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        List<CarInfoDTO> carInfoDTOList = carInfoService.carInfoList(customerId);
        return CommonResult.success(carInfoDTOList);
    }

    @PostMapping("/defaultCar")
    @Operation(summary = "set default car")
    public CommonResult<Boolean> setDefaultCar(@RequestBody DefaultCarInfoReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        carInfoService.setDefaultCar(customerId, reqDTO.getCarId());
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/defaultCar")
    @Operation(summary = "get default car")
    public CommonResult<CarInfoDTO> getDefaultCar() {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        CarInfoDTO carInfoDTO = carInfoService.getDefaultCar(customerId);
        return CommonResult.success(carInfoDTO);
    }
}
