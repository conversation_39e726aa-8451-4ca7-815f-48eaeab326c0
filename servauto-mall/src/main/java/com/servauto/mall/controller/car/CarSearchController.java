package com.servauto.mall.controller.car;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.response.car.CarBrandListDTO;
import com.servauto.mall.model.dto.response.car.CarKeyValueDTO;
import com.servauto.mall.service.car.CarBrandsService;
import com.servauto.mall.service.car.CarExtraService;
import com.servauto.mall.service.car.CarModelsService;
import com.servauto.mall.service.car.CarYearsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Tag(name = "Car Search Keys")
@RestController
@RequestMapping("/car")
@Validated
@Slf4j
public class CarSearchController {

    @Resource
    private CarBrandsService carBrandsService;

    @Resource
    private CarModelsService carModelsService;

    @Resource
    private CarYearsService carYearsService;

    @Resource
    private CarExtraService carExtraService;

    @GetMapping("/brandList")
    @Operation(summary = "brand list")
    public CommonResult<CarBrandListDTO> queryValidBrands() {
        CarBrandListDTO result = new CarBrandListDTO();

        List<CarKeyValueDTO> brandsDtoList = carBrandsService.selectAllValidBrandsKv();
        List<CarKeyValueDTO> topBrandsDtoList = carBrandsService.selectAllValidTopBrandsKv();
        result.setTopBrands(topBrandsDtoList);
        result.setBrandList(brandsDtoList);
        return CommonResult.success(result);
    }

    @GetMapping("/modelList")
    @Operation(summary = "model list")
    public CommonResult<List<CarKeyValueDTO>> queryValidModelsByBrandId(@RequestParam(name = "brandId") Long brandId) {
        List<CarKeyValueDTO> modelsDtoList = carModelsService.selectAllModelsByBrandIdKv(brandId);
        return CommonResult.success(modelsDtoList);
    }

    @GetMapping("/yearList")
    @Operation(summary = "year list")
    public CommonResult<List<CarKeyValueDTO>> queryValidYearByModelId(@RequestParam(name = "modelId") Long modelId) {
        List<CarKeyValueDTO> carYearsDtoList = carYearsService.getCarYearsByModelIdKv(modelId);
        return CommonResult.success(carYearsDtoList);
    }

    @GetMapping("/variantList")
    @Operation(summary = "variant list")
    public CommonResult<List<CarKeyValueDTO>> queryValidVariantByYearId(@RequestParam(name = "yearId") Long yearId) {
        List<CarKeyValueDTO> carYearsDtoList = carExtraService.getCarExtraByYearIdKv(yearId);
        return CommonResult.success(carYearsDtoList);
    }
}

