package com.servauto.mall.controller.packageinfo;

import com.servauto.mall.model.dto.request.packageinfo.ChangePackageDetailDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageDetailDTO;
import com.servauto.mall.service.packageinfo.PackageService;
import com.servauto.common.core.domain.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "Customer - Package")
@RestController
@Validated
@Slf4j
public class PackageController {
    @Resource
    private PackageService packageService;

    /**
     * 查询推荐的 packages
     *
     * @param id id
     * @param carId carId
     * @param productId productId
     * @return List<PackageDetailDTO>
     */
    @GetMapping("/customer/packages")
    @Operation(summary = "package list")
    public CommonResult<List<PackageDetailDTO>> queryPackages(
            @RequestParam(name = "id", required = false) Long id,
            @RequestParam(name = "carId") Long carId,
            @RequestParam(name = "productId", required = false) Long productId,
            @RequestParam(name = "code", defaultValue = "", required = false) String code
    ) {
        var details = packageService.getPackageDetails(id, carId, productId, code);
        return CommonResult.success(details);
    }

    /**
     * 变更 package 商品
     *
     * @param reqDTO reqDTO
     * @return PackageDetailDTO
     */
    @PostMapping("/customer/packages")
    @Operation(summary = "change package")
    public CommonResult<PackageDetailDTO> changePackage(@RequestBody ChangePackageDetailDTO reqDTO) {
        var detail = packageService.changePackageDetail(reqDTO);
        return CommonResult.success(detail);
    }
}
