package com.servauto.mall.controller.customer;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.mall.model.dto.request.customer.CreateAddressReqDTO;
import com.servauto.mall.model.dto.request.customer.EditAddressReqDTO;
import com.servauto.mall.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.mall.service.customer.CustomerService;
import com.servauto.mall.support.security.core.utils.SecurityFrameworkUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "Customer - Address")
@RestController
@RequestMapping("/customer")
@Validated
@Slf4j
public class CustomerAddressController {

    @Resource
    private CustomerService customerService;

    @PostMapping("/addresses")
    public CommonResult<Boolean> createAddress(@RequestBody CreateAddressReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        customerService.createAddress(customerId, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/addresses/{addressId}")
    public CommonResult<Boolean> editAddress(@PathVariable Long addressId, @RequestBody EditAddressReqDTO reqDTO) {
        Long customerId = SecurityFrameworkUtils.getLoginUserId();
        reqDTO.setId(addressId);
        customerService.updateAddress(customerId, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @DeleteMapping("/addresses/{addressId}")
    public CommonResult<Boolean> deleteAddress(@PathVariable Long addressId) {
        customerService.deleteAddress(SecurityFrameworkUtils.getLoginUserId(), addressId);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/addresses/{addressId}")
    public CommonResult<CustomerAddressDTO> queryAddresses(@PathVariable Long addressId) {
        return CommonResult.success(customerService.getAddressesById(addressId));
    }

    @GetMapping("/addresses")
    public CommonResult<List<CustomerAddressDTO>> queryAddressesByCustomerId() {
        return CommonResult.success(customerService.getAddressesByCustomerId(SecurityFrameworkUtils.getLoginUserId()));
    }

}
