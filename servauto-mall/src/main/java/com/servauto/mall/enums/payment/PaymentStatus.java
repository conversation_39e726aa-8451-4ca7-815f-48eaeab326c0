package com.servauto.mall.enums.payment;

public enum PaymentStatus {
    INIT("INIT", "INIT"),
    SUCCESS("SUCCESS", "SUCCESS"),
    FAIL("FAIL", "FAIL"),
    TIME_OUT("TIMEOUT", "TIMEOUT"),
    UNKNOWN("UNKNOWN", "UNKNOWN");

    private final String code;

    private final String desc;

    PaymentStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PaymentStatus getByCode(String code) {
        for (PaymentStatus e : PaymentStatus.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}

