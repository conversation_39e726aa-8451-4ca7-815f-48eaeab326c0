package com.servauto.mall.enums;

/**
 * <p>EntryTypeEnum</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/8 11:30
 */
public enum EntryTypeEnum {

    APP("APP", "app"),
    WEB("WEB", "web"),
    EXTERNAL("EXTERNAL", "external"),
    ;

    private final String code;

    private final String msg;

    EntryTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }


}
