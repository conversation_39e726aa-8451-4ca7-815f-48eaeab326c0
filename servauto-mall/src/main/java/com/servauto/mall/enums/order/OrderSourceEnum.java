package com.servauto.mall.enums.order;

import lombok.Getter;

@Getter
public enum OrderSourceEnum {

    SERVAUTO("SERVAUTO", "servauto"),
    SHOPEE("SHOPEE", "shopee"),
    ;

    private final String code;

    private final String msg;

    OrderSourceEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isServauto(String code) {
        return SERVAUTO.getCode().equals(code);
    }

    public static boolean isShopee(String code) {
        return SHOPEE.getCode().equals(code);
    }

}
