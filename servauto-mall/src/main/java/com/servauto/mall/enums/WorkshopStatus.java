package com.servauto.mall.enums;

import com.servauto.common.utils.StringUtils;

public enum WorkshopStatus {
    UNKNOWN(0, "Unknown"),

    ACTIVE(1, "Active"),

    INACTIVE(2, "Inactive");

    private final Integer code;

    private final String desc;

    WorkshopStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isActive(String code) {
        return StringUtils.isNotBlank(code) && ACTIVE.getCode().equals(Integer.parseInt(code));
    }

    public static WorkshopStatus getByCode(Integer code) {
        for (WorkshopStatus e : WorkshopStatus.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
