package com.servauto.mall.enums.order;

import lombok.Getter;

@Getter
public enum OrderTypeEnum {

    PRODUCT("PRODUCT", "product order"),
    PACKAGE("PACKAGE", "package order"),
    ;

    private final String code;

    private final String msg;

    OrderTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isProduct(String code) {
        return PRODUCT.getCode().equals(code);
    }

    public static boolean isPackage(String code) {
        return PACKAGE.getCode().equals(code);
    }

}
