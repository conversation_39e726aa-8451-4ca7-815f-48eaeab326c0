package com.servauto.mall.enums.customer;

import cn.hutool.core.util.ArrayUtil;

/**
 * <p>CustomerSourceEnum</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/19 17:33
 */
public enum CustomerSourceEnum {

    DEFAULT("DEFAULT", "default source"),

    SHOPPE("SP", "shoppe system");

    private final String code;

    private final String desc;

    CustomerSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomerSourceEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equalsIgnoreCase(code), values());
    }

}
