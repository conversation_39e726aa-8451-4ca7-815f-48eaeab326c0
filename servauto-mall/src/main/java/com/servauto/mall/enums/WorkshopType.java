package com.servauto.mall.enums;

public enum WorkshopType {
    UNKNOWN(0, "Unknown"),

    SERV_AUTO(1, "ServAuto"),

    PARTNER(2, "Partner"),

    FRANCHISE(3, "Franchise");

    private final Integer code;

    private final String desc;

    WorkshopType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static WorkshopType getByCode(Integer code) {
        for (WorkshopType e : WorkshopType.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
