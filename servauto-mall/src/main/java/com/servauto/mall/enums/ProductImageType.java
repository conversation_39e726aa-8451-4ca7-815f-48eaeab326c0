package com.servauto.mall.enums;

public enum ProductImageType {
    UNKNOWN(0, "unknown"),

    COVER_IMAGE(1, "Female"),

    H5_DETAIL_IMAGE(2, "H5DetailImage");

    private final Integer code;

    private final String desc;

    ProductImageType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ProductImageType getByCode(Integer code) {
        for (ProductImageType e : ProductImageType.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
