package com.servauto.mall.enums.car;

import lombok.Getter;

@Getter
public enum CarMatchProductTypeEnum {

    UNKNOWN("UNKNOWN", "UNKNOWN"),
    ENGINE_OIL("ENGINE_OIL", "ENGINE_OIL"),
    OIL_FILTER("O<PERSON>_FILTER", "OIL_FILTER"),
    TIRE("TIRE", "TIRE"),
    FILM("FILM", "FILM"),
    PPF("PPF", "PAINT PROTECTION FILM (PPF)"),
    WRAP_FILM("WRAP_FILM", "WRAP_FILM"),
    WINDOW_FILM("WINDOW_FILM", "WINDOW_FILM"),
    FOOT_PAD("FOOT_PAD", "FOOT_PAD"),
    WIPER("WIPER", "WIPER"),
    ALL("ALL", "ALL"),
    ;

    private final String code;

    private final String desc;


    CarMatchProductTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarMatchProductTypeEnum getCarMatchProductTypeEnumByProductCategoryId(Long categoryId) {
        if (categoryId == null) {
            return ALL;
        }
        return switch (categoryId.intValue()) {
            case 1 -> ENGINE_OIL;
            case 2 -> OIL_FILTER;
            case 3 -> TIRE;
            case 4 -> PPF;
            case 5 -> WRAP_FILM;
            case 6 -> WINDOW_FILM;
            case 10 -> FOOT_PAD;
            case 11 -> WIPER;
            default -> UNKNOWN;
        };
    }

    public static boolean canMatch(CarMatchProductTypeEnum pt) {
        return pt != UNKNOWN;
    }

    public static CarMatchProductTypeEnum getByCode(String code) {
        for (CarMatchProductTypeEnum e : CarMatchProductTypeEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }


}
