package com.servauto.mall.enums.payment.ipay88;

public enum Ipay88Status {
    SUCCESS("1", "SUCCESS"),
    FAIL("0", "FAIL"),
    PENDING("6", "PENDING"),
    AUTHORISED("20", "AUTHORISED"),
    UNKNOWN("UNKNOWN", "UNKNOWN");

    private final String code;

    private final String desc;

    Ipay88Status(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Ipay88Status getByCode(String code) {
        for (Ipay88Status e : Ipay88Status.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}

