package com.servauto.mall.enums.payment.i2c2p;

public enum I2c2pStatus {
    SUCCESS("0000", "SUCCESS"),
    CANCEL("0003", "CANCEL"),
    UNKNOWN("UNKNOWN", "UNKNOWN");

    private final String code;

    private final String desc;

    I2c2pStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static I2c2pStatus getByCode(String code) {
        for (I2c2pStatus e : I2c2pStatus.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}

