package com.servauto.mall.enums.order;

import cn.hutool.core.util.ArrayUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum OrderStatusEnum {

    PENDING_PAY("PENDING_PAY", "Pending Payment"),
    PAYMENT_SUCCESS("PAYMENT_SUCCESS", "Payment Completed"),
    PENDING_WORKSHOP_CONFIRM("PENDING_WORKSHOP_CONFIRM", "Pending Workshop Confirm"),
    PENDING_HQOPS_CONFIRM("PENDING_HQOPS_CONFIRM", "Pending HQ Ops Confirm"),
    PENDING_DELIVERY("PENDING_DELIVERY", "Pending Delivery"),
    COMPLETED("COMPLETED", "Order Completed"),
    DESPATCHED("DESPATCHED", "Despatched"),
    CANCELED("CANCELED", "Cancelled"),
    CANCELED_TIMEOUT("CANCELED_TIMEOUT", "Cancelled"),
    FINISHED("FINISHED", "Order Closed"),
    ;

    private final String code;

    private final String msg;

    OrderStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isUnpaid(String code) {
        return PENDING_PAY.code.equals(code);
    }

    public static boolean isPendingDelivery(String code) {
        OrderStatusEnum status = getByCode(code);
        switch (status) {
            case PAYMENT_SUCCESS, PENDING_WORKSHOP_CONFIRM, PENDING_HQOPS_CONFIRM, PENDING_DELIVERY -> {
                return true;
            }
            default -> {
                return false;
            }
        }
    }

    public static List<String> allowReservation() {
        return Arrays.asList(OrderStatusEnum.PAYMENT_SUCCESS.getCode(),
                OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode(), OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode(), OrderStatusEnum.PENDING_DELIVERY.getCode());
    }

    public static boolean hasPaid(String code) {
        return !PENDING_PAY.code.equals(code) && !CANCELED.code.equals(code) && !CANCELED_TIMEOUT.code.equals(code);
    }

    public static boolean isCanceled(String code) {
        return CANCELED.code.equals(code) || CANCELED_TIMEOUT.code.equals(code);
    }

    public static OrderStatusEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }

    public static String format(String code) {
        OrderStatusEnum status = getByCode(code);
        return switch (status) {
            case PAYMENT_SUCCESS, PENDING_WORKSHOP_CONFIRM, PENDING_HQOPS_CONFIRM, PENDING_DELIVERY ->
                    "Pending Delivery";
            default -> status.getMsg();
        };
    }

    public static List<String> parse() {
        return Arrays.asList(OrderStatusEnum.PAYMENT_SUCCESS.getCode(),
                OrderStatusEnum.PENDING_DELIVERY.getCode(),
                OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode(),
                OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode());
    }

}
