package com.servauto.mall.enums.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
//@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OrderDeliveryTypeEnum {

    SHIPPING("SHIPPING", "Ship to Home"),
    P<PERSON><PERSON><PERSON>("PICKUP", "Self-Pick Up"),
    WOR<PERSON>H<PERSON>("IN-WORKSHOP", "In-Store Service"),
    ;

    private final String code;

    private final String msg;

    OrderDeliveryTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isShipping(String code) {
        return SHIPPING.getCode().equals(code);
    }

    public static boolean isPickup(String code) {
        return PICKUP.getCode().equals(code);
    }

    public static boolean isInWorkshop(String code) {
        return WORKSHOP.getCode().equals(code);
    }

    public static OrderDeliveryTypeEnum getByCode(String code) {
        for (OrderDeliveryTypeEnum e : OrderDeliveryTypeEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }
}
