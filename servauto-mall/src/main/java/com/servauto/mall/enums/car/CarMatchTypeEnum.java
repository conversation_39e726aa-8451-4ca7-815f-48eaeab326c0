package com.servauto.mall.enums.car;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum CarMatchTypeEnum {

    SAE_GRADE("SAE_GRADE", "SAE_GRADE"),
    OIL_TYPE("OIL_TYPE", "OIL_TYPE"),
    WIPER_MAIN("WIPER_MAIN", "WIPER_MAIN"),
    WIPER_REAR("WIPER_REAR", "WIPER_REAR"),
    WIPER_FRONT("WIPER_FRONT", "WIPER_FRONT"),
    SIZE("SIZE", "SIZE"),
    ;

    private final String code;

    private final String desc;


    CarMatchTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static long getProductAttributeIdByCode(CarMatchProductTypeEnum pt, CarMatchTypeEnum mt) {
        switch (pt) {
            case ENGINE_OIL:
                switch (mt) {
                    case SAE_GRADE -> {
                        return 1;
                    }
                    case OIL_TYPE -> {
                        return 6;
                    }
                    default -> {
                        return 0;
                    }
                }
            case OIL_FILTER:
                if (Objects.requireNonNull(mt) == CarMatchTypeEnum.SIZE) {
                    return 107;
                }
                return 0;
            case TIRE:
                if (Objects.requireNonNull(mt) == CarMatchTypeEnum.SIZE) {
                    return 200;
                }
                return 0;
            case PPF:
                if (Objects.requireNonNull(mt) == CarMatchTypeEnum.SIZE) {
                    return 300;
                }
                return 0;
            case WRAP_FILM:
                if (Objects.requireNonNull(mt) == CarMatchTypeEnum.SIZE) {
                    return 400;
                }
                return 0;
            case WINDOW_FILM:
                if (Objects.requireNonNull(mt) == CarMatchTypeEnum.SIZE) {
                    return 500;
                }
                return 0;
            case FOOT_PAD:
                return -1;
            case WIPER:
                switch (mt) {
                    case WIPER_MAIN -> {
                        return 1000;
                    }
                    case WIPER_REAR -> {
                        return 1002;
                    }
                    case WIPER_FRONT -> {
                        return 1001;
                    }
                    default -> {
                        return 0;
                    }
                }
            default:
                return 0;
        }
    }

    public static CarMatchTypeEnum getByCode(String code) {
        for (CarMatchTypeEnum e : CarMatchTypeEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return SIZE;
    }


}
