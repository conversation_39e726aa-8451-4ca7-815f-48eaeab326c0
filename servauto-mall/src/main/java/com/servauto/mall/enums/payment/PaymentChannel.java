package com.servauto.mall.enums.payment;

public enum PaymentChannel {
    IPAY88(1, "Ipay88"),

    I2C2P(2, "I2c2p"),

    UNKNOWN(99, "UNKNOWN");

    private final Integer code;

    private final String desc;

    PaymentChannel(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PaymentChannel getByCode(Integer code) {
        for (PaymentChannel e : PaymentChannel.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}

