package com.servauto.mall.dao.product;

import com.servauto.mall.model.entity.product.ProductAttributeDisplay;

import java.util.List;

public interface ProductAttributeDisplayMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAttributeDisplay record);

    int insertSelective(ProductAttributeDisplay record);

    ProductAttributeDisplay selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAttributeDisplay record);

    int updateByPrimaryKey(ProductAttributeDisplay record);

    List<ProductAttributeDisplay> selectByTypeAndCategoryIds(String type, List<Long> categoryIds);
}