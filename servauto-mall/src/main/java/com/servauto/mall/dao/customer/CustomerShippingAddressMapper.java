package com.servauto.mall.dao.customer;

import com.servauto.mall.model.entity.customer.CustomerShippingAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerShippingAddressMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CustomerShippingAddress record);

    int insertSelective(CustomerShippingAddress record);

    CustomerShippingAddress selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomerShippingAddress record);

    int updateByPrimaryKey(CustomerShippingAddress record);

    List<CustomerShippingAddress> selectByCustomerId(@Param("customerId") Long customerId);
}