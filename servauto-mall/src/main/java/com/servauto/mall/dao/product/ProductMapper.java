package com.servauto.mall.dao.product;

import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.model.entity.product.Product;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductMapper {
    int insert(Product record);

    int insertSelective(Product record);

    Product selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Product record);

    int updateByPrimaryKeyWithBLOBs(Product record);

    int updateByPrimaryKey(Product record);

    List<Product> selectByConditions(@Param("conditions") QueryProductsDTO queryProductsDTO);
}