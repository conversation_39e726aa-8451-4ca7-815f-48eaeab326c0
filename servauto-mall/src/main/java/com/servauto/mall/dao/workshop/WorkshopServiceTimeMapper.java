package com.servauto.mall.dao.workshop;

import com.servauto.mall.model.entity.workshop.WorkshopServiceTime;

import java.util.List;

public interface WorkshopServiceTimeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WorkshopServiceTime record);

    int insertSelective(WorkshopServiceTime record);

    WorkshopServiceTime selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WorkshopServiceTime record);

    int updateByPrimaryKey(WorkshopServiceTime record);

    List<WorkshopServiceTime> selectByWorkshopIds(List<Long> workshopIds);
}