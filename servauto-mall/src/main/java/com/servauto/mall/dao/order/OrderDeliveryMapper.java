package com.servauto.mall.dao.order;

import com.servauto.mall.model.entity.order.OrderDelivery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDeliveryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderDelivery record);

    int insertSelective(OrderDelivery record);

    OrderDelivery selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderDelivery record);

    int updateByPrimaryKey(OrderDelivery record);

    List<OrderDelivery> selectByOrderNos(@Param("orderNos") List<String> orderNos);
}