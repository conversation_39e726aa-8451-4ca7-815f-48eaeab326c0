package com.servauto.mall.dao.car;

import com.servauto.mall.model.entity.car.CarInfo;
import com.servauto.mall.model.entity.car.CarInfoWithBLOBs;

import java.util.List;

public interface CarInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CarInfoWithBLOBs record);

    int insertSelective(CarInfoWithBLOBs record);

    CarInfoWithBLOBs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarInfoWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(CarInfoWithBLOBs record);

    int updateByPrimaryKey(CarInfo record);

    List<CarInfo> selectByCustomerId(Long customerId);

    int deleteByCustomerId(Long customerId);

    void batchInsert(List<CarInfo> carInfoList);

    void deleteByCustomerIdAndCarId(Long customerId, Long carId);

    void setDefaultCar(Long customerId, Long carId);
    void clearDefaultCar(Long customerId);

    CarInfo getDefaultCar(Long customerId);
}