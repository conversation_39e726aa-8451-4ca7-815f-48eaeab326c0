package com.servauto.mall.dao.content;

import com.servauto.mall.model.entity.content.ContentResource;

import java.util.List;

public interface ContentResourceMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContentResource record);

    int insertSelective(ContentResource record);

    ContentResource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentResource record);

    int updateByPrimaryKey(ContentResource record);

    List<ContentResource> selectByIds(List<Long> ids);
}