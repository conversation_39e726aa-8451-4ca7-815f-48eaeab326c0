package com.servauto.mall.dao.customer;

import com.servauto.mall.model.entity.customer.CustomerInfo;
import org.apache.ibatis.annotations.Param;

public interface CustomerInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CustomerInfo record);

    int insertSelective(CustomerInfo record);

    CustomerInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomerInfo record);

    int updateByPrimaryKey(CustomerInfo record);

    CustomerInfo selectByMobile(@Param("mobile") String mobile);
}