package com.servauto.mall.dao.payment;

import com.servauto.mall.model.entity.payment.PaymentLog;

public interface PaymentLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentLog record);

    int insertSelective(PaymentLog record);

    PaymentLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentLog record);

    int updateByPrimaryKeyWithBLOBs(PaymentLog record);

    int updateByPrimaryKey(PaymentLog record);
}