package com.servauto.mall.dao.product;

import com.servauto.mall.model.entity.product.ProductBrand;

import java.util.List;

public interface ProductBrandMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductBrand record);

    int insertSelective(ProductBrand record);

    ProductBrand selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductBrand record);

    int updateByPrimaryKey(ProductBrand record);

    List<ProductBrand> selectAll();

    List<ProductBrand> selectByIds(List<Long> ids);
}