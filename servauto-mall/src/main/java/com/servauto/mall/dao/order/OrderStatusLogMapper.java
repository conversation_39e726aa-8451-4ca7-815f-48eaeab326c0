package com.servauto.mall.dao.order;

import com.servauto.mall.model.entity.order.OrderStatusLog;
import com.servauto.mall.model.entity.order.OrderStatusLogExample;
import java.util.List;

public interface OrderStatusLogMapper {
    int insert(OrderStatusLog record);

    int insertSelective(OrderStatusLog record);

    List<OrderStatusLog> selectByExample(OrderStatusLogExample example);

    int updateByPrimaryKeySelective(OrderStatusLog record);

    int updateByPrimaryKey(OrderStatusLog record);
}