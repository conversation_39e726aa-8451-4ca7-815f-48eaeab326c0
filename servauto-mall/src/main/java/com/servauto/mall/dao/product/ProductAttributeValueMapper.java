package com.servauto.mall.dao.product;

import com.servauto.mall.model.entity.product.ProductAttributeValue;

import java.util.List;

public interface ProductAttributeValueMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAttributeValue record);

    int insertSelective(ProductAttributeValue record);

    ProductAttributeValue selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAttributeValue record);

    int updateByPrimaryKey(ProductAttributeValue record);

    List<ProductAttributeValue> selectByProductIds(List<Long> productIds);

    List<Long> selectProductIdsByAttributeIdAndValues(Long attributeId, List<String> values);
}