package com.servauto.mall.dao.car;

import com.servauto.mall.model.entity.car.CarLibrary;

import java.util.List;

public interface CarLibraryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CarLibrary record);

    int insertSelective(CarLibrary record);

    CarLibrary selectByPrimaryKey(Integer id);

    CarLibrary selectByByLibId(String carLibId);

    List<CarLibrary> selectByByLibIds(List<String> carLibIds);

    int updateByPrimaryKeySelective(CarLibrary record);

    int updateByPrimaryKey(CarLibrary record);

    //获取去重之后的所有brands
    List<String> selectAllBrandsDistinct();

    //根据brand获取所有models
    List<String> selectModelsByBrandDistinct(String brand);

    //根据brand和model获取所有years
    List<String> selectModelYearsByBrandAndModelDistinct(String brand, String model);

    //根据brand、model和model_year获取去重后的grade集合
    List<String> selectVariantByBrandModelAndYearDistinct(String brand, String model, String modelYear);

    // 根据brand、model、model_year和grade获取carlibrary的集合
    List<CarLibrary> selectCarLibrariesByBrandModelYearAndGrade(String brand, String model, String modelYear, String grade);

    List<CarLibrary> selectWaitSyncDistinctBrands();

    void updateCarLibraryStatusByIds(List<Integer> ids);
}