package com.servauto.mall.dao.packageinfo;

import com.servauto.mall.model.entity.packageinfo.PackageInfo;
import org.apache.ibatis.annotations.Param;
import com.servauto.mall.model.dto.request.packageinfo.QueryPackagesDTO;

import java.util.List;

public interface PackageInfoMapper {
    int insert(PackageInfo record);

    int insertSelective(PackageInfo record);

    PackageInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PackageInfo record);

    int updateByPrimaryKey(PackageInfo record);

    List<PackageInfo> selectByConditions(@Param("conditions") QueryPackagesDTO conditions);
}