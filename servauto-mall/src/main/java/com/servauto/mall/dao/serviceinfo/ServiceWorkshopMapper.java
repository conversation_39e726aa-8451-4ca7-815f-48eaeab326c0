package com.servauto.mall.dao.serviceinfo;

import com.servauto.mall.model.entity.serviceinfo.ServiceWorkshop;

import java.util.List;

public interface ServiceWorkshopMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ServiceWorkshop record);

    int insertSelective(ServiceWorkshop record);

    ServiceWorkshop selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceWorkshop record);

    int updateByPrimaryKey(ServiceWorkshop record);

    List<ServiceWorkshop> selectByServiceId(Long serviceId);
}