package com.servauto.mall.dao.car;

import com.servauto.mall.model.entity.car.CarProductMatchInfo;
import com.servauto.mall.model.entity.car.CarProductMatchInfoExample;

import java.util.List;

public interface CarProductMatchInfoMapper {
    List<CarProductMatchInfo> selectByExample(CarProductMatchInfoExample example);

    CarProductMatchInfo selectByPrimaryKey(Integer id);

    List<CarProductMatchInfo> selectMatchInfo(String carLibId, String productType);
}