package com.servauto.mall.dao.payment;

import com.servauto.mall.model.dto.request.payment.QueryPaymentDTO;
import com.servauto.mall.model.entity.payment.Payment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PaymentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Payment record);

    int insertSelective(Payment record);

    Payment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Payment record);

    int updateByPrimaryKey(Payment record);

    List<Payment> selectByConditions(QueryPaymentDTO queryPaymentDTO);

    List<Payment> selectByPaymentIds(@Param("ids") List<Long> ids);
    // 修改支付状态
    int updateStatusByPayNo(@Param("status") String status, @Param("payNo") Long payNo);

    int createPayment(Payment payment);

    Payment selectBySourceId(String sourceId);

    Payment selectByPayNo(String payNo);

    List<Payment> getPaymentByStatus(String status,Integer channel);

    void updatePaymentStatus(Payment payment);
}