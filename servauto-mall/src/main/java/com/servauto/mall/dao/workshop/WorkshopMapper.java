package com.servauto.mall.dao.workshop;

import com.servauto.mall.model.entity.workshop.Workshop;
import org.apache.ibatis.annotations.Param;
import com.servauto.mall.model.dto.request.workshop.QueryWorkshopsDTO;

import java.util.List;

public interface WorkshopMapper {
    Workshop selectByPrimaryKey(Long id);

    List<Workshop> selectByConditions(@Param("conditions") QueryWorkshopsDTO queryWorkshopsDTO);
}