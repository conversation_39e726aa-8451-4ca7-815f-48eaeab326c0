package com.servauto.mall.dao.order;

import com.servauto.mall.model.entity.order.OrderProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderProductMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderProduct record);

    int insertSelective(OrderProduct record);

    OrderProduct selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderProduct record);

    int updateByPrimaryKey(OrderProduct record);

    void batchInsert(@Param("orderProducts") List<OrderProduct> orderProducts);

    List<OrderProduct> selectByOrderNos(@Param("orderNos") List<String> orderNos);


}