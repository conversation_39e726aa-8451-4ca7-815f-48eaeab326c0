package com.servauto.mall.dao.content;

import com.servauto.mall.model.entity.content.ContentLayout;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContentLayoutMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContentLayout record);

    int insertSelective(ContentLayout record);

    ContentLayout selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentLayout record);

    int updateByPrimaryKey(ContentLayout record);

    List<ContentLayout> selectByTemplateCode(@Param("templateCode") String templateCode);
}