package com.servauto.mall.dao.order;

import com.servauto.mall.model.entity.order.OrderPay;
import com.servauto.mall.model.entity.order.OrderPayExample;
import java.util.List;

public interface OrderPayMapper {
    int insert(OrderPay record);

    int insertSelective(OrderPay record);

    List<OrderPay> selectByExample(OrderPayExample example);

    int updateByPrimaryKeySelective(OrderPay record);

    int updateByPrimaryKey(OrderPay record);
}