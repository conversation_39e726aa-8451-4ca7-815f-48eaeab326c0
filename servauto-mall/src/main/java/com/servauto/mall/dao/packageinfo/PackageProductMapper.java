package com.servauto.mall.dao.packageinfo;

import com.servauto.mall.model.entity.packageinfo.PackageProduct;

import java.util.List;

public interface PackageProductMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PackageProduct record);

    int insertSelective(PackageProduct record);

    PackageProduct selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PackageProduct record);

    int updateByPrimaryKey(PackageProduct record);

    List<PackageProduct> selectByPackageId(Long packageId);

    List<PackageProduct> selectByPackageIds(List<Long> packageIds);

    List<PackageProduct> selectByProductId(Long productId);
}