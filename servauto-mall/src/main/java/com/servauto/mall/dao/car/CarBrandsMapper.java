package com.servauto.mall.dao.car;


import com.servauto.mall.model.entity.car.CarBrands;

import java.util.List;

public interface CarBrandsMapper {


    // 新增方法：查询所有有效的brands
    List<CarBrands> selectAllValidBrands();

    // 新增方法：查询所有有效的brands
    List<CarBrands> selectAllValidTopBrands();

    CarBrands selectByPrimaryKey(Long id);

    List<CarBrands> selectByPrimaryKeys(List<Long> brandIds);
}