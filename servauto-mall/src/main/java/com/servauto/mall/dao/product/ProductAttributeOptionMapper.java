package com.servauto.mall.dao.product;

import com.servauto.mall.model.entity.product.ProductAttributeOption;

import java.util.List;

public interface ProductAttributeOptionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAttributeOption record);

    int insertSelective(ProductAttributeOption record);

    ProductAttributeOption selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAttributeOption record);

    int updateByPrimaryKey(ProductAttributeOption record);

    List<ProductAttributeOption> selectByAttributeIds(List<Long> attributeIds);
}