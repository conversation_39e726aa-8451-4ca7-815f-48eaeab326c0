package com.servauto.mall.dao.order;

import com.servauto.mall.model.dto.request.order.QueryOrderReqDTO;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.model.entity.order.OrderExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMapper {

    int insertSelective(Order record);

    List<Order> selectByExample(OrderExample example);

    List<Order> selectByConditions(@Param("conditions") QueryOrderReqDTO reqDTO);

    int checkPickupCodeExist(@Param("pickupCode") String pickupCode);

    int casByOrderNoAndStatusList(@Param("orderNo") String orderNo,
                                  @Param("o") Order order,
                                  @Param("statusList") List<String> statusList);

    Order selectByOrderNo(@Param("orderNo") String orderNo);
}