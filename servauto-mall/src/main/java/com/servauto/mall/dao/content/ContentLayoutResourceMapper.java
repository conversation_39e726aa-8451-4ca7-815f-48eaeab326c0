package com.servauto.mall.dao.content;

import com.servauto.mall.model.entity.content.ContentLayoutResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContentLayoutResourceMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContentLayoutResource record);

    int insertSelective(ContentLayoutResource record);

    ContentLayoutResource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentLayoutResource record);

    int updateByPrimaryKey(ContentLayoutResource record);

    List<ContentLayoutResource> selectByLayoutIds(@Param("layoutIds") List<Long> layoutIds);
}