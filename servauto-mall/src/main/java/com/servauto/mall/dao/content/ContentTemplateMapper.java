package com.servauto.mall.dao.content;

import com.servauto.mall.model.entity.content.ContentTemplate;
import org.apache.ibatis.annotations.Param;

public interface ContentTemplateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContentTemplate record);

    int insertSelective(ContentTemplate record);

    ContentTemplate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentTemplate record);

    int updateByPrimaryKey(ContentTemplate record);

    ContentTemplate selectByTemplateCode(@Param("templateCode") String templateCode);
}