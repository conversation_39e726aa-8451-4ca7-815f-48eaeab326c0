server:
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100
  shutdown: graceful

spring:
  application:
    name: servauto-mall
  main:
    allow-circular-references: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      slave:
        enabled: false
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
  messages:
    basename: i18n/messages
  jackson:
    serialization:
      write-dates-as-timestamps: true
      write-date-timestamps-as-nanoseconds: false
      write-durations-as-timestamps: true
      fail-on-empty-beans: false
  cache:
    type: REDIS

mybatis:
  typeAliasesPackage: com.servauto.mall.**.entity
  mapperLocations: classpath*:mappers/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql