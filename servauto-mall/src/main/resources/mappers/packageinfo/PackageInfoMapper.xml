<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.packageinfo.PackageInfoMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.packageinfo.PackageInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delivery_modes" jdbcType="INTEGER" property="deliveryModes" />
    <result column="is_fixed_price" jdbcType="INTEGER" property="isFixedPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, code, service_id, status, delivery_modes, is_fixed_price, price, updated_by, 
    create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from package_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.packageinfo.PackageInfo">
    insert into package_info (id, name, code, 
      service_id, status, delivery_modes, 
      is_fixed_price, price, updated_by, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{serviceId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{deliveryModes,jdbcType=INTEGER}, 
      #{isFixedPrice,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{updatedBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.packageinfo.PackageInfo">
    insert into package_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deliveryModes != null">
        delivery_modes,
      </if>
      <if test="isFixedPrice != null">
        is_fixed_price,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deliveryModes != null">
        #{deliveryModes,jdbcType=INTEGER},
      </if>
      <if test="isFixedPrice != null">
        #{isFixedPrice,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.packageinfo.PackageInfo">
    update package_info
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deliveryModes != null">
        delivery_modes = #{deliveryModes,jdbcType=INTEGER},
      </if>
      <if test="isFixedPrice != null">
        is_fixed_price = #{isFixedPrice,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.packageinfo.PackageInfo">
    update package_info
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      delivery_modes = #{deliveryModes,jdbcType=INTEGER},
      is_fixed_price = #{isFixedPrice,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from package_info
    <where>
      <if test="conditions.name != null and conditions.name != ''">
        and name LIKE CONCAT(#{conditions.name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="conditions.code != null">
        and code = #{conditions.code,jdbcType=VARCHAR}
      </if>
      <if test="conditions.status != null and conditions.status > 0">
        and status = #{conditions.status,jdbcType=INTEGER}
      </if>
      <if test="conditions.serviceId != null and conditions.serviceId > 0">
        and service_id = #{conditions.serviceId,jdbcType=BIGINT}
      </if>
      <if test="conditions.ids != null and conditions.ids.size() > 0">
        and id IN
        <foreach item="id" collection="conditions.ids" open="(" separator="," close=")">
          #{id,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
</mapper>