<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.product.ProductMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.product.Product">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="featured_tags" jdbcType="VARCHAR" property="featuredTags" />
    <result column="delivery_modes" jdbcType="INTEGER" property="deliveryModes" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="net_content" jdbcType="INTEGER" property="netContent" />
    <result column="content_unit" jdbcType="VARCHAR" property="contentUnit" />
    <result column="main_image" jdbcType="VARCHAR" property="mainImage" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.mall.model.entity.product.Product">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, status, category_id, brand_id, featured_tags, delivery_modes, price, service_id, 
    net_content, content_unit, main_image, updated_by, create_time, update_time, deleted
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from product
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.product.Product">
    insert into product (id, name, status, 
      category_id, brand_id, featured_tags, 
      delivery_modes, price, service_id, 
      net_content, content_unit, main_image, 
      updated_by, create_time, update_time, 
      deleted, description)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{categoryId,jdbcType=BIGINT}, #{brandId,jdbcType=BIGINT}, #{featuredTags,jdbcType=VARCHAR}, 
      #{deliveryModes,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{serviceId,jdbcType=BIGINT}, 
      #{netContent,jdbcType=INTEGER}, #{contentUnit,jdbcType=VARCHAR}, #{mainImage,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT}, #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.product.Product">
    insert into product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="featuredTags != null">
        featured_tags,
      </if>
      <if test="deliveryModes != null">
        delivery_modes,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="netContent != null">
        net_content,
      </if>
      <if test="contentUnit != null">
        content_unit,
      </if>
      <if test="mainImage != null">
        main_image,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="featuredTags != null">
        #{featuredTags,jdbcType=VARCHAR},
      </if>
      <if test="deliveryModes != null">
        #{deliveryModes,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="netContent != null">
        #{netContent,jdbcType=INTEGER},
      </if>
      <if test="contentUnit != null">
        #{contentUnit,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.product.Product">
    update product
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="featuredTags != null">
        featured_tags = #{featuredTags,jdbcType=VARCHAR},
      </if>
      <if test="deliveryModes != null">
        delivery_modes = #{deliveryModes,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="netContent != null">
        net_content = #{netContent,jdbcType=INTEGER},
      </if>
      <if test="contentUnit != null">
        content_unit = #{contentUnit,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.servauto.mall.model.entity.product.Product">
    update product
    set name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=BIGINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      featured_tags = #{featuredTags,jdbcType=VARCHAR},
      delivery_modes = #{deliveryModes,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      service_id = #{serviceId,jdbcType=BIGINT},
      net_content = #{netContent,jdbcType=INTEGER},
      content_unit = #{contentUnit,jdbcType=VARCHAR},
      main_image = #{mainImage,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.product.Product">
    update product
    set name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=BIGINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      featured_tags = #{featuredTags,jdbcType=VARCHAR},
      delivery_modes = #{deliveryModes,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      service_id = #{serviceId,jdbcType=BIGINT},
      net_content = #{netContent,jdbcType=INTEGER},
      content_unit = #{contentUnit,jdbcType=VARCHAR},
      main_image = #{mainImage,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from product
    <where>
      <if test="conditions.ids != null and conditions.ids.size() > 0">
        and id IN
        <foreach item="id" collection="conditions.ids" open="(" separator="," close=")">
          #{id,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="conditions.name != null and conditions.name != ''">
        and name LIKE CONCAT(#{conditions.name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="conditions.footPadNames != null and conditions.footPadNames.size() > 0">
        and name IN
        <foreach item="name" collection="conditions.footPadNames" open="(" separator="," close=")">
          #{name,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="conditions.serviceId != null and conditions.serviceId > 0">
        and service_id = #{conditions.serviceId,jdbcType=BIGINT}
      </if>
      <if test="conditions.categoryId != null and conditions.categoryId > 0">
        and category_id = #{conditions.categoryId,jdbcType=BIGINT}
      </if>
      <if test="conditions.brandIds != null and conditions.brandIds.size() > 0">
        and brand_id IN
        <foreach item="brandId" collection="conditions.brandIds" open="(" separator="," close=")">
          #{brandId,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="conditions.status != null and conditions.status > 0">
        and status = #{conditions.status,jdbcType=INTEGER}
      </if>
    </where>
    <if test="conditions.sortByPrice != null and conditions.sortByPrice > 0">
      order by price
      <if test="conditions.sortByPrice == 1">
        asc
      </if>
      <if test="conditions.sortByPrice == 2">
        desc
      </if>
    </if>
  </select>
</mapper>