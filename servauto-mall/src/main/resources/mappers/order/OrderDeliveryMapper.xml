<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.order.OrderDeliveryMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.order.OrderDelivery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="tracking_no" jdbcType="VARCHAR" property="trackingNo" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="state_name" jdbcType="VARCHAR" property="stateName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, name, mobile, tracking_no, state_code, state_name, city_code, city_name, 
    address, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_delivery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_delivery
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.order.OrderDelivery">
    insert into order_delivery (id, order_no, name, 
      mobile, tracking_no, state_code, 
      state_name, city_code, city_name, 
      address, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{trackingNo,jdbcType=VARCHAR}, #{stateCode,jdbcType=VARCHAR}, 
      #{stateName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.order.OrderDelivery">
    insert into order_delivery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="trackingNo != null">
        tracking_no,
      </if>
      <if test="stateCode != null">
        state_code,
      </if>
      <if test="stateName != null">
        state_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="trackingNo != null">
        #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="stateCode != null">
        #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="stateName != null">
        #{stateName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.order.OrderDelivery">
    update order_delivery
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="trackingNo != null">
        tracking_no = #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="stateCode != null">
        state_code = #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="stateName != null">
        state_name = #{stateName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.order.OrderDelivery">
    update order_delivery
    set order_no = #{orderNo,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      tracking_no = #{trackingNo,jdbcType=VARCHAR},
      state_code = #{stateCode,jdbcType=VARCHAR},
      state_name = #{stateName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_delivery
    where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
  </select>









</mapper>