<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.order.OrderPayMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.order.OrderPay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="grand_total" jdbcType="DECIMAL" property="grandTotal" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="canceled_time" jdbcType="TIMESTAMP" property="canceledTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, pay_no, customer_id, grand_total, paid_amount, error_code, error_msg, 
    pay_method, paid_time, canceled_time, create_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.servauto.mall.model.entity.order.OrderPayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.order.OrderPay">
    insert into order_pay (id, order_no, pay_no, 
      customer_id, grand_total, paid_amount, 
      error_code, error_msg, pay_method, 
      paid_time, canceled_time, create_time, 
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{payNo,jdbcType=VARCHAR}, 
      #{customerId,jdbcType=BIGINT}, #{grandTotal,jdbcType=DECIMAL}, #{paidAmount,jdbcType=DECIMAL}, 
      #{errorCode,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, 
      #{paidTime,jdbcType=TIMESTAMP}, #{canceledTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.order.OrderPay">
    insert into order_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="grandTotal != null">
        grand_total,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>
      <if test="paidTime != null">
        paid_time,
      </if>
      <if test="canceledTime != null">
        canceled_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="grandTotal != null">
        #{grandTotal,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="paidTime != null">
        #{paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canceledTime != null">
        #{canceledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.order.OrderPay">
    update order_pay
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="grandTotal != null">
        grand_total = #{grandTotal,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="paidTime != null">
        paid_time = #{paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canceledTime != null">
        canceled_time = #{canceledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.order.OrderPay">
    update order_pay
    set order_no = #{orderNo,jdbcType=VARCHAR},
      pay_no = #{payNo,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=BIGINT},
      grand_total = #{grandTotal,jdbcType=DECIMAL},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      pay_method = #{payMethod,jdbcType=VARCHAR},
      paid_time = #{paidTime,jdbcType=TIMESTAMP},
      canceled_time = #{canceledTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>