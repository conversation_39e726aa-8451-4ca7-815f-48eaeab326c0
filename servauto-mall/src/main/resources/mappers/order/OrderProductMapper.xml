<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.order.OrderProductMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.order.OrderProduct">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sub_order_no" jdbcType="VARCHAR" property="subOrderNo" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="net_content" jdbcType="INTEGER" property="netContent" />
    <result column="content_unit" jdbcType="VARCHAR" property="contentUnit" />
    <result column="product_attribute" jdbcType="VARCHAR" property="productAttribute" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="reserve_price" jdbcType="DECIMAL" property="reservePrice" />
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
    <result column="discount_price" jdbcType="DECIMAL" property="discountPrice" />
    <result column="promotion_discount" jdbcType="DECIMAL" property="promotionDiscount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, sub_order_no, product_id, product_name, brand_id, brand_name, net_content, 
    content_unit, product_attribute, category_id, category_name, image, reserve_price, 
    actual_price, discount_price, promotion_discount, discount_amount, quantity, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_product
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_product
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.order.OrderProduct">
    insert into order_product (id, order_no, sub_order_no, 
      product_id, product_name, brand_id, 
      brand_name, net_content, content_unit, 
      product_attribute, category_id, category_name, 
      image, reserve_price, actual_price, 
      discount_price, promotion_discount, discount_amount, 
      quantity, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{subOrderNo,jdbcType=VARCHAR}, 
      #{productId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, #{brandId,jdbcType=BIGINT}, 
      #{brandName,jdbcType=VARCHAR}, #{netContent,jdbcType=INTEGER}, #{contentUnit,jdbcType=VARCHAR}, 
      #{productAttribute,jdbcType=VARCHAR}, #{categoryId,jdbcType=BIGINT}, #{categoryName,jdbcType=VARCHAR}, 
      #{image,jdbcType=VARCHAR}, #{reservePrice,jdbcType=DECIMAL}, #{actualPrice,jdbcType=DECIMAL}, 
      #{discountPrice,jdbcType=DECIMAL}, #{promotionDiscount,jdbcType=DECIMAL}, #{discountAmount,jdbcType=DECIMAL}, 
      #{quantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.order.OrderProduct">
    insert into order_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="subOrderNo != null">
        sub_order_no,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="netContent != null">
        net_content,
      </if>
      <if test="contentUnit != null">
        content_unit,
      </if>
      <if test="productAttribute != null">
        product_attribute,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="reservePrice != null">
        reserve_price,
      </if>
      <if test="actualPrice != null">
        actual_price,
      </if>
      <if test="discountPrice != null">
        discount_price,
      </if>
      <if test="promotionDiscount != null">
        promotion_discount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="subOrderNo != null">
        #{subOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="netContent != null">
        #{netContent,jdbcType=INTEGER},
      </if>
      <if test="contentUnit != null">
        #{contentUnit,jdbcType=VARCHAR},
      </if>
      <if test="productAttribute != null">
        #{productAttribute,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="reservePrice != null">
        #{reservePrice,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountPrice != null">
        #{discountPrice,jdbcType=DECIMAL},
      </if>
      <if test="promotionDiscount != null">
        #{promotionDiscount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.order.OrderProduct">
    update order_product
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="subOrderNo != null">
        sub_order_no = #{subOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="netContent != null">
        net_content = #{netContent,jdbcType=INTEGER},
      </if>
      <if test="contentUnit != null">
        content_unit = #{contentUnit,jdbcType=VARCHAR},
      </if>
      <if test="productAttribute != null">
        product_attribute = #{productAttribute,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="reservePrice != null">
        reserve_price = #{reservePrice,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        actual_price = #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountPrice != null">
        discount_price = #{discountPrice,jdbcType=DECIMAL},
      </if>
      <if test="promotionDiscount != null">
        promotion_discount = #{promotionDiscount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.order.OrderProduct">
    update order_product
    set order_no = #{orderNo,jdbcType=VARCHAR},
      sub_order_no = #{subOrderNo,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=BIGINT},
      brand_name = #{brandName,jdbcType=VARCHAR},
      net_content = #{netContent,jdbcType=INTEGER},
      content_unit = #{contentUnit,jdbcType=VARCHAR},
      product_attribute = #{productAttribute,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=BIGINT},
      category_name = #{categoryName,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      reserve_price = #{reservePrice,jdbcType=DECIMAL},
      actual_price = #{actualPrice,jdbcType=DECIMAL},
      discount_price = #{discountPrice,jdbcType=DECIMAL},
      promotion_discount = #{promotionDiscount,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      quantity = #{quantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into order_product
    (order_no, sub_order_no,
    product_id, product_name, brand_id,
    brand_name, net_content, content_unit,
    product_attribute, category_id, category_name,
    image, reserve_price, actual_price,
    discount_price, promotion_discount, discount_amount,
    quantity)

    values
    <foreach collection="orderProducts" item="orderProduct" separator=",">
      (
      #{orderProduct.orderNo,jdbcType=VARCHAR},
      #{orderProduct.subOrderNo,jdbcType=VARCHAR},
      #{orderProduct.productId,jdbcType=BIGINT},
      #{orderProduct.productName,jdbcType=VARCHAR},
      #{orderProduct.brandId,jdbcType=BIGINT},
      #{orderProduct.brandName,jdbcType=VARCHAR},
      #{orderProduct.netContent,jdbcType=INTEGER},
      #{orderProduct.contentUnit,jdbcType=VARCHAR},
      #{orderProduct.productAttribute,jdbcType=VARCHAR},
      #{orderProduct.categoryId,jdbcType=BIGINT},
      #{orderProduct.categoryName,jdbcType=VARCHAR},
      #{orderProduct.image,jdbcType=VARCHAR},
      #{orderProduct.reservePrice,jdbcType=DECIMAL},
      #{orderProduct.actualPrice,jdbcType=DECIMAL},
      #{orderProduct.discountPrice,jdbcType=DECIMAL},
      #{orderProduct.promotionDiscount,jdbcType=DECIMAL},
      #{orderProduct.discountAmount,jdbcType=DECIMAL},
      #{orderProduct.quantity,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_product
    where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo}
    </foreach>
  </select>














</mapper>