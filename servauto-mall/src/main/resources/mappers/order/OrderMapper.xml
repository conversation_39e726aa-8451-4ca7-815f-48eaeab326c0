<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.order.OrderMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.order.Order">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="customer_email" jdbcType="VARCHAR" property="customerEmail" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_mobile" jdbcType="VARCHAR" property="inStoreMobile" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType" />
    <result column="variant" jdbcType="VARCHAR" property="variant" />
    <result column="car_icon" jdbcType="VARCHAR" property="carIcon" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="service_hour" jdbcType="INTEGER" property="serviceHour" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="package_id" jdbcType="BIGINT" property="packageId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
    <result column="delivery_type" jdbcType="VARCHAR" property="deliveryType" />
    <result column="pickup_code" jdbcType="VARCHAR" property="pickupCode" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="shipping_fee" jdbcType="DECIMAL" property="shippingFee" />
    <result column="original_amount" jdbcType="DECIMAL" property="originalAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="subtotal" jdbcType="DECIMAL" property="subtotal" />
    <result column="applied_coupon" jdbcType="BIT" property="appliedCoupon" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="grand_total" jdbcType="DECIMAL" property="grandTotal" />
    <result column="force_fixed_price" jdbcType="BIT" property="forceFixedPrice" />
    <result column="refund_tag" jdbcType="INTEGER" property="refundTag" />
    <result column="reservation_time" jdbcType="TIMESTAMP" property="reservationTime" />
    <result column="suggestion_time" jdbcType="TIMESTAMP" property="suggestionTime" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="completed_time" jdbcType="TIMESTAMP" property="completedTime" />
    <result column="finished_time" jdbcType="TIMESTAMP" property="finishedTime" />
    <result column="refunded_time" jdbcType="TIMESTAMP" property="refundedTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, type, customer_id, customer_name, customer_mobile, customer_email, in_store_name,
    in_store_mobile, license_plate, brand, model, year, transmission_type, variant, car_icon,
    service_id, service_name, service_hour, service_fee, package_id, package_name, workshop_id,
    workshop_name, delivery_type, pickup_code, status, shipping_fee, original_amount, 
    discount_amount, subtotal, applied_coupon, coupon_amount, grand_total, force_fixed_price, 
    refund_tag, reservation_time, suggestion_time, confirm_time, order_time, paid_time, completed_time, finished_time,
    refunded_time, remark, create_by, update_by, create_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.servauto.mall.model.entity.order.OrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from `order`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.order.Order">
    insert into `order`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="customerEmail != null">
        customer_email,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="inStoreMobile != null">
        in_store_mobile,
      </if>
      <if test="licensePlate != null">
        license_plate,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="variant != null">
        variant,
      </if>
        <if test="carIcon != null">
        car_icon,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="serviceHour != null">
        service_hour,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="packageId != null">
        package_id,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="workshopId != null">
        workshop_id,
      </if>
      <if test="workshopName != null">
        workshop_name,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="pickupCode != null">
        pickup_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="shippingFee != null">
        shipping_fee,
      </if>
      <if test="originalAmount != null">
        original_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="subtotal != null">
        subtotal,
      </if>
      <if test="appliedCoupon != null">
        applied_coupon,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="grandTotal != null">
        grand_total,
      </if>
      <if test="forceFixedPrice != null">
        force_fixed_price,
      </if>
      <if test="refundTag != null">
        refund_tag,
      </if>
      <if test="reservationTime != null">
        reservation_time,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="paidTime != null">
        paid_time,
      </if>
      <if test="completedTime != null">
        completed_time,
      </if>
      <if test="finishedTime != null">
        finished_time,
      </if>
      <if test="refundedTime != null">
        refunded_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerEmail != null">
        #{customerEmail,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreMobile != null">
        #{inStoreMobile,jdbcType=VARCHAR},
      </if>
      <if test="licensePlate != null">
        #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="variant != null">
        #{variant,jdbcType=VARCHAR},
      </if>
      <if test="carIcon != null">
        #{carIcon,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="serviceHour != null">
        #{serviceHour,jdbcType=INTEGER},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="packageId != null">
        #{packageId,jdbcType=BIGINT},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="workshopId != null">
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null">
        #{workshopName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="pickupCode != null">
        #{pickupCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="shippingFee != null">
        #{shippingFee,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null">
        #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="subtotal != null">
        #{subtotal,jdbcType=DECIMAL},
      </if>
      <if test="appliedCoupon != null">
        #{appliedCoupon,jdbcType=BIT},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="grandTotal != null">
        #{grandTotal,jdbcType=DECIMAL},
      </if>
      <if test="forceFixedPrice != null">
        #{forceFixedPrice,jdbcType=BIT},
      </if>
      <if test="refundTag != null">
        #{refundTag,jdbcType=INTEGER},
      </if>
      <if test="reservationTime != null">
        #{reservationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidTime != null">
        #{paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completedTime != null">
        #{completedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedTime != null">
        #{finishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundedTime != null">
        #{refundedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="casByOrderNoAndStatusList">
    update `order`
    <set>
      <if test="o.status != null">
        status = #{o.status,jdbcType=VARCHAR},
      </if>
      <if test="o.workshopId != null">
        workshop_id = #{o.workshopId,jdbcType=BIGINT},
      </if>
      <if test="o.workshopName != null">
        workshop_name = #{o.workshopName,jdbcType=VARCHAR},
      </if>
      <if test="o.reservationTime != null">
        reservation_time = #{o.reservationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.paidTime != null">
        paid_time = #{o.paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.completedTime != null">
        completed_time = #{o.completedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.finishedTime != null">
        finished_time = #{o.finishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.refundedTime != null">
        refunded_time = #{o.refundedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.remark != null">
        remark = #{o.remark,jdbcType=VARCHAR},
      </if>
      <if test="o.updateBy != null">
        update_by = #{o.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where order_no = #{orderNo} and deleted = 0 and
    `status` IN
    <foreach close=")" collection="statusList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where customer_id = #{conditions.customerId,jdbcType=BIGINT} and deleted = 0
    <if test="conditions.orderNo != null and conditions.orderNo != ''">
      and order_no = #{conditions.orderNo,jdbcType=VARCHAR}
    </if>
    <if test="conditions.status != null and conditions.status != ''">
      and `status` = #{conditions.status,jdbcType=VARCHAR}
    </if>
    <if test="conditions.statuses != null and conditions.statuses.size() > 0">
      and `status` IN
      <foreach close=")" collection="conditions.statuses" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by id desc
  </select>

  <select id="checkPickupCodeExist" resultType="Integer">
    select count(*) from `order` where pickup_code = #{pickupCode,jdbcType=VARCHAR}
  </select>

    <select id="selectByOrderNo" resultType="com.servauto.mall.model.entity.order.Order">
      select
      <include refid="Base_Column_List" />
          from `order`
      where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>


</mapper>