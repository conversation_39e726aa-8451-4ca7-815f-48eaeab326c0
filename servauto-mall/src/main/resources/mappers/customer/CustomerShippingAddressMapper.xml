<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.customer.CustomerShippingAddressMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.customer.CustomerShippingAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, customer_id, name, mobile, state_code, city_code, address, is_default, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from customer_shipping_address
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from customer_shipping_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.customer.CustomerShippingAddress">
    insert into customer_shipping_address (id, customer_id, name, 
      mobile, state_code, city_code, 
      address, is_default, create_time, 
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{customerId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{stateCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{isDefault,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.customer.CustomerShippingAddress">
    insert into customer_shipping_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="stateCode != null">
        state_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="stateCode != null">
        #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.customer.CustomerShippingAddress">
    update customer_shipping_address
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="stateCode != null">
        state_code = #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.customer.CustomerShippingAddress">
    update customer_shipping_address
    set customer_id = #{customerId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      state_code = #{stateCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByCustomerId" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from customer_shipping_address
    where customer_id = #{customerId,jdbcType=BIGINT} and deleted = 0
  </select>
</mapper>