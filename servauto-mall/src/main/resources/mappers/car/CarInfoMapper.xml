<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarInfoMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_lib_id" jdbcType="VARCHAR" property="carLibId" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="car_source" jdbcType="TINYINT" property="carSource" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="model_id" jdbcType="BIGINT" property="modelId" />
    <result column="year_id" jdbcType="BIGINT" property="yearId" />
    <result column="variant_id" jdbcType="BIGINT" property="variantId" />
    <result column="car_type_id" jdbcType="INTEGER" property="carTypeId" />
    <result column="car_variant" jdbcType="VARCHAR" property="carVariant" />
    <result column="car_engine" jdbcType="VARCHAR" property="carEngine" />
    <result column="car_mileage" jdbcType="BIGINT" property="carMileage" />
    <result column="car_transmission" jdbcType="INTEGER" property="carTransmission" />
    <result column="registration_date" jdbcType="TIMESTAMP" property="registrationDate" />
    <result column="registration_type" jdbcType="INTEGER" property="registrationType" />
    <result column="seat" jdbcType="INTEGER" property="seat" />
    <result column="src_seat" jdbcType="INTEGER" property="srcSeat" />
    <result column="color" jdbcType="INTEGER" property="color" />
    <result column="fuel_type" jdbcType="INTEGER" property="fuelType" />
    <result column="vin_code" jdbcType="VARCHAR" property="vinCode" />
    <result column="engine_no" jdbcType="VARCHAR" property="engineNo" />
    <result column="car_type" jdbcType="BIT" property="carType" />
    <result column="service_book" jdbcType="TINYINT" property="serviceBook" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="location_address" jdbcType="VARCHAR" property="locationAddress" />
    <result column="location_id" jdbcType="VARCHAR" property="locationId" />
    <result column="next_maintenance_date" jdbcType="TIMESTAMP" property="nextMaintenanceDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="default_car" jdbcType="TINYINT" property="defaultCar" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.mall.model.entity.car.CarInfoWithBLOBs">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="source_info" jdbcType="LONGVARCHAR" property="sourceInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, car_lib_id, customer_id, car_source, brand_id, model_id, year_id, variant_id, car_type_id,
    car_variant, car_engine, car_mileage, car_transmission, registration_date, registration_type, 
    seat, src_seat, color, fuel_type, vin_code, engine_no, car_type, service_book, license_plate, 
    image, source, location, location_address, location_id, next_maintenance_date, status, default_car,
    update_time, create_time
  </sql>
  <sql id="Blob_Column_List">
    description, source_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_info
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByCustomerId" resultType="com.servauto.mall.model.entity.car.CarInfo">
      select 'true' as QUERYID,
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      from car_info
      where customer_id = #{customerId,jdbcType=BIGINT}
    </select>
  <select id="getDefaultCar" resultType="com.servauto.mall.model.entity.car.CarInfo">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_info
    where customer_id = #{customerId,jdbcType=BIGINT} and default_car = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByCustomerId">
    delete from car_info
    where customer_id = #{customerId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByCustomerIdAndCarId">
    delete from car_info
    where customer_id = #{customerId,jdbcType=BIGINT} and id = #{carId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.car.CarInfoWithBLOBs">
    insert into car_info (id, car_lib_id, customer_id,
      car_source, brand_id, model_id, 
      year_id, variant_id, car_type_id, 
      car_variant, car_engine, car_mileage, 
      car_transmission, registration_date, registration_type, 
      seat, src_seat, color, 
      fuel_type, vin_code, engine_no, 
      car_type, service_book, license_plate, 
      image, source, location, 
      location_address, location_id, next_maintenance_date, 
      status, default_car, update_time, create_time,
      description, source_info)
    values (#{id,jdbcType=BIGINT}, #{carLibId,jdbcType=VARCHAR}, #{customerId,jdbcType=BIGINT},
      #{carSource,jdbcType=TINYINT}, #{brandId,jdbcType=BIGINT}, #{modelId,jdbcType=BIGINT},
      #{yearId,jdbcType=BIGINT}, #{variantId,jdbcType=BIGINT}, #{carTypeId,jdbcType=INTEGER},
      #{carVariant,jdbcType=VARCHAR}, #{carEngine,jdbcType=VARCHAR}, #{carMileage,jdbcType=BIGINT},
      #{carTransmission,jdbcType=INTEGER}, #{registrationDate,jdbcType=TIMESTAMP}, #{registrationType,jdbcType=INTEGER}, 
      #{seat,jdbcType=INTEGER}, #{srcSeat,jdbcType=INTEGER}, #{color,jdbcType=INTEGER}, 
      #{fuelType,jdbcType=INTEGER}, #{vinCode,jdbcType=VARCHAR}, #{engineNo,jdbcType=VARCHAR}, 
      #{carType,jdbcType=BIT}, #{serviceBook,jdbcType=TINYINT}, #{licensePlate,jdbcType=VARCHAR}, 
      #{image,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{locationAddress,jdbcType=VARCHAR}, #{locationId,jdbcType=VARCHAR}, #{nextMaintenanceDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{defaultCar,jdbcType=TINYINT},#{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
      #{description,jdbcType=LONGVARCHAR}, #{sourceInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.car.CarInfoWithBLOBs">
    insert into car_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carLibId != null">
        car_lib_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="carSource != null">
        car_source,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="yearId != null">
        year_id,
      </if>
      <if test="variantId != null">
        variant_id,
      </if>
      <if test="carTypeId != null">
        car_type_id,
      </if>
      <if test="carVariant != null">
        car_variant,
      </if>
      <if test="carEngine != null">
        car_engine,
      </if>
      <if test="carMileage != null">
        car_mileage,
      </if>
      <if test="carTransmission != null">
        car_transmission,
      </if>
      <if test="registrationDate != null">
        registration_date,
      </if>
      <if test="registrationType != null">
        registration_type,
      </if>
      <if test="seat != null">
        seat,
      </if>
      <if test="srcSeat != null">
        src_seat,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="vinCode != null">
        vin_code,
      </if>
      <if test="engineNo != null">
        engine_no,
      </if>
      <if test="carType != null">
        car_type,
      </if>
      <if test="serviceBook != null">
        service_book,
      </if>
      <if test="licensePlate != null">
        license_plate,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="locationAddress != null">
        location_address,
      </if>
      <if test="locationId != null">
        location_id,
      </if>
      <if test="nextMaintenanceDate != null">
        next_maintenance_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="defaultCar != null">
        default_car,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="sourceInfo != null">
        source_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carLibId != null">
        #{carLibId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="carSource != null">
        #{carSource,jdbcType=TINYINT},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=BIGINT},
      </if>
      <if test="yearId != null">
        #{yearId,jdbcType=BIGINT},
      </if>
      <if test="variantId != null">
        #{variantId,jdbcType=BIGINT},
      </if>
      <if test="carTypeId != null">
        #{carTypeId,jdbcType=INTEGER},
      </if>
      <if test="carVariant != null">
        #{carVariant,jdbcType=VARCHAR},
      </if>
      <if test="carEngine != null">
        #{carEngine,jdbcType=VARCHAR},
      </if>
      <if test="carMileage != null">
        #{carMileage,jdbcType=BIGINT},
      </if>
      <if test="carTransmission != null">
        #{carTransmission,jdbcType=INTEGER},
      </if>
      <if test="registrationDate != null">
        #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registrationType != null">
        #{registrationType,jdbcType=INTEGER},
      </if>
      <if test="seat != null">
        #{seat,jdbcType=INTEGER},
      </if>
      <if test="srcSeat != null">
        #{srcSeat,jdbcType=INTEGER},
      </if>
      <if test="color != null">
        #{color,jdbcType=INTEGER},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="vinCode != null">
        #{vinCode,jdbcType=VARCHAR},
      </if>
      <if test="engineNo != null">
        #{engineNo,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=BIT},
      </if>
      <if test="serviceBook != null">
        #{serviceBook,jdbcType=TINYINT},
      </if>
      <if test="licensePlate != null">
        #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationAddress != null">
        #{locationAddress,jdbcType=VARCHAR},
      </if>
      <if test="locationId != null">
        #{locationId,jdbcType=VARCHAR},
      </if>
      <if test="nextMaintenanceDate != null">
        #{nextMaintenanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="defaultCar != null">
        #{defaultCar,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="sourceInfo != null">
        #{sourceInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.car.CarInfoWithBLOBs">
    update car_info
    <set>
      <if test="carLibId != null">
        car_lib_id = #{carLibId,jdbcType=VARCHAR},
      </if>
      <if test="carSource != null">
        car_source = #{carSource,jdbcType=TINYINT},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=BIGINT},
      </if>
      <if test="yearId != null">
        year_id = #{yearId,jdbcType=BIGINT},
      </if>
      <if test="variantId != null">
        variant_id = #{variantId,jdbcType=BIGINT},
      </if>
      <if test="carTypeId != null">
        car_type_id = #{carTypeId,jdbcType=INTEGER},
      </if>
      <if test="carVariant != null">
        car_variant = #{carVariant,jdbcType=VARCHAR},
      </if>
      <if test="carEngine != null">
        car_engine = #{carEngine,jdbcType=VARCHAR},
      </if>
      <if test="carMileage != null">
        car_mileage = #{carMileage,jdbcType=BIGINT},
      </if>
      <if test="carTransmission != null">
        car_transmission = #{carTransmission,jdbcType=INTEGER},
      </if>
      <if test="registrationDate != null">
        registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registrationType != null">
        registration_type = #{registrationType,jdbcType=INTEGER},
      </if>
      <if test="seat != null">
        seat = #{seat,jdbcType=INTEGER},
      </if>
      <if test="srcSeat != null">
        src_seat = #{srcSeat,jdbcType=INTEGER},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=INTEGER},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="vinCode != null">
        vin_code = #{vinCode,jdbcType=VARCHAR},
      </if>
      <if test="engineNo != null">
        engine_no = #{engineNo,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=BIT},
      </if>
      <if test="serviceBook != null">
        service_book = #{serviceBook,jdbcType=TINYINT},
      </if>
      <if test="licensePlate != null">
        license_plate = #{licensePlate,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationAddress != null">
        location_address = #{locationAddress,jdbcType=VARCHAR},
      </if>
      <if test="locationId != null">
        location_id = #{locationId,jdbcType=VARCHAR},
      </if>
      <if test="nextMaintenanceDate != null">
        next_maintenance_date = #{nextMaintenanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="defaultCar != null">
        default_car = #{defaultCar,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="sourceInfo != null">
        source_info = #{sourceInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and customer_id = #{customerId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.servauto.mall.model.entity.car.CarInfoWithBLOBs">
    update car_info
    set car_lib_id = #{carLibId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=BIGINT},
      car_source = #{carSource,jdbcType=TINYINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      model_id = #{modelId,jdbcType=BIGINT},
      year_id = #{yearId,jdbcType=BIGINT},
      variant_id = #{variantId,jdbcType=BIGINT},
      car_type_id = #{carTypeId,jdbcType=INTEGER},
      car_variant = #{carVariant,jdbcType=VARCHAR},
      car_engine = #{carEngine,jdbcType=VARCHAR},
      car_mileage = #{carMileage,jdbcType=BIGINT},
      car_transmission = #{carTransmission,jdbcType=INTEGER},
      registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      registration_type = #{registrationType,jdbcType=INTEGER},
      seat = #{seat,jdbcType=INTEGER},
      src_seat = #{srcSeat,jdbcType=INTEGER},
      color = #{color,jdbcType=INTEGER},
      fuel_type = #{fuelType,jdbcType=INTEGER},
      vin_code = #{vinCode,jdbcType=VARCHAR},
      engine_no = #{engineNo,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=BIT},
      service_book = #{serviceBook,jdbcType=TINYINT},
      license_plate = #{licensePlate,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      location_address = #{locationAddress,jdbcType=VARCHAR},
      location_id = #{locationId,jdbcType=VARCHAR},
      next_maintenance_date = #{nextMaintenanceDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      default_car = #{defaultCar,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=LONGVARCHAR},
      source_info = #{sourceInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.car.CarInfo">
    update car_info
    set car_lib_id = #{carLibId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=BIGINT},
      car_source = #{carSource,jdbcType=TINYINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      model_id = #{modelId,jdbcType=BIGINT},
      year_id = #{yearId,jdbcType=BIGINT},
      variant_id = #{variantId,jdbcType=BIGINT},
      car_type_id = #{carTypeId,jdbcType=INTEGER},
      car_variant = #{carVariant,jdbcType=VARCHAR},
      car_engine = #{carEngine,jdbcType=VARCHAR},
      car_mileage = #{carMileage,jdbcType=BIGINT},
      car_transmission = #{carTransmission,jdbcType=INTEGER},
      registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      registration_type = #{registrationType,jdbcType=INTEGER},
      seat = #{seat,jdbcType=INTEGER},
      src_seat = #{srcSeat,jdbcType=INTEGER},
      color = #{color,jdbcType=INTEGER},
      fuel_type = #{fuelType,jdbcType=INTEGER},
      vin_code = #{vinCode,jdbcType=VARCHAR},
      engine_no = #{engineNo,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=BIT},
      service_book = #{serviceBook,jdbcType=TINYINT},
      license_plate = #{licensePlate,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      location_address = #{locationAddress,jdbcType=VARCHAR},
      location_id = #{locationId,jdbcType=VARCHAR},
      next_maintenance_date = #{nextMaintenanceDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      default_car = #{defaultCar,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="setDefaultCar">
      UPDATE car_info
      SET default_car = 1
      WHERE customer_id = #{customerId}
      AND id = #{carId}
    </update>
  <update id="clearDefaultCar">
    UPDATE car_info
    SET default_car = 0
    WHERE customer_id = #{customerId} AND default_car = 1
  </update>

  <insert id="batchInsert">
      INSERT INTO car_info (id, car_lib_id,customer_id, brand_id, model_id, year_id, variant_id, license_plate,
      car_mileage)
      VALUES
      <foreach collection="carInfoList" item="carInfo" separator=",">
          (
          #{carInfo.id},
          #{carInfo.carLibId},
          #{carInfo.customerId},
          #{carInfo.brandId},
          #{carInfo.modelId},
          #{carInfo.yearId},
          #{carInfo.variantId},
          #{carInfo.licensePlate},
          #{carInfo.carMileage}
          )
      </foreach>
  </insert>
</mapper>