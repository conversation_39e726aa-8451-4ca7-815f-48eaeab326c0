<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarProductMatchInfoMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarProductMatchInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="car_lib_id" jdbcType="VARCHAR" property="carLibId" />
    <result column="match_content" jdbcType="VARCHAR" property="matchContent" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="match_type" jdbcType="VARCHAR" property="matchType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, car_lib_id, match_content, product_type, match_type, status, created_time, updated_time
  </sql>
  <select id="selectByExample" parameterType="com.servauto.mall.model.entity.car.CarProductMatchInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from car_product_match_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from car_product_match_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectMatchInfo" resultType="com.servauto.mall.model.entity.car.CarProductMatchInfo">
    select
    <include refid="Base_Column_List"/>
    from car_product_match_info
    where car_lib_id = #{carLibId,jdbcType=VARCHAR}
    <if test="productType != null and productType != ''">
      and product_type = #{productType,jdbcType=VARCHAR}
    </if>
    and status = 1
  </select>
</mapper>