<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarExtraMapper">
    <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarExtra">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="car_lib_id" jdbcType="VARCHAR" property="carLibId"/>
        <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="year_id" jdbcType="BIGINT" property="yearId"/>
        <result column="variant_name" jdbcType="VARCHAR" property="variantName"/>
        <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType"/>
        <result column="displacement_value" jdbcType="VARCHAR" property="displacementValue"/>
        <result column="car_type" jdbcType="VARCHAR" property="carType"/>
        <result column="tire_front" jdbcType="VARCHAR" property="tireFront"/>
        <result column="tire_rear" jdbcType="VARCHAR" property="tireRear"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, car_lib_id, brand_id,model_id,year_id,variant_name,transmission_type,displacement_value, car_type, tire_front, tire_rear,
    `status`, `order`, created_time, updated_time
    </sql>

    <select id="selectByYearId" resultType="com.servauto.mall.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where year_id = #{id,jdbcType=BIGINT}
        and status = 1
        order by variant_name
    </select>
    <select id="selectCarExtraById" resultType="com.servauto.mall.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByPrimaryKeys" resultType="com.servauto.mall.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where id in
        <foreach collection="variantIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>