<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarBrandsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarBrands">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="top_status" jdbcType="INTEGER" property="topStatus"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_name, `status`, `order`, icon, top_status, created_time, updated_time
    </sql>

    <!-- 新增 SQL 查询语句 -->
    <select id="selectAllValidBrands" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where status = 1
        order by brand_name
    </select>
    <!-- 新增 SQL 查询语句 -->
    <select id="selectAllValidTopBrands" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where status = 1
        and top_status = 1
        order by `order`
    </select>
    <select id="selectByPrimaryKey" resultType="com.servauto.mall.model.entity.car.CarBrands">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByPrimaryKeys" resultType="com.servauto.mall.model.entity.car.CarBrands">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where id in
        <foreach close=")" collection="brandIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>