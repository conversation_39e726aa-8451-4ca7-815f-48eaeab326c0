<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarYearsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarYears">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="brand_id" jdbcType="BIGINT" property="brandId" />
        <result column="model_id" jdbcType="BIGINT" property="modelId" />
        <result column="year_value" jdbcType="VARCHAR" property="yearValue" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="order" jdbcType="INTEGER" property="order" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_id, model_id, `year_value`, `status`, `order`, created_time, updated_time
    </sql>

    <select id="selectByModelId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where model_id = #{modelId,jdbcType=BIGINT}
        and status = 1
        order by year_value desc
    </select>
    <select id="selectByPrimaryKeys" resultType="com.servauto.mall.model.entity.car.CarYears">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where id in
        <foreach close=")" collection="yearsIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>