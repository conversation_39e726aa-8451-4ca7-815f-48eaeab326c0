<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.car.CarModelsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.car.CarModels">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_id, model_name, `status`, `order`, created_time, updated_time
    </sql>

    <select id="selectAllModelsByBrandId" parameterType="Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where brand_id = #{brandId,jdbcType=BIGINT}
        and status = 1
        order by model_name
    </select>
    <select id="selectByPrimaryKeys" resultType="com.servauto.mall.model.entity.car.CarModels">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where id in
        <foreach collection="modelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>