<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.workshop.WorkshopMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.workshop.Workshop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="featured_tags" jdbcType="VARCHAR" property="featuredTags" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="location_url" jdbcType="VARCHAR" property="locationUrl" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="whats_app_number" jdbcType="LONGVARCHAR" property="whatsAppNumber" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.mall.model.entity.workshop.Workshop">
    <result column="photo" jdbcType="LONGVARCHAR" property="photo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, type, status, state_code, city_code, address, country_code, phone_number, 
    featured_tags, latitude, longitude, location_url, logo, operator_id, remark, create_time, 
    update_time, deleted,whats_app_number
  </sql>
  <sql id="Blob_Column_List">
    photo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from workshop
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from workshop
    <where>
      status = 1
      <if test="conditions.id != null and conditions.id != 0">
        and id = #{conditions.id,jdbcType=BIGINT}
      </if>
      <if test="conditions.name != null and conditions.name != ''">
        and name LIKE CONCAT(#{conditions.name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="conditions.type != null and conditions.type != 0">
        and type = #{conditions.type,jdbcType=INTEGER}
      </if>
      <if test="conditions.ids != null and conditions.ids.size() > 0">
        and id IN
        <foreach item="id" collection="conditions.ids" open="(" separator="," close=")">
          #{id,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
</mapper>