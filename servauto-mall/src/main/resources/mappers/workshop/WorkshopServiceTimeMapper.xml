<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.workshop.WorkshopServiceTimeMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.workshop.WorkshopServiceTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, workshop_id, day, start_time, end_time, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from workshop_service_time
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from workshop_service_time
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.workshop.WorkshopServiceTime">
    insert into workshop_service_time (id, workshop_id, day, 
      start_time, end_time, create_time, 
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT}, #{day,jdbcType=INTEGER}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.workshop.WorkshopServiceTime">
    insert into workshop_service_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="workshopId != null">
        workshop_id,
      </if>
      <if test="day != null">
        day,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null">
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="day != null">
        #{day,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.workshop.WorkshopServiceTime">
    update workshop_service_time
    <set>
      <if test="workshopId != null">
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="day != null">
        day = #{day,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.workshop.WorkshopServiceTime">
    update workshop_service_time
    set workshop_id = #{workshopId,jdbcType=BIGINT},
      day = #{day,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByWorkshopIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from workshop_service_time
    where workshop_id IN
    <foreach item="workshopId" collection="workshopIds" open="(" separator="," close=")">
      #{workshopId,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>