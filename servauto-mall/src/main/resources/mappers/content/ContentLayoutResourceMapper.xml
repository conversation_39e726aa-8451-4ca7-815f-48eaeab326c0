<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.content.ContentLayoutResourceMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.content.ContentLayoutResource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="layout_id" jdbcType="BIGINT" property="layoutId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="resource_seq" jdbcType="BIGINT" property="resourceSeq" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from content_layout_resource
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from content_layout_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.content.ContentLayoutResource">
    insert into content_layout_resource (id, layout_id, resource_id, 
      resource_seq, enabled, create_time, 
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{layoutId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, 
      #{resourceSeq,jdbcType=BIGINT}, #{enabled,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.content.ContentLayoutResource">
    insert into content_layout_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="layoutId != null">
        layout_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceSeq != null">
        resource_seq,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="layoutId != null">
        #{layoutId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceSeq != null">
        #{resourceSeq,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.content.ContentLayoutResource">
    update content_layout_resource
    <set>
      <if test="layoutId != null">
        layout_id = #{layoutId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceSeq != null">
        resource_seq = #{resourceSeq,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.content.ContentLayoutResource">
    update content_layout_resource
    set layout_id = #{layoutId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_seq = #{resourceSeq,jdbcType=BIGINT},
      enabled = #{enabled,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByLayoutIds" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from content_layout_resource
    where layout_id in
    <foreach collection="layoutIds" item="layoutId" open="(" separator="," close=")">
      #{layoutId}
    </foreach>
    order by layout_id, resource_seq
  </select>
</mapper>