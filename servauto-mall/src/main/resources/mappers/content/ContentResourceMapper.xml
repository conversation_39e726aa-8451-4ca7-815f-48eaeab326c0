<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.content.ContentResourceMapper">
  <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.content.ContentResource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="pict_uri" jdbcType="VARCHAR" property="pictUri" />
    <result column="entry_uri" jdbcType="VARCHAR" property="entryUri" />
    <result column="entry_type" jdbcType="VARCHAR" property="entryType" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, title, type, pict_uri, entry_uri, entry_type, ext, create_time, update_time, 
    deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from content_resource
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from content_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.mall.model.entity.content.ContentResource">
    insert into content_resource (id, title, type, 
      pict_uri, entry_uri, entry_type, 
      ext, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{pictUri,jdbcType=VARCHAR}, #{entryUri,jdbcType=VARCHAR}, #{entryType,jdbcType=VARCHAR}, 
      #{ext,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.content.ContentResource">
    insert into content_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="pictUri != null">
        pict_uri,
      </if>
      <if test="entryUri != null">
        entry_uri,
      </if>
      <if test="entryType != null">
        entry_type,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="pictUri != null">
        #{pictUri,jdbcType=VARCHAR},
      </if>
      <if test="entryUri != null">
        #{entryUri,jdbcType=VARCHAR},
      </if>
      <if test="entryType != null">
        #{entryType,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.content.ContentResource">
    update content_resource
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="pictUri != null">
        pict_uri = #{pictUri,jdbcType=VARCHAR},
      </if>
      <if test="entryUri != null">
        entry_uri = #{entryUri,jdbcType=VARCHAR},
      </if>
      <if test="entryType != null">
        entry_type = #{entryType,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.content.ContentResource">
    update content_resource
    set title = #{title,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      pict_uri = #{pictUri,jdbcType=VARCHAR},
      entry_uri = #{entryUri,jdbcType=VARCHAR},
      entry_type = #{entryType,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="selectByIds" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from content_resource
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>



</mapper>