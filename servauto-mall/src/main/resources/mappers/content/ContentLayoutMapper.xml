<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.mall.dao.content.ContentLayoutMapper">
    <resultMap id="BaseResultMap" type="com.servauto.mall.model.entity.content.ContentLayout">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="seq" jdbcType="BIGINT" property="seq"/>
        <result column="enabled" jdbcType="BIT" property="enabled"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, template_code, title, seq, enabled, type, create_time, update_time, deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from content_layout
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from content_layout
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.servauto.mall.model.entity.content.ContentLayout">
        insert into content_layout (id, template_code, title,
                                    seq, enabled, type, create_time,
                                    update_time, deleted)
        values (#{id,jdbcType=BIGINT}, #{templateCode,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
                #{seq,jdbcType=BIGINT}, #{enabled,jdbcType=BIT}, #{type,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.servauto.mall.model.entity.content.ContentLayout">
        insert into content_layout
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="templateCode != null">
                template_code,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="seq != null">
                seq,
            </if>
            <if test="enabled != null">
                enabled,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="templateCode != null">
                #{templateCode,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                #{seq,jdbcType=BIGINT},
            </if>
            <if test="enabled != null">
                #{enabled,jdbcType=BIT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.servauto.mall.model.entity.content.ContentLayout">
        update content_layout
        <set>
            <if test="templateCode != null">
                template_code = #{templateCode,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                seq = #{seq,jdbcType=BIGINT},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=BIT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.servauto.mall.model.entity.content.ContentLayout">
        update content_layout
        set template_code = #{templateCode,jdbcType=VARCHAR},
            title         = #{title,jdbcType=VARCHAR},
            seq           = #{seq,jdbcType=BIGINT},
            enabled       = #{enabled,jdbcType=BIT},
            type          = #{type,jdbcType=VARCHAR},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            deleted       = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectByTemplateCode" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from content_layout
        where template_code = #{templateCode,jdbcType=VARCHAR}
    </select>
</mapper>