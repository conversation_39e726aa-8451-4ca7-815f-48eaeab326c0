package com.servauto.mall.lark;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.bitable.v1.model.CreateAppReq;
import com.lark.oapi.service.bitable.v1.model.CreateAppResp;
import com.lark.oapi.service.bitable.v1.model.ReqApp;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.ServletUtils;
import com.servauto.mall.Config;
import org.junit.jupiter.api.Test;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;


public class BiTableTest {

    private static final Client client = Client.newBuilder(Config.APP_ID, Config.APP_SECRET).build();

    @Test
    public void TestCreateAppAndTables() throws Exception {
//        CreateAppReq req = CreateAppReq.newBuilder()
//                .reqApp(ReqApp.newBuilder()
//                        .name("一篇新的多维表格")
//                        .folderToken("DPwvfgWcMluuZmdskM0lNLCegRe")
//                        .build())
//                .build();
//
//        // 发起请求
//        CreateAppResp resp = client.bitable().app().create(req, RequestOptions.newBuilder()
//                .userAccessToken("**********************************************")
//                .build());
//
//        // 处理服务端错误
//        if(!resp.success()) {
//            System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
//                    resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
//            return;
//        }
//
//        // 业务数据处理
//        System.out.println(Jsons.DEFAULT.toJson(resp.getData()));

    }

    public static void main(String[] args) {

        StringBuffer sb = new StringBuffer();
        sb.append("https://accounts.larksuite.com/open-apis/authen/v1/authorize")
                .append("?")
                .append("client_id=").append(Config.APP_ID)
                .append("&").append("redirect_uri=").append(ServletUtils.urlEncode("http://uc589b3b.natappfree.cc/lark/receiveAccessCode"))
                .append("&").append("scope=").append("offline_access")
        ;
        System.out.println(sb.toString());

    }

}
