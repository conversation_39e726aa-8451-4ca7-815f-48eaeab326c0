package com.servauto.mall.lark;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;

public class LarkMarkdownSender {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final HttpClient httpClient = HttpClient.newBuilder()
        .version(HttpClient.Version.HTTP_2)
        .connectTimeout(Duration.ofSeconds(10))
        .build();

    private String webhookUrl;

    public static void main(String[] args) {

        ContentNode content = geBoldContent("You have a new ${orderType} order. Please handle it on time：");
        ContentNode content1 = geBoldContent("Order ID：{Order ID}");
        ContentNode content2 = geBoldContent("Order Type：{Order Type}");
        ContentNode content3 = geBoldContent("Car Plate Number：{Car Plate Number}");
        ContentNode content4 = geBoldContent("Service Name：{Service Name}");
        ContentNode content5 = geBoldContent("Appointment Info：");
        //无序列表
        ContentNode content6 = geBoldContent("- Store：{Store Name}\n- Time：{Appointment Time（yyyy-mm-dd hh：mm）}");


        //把占位符{}的值进行替换
        //调用webhook，富文本发送
//        WebHookReq req = new WebHookReq();
//        req.addContentNode(List.of(content));
//        req.addContentNode(List.of(content1));
//        req.addContentNode(List.of(content2));
//        req.addContentNode(List.of(content3));
//        req.addContentNode(List.of(content4));
//        req.addContentNode(List.of(content5));
//        req.addContentNode(List.of(content6));

        Map<String,Object> mapOne = Maps.newHashMap();
        mapOne.put("title","我是第一个标题");

        List<Map<String,Object>> childrenList = Lists.newArrayList();
        Map<String,Object> childrenMap = Maps.newHashMap();
        childrenMap.put("tag","text");
        childrenMap.put("text","f");
        childrenList.add(childrenMap);
        mapOne.put("content",childrenList);

        Map<String,Object> mapTwo = Maps.newHashMap();
        mapOne.put("zh_cn",com.alibaba.fastjson2.JSONObject.toJSONString(mapTwo));



        String text = "{\n" +
                "        \"msg_type\": \"post\",\n" +
                "        \"content\": {\n" +
                "                \"post\": {\n" +
                "                        \"zh_cn\": {\n" +
                "                                \"title\": \"测试富文本\",\n" +
                "                                \"content\": [\n" +
                "                                        [{\n" +
                "                                                        \"tag\": \"text\",\n" +
                "                                                        \"text\": \"<u>您有一个新的${orderType}订单。请按时处理：</u>\n" +
                "**订单ID：{订单ID}**\n" +
                "订单类型：{订单类型}\n" +
                "车牌号：{车牌号}\n" +
                "服务名称：{服务名称}\n" +
                "预约信息：\n" +
                "-商店：{商店名称}\n" +
                "-时间：{预约时间（yyyy-mm-dd hh： mm）} \"\n" +
                "                                                }\n" +
                "                                        ]\n" +
                "                                ]\n" +
                "                        }\n" +
                "                }\n" +
                "        }\n" +
                "}";
        JSONObject jsonObject = com.alibaba.fastjson2.JSONObject.parseObject(text,JSONObject.class);

//        {
//            "receive_id": "fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab",
//                "content": "{\"zh_cn\":{\"title\":\"我是一个标题\",\"content\":[[{\"tag\":\"text\",\"text\":\"第一行 :\"},{\"tag\":\"a\",\"href\":\"http://www.larksuite.com\",\"text\":\"超链接\"},{\"tag\":\"at\",\"user_id\":\"ou_1avnmsbv3k45jnk34j5\",\"user_name\":\"tom\"}],[{\"tag\":\"img\",\"image_key\":\"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}],[{\"tag\":\"text\",\"text\":\"第二行:\"},{\"tag\":\"text\",\"text\":\"文本测试\"}],[{\"tag\":\"img\",\"image_key\":\"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}]]}}",
//                "msg_type": "post"
//        }


//        jsonObject.put("content",list);

//        System.out.println(""+ com.alibaba.fastjson2.JSONObject.toJSONString(jsonObject));

        try {
            // 假设这里使用HttpClient发送请求
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab"))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonObject.toString()))
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            System.out.println("WebHook发送结果: " + response.body());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static ContentNode geBoldContent(String text) {
        ContentNode content =  new ContentNode();
        content.setText(text);
        content.setTag("text");
        content.setStyle(List.of("bold"));
        return content;
    }
//    private static String generateSign(String secret, long timestamp) throws InvalidKeyException, NoSuchAlgorithmException {
//        //把timestamp+"\n"+密钥当做签名字符串
//        String stringToSign = timestamp + "\n" + secret;
//        //使用HmacSHA256算法计算签名
//        Mac mac = Mac.getInstance("HmacSHA256");
//        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
//        byte[] signData = mac.doFinal(new byte[]{});
//        return new String(Base64.encodeBase64(signData));
//    }

}
