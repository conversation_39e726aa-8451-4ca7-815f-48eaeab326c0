package com.servauto.mall.tiktok;

import okhttp3.*;
import okio.Buffer;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class TikTokShopClient {
    public static void main(String[] args) {

//        getAuthorized();
        getOrderList();
    }


    public static void getOrderList(){
        String path = "https://open-api.tiktokglobalshop.com/order/202309/orders/search";
        String appKey = "6gifmgej2sm26";
        String appSecret = "0cd910e160d5fa9ad1fb8717727d7265e52e4a8c";
        String accessToken = "ROW_oCE1ewAAAAAub6HllVGgkspeoSO6VWPXz4nsJ79hJUEWd8UaEUjncYujfhJz4Z-S4LdmUHxYs3G_yb3zWnDvH_1xGTrlYVBI-0qicqOUHo5G1PZOCGfy6IiC_XQcxSGIOAx19GDQ6nh6xV1klWxcuA8saIc3PPAoF2_RxvRvwh4emLzUyNEHvr6ekUzsCZo9fEkE7TKo-14QQIVoCMoO-TBLlkAhVNN5ZVwjSJJQZBxKQseeOFFfEvjU6RyMXPdpqytFF8Id7HeK0SWHIUO7tIiYfh4Id6nJI1wMwdrxAPWHg4XvjbZ0eMalHmGxkK5oOFCm3TxXCnCia9hKVr4mVBr1EJXePxZDq2Jnr2WeOOFV1NtzVhT6ov68E9gdZZO2MXoqynXnDa__aA0PjVZGOanMMt7QtjDaveTWreAD6ciHSX2r6XlVu7Ww8y6Qn-nuXBpMw0LMK5Iaas5_PV-wuMSpUvwuJ8Ts9vxnvrRI_w4FPWPmrqFg0kvTdbiA10rYB6sAJZm_FGk";
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        System.out.println( "当前时间戳: " + timeStamp);
        String shopCipher = "ROW_8l-YRgAAAAA8ieExmggmXsQJU2ZhcbLP";
        String url = path + "?"
                + "app_key=" + appKey
                + "&timestamp=" + timeStamp
                + "&page_size=" + 100
                + "&shop_cipher=" + shopCipher ;
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(url)
                .method("GET", null)
                .addHeader("content-type", "application/json")
                .addHeader("x-tts-access-token", accessToken)
                .build();

        String sign = generateSignature(request,appSecret);
        System.out.println(sign);
    }

    public static void getAuthorized(){
        String path = "https://open-api.tiktokglobalshop.com/authorization/202309/shops";
        String appKey = "6gifmgej2sm26";
        String appSecret = "0cd910e160d5fa9ad1fb8717727d7265e52e4a8c";
        String accessToken = "ROW_oCE1ewAAAAAub6HllVGgkspeoSO6VWPXz4nsJ79hJUEWd8UaEUjncYujfhJz4Z-S4LdmUHxYs3G_yb3zWnDvH_1xGTrlYVBI-0qicqOUHo5G1PZOCGfy6IiC_XQcxSGIOAx19GDQ6nh6xV1klWxcuA8saIc3PPAoF2_RxvRvwh4emLzUyNEHvr6ekUzsCZo9fEkE7TKo-14QQIVoCMoO-TBLlkAhVNN5ZVwjSJJQZBxKQseeOFFfEvjU6RyMXPdpqytFF8Id7HeK0SWHIUO7tIiYfh4Id6nJI1wMwdrxAPWHg4XvjbZ0eMalHmGxkK5oOFCm3TxXCnCia9hKVr4mVBr1EJXePxZDq2Jnr2WeOOFV1NtzVhT6ov68E9gdZZO2MXoqynXnDa__aA0PjVZGOanMMt7QtjDaveTWreAD6ciHSX2r6XlVu7Ww8y6Qn-nuXBpMw0LMK5Iaas5_PV-wuMSpUvwuJ8Ts9vxnvrRI_w4FPWPmrqFg0kvTdbiA10rYB6sAJZm_FGk";
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        System.out.println( "当前时间戳: " + timeStamp);
        String url = path + "?" + "app_key=" + appKey + "&timestamp=" + timeStamp ;
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(url)
                .method("GET", null)
                .addHeader("content-type", "application/json")
                .addHeader("x-tts-access-token", accessToken)
                .build();

        String sign = generateSignature(request,appSecret);
        System.out.println(sign);
        Request requestNew = new Request.Builder()
                .url(url+ "&sign=" + sign)
                .method("GET", null)
                .addHeader("content-type", "application/json")
                .addHeader("x-tts-access-token", accessToken)
                .build();
        try {
            Response response = client.newCall(requestNew).execute();
            System.out.println(response.body().string());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String generateSignature(Request request, String secret) {
        HttpUrl httpUrl = request.url();
        List<String> parameterNameList = new ArrayList<>(httpUrl.queryParameterNames());

        // extract all query parameters excluding sign and access_token
        parameterNameList.removeIf(param -> "sign".equals(param) || "access_token".equals(param));

        // reorder the parameters' key in alphabetical order
        Collections.sort(parameterNameList);

        // append the request path
        StringBuilder parameterStr = new StringBuilder(httpUrl.encodedPath());
        for (String parameterName : parameterNameList) {
            // Concatenate all the parameters in the format of {key}{value}
            parameterStr.append(parameterName).append(httpUrl.queryParameter(parameterName));
        }

        // if the request header Content-type is not multipart/form-data, append body to the end
        String contentType = request.header("Content-Type");
        if (!"multipart/form-data".equalsIgnoreCase(contentType)) {
            try {
                RequestBody requestBody = request.body();
                if (requestBody != null) {
                    Buffer bodyBuffer = new Buffer();
                    requestBody.writeTo(bodyBuffer);
                    byte[] bodyBytes = bodyBuffer.readByteArray();
                    parameterStr.append(new String(bodyBytes, StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                throw new RuntimeException("failed to generate signature params", e);
            }
        }

        // wrap the string generated in step 5 with the App secret
        String signatureParams = secret + parameterStr + secret;

        // encode wrapped string using HMAC-SHA256
        return generateSHA256(signatureParams, secret);
    }

    /**
     * generate signature by SHA256
     * @param signatureParams signature params
     * @return signature
     */
    public static String generateSHA256(String signatureParams, String secret) {
        try {
            // Get an HmacSHA256 Mac instance and initialize with the secret key
            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKeySpec);

            // Update with input data
            sha256HMAC.update(signatureParams.getBytes(StandardCharsets.UTF_8));

            // Compute the HMAC and get the result
            byte[] hashBytes = sha256HMAC.doFinal();

            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte hashByte : hashBytes) {
                sb.append(String.format("%02x", hashByte & 0xff));
            }

            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("failed to generate signature result", e);
        }
    }
}