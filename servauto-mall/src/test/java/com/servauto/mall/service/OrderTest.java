package com.servauto.mall.service;

import com.github.pagehelper.PageInfo;
import com.servauto.common.utils.BigDecimalUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.mall.BaseTest;
import com.servauto.mall.dao.order.OrderMapper;
import com.servauto.mall.dao.order.OrderPayMapper;
import com.servauto.mall.enums.order.OrderDeliveryTypeEnum;
import com.servauto.mall.enums.payment.PaymentChannel;
import com.servauto.mall.model.dto.request.order.CreateOrderReqDTO;
import com.servauto.mall.model.dto.request.order.OrderProductReqDTO;
import com.servauto.mall.model.dto.request.order.QueryOrderReqDTO;
import com.servauto.mall.model.dto.request.order.SubmitOrderReqDTO;
import com.servauto.mall.model.dto.response.order.CashierDTO;
import com.servauto.mall.model.dto.response.order.CreateOrderDTO;
import com.servauto.mall.model.dto.response.order.OrderDTO;
import com.servauto.mall.model.dto.response.order.SubmitOrderDTO;
import com.servauto.mall.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.mall.model.dto.response.payment.PaymentDTO;
import com.servauto.mall.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.mall.model.entity.order.Order;
import com.servauto.mall.model.entity.order.OrderPay;
import com.servauto.mall.model.entity.order.OrderPayExample;
import com.servauto.mall.service.content.TemplateService;
import com.servauto.mall.service.notice.NoticeService;
import com.servauto.mall.service.order.TradeService;
import com.servauto.mall.service.packageinfo.PackageService;
import com.servauto.mall.service.payment.PaymentFactory;
import com.servauto.mall.service.payment.impl.I2c2pPaymentFactoryServicesImpl;
import com.servauto.mall.service.serviceinfo.ServiceInfoService;
import com.servauto.mall.support.payment.config.I2c2pPaymentProperties;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>OrderTest</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/8 12:59
 */
public class OrderTest extends BaseTest {

    @Resource
    private TradeService tradeService;

    @Resource
    private PackageService packageService;

    @Resource
    private ServiceInfoService serviceInfoService;

    @Resource
    private OrderPayMapper orderPayMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private TemplateService templateService;

    @Resource
    private NoticeService noticeService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private PaymentFactory paymentFactory;

    private static final Long customerId = 34L;
    private static final Long carId = 1672L;
    private static final Long serviceId = 6L;
    private static final Long serviceServiceId = 5L;
    private static final Long packageId = 6L;

    @Test
    public void testCreateOrderWithPackageService() {

        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(27L).quantity(2).build());
        products.add(OrderProductReqDTO.builder().productId(28L).quantity(1).build());

        CreateOrderDTO createOrderDTO = tradeService.createOrder(customerId,
                CreateOrderReqDTO.builder().carId(carId).serviceId(serviceId).packageId(packageId).products(products).build());
        Assertions.assertNotNull(createOrderDTO);
        Assertions.assertNotNull(createOrderDTO.getOrderNo());

        CreateOrderDTO cacheCreateOrderDTO = tradeService.queryCreateOrder(customerId, createOrderDTO.getOrderNo());
        Assertions.assertNotNull(cacheCreateOrderDTO);

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceId);
        Assertions.assertNotNull(serviceInfo);

        PackageInfoDTO packageInfo = packageService.getPackage(packageId);
        Assertions.assertNotNull(serviceInfo);
        System.out.println(cacheCreateOrderDTO);

        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getShippingFee()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getDiscountAmount()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getCouponAmount()));

        if (Boolean.TRUE.equals(packageInfo.getIsFixedPrice())) {
            Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getServiceFee()));
            Assertions.assertEquals(0, packageInfo.getPrice().compareTo(cacheCreateOrderDTO.getGrandTotal()));
        } else {
            BigDecimal fee;
            if (packageInfo.getServiceFee() != null) {
                fee = new BigDecimal(packageInfo.getServiceFee());
                Assertions.assertEquals(0, new BigDecimal(packageInfo.getServiceFee()).compareTo(cacheCreateOrderDTO.getServiceFee()));
            } else {
                fee = serviceInfo.getFee();
                Assertions.assertEquals(0, serviceInfo.getFee().compareTo(cacheCreateOrderDTO.getServiceFee()));
            }
            Assertions.assertEquals(0, cacheCreateOrderDTO.getGrandTotal().compareTo(cacheCreateOrderDTO.getSubtotal().add(fee)));
        }
    }

    @Test
    public void testCreateOrderWithPackageProductNoService() {

    }

    @Test
    public void testCreateOrderWithProductService() {

        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(27L).quantity(2).build());
        products.add(OrderProductReqDTO.builder().productId(28L).quantity(1).build());

        CreateOrderDTO createOrderDTO = tradeService.createOrder(customerId,
                CreateOrderReqDTO.builder()
                        .carId(carId)
                        .products(products)
                        .serviceId(serviceServiceId)
                        .build());
        Assertions.assertNotNull(createOrderDTO);
        Assertions.assertNotNull(createOrderDTO.getOrderNo());

        CreateOrderDTO cacheCreateOrderDTO = tradeService.queryCreateOrder(customerId, createOrderDTO.getOrderNo());
        Assertions.assertNotNull(cacheCreateOrderDTO);

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceServiceId);
        Assertions.assertNotNull(serviceInfo);
        System.out.println(cacheCreateOrderDTO);

        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getShippingFee()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getDiscountAmount()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getCouponAmount()));
        Assertions.assertEquals(0, serviceInfo.getFee().compareTo(cacheCreateOrderDTO.getServiceFee()));
        Assertions.assertEquals(0, cacheCreateOrderDTO.getGrandTotal().compareTo(cacheCreateOrderDTO.getSubtotal().add(serviceInfo.getFee())));
    }

    @Test
    public void testCreateOrderWithProductsNoService() {

        // [{"orderNo":"1400080186779111424","carId":"6202","serviceId":null,"packageId":null,"products":[{"productId":"41","quantity":1}]
        // ,"deliveryType":"SHIPPING","workshopId":null,"shippingAddressId":"34","reservationTime":null,"licensePlate":"shayuan"}

        // [{"carId":"36","serviceId":null,"packageId":null,"products":[{"productId":"15","quantity":2}],"remark":""}]
        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(15L).quantity(2).build());

        CreateOrderDTO createOrderDTO = tradeService.createOrder(1L,
                CreateOrderReqDTO.builder().carId(36L).products(products).build());
        Assertions.assertNotNull(createOrderDTO);
        Assertions.assertNotNull(createOrderDTO.getOrderNo());

        CreateOrderDTO cacheCreateOrderDTO = tradeService.queryCreateOrder(1L, createOrderDTO.getOrderNo());
        Assertions.assertNotNull(cacheCreateOrderDTO);
        System.out.println(cacheCreateOrderDTO);

        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getShippingFee()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getServiceFee()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getDiscountAmount()));
        Assertions.assertEquals(0, BigDecimal.ZERO.compareTo(cacheCreateOrderDTO.getCouponAmount()));
        Assertions.assertEquals(0, cacheCreateOrderDTO.getGrandTotal().compareTo(cacheCreateOrderDTO.getSubtotal()));
    }

    @Test
    public void testSubmitOrderWithInWorkshopPackage() {
        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(27L).quantity(2).build());
        products.add(OrderProductReqDTO.builder().productId(28L).quantity(1).build());

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceId);
        Assertions.assertNotNull(serviceInfo);

        Long workshopId = serviceInfo.getWorkshopIds().stream().findAny().orElseThrow();

        SubmitOrderDTO submitOrderDTO = tradeService.submitOrder(customerId, SubmitOrderReqDTO.builder()
                .carId(carId)
                .serviceId(serviceId)
                .packageId(packageId)
                .products(products)
                .orderNo("1400136077964169216")
                .deliveryType(OrderDeliveryTypeEnum.WORKSHOP.getCode())
                .workshopId(workshopId)
                .reservationTime(1745291843000L)
                .build());
        Assertions.assertNotNull(submitOrderDTO);
    }

    @Test
    public void testSubmitOrderWithInWorkshopService() {
        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(29L).quantity(2).build());
        products.add(OrderProductReqDTO.builder().productId(30L).quantity(1).build());

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceServiceId);
        Assertions.assertNotNull(serviceInfo);

        Long workshopId = serviceInfo.getWorkshopIds().stream().findAny().orElseThrow();

        SubmitOrderDTO submitOrderDTO = tradeService.submitOrder(customerId, SubmitOrderReqDTO.builder()
                .carId(carId)
                .serviceId(serviceServiceId)
                .products(products)
                .orderNo("1397550293734653952")
                .deliveryType(OrderDeliveryTypeEnum.WORKSHOP.getCode())
                .workshopId(workshopId)
                .reservationTime(1745291843000L)
                .build());
        Assertions.assertNotNull(submitOrderDTO);
    }

    @Test
    public void testSubmitOrderWithShipping() {

        // [{"orderNo":"1400080186779111424","carId":"6202","serviceId":null,"packageId":null,"products":[{"productId":"41","quantity":1}]
        // ,"deliveryType":"SHIPPING","workshopId":null,"shippingAddressId":"34","reservationTime":null,"licensePlate":"shayuan"}
        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(41L).quantity(1).build());

        SubmitOrderDTO submitOrderDTO = tradeService.submitOrder(19L, SubmitOrderReqDTO.builder()
                .orderNo("1400082454924185600")
                .carId(6202L)
                .products(products)
                .deliveryType(OrderDeliveryTypeEnum.SHIPPING.getCode())
                .licensePlate("shayuan")
                .shippingAddressId(34L)
                .build());
        Assertions.assertNotNull(submitOrderDTO);
    }

    @Test
    public void testSubmitOrderWithPickup() {
        List<OrderProductReqDTO> products = new ArrayList<>();
        products.add(OrderProductReqDTO.builder().productId(29L).quantity(2).build());
        products.add(OrderProductReqDTO.builder().productId(30L).quantity(1).build());

        SubmitOrderDTO submitOrderDTO = tradeService.submitOrder(customerId, SubmitOrderReqDTO.builder()
                .orderNo("1398262741152301056")
                .carId(carId)
                .products(products)
                .deliveryType(OrderDeliveryTypeEnum.PICKUP.getCode())
                .workshopId(3L)
                .build());
        Assertions.assertNotNull(submitOrderDTO);
    }

    @Test
    public void testQueryOrders() {
        List<OrderDTO> orders = tradeService.queryOrders(QueryOrderReqDTO.builder().customerId(19L).orderNo("1400082454924185600").build());
        Assertions.assertNotNull(orders);
        Assertions.assertTrue(CollectionUtils.isNotEmpty(orders));
    }

    @Test
    public void testPaymentSuccess() {
        List<String> orderNos = Arrays.asList(
                "1405205956300115968",
                "1404582308073705472",
                "1403477751662907392",
                "1403415819857694720",
                "1402673764214771712",
                "1400267888514306048",
                "1400243852950376448",
                "1400234253702664192",
                "1400170978927448064",
                "1400148934760337408");

        for (String orderNo : orderNos) {
            OrderPayExample example = new OrderPayExample();
            example.createCriteria().andOrderNoEqualTo(orderNo);
            List<OrderPay> orderPays = orderPayMapper.selectByExample(example);
            OrderPay orderPay = orderPays.stream().findFirst().orElseThrow();
            try {
                tradeService.onPaymentSuccess(orderPay.getOrderNo(), orderPay.getPayNo(), orderPay.getGrandTotal(), new Date());
            } catch (Exception ignored) {
            }
        }
    }

    @Test
    public void testPageOrders() {
        PageInfo<OrderDTO> pageInfo = tradeService.pageOrders(QueryOrderReqDTO.builder().customerId(1L).orderNo("1398262741152301056").build());
        System.out.println(pageInfo.isIsFirstPage());
        System.out.println(pageInfo.isIsLastPage());
    }

    @Test
    public void testQueryCacheCreateOrder() {
        CreateOrderDTO createOrderDTO = tradeService.queryCreateOrder(19L, "1400082454924185600");
        System.out.println(JacksonSerializer.serialize(createOrderDTO));
    }

    @Test
    public void testSendHqOPS() throws InterruptedException {
        Order orderDTO = orderMapper.selectByOrderNo("1397540686953971712");
        noticeService.onRescheduleSendWhatsapp(orderDTO);
        TimeUnit.SECONDS.sleep(20);
    }

    @Test
    public void testCashier() {
        CashierDTO cashierDTO = tradeService.cashier(37L, "1405611018491006976", "P1405611293536686080");
        System.out.println(cashierDTO);
        System.out.println(JacksonSerializer.serialize(cashierDTO));
    }

    @Test
    public void testBigDecimal() {
        System.out.println(JacksonSerializer.serialize(BigDecimalUtils.format(null)));
        System.out.println(JacksonSerializer.serialize(BigDecimalUtils.format(BigDecimal.ZERO)));
        System.out.println(JacksonSerializer.serialize(BigDecimalUtils.format(new BigDecimal(100))));
        System.out.println(JacksonSerializer.serialize(BigDecimalUtils.format(new BigDecimal(247))));
    }
    @Test
    public void test() {
        paymentFactory.createPaymentServices(PaymentChannel.IPAY88.getDesc()).batchQueryPaymentStatus();
    }

}
