package com.servauto.mall.service;

import com.servauto.mall.BaseTest;
import com.servauto.mall.model.dto.request.product.QueryProductsDTO;
import com.servauto.mall.service.product.ProductService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class ProductTest extends BaseTest {
    @Resource
    private ProductService productService;

    @Test
    public void getProducts() {
        var detail = productService.getProducts(QueryProductsDTO.builder().categoryId(7L).carId(7L).build());
        Assertions.assertNotNull(detail);
        System.out.println(detail.size());
    }
}
