package com.servauto.mall.service;

import com.servauto.common.constant.TiktokConstants;
import com.servauto.mall.BaseTest;
import com.servauto.mall.model.dto.response.tiktok.TiktokOrdersRespDTO;
import com.servauto.mall.model.dto.response.tiktok.TiktokProductInfoRespDTO;
import com.servauto.mall.service.tiktok.TiktokService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TiktokServiceTest extends BaseTest {
    @Resource
    private TiktokService tiktokService;

    @Test
    public void test() {
        TiktokOrdersRespDTO orders = tiktokService.getOrders(null, "5", null, System.currentTimeMillis() / 1000 - 60 * 60 * 60 * 24, null, TiktokConstants.SELLER);

        List<String> productIds = new ArrayList<>();
        orders.getData().getOrders().forEach(f -> {
            List<TiktokOrdersRespDTO.LineItem> lineItems = f.getLineItems();
            lineItems.forEach(t -> {
                productIds.add(t.getProductId());
            });
        });
        List<TiktokProductInfoRespDTO> productsInfo = tiktokService.getProductsInfo(productIds);

        Map<String,String> packageMap = new HashMap<>();
        Map<String,String> categoryMap = new HashMap<>();
        productsInfo.stream().filter(f->f!=null).forEach(t->{
            List<TiktokProductInfoRespDTO.CategoryChainDTO> categoryChains = t.getData().getCategoryChains();
            TiktokProductInfoRespDTO.PackageWeightDTO packageWeight = t.getData().getPackageWeight();

            packageMap.put(t.getData().getId(),packageWeight.getValue()+"/"+packageWeight.getUnit());

            categoryMap.put(t.getData().getId(),categoryChains.stream().filter(TiktokProductInfoRespDTO.CategoryChainDTO::getIsLeaf).map(TiktokProductInfoRespDTO.CategoryChainDTO::getLocalName).findFirst().orElse(""));


            });
    }
}
