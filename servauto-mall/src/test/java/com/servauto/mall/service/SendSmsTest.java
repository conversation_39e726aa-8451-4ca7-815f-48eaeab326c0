package com.servauto.mall.service;

import com.servauto.framework.sms.api.dto.SmsCodeSendReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeUseReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeValidateReqDTO;
import com.servauto.framework.sms.api.service.SmsCodeApi;
import com.servauto.framework.sms.enums.SceneTemplateEnum;
import com.servauto.mall.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

public class SendSmsTest extends BaseTest {

    @Resource
    private SmsCodeApi smsCodeApi;

    @Test
    public void testSendSms() throws InterruptedException {
        smsCodeApi.sendSmsCode(SmsCodeSendReqDTO.builder().mobile("60102718329").
                scene(SceneTemplateEnum.CUSTOMER_LOGIN.getScene()).createIp("127.0.0.1").build());
        TimeUnit.SECONDS.sleep(20);
    }

    @Test
    public void testSendWhatsapp() throws InterruptedException {
        smsCodeApi.sendSmsCode(SmsCodeSendReqDTO.builder().mobile("8613120356220").
                scene(SceneTemplateEnum.CUSTOMER_WHATSAPP_LOGIN.getScene()).createIp("127.0.0.1").build());
        TimeUnit.SECONDS.sleep(20);
    }

    @Test
    public void useSendSms() {
        smsCodeApi.useSmsCode(SmsCodeUseReqDTO.builder().mobile("18335775996").
                scene(SceneTemplateEnum.CUSTOMER_LOGIN.getScene()).code("123456").usedIp("127.0.0.1").build());
    }

    @Test
    public void validateSendSms() {
        smsCodeApi.validateSmsCode(SmsCodeValidateReqDTO.builder().mobile("8618335775996").
                scene(SceneTemplateEnum.CUSTOMER_LOGIN.getScene()).code("123456").build());
    }




}
