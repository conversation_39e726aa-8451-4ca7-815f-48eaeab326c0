package com.servauto.mall.service;

import com.github.pagehelper.PageInfo;
import com.servauto.mall.BaseTest;
import com.servauto.mall.model.dto.response.workshop.WorkshopDTO;
import com.servauto.mall.service.workshop.WorkshopService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <p>WorkshopTest</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/14 13:39
 */
public class WorkshopTest extends BaseTest {

    @Resource
    private WorkshopService workshopService;

    @Test
    public void testGetWorkshopList() {
        PageInfo<WorkshopDTO> pageInfo = workshopService.pageWorkshops(1, 10);
        Assertions.assertTrue(CollectionUtils.isNotEmpty(pageInfo.getList()));
    }

}
