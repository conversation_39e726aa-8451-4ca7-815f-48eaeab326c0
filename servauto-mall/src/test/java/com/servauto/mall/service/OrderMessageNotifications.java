package com.servauto.mall.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.servauto.common.enums.TriggerType;
import com.servauto.framework.lark.MessageTriggerStrategy;
import com.servauto.framework.lark.MessageTriggerStrategyFactory;
import com.servauto.framework.lark.TriggerMessageReq;
import com.servauto.mall.BaseTest;
import org.junit.jupiter.api.Test;

import java.net.ConnectException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class OrderMessageNotifications extends BaseTest {

    @Test
    public  void test() throws JsonProcessingException, ConnectException {
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.CONFIRM);

        // 创建日期格式化器（注意：标准格式应为 yyyy-MM-dd HH:mm）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 生成当前时间和1小时后的时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime preferTime = now.plusHours(1);

        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId("sf000001");                  // 订单ID
        req.setOrderType("支付");                     // 订单类型
        req.setCarPlateNumber("粤A12345");            // 车牌号
        req.setServiceName("发动机保养");               // 服务名称
        req.setStoreName("广州天河店");                 // 门店名称
        req.setTime(formatter.format(now));           // 当前时间
        req.setPreferTime(formatter.format(preferTime)); // 预约时间（1小时后）

        strategy.doProcess(req);
    }

    @Test
    public  void test1() throws JsonProcessingException, ConnectException {
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.BOOK_SERVICE);

        // 创建日期格式化器（注意：标准格式应为 yyyy-MM-dd HH:mm）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 生成当前时间和1小时后的时间
        LocalDateTime now = LocalDateTime.now();
        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId("sf000002");                  // 订单ID
        req.setOrderType("支付1");                     // 订单类型
        req.setCarPlateNumber("粤A12345");            // 车牌号
        req.setServiceName("发动机保养");               // 服务名称
        req.setStoreName("广州天河店");                 // 门店名称
        req.setTime(formatter.format(now));           // 当前时间


        strategy.doProcess(req);
    }

    @Test
    public  void test2() throws JsonProcessingException, ConnectException {
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.ORDER_PAYMENT_COMPLETED);

        // 创建日期格式化器（注意：标准格式应为 yyyy-MM-dd HH:mm）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 生成当前时间和1小时后的时间
        LocalDateTime now = LocalDateTime.now();

        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId("sf000003");                  // 订单ID
        req.setOrderType("支付2");                     // 订单类型
        req.setCarPlateNumber("粤A12345");            // 车牌号
        req.setServiceName("发动机保养");               // 服务名称
        req.setStoreName("广州天河店");                 // 门店名称
        req.setTime(formatter.format(now));           // 当前时间


        strategy.doProcess(req);
    }


}
