package com.servauto.mall.service;


import com.servauto.mall.BaseTest;
import com.servauto.mall.model.dto.response.content.TemplateDTO;
import com.servauto.mall.service.content.TemplateService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ContentTemplateTest extends BaseTest {

    @Resource
    private TemplateService templateService;

    @Test
    public void testGetTemplateByTemplateCode() {
        String templateCode = "HOME_PAGE";
        TemplateDTO templateDTO = templateService.getTemplateByTemplateCode(templateCode);
        Assertions.assertNotNull(templateDTO);
        System.out.println(templateDTO);
    }

}
