package com.servauto.mall.service;

import com.google.common.collect.Maps;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserResp;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.servauto.mall.BaseTest;
import com.servauto.mall.service.lark.LarkService;
import com.servauto.mall.service.lark.dto.*;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class BiTableServiceTest extends BaseTest {

    @Resource
    private LarkService larkService;

    @Resource
    private RedisTemplate redisTemplate;

    @Test
    public void createAppAndTables() {
        // 创建应用和多维表格
        CreateAppResp createAppResp = larkService.createAppAndTables("ServAuto Test BiTable");

        //响应参数
//        {
//            "app": {
//            "app_token": "AzPIbQNtnaXvGUsVQITlOEYZgMe",
//                    "name": "ServAuto Test BiTable",
//                    "folder_token": "DPwvfgWcMluuZmdskM0lNLCegRe",
//                    "url": "https://test-daugyoamq1u0.sg.larksuite.com/base/AzPIbQNtnaXvGUsVQITlOEYZgMe",
//                    "default_table_id": "tblRSnEsgAi2jwbw"
//        }
//        }

    }

    @Test
    public void updateAppAndTables() {

        UpdateAppResp updateAppResp = larkService.updateAppAndTables("ServAuto Test BiTable(Update)", "AzPIbQNtnaXvGUsVQITlOEYZgMe");
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(updateAppResp.getData().getApp()));
        //响应参数  {"app_token":"AzPIbQNtnaXvGUsVQITlOEYZgMe","name":"ServAuto Test BiTable(Update)","is_advanced":true}
    }

    @Test
    public void getAppAndTablesByAppToken() {
        GetAppResp getAppResp = larkService.getAppAndTablesByAppToken("AzPIbQNtnaXvGUsVQITlOEYZgMe");
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(getAppResp.getData().getApp()));
        //响应参数   {"app_token":"AzPIbQNtnaXvGUsVQITlOEYZgMe","name":"ServAuto Test BiTable(Update)","revision":1,"is_advanced":true,"formula_type":2,"advance_version":"v2"}
    }

    @Test
    public void batchCreateAppTableRecord() {
        BatchCreateTableRecordDTO dto = new BatchCreateTableRecordDTO();
        dto.setAppToken("H7GcbKkyPaeMzUsukWUlvY7OgBh");
        dto.setTableId("tblnMI7AIIOgvIyh");

        List<Map<String, Object>> h = Lists.newArrayList();

        Map<String, Object> fields = Maps.newHashMap();
        fields.put("ORDER ID", "1231234342332");
        fields.put("Buyer Username", "选项1");
        fields.put("Create time", 1674206443000L);
        h.add(fields);

        CreateTableRecordsDTO a = new CreateTableRecordsDTO();
        a.setFields(fields);

        CreateTableRecordsDTO d = new CreateTableRecordsDTO();
        d.setFields(fields);
        List<CreateTableRecordsDTO> l = new ArrayList<>();
        l.add(a);
        l.add(d);
        dto.setRecords(l);
        System.out.println("请求参数:  " + Jsons.DEFAULT.toJson(dto));
        BatchCreateAppTableRecordResp batchCreateAppTableRecordResp = larkService.batchCreateAppTableRecord(dto);
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(batchCreateAppTableRecordResp));
        //响应参数:  {"records":[{"fields":{"单选":"选项1","多选":["选项1","选项2"],"文本":"多行文本内容","日期":1.674206443E12},"record_id":"recuP7gy7m0ITZ"},{"fields":{"单选":"选项1","多选":["选项1","选项2"],"文本":"多行文本内容","日期":1.674206443E12},"record_id":"recuP7gy7mgdHT"}]}
    }

    @Test
    public void batchUpdateAppTableRecord() {
        BatchUpdateTableRecordDTO batchUpdateTableRecordDTO = new BatchUpdateTableRecordDTO();

        batchUpdateTableRecordDTO.setAppToken("C06Jb9KdraUPwBsNIvglbc4hgxf");
        batchUpdateTableRecordDTO.setTableId("tblt8df5vuxTBqJP");
        List<UpdateTableRecordsDTO> records = new ArrayList<>();
        UpdateTableRecordsDTO record = new UpdateTableRecordsDTO();
        record.setRecordId("recuP7gy7m0ITZ");
        Map<String, Object> fields = Maps.newHashMap();
        fields.put("文本", "多行文本内容");
        fields.put("单选", "选项3");
        record.setFields(fields);
        records.add(record);
        batchUpdateTableRecordDTO.setRecords(records);
        System.out.println("请求参数:  " + Jsons.DEFAULT.toJson(batchUpdateTableRecordDTO));
        BatchUpdateAppTableRecordResp batchUpdateAppTableRecordResp = larkService.batchUpdateAppTableRecord(batchUpdateTableRecordDTO);
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(batchUpdateAppTableRecordResp.getData()));
    }

    @Test
    public void queryTableRecords() {
        QueryTableRecordsDTO query = new QueryTableRecordsDTO();
        query.setAppToken("C06Jb9KdraUPwBsNIvglbc4hgxf");
        query.setTableId("tblt8df5vuxTBqJP");
        ListAppTableRecordResp listAppTableRecordResp = larkService.queryTableRecords(query);
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(listAppTableRecordResp));
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(listAppTableRecordResp.getData()));
    }

    @Test
    public void getAssessToken() {
//        boolean b =     redisTemplate.delete("tiktokDeveloper:tenant_access_token");
//        System.out.println(b);

        BatchGetIdUserResp batchGetIdUserResp = larkService.batchGetUserId( new String[]{"<EMAIL>"}, new String[]{});
//        GetUserInfoByNameResp getUserInfoByNameResp = larkService.getUserInfoByName("chong.zhao");
        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(batchGetIdUserResp.getData()));

    }

    @Test
    public void copyAppTable() {
        // 创建应用和多维表格
        CopyAppResp resp = larkService.copyAppTable("C06Jb9KdraUPwBsNIvglbc4hgxf","新订单表");
//
//        System.out.println("响应参数:  " + Jsons.DEFAULT.toJson(resp));
    }

    @Test
    public void createMessage(){
//        BatchGetIdUserResp batchGetIdUserResp = larkService.batchGetUserId( new String[]{"<EMAIL>"}, new String[]{});
        BatchGetIdUserResp batchGetIdUserResp = larkService.batchGetUserId( new String[]{"<EMAIL>"}, new String[]{});
        UserContactInfo[] UserContactInfo = batchGetIdUserResp.getData().getUserList();
        System.out.println("响应参数:  " + UserContactInfo[0].getUserId() );

        CreateMessageResp createMessageResp =   larkService.createMessage(UserContactInfo[0].getUserId(),"Hello Thomas","text");
        System.out.println("createMessageResp 响应参数:  " + Jsons.DEFAULT.toJson(createMessageResp.getData()));
    }

}
