package com.servauto.mall.service;

import com.servauto.mall.BaseTest;
import com.servauto.mall.service.packageinfo.PackageService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <p>PackageTest</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/29 16:52
 */
public class PackageTest extends BaseTest {

    @Resource
    private PackageService packageService;

    @Test
    public void recommendPackages() {
        var detail = packageService.getPackageDetails(0L, 70L, 0L, "");
        Assertions.assertNotNull(detail);
        System.out.println(detail);
    }
}
