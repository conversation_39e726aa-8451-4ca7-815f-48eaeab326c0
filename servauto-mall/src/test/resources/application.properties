spring.profiles.active=local
server.port=8060

logging.level.com.servauto=debug
logging.level.org.springframework=warn
logging.level.mybatis= debug

spring.datasource.druid.master.url=**********************************************************************************************************************************************************************
spring.datasource.druid.master.username=servauto
spring.datasource.druid.master.password=servauto@123

spring.data.redis.host=************
spring.data.redis.port=6679
spring.data.redis.database=1

# token配置
mall.security.token-header=Authorization
mall.security.token-parameter=token
mall.security.permit-all-urls=

# SMS config
sms-code.enable=true
sms-code.begin-code=0
sms-code.end-code=999999
sms-code.expire-times=10m
sms-code.send-frequency=1s
sms-code.send-maximum-quantity-per-day=100000


# Payment config
mall.payment.ipay88.merchant.name=ServAuto Sdn Bhd
mall.payment.ipay88.merchant.code=M44858
mall.payment.ipay88.merchant.key=yVYByNudME
mall.payment.ipay88.merchant.responseURL=yVYByNudME
mall.payment.ipay88.merchant.backendURL=yVYByNudME
mall.payment.ipay88.merchant.appDeeplink=yVYByNudME
mall.payment.ipay88.platform.createurl=https://payment.ipay88.com.my/epayment/entry.asp
mall.payment.ipay88.platform.requeryurl=https://payment.ipay88.com.my/epayment/webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx?op=TxDetailsInquiryCardInfo

# Payment config
mall.payment.i2c2p.merchant.merchantID=JT01
mall.payment.i2c2p.merchant.secretKey=ECC4E54DBA738857B84A7EBC6B5DC7187B8DA68750E88AB53AAA41F548D6F2D9
mall.payment.i2c2p.merchant.currencyCode=SGD
mall.payment.i2c2p.merchant.expirytime=15
mall.payment.i2c2p.merchant.responseURL=http://************:8060/payment/response
mall.payment.i2c2p.merchant.websiteURL=http://************:3000/payment
mall.payment.i2c2p.merchant.backendURL=http://************:8060/payment/backend_response
mall.payment.i2c2p.platform.requestUrl=https://sandbox-pgw.2c2p.com/payment/4.3

webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.confirm.secret: 2FslWuOqM6AjPxU3rvzXCc

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.book.secret: 2FslWuOqM6AjPxU3rvzXCc

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.completed.secret: 2FslWuOqM6AjPxU3rvzXCc


# TikTok ship config
mall.tiktok.appKey=6gifmgej2sm26
mall.tiktok.appSecret=0cd910e160d5fa9ad1fb8717727d7265e52e4a8c
mall.tiktok.serviceId=7516828895666833158

# Lark Developer config
mall.lark.appId=********************
mall.lark.appSecret=Ev1Ah2BBhazi0HKv9mSkccNbY8Xhpe1V
mall.lark.folderToken=DPwvfgWcMluuZmdskM0lNLCegRe