<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.servauto</groupId>
        <artifactId>servauto-bom</artifactId>
        <version>1.0.0</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>1.0.2</version>
    <packaging>jar</packaging>

    <artifactId>servauto-mall</artifactId>
    <description>servauto mall system</description>

    <properties>
        <servauto-framework.version>1.0.5</servauto-framework.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.servauto</groupId>
                <artifactId>servauto-framework</artifactId>
                <version>${servauto-framework.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.servauto</groupId>
            <artifactId>servauto-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.19.0</version>
        </dependency>
    </dependencies>

    <profiles>

        <profile>
            <id>dev</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/profiles/dev</directory>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>test</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/profiles/test</directory>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>uat</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/profiles/uat</directory>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/profiles/prod</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

    <build>

        <finalName>${project.artifactId}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.15</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <configurationFile>src/test/resources/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>