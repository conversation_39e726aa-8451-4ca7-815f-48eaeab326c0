package com.servauto.framework.filter;

import com.servauto.common.utils.generator.UniqueID;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;


public class TraceFilter extends OncePerRequestFilter {

    private static final String HEADER_NAME_TRACE_ID = "X-Trace-Id";
    private static final String TRACE_ID_KET = "traceId";

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            MDC.put(TRACE_ID_KET, UniqueID.generateId(""));
            response.addHeader(HEADER_NAME_TRACE_ID, MDC.get(TRACE_ID_KET));
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

}
