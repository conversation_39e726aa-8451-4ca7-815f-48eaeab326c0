package com.servauto.framework.exception;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.core.text.Convert;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.common.utils.html.EscapeUtil;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.MessageUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public CommonResult<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.error("HttpRequestMethodNotSupportedException Request address '{}', '{}' request is not supported", request.getRequestURI(), e.getMethod());
        return CommonResult.of(ResponseCode.RequestMethodNotSupported.getCode(), String.format("%s %s %s", ResponseCode.RequestMethodNotSupported.getMsg(), request.getRequestURI(), e.getMethod()));
    }

    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    @ResponseBody
    public CommonResult<Object> handleMissingServletException(MissingServletRequestParameterException e, HttpServletRequest request) {
        log.warn("http request MissingServletRequestParameterException, uri={}, args={}", request.getRequestURI(), JacksonSerializer.serialize(request.getParameterMap()), e);
        return CommonResult.of(ResponseCode.ParamNonExist.getCode(), e.getMessage());
    }

    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public CommonResult<Object> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String msg = e.getMsg();
        String code = e.getCode();
        if (e.isConvertI18n() && StringUtils.isNotEmpty(e.getCode())) {
            try {
                code = ResponseCode.ERROR.getCode();
                msg = MessageUtils.message(e.getCode(), e.getArgs());
            } catch (Exception ignored) {
            }
        }
        log.warn("Business exception uri = {} code={} msg={} ", request.getRequestURI(), code, msg);
        return StringUtils.isNotEmpty(code) ? CommonResult.of(code, msg, e.getData()) : CommonResult.error();
    }

    @ExceptionHandler(MissingPathVariableException.class)
    @ResponseBody
    public CommonResult<Object> handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request) {
        log.warn("A required path variable is missing from the request path {}", request.getRequestURI(), e);
        return CommonResult.of(ResponseCode.ParamInValid.getCode(), String.format("A required path variable is missing from the request path[%s]", e.getVariableName()));
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public CommonResult<Object> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String value = Convert.toStr(e.getValue());
        if (StringUtils.isNotEmpty(value)) {
            value = EscapeUtil.clean(value);
        }
        log.warn("Request parameter type does not match '{}'.", requestURI, e);
        return CommonResult.of(ResponseCode.ParamInValid.getCode(), String.format("The request parameter type does not match. The parameter [%s] requires the type: '%s', but the input value is: '%s'", e.getName(), e.getRequiredType() != null ? e.getRequiredType().getName() : null, value));
    }

    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public CommonResult<Object> handleBindException(BindException e) {
        log.warn("Bind exception {}", e.getMessage(), e);
        return CommonResult.of(ResponseCode.ParamBindFailed.getCode(),
                String.format("%s %s", ResponseCode.ParamBindFailed.getMsg(), e.getBindingResult().getFieldErrors().stream()));
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public CommonResult<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("MethodArgument exception {}", e.getMessage(), e);
        return CommonResult.of(ResponseCode.ParamInValid.getCode(), e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage).collect(Collectors.joining("||")));
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    @ResponseBody
    public CommonResult<Object> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("MethodArgument exception {}", e.getMessage(), e);
        return CommonResult.of(ResponseCode.ParamInValid.getCode(), String.format("%s %s", ResponseCode.ParamInValid.getMsg(), StringUtils.isNotEmpty(e.getMessage()) ? e.getMessage() : ""));
    }

    @ExceptionHandler(value = NoResourceFoundException.class)
    @ResponseBody
    public CommonResult<Object> handleNoResourceFoundException(NoResourceFoundException e, HttpServletRequest request) {
        log.warn("Request address '{}', an 404 exception occurred.", request.getRequestURI(), e);
        return CommonResult.of(ResponseCode.RESOURCE_NOT_FOUND);
    }


    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public CommonResult<Object> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.warn("Request address '{}', HttpMessageConverter error ", request.getRequestURI(), e);
        return CommonResult.of(ResponseCode.ParamBindFailed.getCode(), e.getCause() != null ? e.getCause().getMessage() : e.getMessage());
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public CommonResult<Object> handleMaxSizeException(MaxUploadSizeExceededException e) {
        log.warn("Request File is too large maxSize {} ", e.getMaxUploadSize());
        return CommonResult.of(ResponseCode.ParamInValid.getCode(), "File is too large!");
    }

    @ExceptionHandler(value = AuthorizationDeniedException.class)
    @ResponseBody
    public CommonResult<Object> handleAuthorizationDeniedException(AuthorizationDeniedException e, HttpServletRequest request) {
        log.warn("Request address '{}', an AuthorizationDenied exception occurred.", request.getRequestURI(), e);
        return CommonResult.error(ResponseCode.WARN.getCode(), String.format("%s Permission denied", request.getRequestURI()));
    }

    @ExceptionHandler(value = RuntimeException.class)
    @ResponseBody
    public CommonResult<Object> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.warn("Request address '{}', an runtime exception occurred.", request.getRequestURI(), e);
        return CommonResult.error();
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public CommonResult<Object> handleException(Exception e, HttpServletRequest request) {
        log.error("Request address '{}', an system exception occurred.", request.getRequestURI(), e);
        return CommonResult.error();
    }


}
