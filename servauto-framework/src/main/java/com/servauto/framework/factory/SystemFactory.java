package com.servauto.framework.factory;

import com.servauto.framework.config.properties.SystemProperties;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.time.ZoneId;

/**
 * <p>SystemFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 23:00
 */
@Setter
@Getter
@Component
public class SystemFactory {

    @Resource
    private SystemProperties systemProperties;

    private static SystemFactory systemFactory;

    @PostConstruct
    public void init() {
        systemFactory = this;
    }

    public static ZoneId getSystemZonId() {
        return ZoneId.of(systemFactory.systemProperties.getTimezone());
    }

}
