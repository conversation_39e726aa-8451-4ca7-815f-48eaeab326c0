package com.servauto.framework.file.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.servauto.framework.file.config.FileClientConfig;
import com.servauto.framework.file.dao.SystemFileConfigMapper;
import com.servauto.framework.file.factory.FileClient;
import com.servauto.framework.file.factory.FileClientFactory;
import com.servauto.framework.file.model.SystemFileConfig;
import com.servauto.framework.file.service.FileConfigService;
import com.servauto.framework.parse.JacksonSerializer;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executors;

@Service
public class FileConfigServiceImpl implements FileConfigService {

    private static final Long CACHE_MASTER_ID = 0L;

    @Resource
    private FileClientFactory fileClientFactory;

    @Resource
    private SystemFileConfigMapper systemFileConfigMapper;

    @Getter
    private final LoadingCache<Long, FileClient> clientCache = buildAsyncReloadingCache(Duration.ofSeconds(10L),
            new CacheLoader<>() {
                @NotNull
                @Override
                public FileClient load(Long id) {
                    SystemFileConfig config = Objects.equals(CACHE_MASTER_ID, id) ?
                            systemFileConfigMapper.selectByMaster() : systemFileConfigMapper.selectByPrimaryKey(id);
                    if (config != null) {
                        fileClientFactory.createOrUpdateFileClient(config.getId(), config.getStorage(), parseConfig(config.getConfig()));
                    }
                    return fileClientFactory.getFileClient(null == config ? id : config.getId());
                }
            });

    @Override
    public SystemFileConfig getFileConfig(Long id) {
        return systemFileConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public FileClient getFileClient(Long id) {
        return clientCache.getUnchecked(id);
    }

    @Override
    public FileClient getMasterFileClient() {
        return clientCache.getUnchecked(CACHE_MASTER_ID);
    }

    private static <K, V> LoadingCache<K, V> buildAsyncReloadingCache(Duration duration, CacheLoader<K, V> loader) {
        return CacheBuilder.newBuilder()
                .refreshAfterWrite(duration)
                .build(CacheLoader.asyncReloading(loader, Executors.newCachedThreadPool()));
    }

    private static FileClientConfig parseConfig(String config) {
        FileClientConfig fileClientConfig = JacksonSerializer.deSerialize(config, new TypeReference<>() {
        });
        if (fileClientConfig == null) {
            throw new IllegalArgumentException("Unknown FileClient Config type: " + config);
        }
        return fileClientConfig;
    }
}
