package com.servauto.framework.file.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.file.dao.SystemFileMapper;
import com.servauto.framework.file.enums.FileResponseEnum;
import com.servauto.framework.file.factory.FileClient;
import com.servauto.framework.file.model.SystemFile;
import com.servauto.framework.file.model.dto.FileCreateReqVO;
import com.servauto.framework.file.service.FileConfigService;
import com.servauto.framework.file.service.FileService;
import com.servauto.framework.file.utils.FileTypeUtils;
import com.servauto.framework.file.utils.FileUtils;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private SystemFileMapper systemFileMapper;

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // execute upload file
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "Client(master) cannot null");
        String url = client.upload(content, path, type);

        // save result
        SystemFile file = new SystemFile();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        systemFileMapper.insertSelective(file);
        return url;
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        SystemFile file = BeanUtils.toBean(createReqVO, SystemFile.class);
        systemFileMapper.insertSelective(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // validate exist
        SystemFile file = validateFileExists(id);

        // delete file
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "Client({}) cannot null", file.getConfigId());
        client.delete(file.getPath());
        systemFileMapper.deleteByPrimaryKey(id);
    }

    private SystemFile validateFileExists(Long id) {
        SystemFile fileDO = systemFileMapper.selectByPrimaryKey(id);
        if (fileDO == null) {
            throw BusinessException.of(FileResponseEnum.FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "Client({}) cannot null", configId);
        return client.getContent(path);
    }

}
