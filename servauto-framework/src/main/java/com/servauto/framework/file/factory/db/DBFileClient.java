package com.servauto.framework.file.factory.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.servauto.framework.file.dao.SystemFileContentMapper;
import com.servauto.framework.file.factory.AbstractFileClient;
import com.servauto.framework.file.model.SystemFileContent;

import java.util.Comparator;
import java.util.List;


public class DBFileClient extends AbstractFileClient<DBFileClientConfig> {

    private SystemFileContentMapper fileContentMapper;

    public DBFileClient(Long id, DBFileClientConfig config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        fileContentMapper = SpringUtil.getBean(SystemFileContentMapper.class);
    }

    @Override
    public String upload(byte[] content, String path, String type) {
        SystemFileContent contentDO = new SystemFileContent();
        contentDO.setConfigId(getId());
        contentDO.setPath(path);
        contentDO.setContent(content);
        fileContentMapper.insertSelective(contentDO);
        return super.formatFileUrl(config.getDomain(), path);
    }

    @Override
    public void delete(String path) {
        fileContentMapper.deleteByConfigIdAndPath(getId(), path);
    }

    @Override
    public byte[] getContent(String path) {
        List<SystemFileContent> list = fileContentMapper.selectListByConfigIdAndPath(getId(), path);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        list.sort(Comparator.comparing(SystemFileContent::getId));
        return CollUtil.getLast(list).getContent();
    }

}
