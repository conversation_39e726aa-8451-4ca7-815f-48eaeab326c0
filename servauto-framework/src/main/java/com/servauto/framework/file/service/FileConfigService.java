package com.servauto.framework.file.service;

import com.servauto.framework.file.factory.FileClient;
import com.servauto.framework.file.model.SystemFileConfig;

public interface FileConfigService {

    /**
     * Obtain the file configuration.
     *
     * @param id The ID.
     * @return The file configuration.
     */
    SystemFileConfig getFileConfig(Long id);

    /**
     * Obtain the file client with the specified ID.
     *
     * @param id The configuration ID.
     * @return The file client.
     */
    FileClient getFileClient(Long id);

    /**
     * Obtain the Master file client.
     *
     * @return The file client.
     */
    FileClient getMasterFileClient();

}
