package com.servauto.framework.file.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Schema(description = "SYS - Upload File Request VO")
@Data
public class FileUploadReqVO {

    @Schema(description = "File attachment", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "File attachment cannot be empty")
    private MultipartFile file;

    @Schema(description = "File attachment", example = "a.png")
    private String path;

}
