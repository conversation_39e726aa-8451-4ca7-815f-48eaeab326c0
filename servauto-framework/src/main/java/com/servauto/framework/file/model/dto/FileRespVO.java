package com.servauto.framework.file.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "File Response VO")
@Data
public class FileRespVO {

    @Schema(description = "File ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "Configuration ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11")
    private Long configId;

    @Schema(description = "File path", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton.jpg")
    private String path;

    @Schema(description = "Original file name", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton.jpg")
    private String name;

    @Schema(description = "File URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://127.0.0.1:8080/orton.jpg")
    private String url;

    @Schema(description = "File MIME type", example = "application/octet-stream")
    private String type;

    @Schema(description = "File size", example = "2048", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer size;

    @Schema(description = "Creation time", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
