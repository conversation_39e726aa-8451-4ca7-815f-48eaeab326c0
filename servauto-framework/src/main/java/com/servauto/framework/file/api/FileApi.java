package com.servauto.framework.file.api;

/**
 * File API
 */
public interface FileApi {

    /**
     * Save the file and return the access path of the file.
     *
     * @param content The content of the file.
     * @return The path of the file.
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * Save the file and return the access path of the file.
     *
     * @param path    The path of the file.
     * @param content The content of the file.
     * @return The path of the file.
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * Save the file and return the access path of the file.
     *
     * @param name    The name of the file.
     * @param path    The path of the file.
     * @param content The content of the file.
     * @return The path of the file.
     */
    String createFile(String name, String path, byte[] content);

    /**
     * Delete the file
     *
     * @param id id
     */
    void deleteFile(Long id) throws Exception;

    /**
     * Get file content
     *
     * @param path path
     * @return []byte
     */
    byte[] getFile(Long configId, String path) throws Exception;

    default byte[] getFile(String path) throws Exception {
        return getFile(0L, path);
    }
}
