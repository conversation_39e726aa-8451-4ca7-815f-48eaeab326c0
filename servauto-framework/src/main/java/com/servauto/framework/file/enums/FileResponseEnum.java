package com.servauto.framework.file.enums;

import com.servauto.common.core.domain.Resultable;

public enum FileResponseEnum implements Resultable {

    FILE_NOT_EXISTS("40001", "File not exists"),
    ;

    private final String code;

    private final String msg;

    FileResponseEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
