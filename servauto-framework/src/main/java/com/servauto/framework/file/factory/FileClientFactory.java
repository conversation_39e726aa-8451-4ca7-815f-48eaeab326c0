package com.servauto.framework.file.factory;

import com.servauto.framework.file.config.FileClientConfig;
import com.servauto.framework.file.enums.FileStorageEnum;

public interface FileClientFactory {

    /**
     * Get the file client.
     *
     * @param configId The configuration ID.
     * @return The file client.
     */
    FileClient getFileClient(Long configId);

    /**
     * Create or update the file client.
     *
     * @param configId The configuration ID.
     * @param storage  The enumeration of the storage device {@link FileStorageEnum}.
     * @param config   The file configuration.
     */
    <Config extends FileClientConfig> void createOrUpdateFileClient(Long configId, Integer storage, Config config);
}
