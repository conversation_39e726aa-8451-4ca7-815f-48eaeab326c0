package com.servauto.framework.file.config;

import com.servauto.framework.file.factory.FileClientFactory;
import com.servauto.framework.file.factory.FileClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * file configuration
 */
@Configuration(proxyBeanMethods = false)
public class FileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }


}
