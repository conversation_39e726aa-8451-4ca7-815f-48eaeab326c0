package com.servauto.framework.file.dao;

import com.servauto.framework.file.model.SystemFileContent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SystemFileContentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SystemFileContent record);

    int insertSelective(SystemFileContent record);

    SystemFileContent selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemFileContent record);

    int updateByPrimaryKeyWithBLOBs(SystemFileContent record);

    int updateByPrimaryKey(SystemFileContent record);

    void deleteByConfigIdAndPath(@Param("id") Long id, @Param("path") String path);

    List<SystemFileContent> selectListByConfigIdAndPath(@Param("id") Long id, @Param("path") String path);
}