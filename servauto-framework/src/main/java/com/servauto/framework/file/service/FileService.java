package com.servauto.framework.file.service;

import com.servauto.framework.file.model.dto.FileCreateReqVO;

public interface FileService {

    /**
     * Save the file and return the access path of the file.
     *
     * @param name    The name of the file.
     * @param path    The path of the file.
     * @param content The content of the file.
     * @return The path of the file.
     */
    String createFile(String name, String path, byte[] content);

    /**
     * Create a file.
     *
     * @param createReqVO The creation information.
     * @return The ID.
     */
    Long createFile(FileCreateReqVO createReqVO);

    /**
     * Delete a file.
     *
     * @param id The ID.
     */
    void deleteFile(Long id) throws Exception;

    /**
     * Get the content of the file.
     *
     * @param configId The configuration ID.
     * @param path     The path of the file.
     * @return The content of the file.
     */
    byte[] getFileContent(Long configId, String path) throws Exception;
}
