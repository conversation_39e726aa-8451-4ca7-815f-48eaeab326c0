package com.servauto.framework.file.enums;

import cn.hutool.core.util.ArrayUtil;
import com.servauto.framework.file.config.FileClientConfig;
import com.servauto.framework.file.factory.FileClient;
import com.servauto.framework.file.factory.db.DBFileClient;
import com.servauto.framework.file.factory.db.DBFileClientConfig;
import com.servauto.framework.file.factory.s3.S3FileClient;
import com.servauto.framework.file.factory.s3.S3FileClientConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件存储器枚举
 */
@AllArgsConstructor
@Getter
public enum FileStorageEnum {

    DB(1, DBFileClientConfig.class, DBFileClient.class),
    S3(20, S3FileClientConfig.class, S3FileClient.class),
    ;

    /**
     * 存储器
     */
    private final Integer storage;

    /**
     * 配置类
     */
    private final Class<? extends FileClientConfig> configClass;

    /**
     * 客户端类
     */
    private final Class<? extends FileClient> clientClass;

    public static FileStorageEnum getByStorage(Integer storage) {
        return ArrayUtil.firstMatch(o -> o.getStorage().equals(storage), values());
    }

}
