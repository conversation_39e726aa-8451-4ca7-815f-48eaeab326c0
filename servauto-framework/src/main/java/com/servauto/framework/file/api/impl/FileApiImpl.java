package com.servauto.framework.file.api.impl;

import com.servauto.framework.file.api.FileApi;
import com.servauto.framework.file.service.FileService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


@Service
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        fileService.deleteFile(id);
    }

    @Override
    public byte[] getFile(Long configId, String path) throws Exception {
        return fileService.getFileContent(configId, path);
    }
}
