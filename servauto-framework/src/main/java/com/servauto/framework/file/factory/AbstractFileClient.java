package com.servauto.framework.file.factory;

import cn.hutool.core.util.StrUtil;
import com.servauto.framework.file.config.FileClientConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractFileClient<Config extends FileClientConfig> implements FileClient {

    private final Long id;

    protected Config config;

    public AbstractFileClient(Long id, Config config) {
        this.id = id;
        this.config = config;
    }

    final void init() {
        doInit();
        log.debug("[init][configuration({}) Initialization complete]", config);
    }

    /**
     * 自定义初始化
     */
    protected abstract void doInit();

    public final void refresh(Config config) {
        if (config.equals(this.config)) {
            return;
        }
        log.info("[refresh][configuration({})Changes occur, reinitialize]", config);
        this.config = config;
        this.init();
    }

    protected String formatFileUrl(String domain, String path) {
        return StrUtil.format("{}/file/api/{}/get/{}", domain, getId(), path);
    }

    @Override
    public Long getId() {
        return id;
    }

}
