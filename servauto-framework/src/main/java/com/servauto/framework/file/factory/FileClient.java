package com.servauto.framework.file.factory;

public interface FileClient {

    /**
     * Get the client ID.
     *
     * @return The client ID.
     */
    Long getId();

    /**
     * Upload a file.
     *
     * @param content The file stream.
     * @param path    The relative path.
     * @param type    The file type.
     * @return The full path, i.e., the HTTP access address.
     * @throws Exception Throws an Exception when uploading the file.
     */
    String upload(byte[] content, String path, String type) throws Exception;

    /**
     * Delete a file.
     *
     * @param path The relative path.
     * @throws Exception Throws an Exception when deleting the file.
     */
    void delete(String path) throws Exception;

    /**
     * Get the content of the file.
     *
     * @param path The relative path.
     * @return The content of the file.
     * @throws Exception Throws an Exception when getting the file content.
     */
    byte[] getContent(String path) throws Exception;

}
