package com.servauto.framework.file.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "Create file Request VO")
@Data
public class FileCreateReqVO {

    @NotNull(message = "File configuration ID cannot be empty")
    @Schema(description = "File configuration ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11")
    private Long configId;

    @NotNull(message = "File path cannot be empty")
    @Schema(description = "File path", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton.jpg")
    private String path;

    @NotNull(message = "Original file name cannot be empty")
    @Schema(description = "Original file name", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton.jpg")
    private String name;

    @NotNull(message = "File URL cannot be empty")
    @Schema(description = "File URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://127.0.0.1:8080/orton.jpg")
    private String url;

    @Schema(description = "File MIME type", example = "application/octet-stream")
    private String type;

    @Schema(description = "File size", example = "2048", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer size;

}
