package com.servauto.framework.file.factory.s3;

import com.servauto.framework.file.config.FileClientConfig;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

/**
 * S3 client config
 */
@Data
public class S3FileClientConfig implements FileClientConfig {

    /**
     * 节点地址
     * 1. 阿里云：<a href="https://help.aliyun.com/document_detail/31837.html">...</a>
     */
    @NotNull(message = "endpoint 不能为空")
    private String endpoint;

    /**
     * 自定义域名
     * 1. 阿里云：<a href="https://help.aliyun.com/document_detail/31836.html">...</a>
     */
    @URL(message = "domain 必须是 URL 格式")
    private String domain;

    /**
     * 存储 Bucket
     */
    @NotNull(message = "bucket 不能为空")
    private String bucket;

    /**
     * 访问 Key
     * 阿里云：<a href="https://ram.console.aliyun.com/manage/ak">...</a>
     */
    @NotNull(message = "accessKey 不能为空")
    private String accessKey;

    /**
     * 访问 Secret
     */
    @NotNull(message = "accessSecret 不能为空")
    private String accessSecret;

}
