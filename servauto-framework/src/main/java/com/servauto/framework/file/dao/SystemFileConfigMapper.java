package com.servauto.framework.file.dao;

import com.servauto.framework.file.model.SystemFileConfig;

public interface SystemFileConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SystemFileConfig record);

    int insertSelective(SystemFileConfig record);

    SystemFileConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemFileConfig record);

    int updateByPrimaryKey(SystemFileConfig record);

    SystemFileConfig selectByMaster();

}