package com.servauto.framework.parse;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.servauto.common.utils.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

@AutoConfiguration(after = org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class)
@Slf4j
public class JacksonAutoConfiguration {

    @Bean
    @SuppressWarnings("InstantiationOfUtilityClass")
    public JacksonSerializer jacksonSerializer(List<ObjectMapper> objectMappers) {

        SimpleModule simpleModule = new SimpleModule();
        simpleModule
                // 新增 Long 类型序列化规则，数值超过 2^53-1，在 JS 会出现精度丢失问题，因此 Long 自动序列化为字符串类型
                .addSerializer(Long.class, ToStringSerializer.instance)
                .addSerializer(Long.TYPE, ToStringSerializer.instance)
                .addSerializer(LocalDate.class, LocalDateSerializer.INSTANCE)
                .addDeserializer(LocalDate.class, LocalDateDeserializer.INSTANCE)
                .addSerializer(LocalTime.class, LocalTimeSerializer.INSTANCE)
                .addDeserializer(LocalTime.class, LocalTimeDeserializer.INSTANCE)
                .addSerializer(LocalDateTime.class, JacksonAutoConfiguration.TimestampLocalDateTimeSerializer.INSTANCE)
                .addDeserializer(LocalDateTime.class, JacksonAutoConfiguration.TimestampLocalDateTimeDeserializer.INSTANCE)
                .addSerializer(BigDecimal.class, JacksonAutoConfiguration.BigDecimalSerializer.INSTANCE)
                .addDeserializer(BigDecimal.class, JacksonAutoConfiguration.BigDecimalDeserializer.INSTANCE);

        objectMappers.forEach(objectMapper -> objectMapper.registerModule(simpleModule));
        JacksonSerializer.init(CollUtil.getFirst(objectMappers));
        return new JacksonSerializer();
    }

    public static class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

        public static final BigDecimalSerializer INSTANCE = new BigDecimalSerializer();

        @Override
        public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeNumber(BigDecimalUtils.format(value));
        }
    }

    public static class BigDecimalDeserializer extends JsonDeserializer<BigDecimal> {

        public static final BigDecimalDeserializer INSTANCE = new BigDecimalDeserializer();

        @Override
        public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            return BigDecimalUtils.format(p.getDecimalValue());
        }
    }

    public static class TimestampLocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {

        public static final TimestampLocalDateTimeSerializer INSTANCE = new TimestampLocalDateTimeSerializer();

        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeNumber(value.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
    }

    public static class TimestampLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

        public static final TimestampLocalDateTimeDeserializer INSTANCE = new TimestampLocalDateTimeDeserializer();

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(p.getValueAsLong()), ZoneId.systemDefault());
        }

    }


}
