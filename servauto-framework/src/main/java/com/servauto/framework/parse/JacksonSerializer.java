package com.servauto.framework.parse;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.core.json.JsonReadFeature.*;

@Slf4j
public class JacksonSerializer {

    protected static ObjectMapper mapper = new ObjectMapper() {
        {
            disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
            disable(SerializationFeature.INDENT_OUTPUT);
            disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
            configure(JsonParser.Feature.ALLOW_COMMENTS, true);
            configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
            configure(ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER.mappedFeature(), true);
            configure(ALLOW_NON_NUMERIC_NUMBERS.mappedFeature(), true);
            configure(ALLOW_LEADING_ZEROS_FOR_NUMBERS.mappedFeature(), true);
            configure(ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
            configure(ALLOW_UNQUOTED_FIELD_NAMES.mappedFeature(), true);
            setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            registerModules(new JavaTimeModule()); // 解决 LocalDateTime 的序列化
        }
    };

    public static void init(ObjectMapper objectMapper) {
        JacksonSerializer.mapper = objectMapper;
    }


    public static String serialize(Object data) {
        if (data == null) {
            return null;
        }
        if (data instanceof String) {
            return (String) data;
        }
        try {
            return mapper.writeValueAsString(data);
        } catch (IOException e) {
            log.error("serialize data error", e);
            return null;
        }
    }

    public static <T> T deSerialize(String content, Class<T> clazz) {
        return deSerialize(content, clazz, null);
    }

    public static <T> T deSerialize(String content, TypeReference<T> t) {
        return deSerialize(content, null, t);
    }

    public static Map<String, String> jsonToMap(String jsonStr) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (Strings.isNullOrEmpty(jsonStr)) {
            return resultMap;
        }

        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        for (String key : jsonObject.keySet()) {
            Object o = jsonObject.get(key);
            if (o == null) {
                resultMap.put(key, null);
            } else {
                resultMap.put(key, o.toString());
            }
        }
        return resultMap;
    }

    public static <T> Map<String, T> jsonToTMap(String jsonStr) {
        Map<String, T> resultMap = Maps.newHashMap();
        if (Strings.isNullOrEmpty(jsonStr)) {
            return resultMap;
        }
        try {
            resultMap = mapper.readValue(jsonStr, Map.class); //json转换成map
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultMap;
    }


    public static void registerModule(SimpleModule module) {
        mapper.registerModule(module);
    }

    private static <T> T deSerialize(String content, Class<T> clazz, TypeReference<T> t) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        if (clazz == null && t == null) {
            return null;
        }

        try {
            if (clazz != null) {
                return mapper.readValue(content, clazz);
            } else {
                return mapper.readValue(content, t);
            }
        } catch (IOException e) {
            log.error("deserialize object error: {}", content, e);
            return null;
        }
    }

    public static <T> List<T> parseArray(String text, Class<T> clazz) {
        if (StrUtil.isEmpty(text)) {
            return new ArrayList<>();
        }
        try {
            return mapper.readValue(text, mapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("deserialize parse err,json:{}", text, e);
            return null;
        }
    }




}