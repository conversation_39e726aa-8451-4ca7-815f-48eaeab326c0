package com.servauto.framework.utils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.servauto.common.core.text.Convert;
import com.servauto.common.utils.StringUtils;
import com.servauto.common.utils.sql.SqlUtil;
import com.servauto.framework.utils.bean.BeanUtils;

import java.util.List;

public class PageSupport {

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNo";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 分页参数合理化
     */
    public static final String REASONABLE = "reasonable";

    public static void startPage(Integer pageNum, Integer pageSize) {

        Boolean reasonable = null;
        String orderByColumn = null;
        String orderly = null;

        if ("GET".equals(ServletUtils.getRequest().getMethod())) {
            reasonable = ServletUtils.getParameterToBool(REASONABLE);
            orderByColumn = ServletUtils.getParameter(ORDER_BY_COLUMN);
            orderly = StringUtils.isEmpty(ServletUtils.getParameter(IS_ASC)) ? "asc" : ServletUtils.getParameter(IS_ASC);
        }

        if (StringUtils.isNotEmpty(orderly)) {
            // 兼容前端排序类型
            if ("ascending".equals(orderly)) {
                orderly = "asc";
            } else if ("descending".equals(orderly)) {
                orderly = "desc";
            }
        }

        String orderBy = StringUtils.isNotEmpty(orderByColumn) ? SqlUtil.escapeOrderBySql(StringUtils.toUnderScoreCase(orderByColumn) + " " + orderly) : "";
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    public static void startPage() {
        Integer pageNum = 1;
        Integer pageSize = 1;
        if ("GET".equals(ServletUtils.getRequest().getMethod())) {
            pageNum = Convert.toInt(ServletUtils.getParameter(PAGE_NUM), 1);
            pageSize = Convert.toInt(ServletUtils.getParameter(PAGE_SIZE), 10);
        }
        startPage(pageNum, pageSize);
    }

    public static <R, T> PageInfo<T> copyProperties(PageInfo<R> pageInfo, List<T> data) {
        PageInfo<T> resultPage = new PageInfo<>(data);
        BeanUtils.copyProperties(pageInfo, resultPage,"list");
        resultPage.setList(data);
        return resultPage;
    }


}
