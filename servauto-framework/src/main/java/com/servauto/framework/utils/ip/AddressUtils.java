package com.servauto.framework.utils.ip;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取地址类
 *
 * <AUTHOR>
 */
@Slf4j
public class AddressUtils {

    // IP地址查询
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";

    // 未知地址
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIP(String ip) {
        // 内网不查询
        if (IpUtils.internalIp(ip)) {
            return "Intranet IP";
        }
//        if (ServAutoConfig.isAddressEnabled()) {
//            try {
//                String rspStr = HttpUtils.sendGet(IP_URL, "ip=" + ip + "&json=true", Constants.GBK);
//                if (StringUtils.isEmpty(rspStr)) {
//                    log.error("Abnormal geolocation acquisition {}", ip);
//                    return UNKNOWN;
//                }
//                JSONObject obj = JSON.parseObject(rspStr);
//                String region = obj.getString("pro");
//                String city = obj.getString("city");
//                return String.format("%s %s", region, city);
//            } catch (Exception e) {
//                log.error("Abnormal geolocation acquisition {}", ip);
//            }
//        }
        return UNKNOWN;
    }
}
