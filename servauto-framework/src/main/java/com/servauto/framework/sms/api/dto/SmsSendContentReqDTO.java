package com.servauto.framework.sms.api.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <p>SmsTextSendReqDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/28 11:16
 */
@Data
@Builder
public class SmsSendContentReqDTO {

    @NotEmpty(message = "Mobile phone number cannot be empty")
    private String mobile;

    @NotEmpty(message = "template code cannot null")
    private String templateCode;

    @NotEmpty(message = "Content cannot be empty")
    private Map<String, Object> templateParams;

    private Long userId;

    private Integer userType;
}
