package com.servauto.framework.sms.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SmsChannelEnum {

    YCloud_SMS("YCloud-SMS", "YCloud SMS"),
    YCloud_WHATSAPP("YCloud_WHATSAPP", "YCloud WHATSAPP"),
    ;

    private final String code;
    private final String name;

    public static SmsChannelEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }

    public static boolean isYunCloud(String code) {
        return YCloud_SMS.getCode().equals(code) || YCloud_WHATSAPP.getCode().equals(code);
    }

}

