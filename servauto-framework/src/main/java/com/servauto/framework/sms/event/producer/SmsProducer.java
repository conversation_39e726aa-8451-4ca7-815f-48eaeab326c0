package com.servauto.framework.sms.event.producer;

import com.servauto.framework.sms.event.dto.SmsSendMessage;
import com.servauto.framework.sms.model.dto.KeyValue;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class SmsProducer {

    @Resource
    private ApplicationContext applicationContext;

    public void sendSmsSendMessage(Long logId, String mobile,
                                   Long channelId, String apiTemplateId, String content, List<KeyValue<String, Object>> templateParams) {
        SmsSendMessage message = SmsSendMessage.builder()
                .logId(logId)
                .mobile(mobile)
                .content(content)
                .channelId(channelId)
                .apiTemplateId(apiTemplateId)
                .templateParams(templateParams)
                .build();
        applicationContext.publishEvent(message);
    }

}
