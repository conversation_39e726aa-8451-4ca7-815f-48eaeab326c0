package com.servauto.framework.sms.enums;

import com.servauto.common.core.domain.Resultable;

public enum SmsResponseEnum implements Resultable {

    SMS_CODE_NOT_FOUND("30001", "Verification code does not exist"),
    SMS_CODE_EXPIRED("30002", "Verification code has expired"),
    SMS_CODE_USED("30003", "Verification code has been used"),
    SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY("30004", "Exceed the daily number of SMS messages sent"),
    SMS_CODE_SEND_TOO_FAST("30005", "Too frequent text messages"),
    SMS_SEND_MOBILE_NOT_EXISTS("30006", "Mobile number does not exist"),
    SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS("30007", "Template parameter ({}) is missing"),
    SMS_SEND_TEMPLATE_NOT_EXISTS("30008", "The SMS template does not exist"),
    SMS_CHANNEL_NOT_EXISTS("30009", "The SMS channel does not exist"),
    SMS_CHANNEL_DISABLE("30010", "The SMS channel is not open and selection is not allowed"),
    SMS_CHANNEL_HAS_CHILDREN("30010", "Unable to delete, this SMS channel also has SMS templates"),
    ;

    private final String code;

    private final String msg;

    SmsResponseEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
