package com.servauto.framework.sms.factory;

import com.servauto.framework.sms.config.properties.SmsChannelProperties;

/**
 * 短信客户端的工厂接口
 */
public interface SmsClientFactory {

    /**
     * Get SMS Client
     *
     * @param channelId channelId
     * @return Client
     */
    SmsClient getSmsClient(Long channelId);

    /**
     * Get SMS Client
     *
     * @param channelCode channelCode
     * @return Client
     */
    SmsClient getSmsClient(String channelCode);

    /**
     * Create Client
     *
     * @param properties config
     * @return Client
     */
    SmsClient createOrUpdateSmsClient(SmsChannelProperties properties);

}
