package com.servauto.framework.sms.service;

import com.servauto.framework.sms.factory.SmsClient;
import com.servauto.framework.sms.model.SystemSmsChannel;

public interface SmsChannelService {

    /**
     * Get SMS channel
     *
     * @param id id
     * @return channel
     */
    SystemSmsChannel getSmsChannel(Long id);

    /**
     * Get SMS channel
     *
     * @param code code
     * @return channel
     */
    SystemSmsChannel getSmsChannel(String code);

    /**
     * Get SMS client
     *
     * @param id id
     * @return client
     */
    SmsClient getSmsClient(Long id);

    /**
     * Get SMS client
     *
     * @param code code
     * @return client
     */
    SmsClient getSmsClient(String code);

}
