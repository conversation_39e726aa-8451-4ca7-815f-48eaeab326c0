package com.servauto.framework.sms.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.sms.api.dto.SmsCodeSendReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeUseReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeValidateReqDTO;
import com.servauto.framework.sms.config.properties.SmsCodeProperties;
import com.servauto.framework.sms.dao.SystemSmsCodeMapper;
import com.servauto.framework.sms.enums.SmsResponseEnum;
import com.servauto.framework.sms.enums.SceneTemplateEnum;
import com.servauto.framework.sms.model.SystemSmsCode;
import com.servauto.framework.sms.service.SmsCodeService;
import com.servauto.framework.sms.service.SmsSendService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.randomInt;

@Slf4j
@Service
@Validated
public class SmsCodeServiceImpl implements SmsCodeService {

    @Resource
    private SmsCodeProperties smsCodeProperties;

    @Resource
    private SystemSmsCodeMapper systemSmsCodeMapper;

    @Resource
    private SmsSendService smsSendService;

    @Override
    public void sendSmsCode(SmsCodeSendReqDTO reqDTO) {
        SceneTemplateEnum sceneEnum = SceneTemplateEnum.getCodeByScene(reqDTO.getScene());
        Assert.notNull(sceneEnum, "SmsScene({})cannot find config", reqDTO.getScene());

        String code = createSmsCode(reqDTO.getMobile(), reqDTO.getScene(), reqDTO.getCreateIp());
        Long logId = smsSendService.sendSms(reqDTO.getMobile(), reqDTO.getUserId(), reqDTO.getUserType(), sceneEnum.getTemplateCode(), MapUtil.of("code", code));
        log.info("send sms code success mobile: [{}] , scene :[{}] logId: [{}]", reqDTO.getMobile(), sceneEnum.getDescription(), logId);
    }

    private String createSmsCode(String mobile, Integer scene, String ip) {
        SystemSmsCode lastSmsCode = systemSmsCodeMapper.selectLastByMobile(mobile, null, null);
        if (lastSmsCode != null) {

            // Today send frequency
            if (LocalDateTimeUtil.between(lastSmsCode.getCreateTime(), LocalDateTime.now()).toMillis()
                    < smsCodeProperties.getSendFrequency().toMillis()) {
                throw BusinessException.of(SmsResponseEnum.SMS_CODE_SEND_TOO_FAST);
            }

            // Today max quantity
            if (isToday(lastSmsCode.getCreateTime()) &&
                    lastSmsCode.getTodayIndex() >= smsCodeProperties.getSendMaximumQuantityPerDay()) {
                throw BusinessException.of(SmsResponseEnum.SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY);
            }
        }

        String code = String.format("%0" + smsCodeProperties.getEndCode().toString().length() + "d",
                randomInt(smsCodeProperties.getBeginCode(), smsCodeProperties.getEndCode() + 1));
        SystemSmsCode newSmsCode = SystemSmsCode.builder().mobile(mobile).code(code).scene(scene)
                .todayIndex(lastSmsCode != null && isToday(lastSmsCode.getCreateTime()) ? lastSmsCode.getTodayIndex() + 1 : 1)
                .createIp(ip).used(false).build();
        systemSmsCodeMapper.insertSelective(newSmsCode);
        return code;
    }

    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    @Override
    public void useSmsCode(SmsCodeUseReqDTO reqDTO) {
        SystemSmsCode lastSmsCode = validateSmsCode0(reqDTO.getMobile(), reqDTO.getCode(), reqDTO.getScene());

        systemSmsCodeMapper.updateByPrimaryKeySelective(SystemSmsCode.builder().id(lastSmsCode.getId())
                .used(true).usedTime(LocalDateTime.now()).usedIp(reqDTO.getUsedIp()).build());
    }

    @Override
    public void validateSmsCode(SmsCodeValidateReqDTO reqDTO) {
        validateSmsCode0(reqDTO.getMobile(), reqDTO.getCode(), reqDTO.getScene());
    }

    private SystemSmsCode validateSmsCode0(String mobile, String code, Integer scene) {
        SystemSmsCode lastSmsCode = systemSmsCodeMapper.selectLastByMobile(mobile, code, scene);
        if (lastSmsCode == null) {
            throw BusinessException.of(SmsResponseEnum.SMS_CODE_NOT_FOUND);
        }

        // is expired ?
        if (LocalDateTimeUtil.between(lastSmsCode.getCreateTime(), LocalDateTime.now()).toMillis()
                >= smsCodeProperties.getExpireTimes().toMillis()) {
            throw BusinessException.of(SmsResponseEnum.SMS_CODE_EXPIRED);
        }

        // is used ?
        if (Boolean.TRUE.equals(lastSmsCode.getUsed())) {
            throw BusinessException.of(SmsResponseEnum.SMS_CODE_USED);
        }
        return lastSmsCode;
    }

}
