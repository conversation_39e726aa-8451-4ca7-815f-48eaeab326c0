package com.servauto.framework.sms.dao;

import com.servauto.framework.sms.model.SystemSmsCode;
import org.apache.ibatis.annotations.Param;

public interface SystemSmsCodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SystemSmsCode record);

    int insertSelective(SystemSmsCode record);

    SystemSmsCode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemSmsCode record);

    int updateByPrimaryKey(SystemSmsCode record);

    SystemSmsCode selectLastByMobile(@Param("mobile") String mobile, @Param("code") String code, @Param("scene") Integer scene);
}