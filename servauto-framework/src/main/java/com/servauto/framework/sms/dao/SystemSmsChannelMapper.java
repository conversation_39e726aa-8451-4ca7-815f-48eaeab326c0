package com.servauto.framework.sms.dao;

import com.servauto.framework.sms.model.SystemSmsChannel;

public interface SystemSmsChannelMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SystemSmsChannel record);

    int insertSelective(SystemSmsChannel record);

    SystemSmsChannel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemSmsChannel record);

    int updateByPrimaryKey(SystemSmsChannel record);

    SystemSmsChannel selectByCode(String code);
}