package com.servauto.framework.sms.api.service.impl;

import com.servauto.framework.sms.api.dto.SmsCodeSendReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeUseReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeValidateReqDTO;
import com.servauto.framework.sms.api.service.SmsCodeApi;
import com.servauto.framework.sms.service.SmsCodeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class SmsCodeApiImpl implements SmsCodeApi {

    @Resource
    private SmsCodeService smsCodeService;

    @Override
    public void sendSmsCode(SmsCodeSendReqDTO reqDTO) {
        smsCodeService.sendSmsCode(reqDTO);
    }

    @Override
    public void useSmsCode(SmsCodeUseReqDTO reqDTO) {
        smsCodeService.useSmsCode(reqDTO);
    }

    @Override
    public void validateSmsCode(SmsCodeValidateReqDTO reqDTO) {
        smsCodeService.validateSmsCode(reqDTO);
    }

}
