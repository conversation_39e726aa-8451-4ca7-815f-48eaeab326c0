package com.servauto.framework.sms.service;

import com.servauto.framework.sms.model.SystemSmsTemplate;

import java.util.Map;


public interface SmsTemplateService {

    /**
     * Get SMS template
     *
     * @param id id
     * @return sms template
     */
    SystemSmsTemplate getSmsTemplate(Long id);

    /**
     * Get SMS template
     *
     * @param code id
     * @return sms template
     */
    SystemSmsTemplate getSmsTemplateByCode(String code);

    /**
     * format sms content
     *
     * @param content content
     * @param params  params
     * @return formatted content
     */
    String formatSmsTemplateContent(String content, Map<String, Object> params);

}
