package com.servauto.framework.sms.model;

import lombok.Builder;

import java.time.LocalDateTime;

@Builder
public class SystemSmsCode {
    private Long id;

    private String mobile;

    private String code;

    private String createIp;

    private Integer scene;

    private Integer todayIndex;

    private Boolean used;

    private LocalDateTime usedTime;

    private String usedIp;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getCreateIp() {
        return createIp;
    }

    public void setCreateIp(String createIp) {
        this.createIp = createIp == null ? null : createIp.trim();
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public Integer getTodayIndex() {
        return todayIndex;
    }

    public void setTodayIndex(Integer todayIndex) {
        this.todayIndex = todayIndex;
    }

    public Boolean getUsed() {
        return used;
    }

    public LocalDateTime getUsedTime() {
        return usedTime;
    }

    public void setUsedTime(LocalDateTime usedTime) {
        this.usedTime = usedTime;
    }

    public String getUsedIp() {
        return usedIp;
    }

    public void setUsedIp(String usedIp) {
        this.usedIp = usedIp == null ? null : usedIp.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }
}