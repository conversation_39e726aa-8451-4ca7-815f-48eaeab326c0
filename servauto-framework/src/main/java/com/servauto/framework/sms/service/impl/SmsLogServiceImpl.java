package com.servauto.framework.sms.service.impl;

import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.sms.dao.SystemSmsLogMapper;
import com.servauto.framework.sms.enums.SmsReceiveStatusEnum;
import com.servauto.framework.sms.enums.SmsSendStatusEnum;
import com.servauto.framework.sms.model.SystemSmsLog;
import com.servauto.framework.sms.model.SystemSmsTemplate;
import com.servauto.framework.sms.service.SmsLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
public class SmsLogServiceImpl implements SmsLogService {

    @Resource
    private SystemSmsLogMapper systemSmsLogMapper;

    @Override
    public SystemSmsLog getSmsLogById(Long id) {
        return systemSmsLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public Long createSmsLog(String mobile, Long userId, Integer userType, Boolean isSend,
                             SystemSmsTemplate template, String templateContent, Map<String, Object> templateParams) {

        SystemSmsLog.SystemSmsLogBuilder logBuilder = SystemSmsLog.builder();
        logBuilder.sendStatus(Objects.equals(isSend, true) ? SmsSendStatusEnum.INIT.getStatus() : SmsSendStatusEnum.IGNORE.getStatus());
        logBuilder.mobile(mobile).userId(userId).userType(userType);
        logBuilder.templateId(template.getId()).templateCode(template.getCode()).templateType(template.getType());
        logBuilder.templateContent(templateContent).templateParams(JacksonSerializer.serialize(templateParams)).apiTemplateId(template.getApiTemplateId());
        logBuilder.channelId(template.getChannelId()).channelCode(template.getChannelCode());
        logBuilder.receiveStatus(SmsReceiveStatusEnum.INIT.getStatus());

        SystemSmsLog logDO = logBuilder.build();
        systemSmsLogMapper.insertSelective(logDO);
        return logDO.getId();
    }

    @Override
    public void updateSmsSendResult(Long id, Boolean success,
                                    String apiSendCode, String apiSendMsg,
                                    String apiRequestId, String apiSerialNo) {
        SmsSendStatusEnum sendStatus = success ? SmsSendStatusEnum.SUCCESS : SmsSendStatusEnum.FAILURE;
        systemSmsLogMapper.updateByPrimaryKeySelective(SystemSmsLog.builder().id(id)
                .sendStatus(sendStatus.getStatus()).sendTime(LocalDateTime.now())
                .apiSendCode(apiSendCode).apiSendMsg(apiSendMsg)
                .apiRequestId(apiRequestId).apiSerialNo(apiSerialNo).build());
    }

    @Override
    public void updateSmsReceiveResult(Long id, Boolean success, LocalDateTime receiveTime,
                                       String apiReceiveCode, String apiReceiveMsg) {
        SmsReceiveStatusEnum receiveStatus = Objects.equals(success, true) ?
                SmsReceiveStatusEnum.SUCCESS : SmsReceiveStatusEnum.FAILURE;
        systemSmsLogMapper.updateByPrimaryKeySelective(SystemSmsLog.builder().id(id).receiveStatus(receiveStatus.getStatus())
                .receiveTime(receiveTime).apiReceiveCode(apiReceiveCode).apiReceiveMsg(apiReceiveMsg).build());
    }


}
