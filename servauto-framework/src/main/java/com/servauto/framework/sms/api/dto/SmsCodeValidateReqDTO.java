package com.servauto.framework.sms.api.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * 短信验证码的校验 Request DTO
 */
@Data
@Builder
public class SmsCodeValidateReqDTO {

    /**
     * Mobile phone number
     */
    @NotEmpty(message = "Mobile phone number cannot be empty")
    private String mobile;

    /**
     * Sending scenario
     */
    @NotNull(message = "Sending scenario cannot be null")
    private Integer scene;

    /**
     * Verification code
     */
    @NotEmpty(message = "Verification code cannot be empty")
    private String code;

}
