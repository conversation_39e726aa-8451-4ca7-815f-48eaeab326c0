package com.servauto.framework.sms.service;

import com.servauto.framework.sms.api.dto.SmsCodeSendReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeUseReqDTO;
import com.servauto.framework.sms.api.dto.SmsCodeValidateReqDTO;
import jakarta.validation.Valid;


public interface SmsCodeService {

    /**
     * Send SMS code
     *
     * @param reqDTO reqDTO
     */
    void sendSmsCode(@Valid SmsCodeSendReqDTO reqDTO);

    /**
     * Verify the SMS verification code and use it
     *
     * @param reqDTO reqDTO
     */
    void useSmsCode(@Valid SmsCodeUseReqDTO reqDTO);

    /**
     * Check if the verification code is valid
     *
     * @param reqDTO reqDTO
     */
    void validateSmsCode(@Valid SmsCodeValidateReqDTO reqDTO);

}
