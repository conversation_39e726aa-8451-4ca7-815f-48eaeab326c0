package com.servauto.framework.sms.factory.impl;

import cn.hutool.core.lang.Assert;
import com.google.common.base.Preconditions;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.sms.config.properties.SmsChannelProperties;
import com.servauto.framework.sms.model.dto.KeyValue;
import com.servauto.framework.sms.model.dto.SmsReceiveRespDTO;
import com.servauto.framework.sms.model.dto.SmsSendRespDTO;
import com.ycloud.client.ApiClient;
import com.ycloud.client.ApiException;
import com.ycloud.client.Configuration;
import com.ycloud.client.JSON;
import com.ycloud.client.api.SmsApi;
import com.ycloud.client.auth.ApiKeyAuth;
import com.ycloud.client.model.Event;
import com.ycloud.client.model.EventType;
import com.ycloud.client.model.Sms;
import com.ycloud.client.model.SmsSendRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Validated
@Slf4j
public class YunCloudSmsClient extends AbstractSmsClient {

    private final ApiClient apiClient;

    public YunCloudSmsClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiKey(), "apiKey cannot empty");
        apiClient = Configuration.getDefaultApiClient();
    }

    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String apiTemplateId, List<KeyValue<String, Object>> templateParams) throws Throwable {
        throw BusinessException.of("YCloud cannot support api template send sms");
    }

    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String content) throws Throwable {
        if (StringUtils.isBlank(content)) {
            throw BusinessException.of("send sms content is null logId:" + logId);
        }
        setYunCloudApiKey(apiClient);

        SmsSendRequest request = new SmsSendRequest();
        request.setTo(mobile);
        request.setText(content);
        request.setExternalId(logId.toString());
        if (StringUtils.isNotBlank(properties.getCallbackUrl())) {
            request.setCallbackUrl(properties.getCallbackUrl());
        }
        if (request.getTo().startsWith("+86")) {
            throw BusinessException.of("cannot support +86");
        }

        try {
            log.info("YunCloud send sms start {}", request);
            Sms sms = new SmsApi(apiClient).send(request);
            log.info("YunCloud send sms end {}", sms);
            return SmsSendRespDTO.builder()
                    .success(!Sms.StatusEnum.FAILED.equals(sms.getStatus()))
                    .serialNo(sms.getId())
                    .apiRequestId(sms.getExternalId())
                    .apiCode(sms.getErrorCode())
                    .apiMsg(sms.getErrorCode())
                    .build();
        } catch (ApiException e) {
            log.error("YunCloud send sms end exception ", e);
            return SmsSendRespDTO.builder()
                    .success(false)
                    .apiRequestId(logId.toString())
                    .apiCode(String.valueOf(e.getCode()))
                    .apiMsg(e.getResponseBody())
                    .build();
        }
    }

    @Override
    public boolean validateCallbackRequest(String signature, String body) {
        ApiKeyAuth api_key = (ApiKeyAuth) apiClient.getAuthentication("api_key");
        api_key.setApiKey(properties.getApiKey());
        return validateYunCloudCallbackRequest(apiClient, signature, body);
    }

    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable {
        Event result = JSON.deserialize(text, Event.class);
        Preconditions.checkNotNull(result);
        Preconditions.checkNotNull(result.getSms());
        Preconditions.checkArgument(EventType.SMS_MESSAGE_UPDATED.equals(result.getType()));
        Preconditions.checkNotNull(result.getSms().getCreateTime());
        Preconditions.checkNotNull(result.getSms().getExternalId());

        LocalDateTime receiveTime = LocalDateTime.ofInstant(result.getSms().getCreateTime().toInstant(), ZoneId.systemDefault());
        if (result.getSms().getUpdateTime() != null) {
            receiveTime = LocalDateTime.ofInstant(result.getSms().getUpdateTime().toInstant(), ZoneId.systemDefault());
        }

        Sms.StatusEnum messageStatus = result.getSms().getStatus();
        Preconditions.checkNotNull(messageStatus);

        SmsReceiveRespDTO.SmsReceiveRespDTOBuilder builder = SmsReceiveRespDTO.builder();
        switch (messageStatus) {
            case FAILED, UNDELIVERED ->
                    builder.success(false).errorCode(result.getSms().getErrorCode()).errorMsg(result.getSms().getErrorCode());
            case DELIVERED -> builder.success(true);
            default -> {
                return Lists.newArrayList();
            }
        }

        return List.of(builder.receiveTime(receiveTime)
                .serialNo(result.getId())
                .logId(Long.parseLong(result.getSms().getExternalId()))
                .build());
    }

}
