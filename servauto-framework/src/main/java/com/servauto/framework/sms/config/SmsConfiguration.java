package com.servauto.framework.sms.config;

import com.servauto.framework.sms.config.properties.SmsCodeProperties;
import com.servauto.framework.sms.factory.SmsClientFactory;
import com.servauto.framework.sms.factory.impl.SmsClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(prefix = "sms-code.enable", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(SmsCodeProperties.class)
public class SmsConfiguration {

    @Bean
    public SmsClientFactory smsClientFactory() {
        return new SmsClientFactoryImpl();
    }

}
