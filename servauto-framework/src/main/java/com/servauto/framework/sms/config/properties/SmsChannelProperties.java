package com.servauto.framework.sms.config.properties;

import com.servauto.framework.sms.enums.SmsChannelEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.springframework.validation.annotation.Validated;


@Data
@Builder
@Validated
public class SmsChannelProperties {

    /**
     * Channel ID
     */
    @NotNull(message = "SMS channel ID cannot be null")
    private Long id;

    /**
     * SMS signature
     */
    private String signature;

    /**
     * Channel code
     * Enum {@link SmsChannelEnum}
     */
    @NotEmpty(message = "Channel code cannot be empty")
    private String code;

    /**
     * Account for SMS API
     */
    @NotEmpty(message = "Account for SMS API cannot be empty")
    private String apiKey;

    /**
     * Secret key for SMS API
     */
    private String apiSecret;

    /**
     * Callback URL for SMS sending
     */
    private String callbackUrl;

    /**
     * remark
     */
    private String remark;

}
