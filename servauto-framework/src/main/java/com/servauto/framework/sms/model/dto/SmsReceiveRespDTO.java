package com.servauto.framework.sms.model.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Builder
public class SmsReceiveRespDTO {

    /**
     * Whether the reception is successful
     */
    private Boolean success;

    /**
     * The code of the API reception result
     */
    private String errorCode;

    /**
     * The description of the API reception result
     */
    private String errorMsg;

    /**
     * Mobile phone number
     */
    private String mobile;

    /**
     * User reception time
     */
    private LocalDateTime receiveTime;

    /**
     * The serial number returned by the SMS API after sending
     */
    private String serialNo;

    /**
     * SMS log number
     * <p>
     * Corresponds to the number of SysSmsLogDO
     */
    private Long logId;

}
