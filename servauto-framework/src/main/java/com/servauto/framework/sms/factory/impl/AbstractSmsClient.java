package com.servauto.framework.sms.factory.impl;

import com.google.common.collect.Maps;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.sms.config.properties.SmsChannelProperties;
import com.servauto.framework.sms.factory.SmsClient;
import com.ycloud.client.ApiClient;
import com.ycloud.client.api.WebhookEndpointsApi;
import com.ycloud.client.auth.ApiKeyAuth;
import com.ycloud.client.model.WebhookEndpoint;
import com.ycloud.client.model.WebhookEndpointPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Map;


@Slf4j
public abstract class AbstractSmsClient implements SmsClient {

    protected volatile SmsChannelProperties properties;

    public AbstractSmsClient(SmsChannelProperties properties) {
        this.properties = properties;
    }

    public final void init() {
        log.debug("[init][config({}) succeed]", properties);
    }

    public final void refresh(SmsChannelProperties properties) {
        if (properties.equals(this.properties)) {
            return;
        }
        this.properties = properties;
        this.init();
    }

    @Override
    public Long getId() {
        return properties.getId();
    }

    @Override
    public boolean validateCallbackRequest(String signature, String body) {
        return true;
    }

    protected void setYunCloudApiKey(ApiClient apiClient) {
        ApiKeyAuth api_key = (ApiKeyAuth) apiClient.getAuthentication("api_key");
        api_key.setApiKey(properties.getApiKey());
    }

    protected static boolean validateYunCloudCallbackRequest(ApiClient apiClient, String signature, String body) {
        String[] signatures = StringUtils.split(signature, ",");
        if (signatures == null || signatures.length != 2) {
            throw new IllegalArgumentException("YCloud-Signature is invalid");
        }

        Map<String, String> header = Maps.newHashMap();
        for (String st : signatures) {
            String[] kv = StringUtils.split(st, "=");
            header.put(kv[0], kv[1]);
        }

        WebhookEndpointsApi api = new WebhookEndpointsApi(apiClient);
        WebhookEndpointPage page;
        try {
            page = api.list().page(1).limit(10).includeTotal(false).execute();
        } catch (Exception e) {
            log.error("validate callback request error {}", signatures, e);
            return false;
        }

        if (CollectionUtils.isEmpty(page.getItems())) {
            return false;
        }

        WebhookEndpoint endpoint = page.getItems().stream().filter(e -> StringUtils.isNotBlank(e.getUrl()) && e.getUrl().contains("/sys/sms/callback/ycloud-sms")).findFirst().orElseThrow();

        HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, endpoint.getSecret());
        if (!header.get("s").equals(hmacUtils.hmacHex(header.get("t") + "." + body))) {
            log.error("validate signature failed signature = {} ,body = {} secret = {} ", signature, body, endpoint.getSecret());
            throw new IllegalArgumentException("YCloud-Signature is invalid");
        }

        return true;
    }


}
