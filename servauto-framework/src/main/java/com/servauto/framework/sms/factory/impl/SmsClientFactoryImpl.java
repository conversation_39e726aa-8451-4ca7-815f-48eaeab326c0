package com.servauto.framework.sms.factory.impl;

import com.servauto.framework.sms.config.properties.SmsChannelProperties;
import com.servauto.framework.sms.enums.SmsChannelEnum;
import com.servauto.framework.sms.factory.SmsClient;
import com.servauto.framework.sms.factory.SmsClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Validated
@Slf4j
public class SmsClientFactoryImpl implements SmsClientFactory {

    private final ConcurrentMap<Long, AbstractSmsClient> channelIdClients = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, AbstractSmsClient> channelCodeClients = new ConcurrentHashMap<>();

    public SmsClientFactoryImpl() {
        Arrays.stream(SmsChannelEnum.values()).forEach(channel -> {
            SmsChannelProperties properties = SmsChannelProperties.builder()
                    .code(channel.getCode()).apiKey("default").apiSecret("default").remark("default").build();
            AbstractSmsClient smsClient = createSmsClient(properties);
            channelCodeClients.put(channel.getCode(), smsClient);
        });
    }

    @Override
    public SmsClient getSmsClient(Long channelId) {
        return channelIdClients.get(channelId);
    }

    @Override
    public SmsClient getSmsClient(String channelCode) {
        return channelCodeClients.get(channelCode);
    }

    @Override
    public SmsClient createOrUpdateSmsClient(SmsChannelProperties properties) {
        AbstractSmsClient client = channelIdClients.get(properties.getId());
        if (client == null) {
            client = this.createSmsClient(properties);
            client.init();
            channelIdClients.put(client.getId(), client);
        } else {
            client.refresh(properties);
        }
        return client;
    }

    private AbstractSmsClient createSmsClient(SmsChannelProperties properties) {
        SmsChannelEnum channelEnum = SmsChannelEnum.getByCode(properties.getCode());
        Assert.notNull(channelEnum, String.format("Channel (%s) is null", properties));
        switch (channelEnum) {
            case YCloud_SMS:
                return new YunCloudSmsClient(properties);
            case YCloud_WHATSAPP:
                return new YunCloudWhatsappClient(properties);
            default:
        }
        log.error("[CreateSmsClient][Configuration({}) cannot find client]", properties);
        throw new IllegalArgumentException(String.format("Configuration(%s) cannot find client", properties));
    }
}
