package com.servauto.framework.sms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.sms.dao.SystemSmsTemplateMapper;
import com.servauto.framework.sms.enums.CommonStatusEnum;
import com.servauto.framework.sms.enums.SmsResponseEnum;
import com.servauto.framework.sms.model.SystemSmsChannel;
import com.servauto.framework.sms.model.SystemSmsTemplate;
import com.servauto.framework.sms.service.SmsChannelService;
import com.servauto.framework.sms.service.SmsTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
@Slf4j

public class SmsTemplateServiceImpl implements SmsTemplateService {

    @Resource
    private SystemSmsTemplateMapper systemSmsTemplateMapper;

    @Override
    public SystemSmsTemplate getSmsTemplate(Long id) {
        return systemSmsTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public SystemSmsTemplate getSmsTemplateByCode(String code) {
        return systemSmsTemplateMapper.selectByCode(code);
    }

    @Override
    public String formatSmsTemplateContent(String content, Map<String, Object> params) {
        return StrUtil.format(content, params);
    }


}
