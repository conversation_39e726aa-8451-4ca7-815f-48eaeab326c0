package com.servauto.framework.sms.api.service.impl;

import com.servauto.framework.sms.api.dto.SmsSendContentReqDTO;
import com.servauto.framework.sms.api.service.SmsSendApi;
import com.servauto.framework.sms.service.SmsSendService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>SmsContentApiImpl</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/28 11:17
 */
@Service
public class SmsSendApiImpl implements SmsSendApi {

    @Resource
    private SmsSendService smsSendService;

    @Override
    public void sendContent(SmsSendContentReqDTO reqDTO) {
        smsSendService.sendSms(reqDTO.getMobile(), reqDTO.getUserId(), reqDTO.getUserType(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams());
    }

}
