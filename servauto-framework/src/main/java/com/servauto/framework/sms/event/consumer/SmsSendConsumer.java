package com.servauto.framework.sms.event.consumer;

import com.servauto.framework.sms.event.dto.SmsSendMessage;
import com.servauto.framework.sms.service.SmsSendService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class SmsSendConsumer {

    @Resource
    private SmsSendService smsSendService;

    @EventListener
    @Async
    public void onMessage(SmsSendMessage message) {
        log.info("[onMessage][Message is({})]", message);
        smsSendService.doSendSms(message);
    }

}
