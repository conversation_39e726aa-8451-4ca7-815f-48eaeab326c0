package com.servauto.framework.sms.dao;

import com.servauto.framework.sms.model.SystemSmsTemplate;
import org.apache.ibatis.annotations.Param;

public interface SystemSmsTemplateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SystemSmsTemplate record);

    int insertSelective(SystemSmsTemplate record);

    SystemSmsTemplate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemSmsTemplate record);

    int updateByPrimaryKey(SystemSmsTemplate record);

    SystemSmsTemplate selectByCode(@Param("code") String code);
}