package com.servauto.framework.sms.service;

import com.servauto.framework.sms.event.dto.SmsSendMessage;

import java.util.Map;

public interface SmsSendService {

    /**
     * Send SMS
     *
     * @param mobile         Mobile phone number
     * @param userId         User number
     * @param userType       User type
     * @param templateCode   SMS template number
     * @param templateParams SMS template parameters
     * @return send result id: ID of the sending result
     */
    Long sendSms(String mobile, Long userId, Integer userType, String templateCode, Map<String, Object> templateParams);

    /**
     * Send SMS
     *
     * @param message message
     */
    void doSendSms(SmsSendMessage message);

    /**
     * receive SMS result status
     *
     * @param channelCode channelCode
     * @param header      header
     * @param text        text
     * @throws Throwable exception
     */
    void receiveSmsStatus(String channelCode, String header, String text) throws Throwable;

}
