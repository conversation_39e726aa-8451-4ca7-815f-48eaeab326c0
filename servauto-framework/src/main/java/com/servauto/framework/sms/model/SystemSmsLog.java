package com.servauto.framework.sms.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemSmsLog {
    private Long id;

    private Long channelId;

    private String channelCode;

    private Long templateId;

    private String templateCode;

    private Integer templateType;

    private String templateContent;

    private String templateParams;

    private String apiTemplateId;

    private String mobile;

    private Long userId;

    private Integer userType;

    private Integer sendStatus;

    private LocalDateTime sendTime;

    private String apiSendCode;

    private String apiSendMsg;

    private String apiRequestId;

    private String apiSerialNo;

    private Integer receiveStatus;

    private LocalDateTime receiveTime;

    private String apiReceiveCode;

    private String apiReceiveMsg;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode == null ? null : templateCode.trim();
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent == null ? null : templateContent.trim();
    }

    public String getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams == null ? null : templateParams.trim();
    }

    public String getApiTemplateId() {
        return apiTemplateId;
    }

    public void setApiTemplateId(String apiTemplateId) {
        this.apiTemplateId = apiTemplateId == null ? null : apiTemplateId.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public String getApiSendCode() {
        return apiSendCode;
    }

    public void setApiSendCode(String apiSendCode) {
        this.apiSendCode = apiSendCode == null ? null : apiSendCode.trim();
    }

    public String getApiSendMsg() {
        return apiSendMsg;
    }

    public void setApiSendMsg(String apiSendMsg) {
        this.apiSendMsg = apiSendMsg == null ? null : apiSendMsg.trim();
    }

    public String getApiRequestId() {
        return apiRequestId;
    }

    public void setApiRequestId(String apiRequestId) {
        this.apiRequestId = apiRequestId == null ? null : apiRequestId.trim();
    }

    public String getApiSerialNo() {
        return apiSerialNo;
    }

    public void setApiSerialNo(String apiSerialNo) {
        this.apiSerialNo = apiSerialNo == null ? null : apiSerialNo.trim();
    }

    public Integer getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(Integer receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getApiReceiveCode() {
        return apiReceiveCode;
    }

    public void setApiReceiveCode(String apiReceiveCode) {
        this.apiReceiveCode = apiReceiveCode == null ? null : apiReceiveCode.trim();
    }

    public String getApiReceiveMsg() {
        return apiReceiveMsg;
    }

    public void setApiReceiveMsg(String apiReceiveMsg) {
        this.apiReceiveMsg = apiReceiveMsg == null ? null : apiReceiveMsg.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}