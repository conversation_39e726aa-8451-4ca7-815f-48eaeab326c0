package com.servauto.framework.sms.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

@ConfigurationProperties(prefix = "sms-code")
@Validated
@Data
public class SmsCodeProperties {

    /**
     * enable
     */
    private Boolean enable;

    /**
     * Expiration time
     */
    private Duration expireTimes;

    /**
     * SMS sending frequency
     */
    private Duration sendFrequency;

    /**
     * Maximum daily sending quantity
     */
    private Integer sendMaximumQuantityPerDay;

    /**
     * Minimum value of verification code
     */
    private Integer beginCode;

    /**
     * Maximum value of verification code
     */
    private Integer endCode;
}
