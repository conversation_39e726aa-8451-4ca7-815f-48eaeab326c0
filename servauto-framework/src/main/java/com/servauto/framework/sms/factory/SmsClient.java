package com.servauto.framework.sms.factory;


import com.servauto.framework.sms.model.dto.KeyValue;
import com.servauto.framework.sms.model.dto.SmsReceiveRespDTO;
import com.servauto.framework.sms.model.dto.SmsSendRespDTO;

import java.util.List;

public interface SmsClient {

    /**
     * Get Channel id
     *
     * @return ChannelId
     */
    Long getId();

    /**
     * Send SMS
     *
     * @param logId          logId
     * @param mobile         mobile
     * @param apiTemplateId  apiTemplateId
     * @param templateParams templateParams
     * @return send sms result
     */
    SmsSendRespDTO sendSms(Long logId, String mobile, String apiTemplateId, List<KeyValue<String, Object>> templateParams) throws Throwable;

    /**
     * Send SMS
     *
     * @param logId   logId
     * @param mobile  mobile
     * @param content content
     * @return send sms result
     */
    SmsSendRespDTO sendSms(Long logId, String mobile, String content) throws Throwable;

    /**
     * validateCallbackRequest
     *
     * @param header callback request header
     * @return true if the request is valid, false otherwise
     */
    boolean validateCallbackRequest(String header, String body);

    /**
     * Parse the results of receiving SMS messages
     *
     * @param text results
     * @return results
     * @throws Throwable When an exception occurs while parsing text, an exception is thrown.
     */
    List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable;
}
