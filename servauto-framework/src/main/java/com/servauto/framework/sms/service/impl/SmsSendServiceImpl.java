package com.servauto.framework.sms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.sms.enums.CommonStatusEnum;
import com.servauto.framework.sms.enums.SmsChannelEnum;
import com.servauto.framework.sms.enums.SmsResponseEnum;
import com.servauto.framework.sms.event.dto.SmsSendMessage;
import com.servauto.framework.sms.event.producer.SmsProducer;
import com.servauto.framework.sms.factory.SmsClient;
import com.servauto.framework.sms.model.SystemSmsChannel;
import com.servauto.framework.sms.model.SystemSmsLog;
import com.servauto.framework.sms.model.SystemSmsTemplate;
import com.servauto.framework.sms.model.dto.KeyValue;
import com.servauto.framework.sms.model.dto.SmsReceiveRespDTO;
import com.servauto.framework.sms.model.dto.SmsSendRespDTO;
import com.servauto.framework.sms.service.SmsChannelService;
import com.servauto.framework.sms.service.SmsLogService;
import com.servauto.framework.sms.service.SmsSendService;
import com.servauto.framework.sms.service.SmsTemplateService;
import com.ycloud.client.JSON;
import com.ycloud.client.model.Event;
import com.ycloud.client.model.EventType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SmsSendServiceImpl implements SmsSendService {

    @Resource
    private SmsChannelService smsChannelService;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Resource
    private SmsLogService smsLogService;

    @Resource
    private SmsProducer smsProducer;

    @Override
    public Long sendSms(String mobile, Long userId, Integer userType, String templateCode, Map<String, Object> templateParams) {
        SystemSmsTemplate template = validateSmsTemplate(templateCode);
        SystemSmsChannel smsChannel = validateSmsChannel(template.getChannelId());

        mobile = validateMobile(mobile);

        List<KeyValue<String, Object>> newTemplateParams = buildTemplateParams(template, templateParams);
        String content = smsTemplateService.formatSmsTemplateContent(template.getContent(), templateParams);

        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus()) && CommonStatusEnum.ENABLE.getStatus().equals(smsChannel.getStatus());
        Long sendLogId = smsLogService.createSmsLog(mobile, userId, userType, isSend, template, content, templateParams);
        if (isSend) {
            smsProducer.sendSmsSendMessage(sendLogId, mobile, template.getChannelId(), template.getApiTemplateId(), content, newTemplateParams);
        }
        return sendLogId;
    }

    SystemSmsChannel validateSmsChannel(Long channelId) {
        SystemSmsChannel channel = smsChannelService.getSmsChannel(channelId);
        if (channel == null) {
            throw BusinessException.of(SmsResponseEnum.SMS_CHANNEL_NOT_EXISTS);
        }
        return channel;
    }

    SystemSmsTemplate validateSmsTemplate(String templateCode) {
        SystemSmsTemplate template = smsTemplateService.getSmsTemplateByCode(templateCode);
        if (template == null) {
            throw BusinessException.of(SmsResponseEnum.SMS_SEND_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    List<KeyValue<String, Object>> buildTemplateParams(SystemSmsTemplate template, Map<String, Object> templateParams) {
        List<String> list = JacksonSerializer.deSerialize(template.getParams(), new TypeReference<>() {
        });
        return list.stream().map(key -> {
            Object value = templateParams.get(key);
            if (value == null) {
                throw BusinessException.of(SmsResponseEnum.SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS);
            }
            return new KeyValue<>(key, value);
        }).collect(Collectors.toList());
    }

    public String validateMobile(String mobile) {
        if (StrUtil.isEmpty(mobile)) {
            throw BusinessException.of(SmsResponseEnum.SMS_SEND_MOBILE_NOT_EXISTS);
        }
        if (!mobile.startsWith("+")) {
            mobile = String.format("+%s", mobile);
        }
        return mobile;
    }

    @Override
    public void doSendSms(SmsSendMessage message) {
        SmsClient smsClient = smsChannelService.getSmsClient(message.getChannelId());
        Assert.notNull(smsClient, "SMS Client({}) does not exist", message.getChannelId());

        try {
            SmsSendRespDTO sendResponse;
            if (StringUtils.isNotBlank(message.getApiTemplateId())) {
                sendResponse = smsClient.sendSms(message.getLogId(), message.getMobile(), message.getApiTemplateId(), message.getTemplateParams());
            } else {
                sendResponse = smsClient.sendSms(message.getLogId(), message.getMobile(), message.getContent());
            }
            smsLogService.updateSmsSendResult(message.getLogId(), sendResponse.getSuccess(),
                    sendResponse.getApiCode(), sendResponse.getApiMsg(), sendResponse.getApiRequestId(), sendResponse.getSerialNo());
        } catch (Throwable ex) {
            log.error("[doSendSms][Send SMS failed，LogId({})]", message.getLogId(), ex);
            smsLogService.updateSmsSendResult(message.getLogId(), false,
                    "EXCEPTION", ExceptionUtil.getRootCauseMessage(ex), null, null);
        }
    }

    @Override
    public void receiveSmsStatus(String channelCode, String header, String body) throws Throwable {
        SmsClient smsClient = null;
        if (SmsChannelEnum.isYunCloud(channelCode)) {
            Event result = JSON.deserialize(body, Event.class);
            if (EventType.SMS_MESSAGE_UPDATED.equals(result.getType())) {
                smsClient = smsChannelService.getSmsClient(SmsChannelEnum.YCloud_SMS.getCode());
            } else if (EventType.WHATSAPP_MESSAGE_UPDATED.equals(result.getType())) {
                if (result.getWhatsappMessage() != null && StringUtils.isNotBlank(result.getWhatsappMessage().getExternalId())) {
                    SystemSmsLog systemSmsLog = smsLogService.getSmsLogById(Long.parseLong(result.getWhatsappMessage().getExternalId()));
                    if (systemSmsLog == null) {
                        return;
                    }
                    smsClient = smsChannelService.getSmsClient(systemSmsLog.getChannelCode());
                }
            }
        } else {
            smsClient = smsChannelService.getSmsClient(channelCode);
        }

        if (smsClient == null) {
            return;
        }

        if (StringUtils.isNotBlank(header)) {
            boolean isValid = smsClient.validateCallbackRequest(header, body);
            if (!isValid) {
                throw BusinessException.of("callback header is invalid");
            }
        }

        List<SmsReceiveRespDTO> receiveResults = smsClient.parseSmsReceiveStatus(body);
        if (CollUtil.isEmpty(receiveResults)) {
            return;
        }

        receiveResults.forEach(result -> smsLogService.updateSmsReceiveResult(result.getLogId(), result.getSuccess(), result.getReceiveTime(), result.getErrorCode(), result.getErrorMsg()));
    }

}
