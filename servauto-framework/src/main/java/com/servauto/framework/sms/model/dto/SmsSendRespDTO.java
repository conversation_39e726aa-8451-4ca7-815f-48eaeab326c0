package com.servauto.framework.sms.model.dto;

import lombok.Builder;
import lombok.Data;


@Builder
@Data
public class SmsSendRespDTO {

    /**
     * Whether it is successful
     */
    private Boolean success;

    /**
     * API request number
     */
    private String apiRequestId;

// ==================== Fields when successful ====================

    /**
     * Serial number returned by the SMS API after sending
     */
    private String serialNo;

// ==================== Fields when failed ====================

    /**
     * Error code returned by the API
     * <p>
     * Since the error code of the third - party may be a string, the String type is used.
     */
    private String apiCode;

    /**
     * Prompt returned by the API
     */
    private String apiMsg;

}
