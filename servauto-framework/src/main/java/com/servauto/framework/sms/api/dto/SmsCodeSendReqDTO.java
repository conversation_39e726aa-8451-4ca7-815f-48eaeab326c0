package com.servauto.framework.sms.api.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;


@Data
@Builder
public class SmsCodeSendReqDTO {

    /**
     * Mobile phone number
     */
    @NotEmpty(message = "Mobile phone number cannot be empty")
    private String mobile;

    /**
     * Sending scenario
     */
    @NotNull(message = "Sending scenario cannot be null")
    private Integer scene;

    /**
     * Sending IP
     */
    @NotEmpty(message = "Sending IP cannot be empty")
    private String createIp;

    /**
     * Sending userId
     */
    private Long userId;

    /**
     * Sending userType
     */
    private Integer userType;
}
