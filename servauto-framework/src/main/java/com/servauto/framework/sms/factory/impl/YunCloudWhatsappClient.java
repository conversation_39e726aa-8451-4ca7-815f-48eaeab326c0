package com.servauto.framework.sms.factory.impl;

import cn.hutool.core.lang.Assert;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.servauto.framework.sms.config.properties.SmsChannelProperties;
import com.servauto.framework.sms.model.dto.KeyValue;
import com.servauto.framework.sms.model.dto.SmsReceiveRespDTO;
import com.servauto.framework.sms.model.dto.SmsSendRespDTO;
import com.ycloud.client.ApiClient;
import com.ycloud.client.ApiException;
import com.ycloud.client.Configuration;
import com.ycloud.client.JSON;
import com.ycloud.client.api.WhatsappMessagesApi;
import com.ycloud.client.auth.ApiKeyAuth;
import com.ycloud.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

@Slf4j
public class YunCloudWhatsappClient extends AbstractSmsClient {

    private final ApiClient apiClient;

    /**
     * accepted: The messaging request is accepted by our system.
     * failed: A message sent by your business failed to send.
     * sent: A message sent by your business is in transit within WhatsApp's systems.
     * delivered: A message sent by your business was delivered to the user's device.
     * read: A message sent by your business was read by the user.
     */
    public YunCloudWhatsappClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiKey(), "apiKey cannot empty");
        Assert.notEmpty(properties.getRemark(), "remark cannot empty");
        apiClient = Configuration.getDefaultApiClient();
    }

    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String apiTemplateId, List<KeyValue<String, Object>> templateParams) throws Throwable {
        ApiKeyAuth api_key = (ApiKeyAuth) apiClient.getAuthentication("api_key");
        api_key.setApiKey(properties.getApiKey());

        WhatsappMessageTemplateLanguage language = new WhatsappMessageTemplateLanguage();
        language.setCode("en");
        language.setPolicy("deterministic");

        List<WhatsappMessageTemplateComponentParameter> parameters = Lists.newArrayList();
        Map<String, Object> newParams = convertMap(templateParams);
        newParams.forEach((k, v) -> {
            WhatsappMessageTemplateComponentParameter parameter = new WhatsappMessageTemplateComponentParameter();
            parameter.setText(v.toString());
            parameter.setType(WhatsappMessageTemplateComponentParameter.TypeEnum.TEXT);
            parameters.add(parameter);
        });

        WhatsappMessageTemplateComponent component = new WhatsappMessageTemplateComponent();
        component.setType(WhatsappMessageTemplateComponent.TypeEnum.BODY);
        component.setParameters(parameters);

        List<WhatsappMessageTemplateComponent> components = Lists.newArrayList();
        components.add(component);

        if (newParams.containsKey("code")) {
            WhatsappMessageTemplateComponent button = new WhatsappMessageTemplateComponent();
            button.setType(WhatsappMessageTemplateComponent.TypeEnum.BUTTON);
            button.setSubType(WhatsappMessageTemplateComponent.SubTypeEnum.URL);
            button.setIndex(0);
            button.setParameters(parameters);
            components.add(button);
        }

        WhatsappMessageTemplate template = new WhatsappMessageTemplate();
        template.setLanguage(language);
        template.setName(apiTemplateId);
        template.setComponents(components);

        WhatsappMessageSendRequest request = new WhatsappMessageSendRequest();
        request.setType(WhatsappMessageType.TEMPLATE);
        request.setTemplate(template);
        request.setFrom(properties.getRemark());
        request.setTo(mobile);
        request.setExternalId(logId.toString());
        WhatsappMessage message = new WhatsappMessagesApi(apiClient).send(request);

        return SmsSendRespDTO.builder()
                .success(!WhatsappMessageStatus.FAILED.equals(message.getStatus()))
                .serialNo(message.getId())
                .apiRequestId(message.getExternalId())
                .apiCode(message.getErrorCode())
                .apiMsg(message.getErrorMessage())
                .build();
    }

    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String content) throws Throwable {
        setYunCloudApiKey(apiClient);

        WhatsappMessageTemplateLanguage language = new WhatsappMessageTemplateLanguage();
        language.setCode("en");
        language.setPolicy("deterministic");

        WhatsappMessageText text = new WhatsappMessageText();
        text.setBody(content);

        WhatsappMessageSendRequest request = new WhatsappMessageSendRequest();
        request.setType(WhatsappMessageType.TEXT);
        request.setFrom(properties.getRemark());
        request.setTo(mobile);
        request.setText(text);
        request.setExternalId(logId.toString());

        try {
            log.info("YunCloud send whatsapp start {}", request);
            WhatsappMessage message = new WhatsappMessagesApi(apiClient).send(request);
            log.info("YunCloud send whatsapp end {}", message);
            return SmsSendRespDTO.builder()
                    .success(!WhatsappMessageStatus.FAILED.equals(message.getStatus()))
                    .serialNo(message.getId())
                    .apiRequestId(message.getExternalId())
                    .apiCode(message.getErrorCode())
                    .apiMsg(message.getErrorMessage())
                    .build();
        } catch (ApiException e) {
            log.error("YunCloud send whatsapp end exception", e);
            return SmsSendRespDTO.builder()
                    .success(false)
                    .apiRequestId(logId.toString())
                    .apiCode(String.valueOf(e.getCode()))
                    .apiMsg(e.getResponseBody())
                    .build();
        }
    }

    @Override
    public boolean validateCallbackRequest(String signature, String body) {
        ApiKeyAuth api_key = (ApiKeyAuth) apiClient.getAuthentication("api_key");
        api_key.setApiKey(properties.getApiKey());
        return validateYunCloudCallbackRequest(apiClient, signature, body);
    }

    public static <K, V> Map<K, V> convertMap(List<KeyValue<K, V>> keyValues) {
        Map<K, V> map = Maps.newLinkedHashMapWithExpectedSize(keyValues.size());
        keyValues.forEach(keyValue -> map.put(keyValue.getKey(), keyValue.getValue()));
        return map;
    }

    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable {

        Event result = JSON.deserialize(text, Event.class);
        Preconditions.checkNotNull(result);
        if (!EventType.WHATSAPP_MESSAGE_UPDATED.equals(result.getType())) {
            return Lists.newArrayList();
        }
        Preconditions.checkNotNull(result.getWhatsappMessage());
        Preconditions.checkNotNull(result.getWhatsappMessage().getCreateTime());
        Preconditions.checkNotNull(result.getWhatsappMessage().getExternalId());

        LocalDateTime receiveTime = LocalDateTime.ofInstant(result.getWhatsappMessage().getCreateTime().toInstant(), ZoneId.systemDefault());
        if (result.getWhatsappMessage().getSendTime() != null) {
            receiveTime = LocalDateTime.ofInstant(result.getWhatsappMessage().getSendTime().toInstant(), ZoneId.systemDefault());
        }

        WhatsappMessageStatus messageStatus = result.getWhatsappMessage().getStatus();
        Preconditions.checkNotNull(messageStatus);

        SmsReceiveRespDTO.SmsReceiveRespDTOBuilder builder = SmsReceiveRespDTO.builder();
        switch (messageStatus) {
            case FAILED -> builder.success(false).errorCode(result.getWhatsappMessage().getErrorCode())
                    .errorMsg(result.getWhatsappMessage().getErrorMessage());
            case DELIVERED, READ -> builder.success(true);
            default -> {
                return Lists.newArrayList();
            }
        }

        return List.of(builder.receiveTime(receiveTime)
                .serialNo(result.getId())
                .logId(Long.parseLong(result.getWhatsappMessage().getExternalId())).build());
    }


}
