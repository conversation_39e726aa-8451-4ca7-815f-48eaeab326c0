package com.servauto.framework.sms.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SceneTemplateEnum {

    CUSTOMER_LOGIN(1, "user-mobile-login", "mobile sms login"),
    CUSTOMER_WHATSAPP_LOGIN(2, "user-whatsapp-login", "mobile whatsapp login"),
    PAYMENT_COMPLETED(3, "payment-completed", "payment completed notice"),
    BOOK_SERVICE(4, "book-service", "book service notice"),
    RESCHEDULE(5, "reschedule", "reschedule notice"),
    ONE_DAY_ADVANCE_REMINDER(6, "1day-advance-reminder", "1day advance reminder notice"),
    TWO_HOUR_ADVANCE_REMINDER(7, "2hour-advance-reminder", "2hour advance reminder notice"),
    RESCHEDULE_CUSTOMER(8, "reschedule-customer", "reschedule customer notice"),;

    /**
     * Verification scenario number
     */
    private final Integer scene;

    /**
     * Template code
     */
    private final String templateCode;

    /**
     * Description
     */
    private final String description;

    public static SceneTemplateEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getScene().equals(scene), values());
    }

}
