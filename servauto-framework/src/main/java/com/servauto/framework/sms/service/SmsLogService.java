package com.servauto.framework.sms.service;


import com.servauto.framework.sms.model.SystemSmsLog;
import com.servauto.framework.sms.model.SystemSmsTemplate;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 短信日志 Service 接口
 */
public interface SmsLogService {

    /**
     * Get SMS log by id
     *
     * @param id id
     * @return SystemSmsLog
     */
    SystemSmsLog getSmsLogById(Long id);

    /**
     * Create SMS log
     *
     * @param mobile          mobile
     * @param userId          userId
     * @param userType        userType
     * @param isSend          isSend
     * @param template        template
     * @param templateContent templateContent
     * @param templateParams  templateParams
     * @return send result id
     */
    Long createSmsLog(String mobile, Long userId, Integer userType, Boolean isSend, SystemSmsTemplate template, String templateContent, Map<String, Object> templateParams);

    /**
     * Update log result
     *
     * @param id           id
     * @param success      success
     * @param apiSendCode  apiSendCode
     * @param apiSendMsg   apiSendMsg
     * @param apiRequestId apiRequestId
     * @param apiSerialNo  apiSerialNo
     */
    void updateSmsSendResult(Long id, Boolean success, String apiSendCode, String apiSendMsg, String apiRequestId, String apiSerialNo);

    /**
     * Update log result
     *
     * @param id             id
     * @param success        success
     * @param receiveTime    receiveTime
     * @param apiReceiveCode apiReceiveCode
     * @param apiReceiveMsg  apiReceiveMsg
     */
    void updateSmsReceiveResult(Long id, Boolean success, LocalDateTime receiveTime, String apiReceiveCode, String apiReceiveMsg);

}
