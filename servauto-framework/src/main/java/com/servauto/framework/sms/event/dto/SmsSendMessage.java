package com.servauto.framework.sms.event.dto;

import com.servauto.framework.sms.model.dto.KeyValue;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.util.List;


@Data
@Builder
public class SmsSendMessage {

    /**
     * SMS log number
     */
    @NotNull(message = "SMS log number cannot be null")
    private Long logId;

    /**
     * Mobile phone number
     */
    @NotNull(message = "Mobile phone number cannot be null")
    private String mobile;

    /**
     * SMS channel number
     */
    @NotNull(message = "SMS channel number cannot be null")
    private Long channelId;

    /**
     * Template number of the SMS API
     */
    @NotNull(message = "Template number of the SMS API cannot be null")
    private String apiTemplateId;

    /**
     * SMS template parameters
     */
    private List<KeyValue<String, Object>> templateParams;

    private String content;

}
