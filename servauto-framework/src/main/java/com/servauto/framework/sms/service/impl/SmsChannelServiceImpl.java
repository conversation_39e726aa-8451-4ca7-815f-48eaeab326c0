package com.servauto.framework.sms.service.impl;

import com.servauto.framework.sms.config.properties.SmsChannelProperties;
import com.servauto.framework.sms.dao.SystemSmsChannelMapper;
import com.servauto.framework.sms.factory.SmsClient;
import com.servauto.framework.sms.factory.SmsClientFactory;
import com.servauto.framework.sms.model.SystemSmsChannel;
import com.servauto.framework.sms.service.SmsChannelService;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class SmsChannelServiceImpl implements SmsChannelService {

    @Resource
    private SmsClientFactory smsClientFactory;

    @Resource
    private SystemSmsChannelMapper systemSmsChannelMapper;

    @Override
    public SystemSmsChannel getSmsChannel(Long id) {
        return systemSmsChannelMapper.selectByPrimaryKey(id);
    }

    @Override
    public SystemSmsChannel getSmsChannel(String code) {
        return systemSmsChannelMapper.selectByCode(code);
    }

    @Override
    public SmsClient getSmsClient(Long id) {
        SystemSmsChannel channel = systemSmsChannelMapper.selectByPrimaryKey(id);
        SmsChannelProperties properties = BeanUtils.toBean(channel, SmsChannelProperties.class);
        return smsClientFactory.createOrUpdateSmsClient(properties);
    }

    @Override
    public SmsClient getSmsClient(String code) {
        SystemSmsChannel channel = systemSmsChannelMapper.selectByCode(code);
        SmsChannelProperties properties = BeanUtils.toBean(channel, SmsChannelProperties.class);
        return smsClientFactory.createOrUpdateSmsClient(properties);
    }

}
