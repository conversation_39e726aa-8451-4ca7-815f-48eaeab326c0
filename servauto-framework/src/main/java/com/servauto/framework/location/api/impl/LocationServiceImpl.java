package com.servauto.framework.location.api.impl;

import com.servauto.common.utils.StringUtils;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.dao.AreaMapper;
import com.servauto.framework.location.dao.StateMapper;
import com.servauto.framework.location.model.Area;
import com.servauto.framework.location.model.State;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.utils.SpringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LocationServiceImpl implements LocationService {

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private StateMapper stateMapper;

    @Override
    @Cacheable(cacheNames = "LOCATIONS:AREAS", keyGenerator = "customizeKeyGenerator", unless = "#result == null")
    public List<AreaDTO> getAreas() {
        List<Area> areaList = areaMapper.selectAreas();
        return areaList.stream().map(e -> AreaDTO.builder()
                .code(e.getCode()).name(e.getName()).parentCode(e.getParentCode())
                .order(e.getOrder()).build()).collect(Collectors.toList());
    }

    @Override
    @Cacheable(cacheNames = "LOCATIONS:AREAS", key = "#code", unless = "#result == null")
    public AreaDTO getAreaByCode(String code) {
        return areaMapper.selectByCodes(List.of(code)).stream().map(e -> AreaDTO.builder()
                .code(e.getCode()).name(e.getName()).parentCode(e.getParentCode()).order(e.getOrder()).build()).findFirst().orElseThrow();
    }

    @Override
    public List<AreaDTO> getAreaByCodes(List<String> codes) {
        return getBean().getAreas().stream().filter(e -> codes.contains(e.getCode())).map(e -> AreaDTO.builder()
                .code(e.getCode()).name(e.getName()).parentCode(e.getParentCode()).order(e.getOrder()).build()).toList();
    }

    @Override
    @Cacheable(cacheNames = "LOCATIONS:AREAS:PARENTS", key = "#parentCode", unless = "#result == null")
    public List<AreaDTO> getAreasByParentCode(String parentCode) {
        if (StringUtils.isEmpty(parentCode)) {
            return Lists.newArrayList();
        }
        List<Area> areaList = areaMapper.selectByParentCode(parentCode);
        return areaList.stream().map(e -> AreaDTO.builder()
                .code(e.getCode()).name(e.getName()).parentCode(e.getParentCode()).order(e.getOrder()).build()).collect(Collectors.toList());
    }

    @Override
    @Cacheable(cacheNames = "LOCATIONS:STATES", keyGenerator = "customizeKeyGenerator", unless = "#result == null")
    public List<StateDTO> getStates() {
        List<State> stateList = stateMapper.selectStates();
        return stateList.stream().map(e -> StateDTO.builder()
                .code(e.getCode()).name(e.getName()).order(e.getOrder()).build()).collect(Collectors.toList());
    }

    @Override
    @Cacheable(cacheNames = "LOCATIONS:STATES", key = "#code", unless = "#result == null")
    public StateDTO getStateByCode(String code) {
        List<State> stateList = stateMapper.selectByCodes(List.of(code));
        return stateList.stream().map(e -> StateDTO.builder()
                .code(e.getCode()).name(e.getName()).order(e.getOrder()).build()).findFirst().orElseThrow();
    }

    @Override
    public List<StateDTO> getStateByCodes(List<String> codes) {
        List<StateDTO> states = getBean().getStates();
        return states.stream().filter(e -> codes.contains(e.getCode())).map(e -> StateDTO.builder()
                .code(e.getCode()).name(e.getName()).order(e.getOrder()).build()).toList();
    }

    @Override
    public Map<String, AreaDTO> getAreaMap() {
        return getBean().getAreas().stream().collect(Collectors.toMap(AreaDTO::getCode, k -> k, (v1, v2) -> v1));
    }

    @Override
    public Map<String, StateDTO> getStateMap() {
        return getBean().getStates().stream().collect(Collectors.toMap(StateDTO::getCode, k -> k, (v1, v2) -> v1));
    }

    private LocationService getBean() {
        return SpringUtils.getBean("locationServiceImpl");
    }

}
