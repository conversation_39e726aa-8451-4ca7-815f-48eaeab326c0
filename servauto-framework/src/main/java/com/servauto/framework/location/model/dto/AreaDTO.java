package com.servauto.framework.location.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Area - location Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AreaDTO {

    @Schema(description = "area code", example = "area code")
    private String code;

    @Schema(description = "area name", example = "area name")
    private String name;

    @Schema(description = "parent code", example = "parent code")
    private String parentCode;

    @Schema(description = "area order", example = "1")
    private Integer order;
}
