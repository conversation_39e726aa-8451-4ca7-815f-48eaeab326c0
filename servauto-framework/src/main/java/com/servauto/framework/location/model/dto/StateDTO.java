package com.servauto.framework.location.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "State - location Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StateDTO {

    @Schema(description = "state code", example = "state code")
    private String code;

    @Schema(description = "state name", example = "state name")
    private String name;

    @Schema(description = "state order", example = "state order")
    private Integer order;
}
