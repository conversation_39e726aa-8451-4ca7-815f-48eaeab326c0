package com.servauto.framework.location.api;


import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;

import java.util.List;
import java.util.Map;

public interface LocationService {

    /**
     * Get all areas
     *
     * @return List<AreaDTO>
     */
    List<AreaDTO> getAreas();

    /**
     * Get Area by  code
     *
     * @param code code
     * @return List<AreaDTO>
     */
    AreaDTO getAreaByCode(String code);

    /**
     * Get Area by  code
     *
     * @param codes code
     * @return List<AreaDTO>
     */
    List<AreaDTO> getAreaByCodes(List<String> codes);

    /**
     * Get Area by parent code
     *
     * @param parentCode parent code
     * @return List<AreaDTO>
     */
    List<AreaDTO> getAreasByParentCode(String parentCode);

    /**
     * Get states
     *
     * @return List<StateDTO>
     */
    List<StateDTO> getStates();

    /**
     * Get states
     *
     * @return List<StateDTO>
     */
    StateDTO getStateByCode(String code);

    /**
     * Get states
     *
     * @return List<StateDTO>
     */
    List<StateDTO> getStateByCodes(List<String> codes);

    Map<String, AreaDTO> getAreaMap();

    Map<String, StateDTO> getStateMap();
}
