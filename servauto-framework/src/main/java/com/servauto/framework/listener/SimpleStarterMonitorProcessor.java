package com.servauto.framework.listener;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>SimpleStarterMonitorProcessor</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22 17:07
 */
@Slf4j
//@Component
public class SimpleStarterMonitorProcessor implements BeanPostProcessor {

    private final Map<String, Long> timestamps = new ConcurrentHashMap<>();

    @Override
    public Object postProcessBeforeInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        timestamps.put(beanName, System.currentTimeMillis());
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        if (timestamps.containsKey(beanName)) {
            long cost = System.currentTimeMillis() - timestamps.get(beanName);
            if (cost > 100) {
                log.debug("********************* {} took {} ms *********************", beanName, cost);
            }
        }
        return bean;
    }
}
