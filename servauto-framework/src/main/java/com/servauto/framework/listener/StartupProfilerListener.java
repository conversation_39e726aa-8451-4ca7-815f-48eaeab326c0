package com.servauto.framework.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ConfigurableBootstrapContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringApplicationRunListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>StartupProfilerListener</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22 15:47
 */
@Slf4j
public class StartupProfilerListener implements SpringApplicationRunListener {

    private final Map<String, Long> timestamps = new ConcurrentHashMap<>();
    private final SpringApplication springApplication;
    private final String[] args;

    public StartupProfilerListener(SpringApplication app, String[] args) {
        this.springApplication = app;
        this.args = args;
    }

    @Override
    public void starting(ConfigurableBootstrapContext bootstrapContext) {
        start("spring.boot.application.starting");
    }

    @Override
    public void environmentPrepared(ConfigurableBootstrapContext bootstrapContext, ConfigurableEnvironment environment) {
        end("spring.boot.application.starting");
        start("spring.boot.application.environment-prepared");
    }

    @Override
    public void contextPrepared(ConfigurableApplicationContext context) {
        end("spring.boot.application.environment-prepared");
        start("spring.boot.application.context-prepared");
    }

    @Override
    public void contextLoaded(ConfigurableApplicationContext context) {
        end("spring.boot.application.context-prepared");
        start("spring.boot.application.context-loaded");
    }

    @Override
    public void started(ConfigurableApplicationContext context, Duration timeTaken) {
        end("spring.boot.application.context-loaded");
        start("spring.boot.application.started");
    }

    @Override
    public void ready(ConfigurableApplicationContext context, Duration timeTaken) {
        end("spring.boot.application.started");
        printPhase();
    }

    private void start(String phase) {
        timestamps.put(phase, System.nanoTime());
    }

    private void end(String phase) {
        Long start = timestamps.get(phase);
        if (start != null) {
            long duration = (System.nanoTime() - start) / 1_000_000;
            timestamps.put(phase + "_END", duration);
        }
    }

    private void printPhase() {
        timestamps.forEach((phase, time) -> {
            if (phase.endsWith("_END")) {
                String phaseMsg = String.format("Phase %-20s took %5d ms", phase.replace("_END", ""), time);
                log.debug(phaseMsg);
            }
        });
    }

}
