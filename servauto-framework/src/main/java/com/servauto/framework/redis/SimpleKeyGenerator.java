package com.servauto.framework.redis;

import java.lang.reflect.Method;

public class SimpleKeyGenerator extends org.springframework.cache.interceptor.SimpleKeyGenerator {

    public SimpleKeyGenerator() {
    }

    @Override
    public Object generate(Object target, Method method, Object... params) {
        if (params.length == 0) {
            return "ALL";
        }
        return super.generate(target, method, params);
    }
}
