package com.servauto.framework.redis;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.ArrayList;
import java.util.List;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@ConditionalOnClass(org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class)
@ConditionalOnBean(ObjectMapper.class)
@AutoConfiguration(before = RedissonAutoConfigurationV2.class)
public class RedisConfig {

    @Resource
    private RedisProperties redisProperties;

    @Bean
    @Primary
    public GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer() {
        GenericJackson2JsonRedisSerializer redisSerializer = new GenericJackson2JsonRedisSerializer();
        ObjectMapper objectMapper = (ObjectMapper) ReflectUtil.getFieldValue(redisSerializer, "mapper");
        objectMapper.registerModule(new JavaTimeModule());
        return redisSerializer;
    }

    @Bean
    @Qualifier("reactiveRedisConnectionFactory")
    public ReactiveRedisConnectionFactory reactiveRedisConnectionFactory() {
        return getLettuceConnectionFactory();
    }

    @Bean
    @Qualifier("redisConnectionFactory")
    @Primary
    public LettuceConnectionFactory redisConnectionFactory() {
        return getLettuceConnectionFactory();
    }

    @Bean
    public KeyGenerator customizeKeyGenerator() {
        return new SimpleKeyGenerator();
    }

    private LettuceConnectionFactory getLettuceConnectionFactory() {
        LettuceConnectionFactory lettuceConnectionFactory;
        if (redisProperties.getSentinel() != null && CollectionUtils.isNotEmpty(redisProperties.getSentinel().getNodes())) {
            lettuceConnectionFactory = new LettuceConnectionFactory(sentinelConfig(), getLettucePoolingClientConfiguration());
        } else {
            lettuceConnectionFactory = new LettuceConnectionFactory(standaloneConfig(), getLettucePoolingClientConfiguration());
        }
        return lettuceConnectionFactory;
    }

    private LettucePoolingClientConfiguration getLettucePoolingClientConfiguration() {
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder = LettucePoolingClientConfiguration.builder();
        GenericObjectPoolConfig<Object> genericObjectPoolConfig = new GenericObjectPoolConfig<>();
        genericObjectPoolConfig.setMaxTotal(redisProperties.getLettuce().getPool().getMaxActive());
        genericObjectPoolConfig.setMaxIdle(redisProperties.getLettuce().getPool().getMaxIdle());
        genericObjectPoolConfig.setMinIdle(redisProperties.getLettuce().getPool().getMinIdle());
        genericObjectPoolConfig.setMaxWait(redisProperties.getLettuce().getPool().getMaxWait());
        builder.poolConfig(genericObjectPoolConfig);
        return builder.build();
    }

    private RedisSentinelConfiguration sentinelConfig() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration();
        sentinelConfig.setMaster(redisProperties.getSentinel().getMaster());
        List<String> sentinels = redisProperties.getSentinel().getNodes();
        List<RedisNode> list = new ArrayList<>();
        for (String sentinel : sentinels) {
            String[] nodes = sentinel.split(":");
            list.add(new RedisNode(nodes[0], Integer.parseInt(nodes[1])));
        }
        sentinelConfig.setSentinels(list);
        sentinelConfig.setPassword(RedisPassword.of(redisProperties.getPassword()));
        return sentinelConfig;
    }

    private RedisStandaloneConfiguration standaloneConfig() {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration(
                redisProperties.getHost(), redisProperties.getPort());
        redisStandaloneConfiguration.setPassword(RedisPassword.of(redisProperties.getPassword()));
        redisStandaloneConfiguration.setDatabase(redisProperties.getDatabase());
        return redisStandaloneConfiguration;
    }

    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory, GenericJackson2JsonRedisSerializer serializer) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setKeySerializer(StringRedisSerializer.UTF_8);
        template.setHashKeySerializer(StringRedisSerializer.UTF_8);
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);
        template.setEnableTransactionSupport(true);
        template.setConnectionFactory(connectionFactory);
        template.afterPropertiesSet();
        return template;
    }

}
