package com.servauto.framework.lark;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TriggerMessageReq {
    private String orderId;
    private String orderType;
    private String carPlateNumber;
    private String serviceName;
    private String storeName;
    //yyyy-mm-dd hh：mm
    private String time;
    //yyyy-mm-dd hh：mm
    private String preferTime;



}
