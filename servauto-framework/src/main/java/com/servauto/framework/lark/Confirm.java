package com.servauto.framework.lark;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.servauto.common.constant.TriggerConstants;
import com.servauto.common.enums.TriggerType;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component("CONFIRM")
@Slf4j
public class Confirm extends AbstractMessageTriggerStrategy {

    @Value("${webHooks.confirm.url}")
    private String webHookUrl;

    @Value("${webHooks.confirm.secret}")
    private String botSecret;


    @PostConstruct
    public void init() {
        MessageTriggerStrategyFactory.registered(TriggerType.CONFIRM, this);
    }

    @Override
    protected String getDynamicContent() {
        return TriggerConstants.CONFIRM_NOTICE;
    }

    @Override
    protected String getSecret() {
        return botSecret;
    }

    @Override
    @Async("messageSendExecutor")
    public void sendMessage(String body) {

        try (HttpResponse response = HttpRequest.post(webHookUrl)
                .header("Content-Type", "application/json")
                .body(body)
                .execute()) {
            if (response.isOk()) {
                log.info("message send success,response:{}", response.body());

            } else {
                log.error("message send fail,errorCode:{},errorInfo:{},request:{}", response.getStatus(), response.body(), body);
            }
        }
    }
}
