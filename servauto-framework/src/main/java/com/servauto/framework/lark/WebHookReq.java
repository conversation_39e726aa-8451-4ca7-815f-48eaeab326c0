package com.servauto.framework.lark;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.servauto.framework.lark.interactive.CardType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class WebHookReq {

    private Long timestamp;
    private String sign;

    @JsonProperty("msg_type")
    private String msgType;
    private CardType card;

    private Map<String, Object> content;
    public void putContent(String key, Object value) {
        if (content == null) {
            content = new HashMap<>();
        }
        content.put(key, value);
    }
}
