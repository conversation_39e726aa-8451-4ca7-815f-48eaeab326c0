package com.servauto.framework.lark;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.servauto.common.constant.GlobalConstants;
import com.servauto.common.constant.TriggerConstants;
import com.servauto.framework.lark.interactive.CardType;
import com.servauto.framework.lark.interactive.Element;
import com.servauto.framework.lark.interactive.Text;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class AbstractMessageTriggerStrategy implements MessageTriggerStrategy {
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{.*?}");

    @Override
    public void doProcess(TriggerMessageReq triggerMessage){
        //生成消息body
        String body = builderContent(triggerMessage);
        sendMessage(body);
    }

    private String builderContent(TriggerMessageReq triggerMessage) {
        boolean hasReplace = true;
        List<Element> elements = new ArrayList<>();
        elements.add(getElement(getDynamicContent(), hasReplace, triggerMessage.getOrderType()));
        if (triggerMessage.getOrderId() != null) {
            elements.add(getElement(TriggerConstants.ORDER_ID_MESSAGE, hasReplace, triggerMessage.getOrderId()));
        }
        if (triggerMessage.getOrderType() != null) {
            elements.add(getElement(TriggerConstants.ORDER_TYPE_MESSAGE, hasReplace, triggerMessage.getOrderType()));
        }
        if (triggerMessage.getCarPlateNumber() != null) {
            elements.add(getElement(TriggerConstants.CAR_PLATE_NUMBER_MESSAGE, hasReplace, triggerMessage.getCarPlateNumber()));
        }
        if (triggerMessage.getServiceName() != null) {
            elements.add(getElement(TriggerConstants.SERVICE_NAME_MESSAGE, hasReplace, triggerMessage.getServiceName()));
        }
        if (triggerMessage.getStoreName() != null || triggerMessage.getTime() != null) {
            elements.add(getElement(TriggerConstants.APPOINTMENT_INFO_MESSAGE, false, null));
            elements.add(getElement(TriggerConstants.STORE_MESSAGE, hasReplace, triggerMessage.getStoreName()));
            elements.add(getElement(TriggerConstants.TIME_MESSAGE, hasReplace, triggerMessage.getTime()));
        }
        if (triggerMessage.getPreferTime() != null) {
            elements.add(getElement(TriggerConstants.FEEDBACK_INFO_MESSAGE, false, null));
            elements.add(getElement(TriggerConstants.WORKSHOP_PREFER_TIME_MESSAGE, hasReplace, triggerMessage.getPreferTime()));
        }
        try {
            WebHookReq webHookReq = new WebHookReq();
            webHookReq.setMsgType(GlobalConstants.INTERACTIVE);
            CardType cardType = new CardType();
            cardType.setElements(elements);
            long timestamp = System.currentTimeMillis() / 1000;
            String sign = generateSign(getSecret(), timestamp);
            webHookReq.setTimestamp(timestamp);
            webHookReq.setSign(sign);
            webHookReq.setCard(cardType);
            return new Jackson2ObjectMapperBuilder().build().writeValueAsString(webHookReq);
        } catch (JsonProcessingException | InvalidKeyException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

    }

    private String generateSign(String secret, Long timestamp) throws RuntimeException, NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" +secret;
        Mac mac = Mac.getInstance(GlobalConstants.H_MAC_SHA256);
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), GlobalConstants.H_MAC_SHA256));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    public static Element getElement(String content, boolean hasReplace, String replacement) {
        Element element = new Element();
        element.setTag(TriggerConstants.DIV);
        Text text = new Text();

        text.setTag(TriggerConstants.LARK_MD);
        text.setContent(hasReplace ? replacePlaceholders(content, replacement) : content);
        element.setText(text);
        return element;

    }

    public static String replacePlaceholders(String input, String replacement) {

        Matcher matcher = PLACEHOLDER_PATTERN.matcher(input);
        StringBuilder result = new StringBuilder();
        int lastIndex = 0;
        while (matcher.find()) {
            result.append(input, lastIndex, matcher.start());
            result.append(replacement);
            lastIndex = matcher.end();
        }
        result.append(input.substring(lastIndex));
        return result.toString();
    }

    protected abstract String getDynamicContent();

    protected abstract String getSecret();

    protected abstract void sendMessage(String body);

}
