package com.servauto.framework.lark;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.servauto.common.constant.TriggerConstants;
import com.servauto.common.enums.TriggerType;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component("ORDER_PAYMENT_COMPLETED")
@Slf4j
public class OrderPaymentCompleted extends AbstractMessageTriggerStrategy {
    @Value("${webHooks.completed.url}")
    private String webhookUrl;

    @Value("${webHooks.completed.secret}")
    private String botSecret;

    @Override
    protected String getDynamicContent() {
        return TriggerConstants.ORDER_PAYMENT_COMPLETED_NOTICE;
    }

    @Override
    protected String getSecret() {
        return botSecret;
    }

    @Override
    @Async("messageSendExecutor")
    public void sendMessage(String body) {
        try (HttpResponse response = HttpRequest.post(webhookUrl)
                .header("Content-Type", "application/json")
                .body(body)
                .execute()) {
            if (response.isOk()) {
                log.info("message send success,response:{}", response.body());

            } else {
                log.error("message send fail,errorCode:{},errorInfo:{},request:{}", response.getStatus(), response.body(), body);
            }
        }
    }

    @PostConstruct
    public void init() {
        MessageTriggerStrategyFactory.registered(TriggerType.ORDER_PAYMENT_COMPLETED, this);
    }
}
