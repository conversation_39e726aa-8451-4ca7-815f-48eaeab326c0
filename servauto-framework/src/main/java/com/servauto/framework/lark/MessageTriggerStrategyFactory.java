package com.servauto.framework.lark;

import com.servauto.common.enums.TriggerType;

import java.util.HashMap;
import java.util.Map;

public class MessageTriggerStrategyFactory {
    private static final Map<TriggerType, MessageTriggerStrategy> strategyMap = new HashMap<>();

    public static void registered(TriggerType eventType, MessageTriggerStrategy messageStrategy) {
        strategyMap.put(eventType, messageStrategy);
    }

    public static MessageTriggerStrategy getStrategy(TriggerType eventType) {
        if (strategyMap.containsKey(eventType)) {
            return strategyMap.get(eventType);
        }
        throw new IllegalArgumentException("Unsupported event types：" + eventType);
    }
}
