package com.servauto.framework.dictionary.service.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.common.constant.CacheConstants;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.dictionary.dao.SysDictDataMapper;
import com.servauto.framework.dictionary.dao.SysDictTypeMapper;
import com.servauto.framework.dictionary.model.SysDictData;
import com.servauto.framework.dictionary.model.SysDictType;
import com.servauto.framework.dictionary.service.SysDictionaryService;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.SpringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysDictionaryServiceImpl implements SysDictionaryService {

    @Resource
    private SysDictTypeMapper sysDictTypeMapper;


    @Resource
    private SysDictDataMapper sysDictDataMapper;


    @Override
    public PageInfo<SysDictType> selectDictTypeList(SysDictType dictType) {
        PageSupport.startPage();
        List<SysDictType> list = sysDictTypeMapper.selectDictTypeList(dictType);
        return PageInfo.of(list);
    }

    @Override
    public List<SysDictType> selectDictTypeAll() {
        return sysDictTypeMapper.selectDictTypeAll();
    }

    @Override
    @Cacheable(cacheNames = CacheConstants.SYS_DICT_KEY, key = "#dictType", unless = "#result == null")
    public List<SysDictData> selectDictDataByType(String dictType) {
        return sysDictDataMapper.selectDictDataByType(dictType);
    }

    @Override
    public SysDictType selectDictTypeById(Long dictId) {
        return sysDictTypeMapper.selectDictTypeById(dictId);
    }

    @Override
    public void deleteDictTypeByIds(Long[] dictIds) {
        for (Long dictId : dictIds) {
            SysDictType dictType = selectDictTypeById(dictId);
            if (sysDictDataMapper.countDictDataByType(dictType.getDictType()) > 0) {
                throw BusinessException.of(String.format("%1$s has been allocated and cannot be deleted", dictType.getDictName()));
            }
            sysDictTypeMapper.deleteDictTypeById(dictId);
            getBean().evictCache(dictType.getDictType());
        }
    }

    @Override
    public int insertDictType(SysDictType dict) {
        return sysDictTypeMapper.insertDictType(dict);
    }

    @Override
    @Transactional
    public void updateDictType(SysDictType dict) {
        SysDictType oldDict = sysDictTypeMapper.selectDictTypeById(dict.getDictId());
        int rows = sysDictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
        if (rows == 0) {
            throw BusinessException.of("update dict data error");
        }
        rows = sysDictTypeMapper.updateDictType(dict);
        if (rows == 0) {
            throw BusinessException.of("update dict type error");
        }
        getBean().evictCache(oldDict.getDictType());
    }

    @Override
    public boolean checkDictTypeUnique(SysDictType dict) {
        long dictId = StringUtils.isNull(dict.getDictId()) ? -1L : dict.getDictId();
        SysDictType dictType = sysDictTypeMapper.checkDictTypeUnique(dict.getDictType());
        return StringUtils.isNull(dictType) || dictType.getDictId() == dictId;
    }

    @Override
    public PageInfo<SysDictData> selectDictDataList(SysDictData dictData) {
        PageSupport.startPage();
        List<SysDictData> list = sysDictDataMapper.selectDictDataList(dictData);
        return PageInfo.of(list);
    }

    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return sysDictDataMapper.selectDictDataById(dictCode);
    }

    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            sysDictDataMapper.deleteDictDataById(dictCode);
            evictCache(dictCode);
        }
    }

    @Override
    public int insertDictData(SysDictData data) {
        return sysDictDataMapper.insertDictData(data);
    }

    @Override
    public int updateDictData(SysDictData data) {
        int rows = sysDictDataMapper.updateDictData(data);
        evictCache(data.getDictCode());
        return rows;
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.SYS_DICT_KEY, key = "#dictType")
    public void evictCache(String dictType) {
        log.info("evictCache {} succeed", dictType);
    }

    public void evictCache(Long dictCode) {
        SysDictData sysDictData = sysDictDataMapper.selectDictDataById(dictCode);
        if (sysDictData == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        getBean().evictCache(sysDictData.getDictType());
    }

    private SysDictionaryService getBean() {
        return SpringUtils.getBean("sysDictionaryServiceImpl");
    }
}
