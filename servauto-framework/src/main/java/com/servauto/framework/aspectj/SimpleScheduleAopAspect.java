package com.servauto.framework.aspectj;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <p>SimpleScheduleAopAspect</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/27 15:57
 */
@Aspect
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class SimpleScheduleAopAspect implements DisposableBean {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private Environment environment;

    private static final String SCHEDULE_LOCK_KEY = "schedule_lock_key";

    private volatile boolean locked = false;

    private RLock lock;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(1);

    @PostConstruct
    public void init() {
        String env = environment.getProperty("spring.profiles.active");
        String lockKey = String.format("%s:%s-%s:%s", SCHEDULE_LOCK_KEY,
                environment.getProperty("spring.application.name"), environment.getProperty("spring.profiles.active"), "master");
        RLock rLock = redissonClient.getLock(lockKey);

        executorService.submit(() -> {
            while (true) {
                try {
                    locked = rLock.tryLock(10, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    locked = false;
                }
                lock = locked ? rLock : null;
                if (locked) {
                    if (log.isDebugEnabled()) {
                        log.debug("Current environment is {} get schedule_lock succeed", env);
                    }
                    break;
                }
            }
        });
    }

    @Pointcut("@annotation(org.springframework.scheduling.annotation.Scheduled) || @within(org.springframework.scheduling.annotation.Scheduled)")
    public void dsPointCut() {

    }

    @Around("dsPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        return locked ? point.proceed() : null;
    }

    @Override
    public void destroy() {
        if (log.isDebugEnabled()) {
            log.debug("schedule aop aspect closing");
        }

        if (locked && lock != null) {
            lock.forceUnlock();
            locked = false;
            if (log.isDebugEnabled()) {
                log.debug("schedule aop aspect closed");
            }
        }

        if (!executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }

    }
}
