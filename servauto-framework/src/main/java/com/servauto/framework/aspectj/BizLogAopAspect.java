package com.servauto.framework.aspectj;

import com.servauto.common.annotation.BizAopLog;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.lang.reflect.Method;

@Aspect
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class BizLogAopAspect {

    @Pointcut("execution(* com..*.controller..*.*(..))")
    public void logPointCut() {
    }

    @Around(value = "logPointCut()")
    public Object log(ProceedingJoinPoint joinPoint) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        String methodName = method.getName();
        String clazz = method.getDeclaringClass().getSimpleName();
        Object[] joinPointArgs = joinPoint.getArgs();
        Object[] args = new Object[joinPointArgs.length];
        for (int i = 0; i < joinPointArgs.length; i++) {
            if (!(joinPointArgs[i] instanceof HttpServletRequest || joinPointArgs[i] instanceof HttpServletResponse)) {
                args[i] = joinPointArgs[i];
            }
        }

        BizAopLog aopLog = method.getAnnotation(BizAopLog.class);
        aopLog = aopLog == null ? method.getDeclaringClass().getAnnotation(BizAopLog.class) : aopLog;

        //默认开启
        boolean isPrint = aopLog == null || aopLog.isPrint();
        //默认开启
        boolean isPrintArgs = aopLog == null || aopLog.isPrintArgs();
        //默认关闭
        boolean isPrintResp = aopLog != null && aopLog.isPrintResp();
        //默认开启
        boolean isPrintExc = aopLog == null || aopLog.isPrintExc();

        String desc = aopLog != null ? aopLog.value() : "";
        String url = ServletUtils.getRequest().getRequestURI();

        if ((isPrint && isPrintArgs) && log.isInfoEnabled()) {
            log.info("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--begin, [args:{}]", url, clazz, methodName, desc, JacksonSerializer.serialize(args));
        }

        if (isPrint && !isPrintArgs && log.isInfoEnabled()) {
            log.info("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--begin", url, clazz, methodName, desc);
        }

        try {
            Object result = joinPoint.proceed();

            if ((isPrint && isPrintResp) && log.isInfoEnabled()) {
                log.info("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--end, [cost:{}ms], [result:{}]",
                        url, clazz, methodName, desc, stopWatch.getDuration().toMillis(), JacksonSerializer.serialize(result));
            }

            if ((isPrint && !isPrintResp) && log.isInfoEnabled()) {
                log.info("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--end, [cost:{}ms]",
                        url, clazz, methodName, desc, stopWatch.getDuration().toMillis());
            }

            return result;
        } catch (BusinessException e) {
            if (isPrint && log.isInfoEnabled()) {
                log.info("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--business exception, [cost:{}ms][errCode:{}][errMsg:{}][errData:{}]",
                        url, clazz, methodName, desc, stopWatch.getDuration().toMillis(), e.getCode(), e.getMessage(), e.getData());
            }

            throw e;
        } catch (Throwable e) {
            if ((isPrint && isPrintExc) && log.isInfoEnabled()) {
                log.error("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--exception, [cost:{}ms]", url, clazz, methodName, desc, stopWatch.getDuration().toMillis(), e);
            }

            if ((isPrint && !isPrintExc) && log.isInfoEnabled()) {
                log.error("[monitor]-[url:{}][class:{}][methodName:{}][desc:{}]--exception, [cost:{}ms][errMsg:{}]", url, clazz, methodName, desc, stopWatch.getDuration().toMillis(), e.getMessage());
            }
            throw e;
        }
    }

}
