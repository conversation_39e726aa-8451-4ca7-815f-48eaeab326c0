package com.servauto.framework.gemini;

import com.google.genai.Client;
import com.google.genai.types.GenerateContentResponse;

public class GeminiTest {

    // 1. 在这里配置你的 API Key
    private static final String API_KEY = "AIzaSyB6HW9dMHNaPqR_8-kV_ycfmuZJ1ajjttQ"; // 替换为你的实际 API Key

    // 2. 在这里配置你的提示词
    private static final String PROMPT =
            "Instruction for PDF Data Extraction\n" +
            "\n" +
            "Please extract the following fields from all invoices in the PDF(s):\n" +
            "\n" +
            "1. Shop Name\n" +
            "2. Number Plate → Remove all spaces (e.g., \"ABC 1234\" becomes \"ABC1234\")\n" +
            "3. Invoice Number\n" +
            "4. Amount\n" +
            "\n" +
            "Important:\n" +
            "- Check each invoice individually\n" +
            "- Ensure no invoices are skipped\n" +
            "- Present the data in table format\n" +
            "\n" +
            "Cross check between both AI to see which is more accurate or any missed out";

    public static void main(String[] args) {
        try {
            // 检查环境变量

            Client client = Client.builder().apiKey(API_KEY).build();

            // 调用 Gemini API
            System.out.println("正在调用 Gemini API...");
            GenerateContentResponse response = client.models.generateContent(
                    "gemini-2.5-flash",
                    PROMPT,
                    null
            );

            // 输出结果
            System.out.println("\n=== Gemini 响应结果 ===");
            System.out.println(response.text());

        } catch (Exception e) {
            System.err.println("调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
