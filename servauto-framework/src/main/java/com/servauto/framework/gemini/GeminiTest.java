package com.servauto.framework.gemini;

import com.google.genai.Client;
import com.google.genai.types.GenerateContentResponse;

public class GeminiTest {
    public static void main(String[] args) {
        // The client gets the API key from the environment variable `GEMINI_API_KEY`.
        Client client = new Client();

        GenerateContentResponse response =
                client.models.generateContent(
                        "gemini-2.5-flash",
                        "\n" +
                                "Instruction for PDF Data Extraction\n" +
                                "\n" +
                                "Please extract the following fields from all invoices in the PDF(s):\n" +
                                "\n" +
                                "Shop Name\n" +
                                "\n" +
                                "Number Plate → Remove all spaces (e.g., \"ABC 1234\" becomes \"ABC1234\")\n" +
                                "\n" +
                                "Invoice Number\n" +
                                "\n" +
                                "Amount\n" +
                                "\n" +
                                "Important:\n" +
                                "\n" +
                                "Check each invoice individually\n" +
                                "\n" +
                                "Ensure no invoices are skipped\n" +
                                "\n" +
                                "Present the data in table format\n" +
                                "\n" +
                                "\n" +
                                "\n" +
                                "Cross check between both AI to see which is more accurate or any missed out",
                        null);

        System.out.println(response.text());
    }
}
