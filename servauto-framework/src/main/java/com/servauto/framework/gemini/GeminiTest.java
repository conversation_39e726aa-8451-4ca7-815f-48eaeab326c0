package com.servauto.framework.gemini;

import com.google.genai.Client;
import com.google.genai.types.GenerateContentResponse;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class GeminiTest {

    // 配置区域 - 请根据实际情况修改
    private static final String API_KEY = "your-gemini-api-key-here"; // 替换为你的 API Key
    private static final String PDF_FILE_PATH = "/path/to/your/invoice.pdf"; // 替换为你的 PDF 文件路径
    private static final String MODEL_NAME = "gemini-2.5-flash"; // 或者使用 "gemini-2.5-pro"

    public static void main(String[] args) {
        try {
            // 创建客户端
            Client client = createClientWithApiKey();

            // 如果提供了命令行参数，使用参数中的文件路径
            if (args.length > 0) {
                String pdfPath = args[0];
                System.out.println("使用命令行参数指定的文件: " + pdfPath);
                extractDataFromPdf(client, pdfPath);
            } else {
                // 否则进行文本测试
                System.out.println("未提供PDF文件路径，进行文本测试...");
                testTextOnly(client);
            }

        } catch (Exception e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建 Gemini 客户端
     */
    private static Client createClientWithApiKey() {
        // 方式1: 从环境变量获取 API Key
        String apiKey = System.getenv("GEMINI_API_KEY");

        // 方式2: 如果环境变量没有设置，使用代码中配置的 API Key
        if (apiKey == null || apiKey.isEmpty()) {
            apiKey = API_KEY;
            System.out.println("使用代码中配置的 API Key");
        } else {
            System.out.println("使用环境变量中的 API Key");
        }

        // 验证 API Key
        if (apiKey == null || apiKey.isEmpty() || "your-gemini-api-key-here".equals(apiKey)) {
            throw new IllegalArgumentException("请设置有效的 GEMINI_API_KEY 环境变量或在代码中配置 API_KEY");
        }

        return new Client(apiKey);
    }

    /**
     * 从 PDF 文件提取发票数据
     * 注意：当前版本的 google-genai 1.0.0 可能不支持文件上传，这个方法仅作为示例
     */
    private static void extractDataFromPdf(Client client, String pdfFilePath) throws IOException {
        System.out.println("开始处理 PDF 文件: " + pdfFilePath);

        // 检查文件是否存在
        Path path = Paths.get(pdfFilePath);
        if (!Files.exists(path)) {
            throw new IllegalArgumentException("PDF 文件不存在: " + pdfFilePath);
        }

        System.out.println("注意：当前版本的 google-genai 1.0.0 可能不支持直接上传PDF文件");
        System.out.println("建议使用 Google AI Studio 或 REST API 进行文件处理");

        // 进行文本提示测试
        String prompt = buildExtractionPrompt();
        System.out.println("使用文本提示进行测试...");

        GenerateContentResponse response = client.models.generateContent(MODEL_NAME, prompt, null);

        System.out.println("\n=== 提示词测试结果 ===");
        System.out.println(response.text());
    }

    /**
     * 构建数据提取的提示词
     */
    private static String buildExtractionPrompt() {
        return """
                Instruction for PDF Data Extraction

                Please extract the following fields from all invoices in the PDF(s):

                1. Shop Name
                2. Number Plate → Remove all spaces (e.g., "ABC 1234" becomes "ABC1234")
                3. Invoice Number
                4. Amount

                Important Requirements:
                - Check each invoice individually
                - Ensure no invoices are skipped
                - Present the data in table format with clear headers
                - If multiple invoices exist, number them clearly
                - Include currency symbols for amounts
                - If any field is not found, mark as "N/A"

                Output Format:
                | Invoice # | Shop Name | Number Plate | Invoice Number | Amount |
                |-----------|-----------|--------------|----------------|--------|
                | 1         | ...       | ...          | ...            | ...    |

                Additional Notes:
                - Double-check all extracted information for accuracy
                - If text is unclear or partially visible, indicate with [UNCLEAR] tag
                - Provide a summary count of total invoices processed
                """;
    }

    /**
     * 保存结果到文件
     */
    private static void saveResultToFile(String result, String originalPdfPath) {
        try {
            String outputFileName = originalPdfPath.replaceAll("\\.[^.]+$", "_extracted_data.txt");
            Path outputPath = Paths.get(outputFileName);
            Files.write(outputPath, result.getBytes());
            System.out.println("\n结果已保存到: " + outputFileName);
        } catch (IOException e) {
            System.err.println("保存结果文件时出错: " + e.getMessage());
        }
    }

    /**
     * 仅文本模式测试（不需要 PDF 文件）
     */
    public static void testTextOnly() {
        try {
            Client client = createClientWithApiKey();

            String testPrompt = "请解释一下什么是发票数据提取，以及在处理PDF发票时需要注意哪些要点？";

            GenerateContentResponse response = client.models.generateContent(MODEL_NAME, testPrompt);

            System.out.println("=== 文本测试结果 ===");
            System.out.println(response.text());

        } catch (Exception e) {
            System.err.println("文本测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
