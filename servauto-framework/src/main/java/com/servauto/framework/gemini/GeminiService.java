package com.servauto.framework.gemini;

import com.google.genai.Client;
import com.google.genai.types.Content;
import com.google.genai.types.GenerateContentResponse;
import com.google.genai.types.Part;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;

/**
 * Gemini API 服务类
 * 
 * <AUTHOR>
 */
@Service
public class GeminiService {
    
    private static final Logger logger = LoggerFactory.getLogger(GeminiService.class);
    
    @Autowired
    private GeminiConfig geminiConfig;
    
    private Client client;
    
    /**
     * 获取 Gemini 客户端实例
     */
    private Client getClient() {
        if (client == null) {
            String apiKey = getApiKey();
            if (apiKey == null || apiKey.isEmpty()) {
                throw new IllegalStateException("Gemini API Key 未配置");
            }
            client = new Client(apiKey);
            logger.info("Gemini 客户端初始化成功");
        }
        return client;
    }
    
    /**
     * 获取 API Key
     */
    private String getApiKey() {
        // 优先从配置文件获取
        if (geminiConfig.isValid()) {
            return geminiConfig.getApiKey();
        }
        
        // 其次从环境变量获取
        String envApiKey = System.getenv("GEMINI_API_KEY");
        if (envApiKey != null && !envApiKey.trim().isEmpty()) {
            return envApiKey;
        }
        
        throw new IllegalStateException("请在配置文件中设置 gemini.api-key 或设置环境变量 GEMINI_API_KEY");
    }
    
    /**
     * 从 PDF 文件提取发票数据
     */
    public String extractInvoiceDataFromPdf(MultipartFile pdfFile) throws IOException {
        validateFile(pdfFile);
        
        byte[] fileBytes = pdfFile.getBytes();
        String prompt = buildInvoiceExtractionPrompt();
        
        return processFileWithPrompt(fileBytes, "application/pdf", prompt);
    }
    
    /**
     * 从本地 PDF 文件提取发票数据
     */
    public String extractInvoiceDataFromPdf(Path pdfPath) throws IOException {
        if (!Files.exists(pdfPath)) {
            throw new IllegalArgumentException("文件不存在: " + pdfPath);
        }
        
        byte[] fileBytes = Files.readAllBytes(pdfPath);
        String prompt = buildInvoiceExtractionPrompt();
        
        return processFileWithPrompt(fileBytes, "application/pdf", prompt);
    }
    
    /**
     * 通用文件处理方法
     */
    public String processFileWithPrompt(byte[] fileBytes, String mimeType, String prompt) {
        try {
            Client client = getClient();
            
            Content content = Content.builder()
                    .addPart(Part.text(prompt))
                    .addPart(Part.inlineData(mimeType, fileBytes))
                    .build();
            
            logger.info("正在调用 Gemini API 处理文件，大小: {} bytes", fileBytes.length);
            
            GenerateContentResponse response = client.models.generateContent(
                    geminiConfig.getDefaultModel(), 
                    content
            );
            
            String result = response.text();
            logger.info("Gemini API 调用成功，返回结果长度: {} 字符", result.length());
            
            return result;
            
        } catch (Exception e) {
            logger.error("调用 Gemini API 失败", e);
            throw new RuntimeException("处理文件时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 纯文本处理
     */
    public String processTextPrompt(String prompt) {
        try {
            Client client = getClient();
            
            logger.info("正在调用 Gemini API 处理文本提示");
            
            GenerateContentResponse response = client.models.generateContent(
                    geminiConfig.getDefaultModel(), 
                    prompt
            );
            
            String result = response.text();
            logger.info("Gemini API 调用成功，返回结果长度: {} 字符", result.length());
            
            return result;
            
        } catch (Exception e) {
            logger.error("调用 Gemini API 失败", e);
            throw new RuntimeException("处理文本时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        // 检查文件大小
        long fileSizeBytes = file.getSize();
        long maxSizeBytes = geminiConfig.getMaxFileSizeMb() * 1024 * 1024L;
        if (fileSizeBytes > maxSizeBytes) {
            throw new IllegalArgumentException(
                String.format("文件大小超过限制。当前: %.2f MB，最大: %d MB", 
                    fileSizeBytes / 1024.0 / 1024.0, geminiConfig.getMaxFileSizeMb())
            );
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null) {
            String extension = getFileExtension(originalFilename);
            if (!geminiConfig.isSupportedFileType(extension)) {
                throw new IllegalArgumentException(
                    String.format("不支持的文件类型: %s。支持的类型: %s", 
                        extension, Arrays.toString(geminiConfig.getSupportedFileTypes()))
                );
            }
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 构建发票数据提取的提示词
     */
    private String buildInvoiceExtractionPrompt() {
        return """
                Instruction for PDF Data Extraction
                
                Please extract the following fields from all invoices in the PDF(s):
                
                1. Shop Name
                2. Number Plate → Remove all spaces (e.g., "ABC 1234" becomes "ABC1234")
                3. Invoice Number
                4. Amount
                
                Important Requirements:
                - Check each invoice individually
                - Ensure no invoices are skipped
                - Present the data in table format with clear headers
                - If multiple invoices exist, number them clearly
                - Include currency symbols for amounts
                - If any field is not found, mark as "N/A"
                
                Output Format:
                | Invoice # | Shop Name | Number Plate | Invoice Number | Amount |
                |-----------|-----------|--------------|----------------|--------|
                | 1         | ...       | ...          | ...            | ...    |
                
                Additional Notes:
                - Double-check all extracted information for accuracy
                - If text is unclear or partially visible, indicate with [UNCLEAR] tag
                - Provide a summary count of total invoices processed
                """;
    }
}
