package com.servauto.framework.gemini;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Gemini API 配置类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "gemini")
public class GeminiConfig {
    
    /**
     * Gemini API Key
     */
    private String apiKey;
    
    /**
     * 默认模型名称
     */
    private String defaultModel = "gemini-2.5-flash";
    
    /**
     * 请求超时时间（秒）
     */
    private int timeoutSeconds = 60;
    
    /**
     * 最大文件大小（MB）
     */
    private int maxFileSizeMb = 20;
    
    /**
     * 支持的文件类型
     */
    private String[] supportedFileTypes = {"pdf", "png", "jpg", "jpeg", "gif", "webp"};
    
    // Getters and Setters
    
    public String getApiKey() {
        return apiKey;
    }
    
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public void setDefaultModel(String defaultModel) {
        this.defaultModel = defaultModel;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public int getMaxFileSizeMb() {
        return maxFileSizeMb;
    }
    
    public void setMaxFileSizeMb(int maxFileSizeMb) {
        this.maxFileSizeMb = maxFileSizeMb;
    }
    
    public String[] getSupportedFileTypes() {
        return supportedFileTypes;
    }
    
    public void setSupportedFileTypes(String[] supportedFileTypes) {
        this.supportedFileTypes = supportedFileTypes;
    }
    
    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return apiKey != null && !apiKey.trim().isEmpty() && !apiKey.equals("your-api-key-here");
    }
    
    /**
     * 检查文件类型是否支持
     */
    public boolean isSupportedFileType(String fileExtension) {
        if (fileExtension == null) return false;
        String ext = fileExtension.toLowerCase().replaceFirst("^\\.", "");
        for (String supportedType : supportedFileTypes) {
            if (supportedType.equalsIgnoreCase(ext)) {
                return true;
            }
        }
        return false;
    }
}
