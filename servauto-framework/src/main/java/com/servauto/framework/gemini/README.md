# Gemini PDF 数据提取功能

这个模块提供了使用 Google Gemini AI 从 PDF 文件中提取发票数据的功能。

## 功能特性

- 📄 PDF 发票数据提取
- 🖼️ 支持多种图片格式处理
- 🔧 灵活的配置管理
- 🌐 RESTful API 接口
- 📝 详细的日志记录
- ✅ 文件类型和大小验证

## 快速开始

### 1. 配置 API Key

#### 方式一：环境变量（推荐）
```bash
export GEMINI_API_KEY=your-actual-api-key-here
```

#### 方式二：配置文件
在 `application.yml` 中添加：
```yaml
gemini:
  api-key: your-actual-api-key-here
  default-model: gemini-2.5-flash
  max-file-size-mb: 20
```

### 2. 获取 API Key

1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录你的 Google 账户
3. 创建新的 API Key
4. 复制 API Key 并配置到项目中

### 3. 使用方式

#### 方式一：直接运行测试类
```java
// 修改 GeminiTest.java 中的配置
private static final String API_KEY = "your-api-key-here";
private static final String PDF_FILE_PATH = "/path/to/your/invoice.pdf";

// 运行 main 方法
public static void main(String[] args) {
    // 会自动处理指定的 PDF 文件
}
```

#### 方式二：使用 Spring Service
```java
@Autowired
private GeminiService geminiService;

// 处理上传的文件
public String processInvoice(MultipartFile pdfFile) {
    return geminiService.extractInvoiceDataFromPdf(pdfFile);
}

// 处理本地文件
public String processLocalFile(String filePath) {
    return geminiService.extractInvoiceDataFromPdf(Paths.get(filePath));
}
```

#### 方式三：使用 REST API

**提取发票数据：**
```bash
curl -X POST http://localhost:8080/api/gemini/extract-invoice \
  -F "file=@/path/to/invoice.pdf"
```

**自定义文件处理：**
```bash
curl -X POST http://localhost:8080/api/gemini/process-file \
  -F "file=@/path/to/document.pdf" \
  -F "prompt=请提取这个文档中的所有重要信息"
```

**文本处理：**
```bash
curl -X POST http://localhost:8080/api/gemini/process-text \
  -H "Content-Type: application/json" \
  -d '{"prompt": "请解释什么是人工智能"}'
```

**检查服务状态：**
```bash
curl http://localhost:8080/api/gemini/status
```

## 配置说明

### 完整配置示例
```yaml
gemini:
  # API Key
  api-key: ${GEMINI_API_KEY:your-default-key}
  
  # 模型选择
  default-model: gemini-2.5-flash  # 或 gemini-2.5-pro
  
  # 超时设置
  timeout-seconds: 60
  
  # 文件限制
  max-file-size-mb: 20
  
  # 支持的文件类型
  supported-file-types:
    - pdf
    - png
    - jpg
    - jpeg
    - gif
    - webp
```

### 模型选择建议

- **gemini-2.5-flash**: 速度快，成本低，适合大量处理
- **gemini-2.5-pro**: 准确度高，适合复杂文档

## API 接口文档

### 1. 提取发票数据
- **URL**: `POST /api/gemini/extract-invoice`
- **参数**: `file` (multipart/form-data)
- **返回**: JSON 格式的提取结果

### 2. 通用文件处理
- **URL**: `POST /api/gemini/process-file`
- **参数**: 
  - `file` (multipart/form-data)
  - `prompt` (string)
- **返回**: JSON 格式的处理结果

### 3. 文本处理
- **URL**: `POST /api/gemini/process-text`
- **参数**: `{"prompt": "your text prompt"}`
- **返回**: JSON 格式的处理结果

### 4. 服务状态
- **URL**: `GET /api/gemini/status`
- **返回**: 服务状态信息

## 输出格式

发票数据提取会返回表格格式的数据：

```
| Invoice # | Shop Name | Number Plate | Invoice Number | Amount |
|-----------|-----------|--------------|----------------|--------|
| 1         | ABC Shop  | ABC1234      | INV-001        | $100.00|
| 2         | XYZ Store | XYZ5678      | INV-002        | $250.50|
```

## 错误处理

常见错误及解决方案：

1. **API Key 未配置**
   - 错误：`Gemini API Key 未配置`
   - 解决：设置环境变量或配置文件中的 API Key

2. **文件过大**
   - 错误：`文件大小超过限制`
   - 解决：压缩文件或调整 `max-file-size-mb` 配置

3. **不支持的文件类型**
   - 错误：`不支持的文件类型`
   - 解决：转换为支持的格式或添加到 `supported-file-types`

4. **API 调用失败**
   - 错误：`调用 Gemini API 失败`
   - 解决：检查网络连接和 API Key 有效性

## 注意事项

1. **API Key 安全**: 不要在代码中硬编码 API Key，使用环境变量
2. **文件大小**: Gemini API 对文件大小有限制，建议不超过 20MB
3. **请求频率**: 注意 API 的调用频率限制
4. **成本控制**: Pro 模型比 Flash 模型成本更高
5. **数据隐私**: 上传的文件会发送到 Google 服务器处理

## 开发和调试

### 启用调试日志
```yaml
logging:
  level:
    com.servauto.framework.gemini: DEBUG
```

### 测试连接
运行 `GeminiTest.testTextOnly()` 方法测试基本连接。

## 扩展功能

可以基于现有框架扩展更多功能：

1. 批量文件处理
2. 异步处理队列
3. 结果缓存
4. 多语言支持
5. 自定义提示模板

## 技术支持

如有问题，请检查：
1. API Key 是否正确配置
2. 网络连接是否正常
3. 文件格式是否支持
4. 日志中的详细错误信息
