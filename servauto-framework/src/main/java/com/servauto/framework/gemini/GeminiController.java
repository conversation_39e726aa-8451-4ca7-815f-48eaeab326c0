package com.servauto.framework.gemini;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * Gemini API 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/gemini")
@Tag(name = "Gemini API", description = "Gemini AI 数据提取接口")
public class GeminiController {
    
    private static final Logger logger = LoggerFactory.getLogger(GeminiController.class);
    
    @Autowired
    private GeminiService geminiService;
    
    /**
     * 从 PDF 文件提取发票数据
     */
    @PostMapping(value = "/extract-invoice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "从PDF提取发票数据", description = "上传PDF文件，提取其中的发票信息")
    public ResponseEntity<Map<String, Object>> extractInvoiceData(
            @Parameter(description = "PDF文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("收到发票数据提取请求，文件名: {}, 大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());
            
            String extractedData = geminiService.extractInvoiceDataFromPdf(file);
            
            response.put("success", true);
            response.put("message", "数据提取成功");
            response.put("data", extractedData);
            response.put("filename", file.getOriginalFilename());
            response.put("fileSize", file.getSize());
            
            logger.info("发票数据提取成功，文件: {}", file.getOriginalFilename());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("发票数据提取失败", e);
            
            response.put("success", false);
            response.put("message", "数据提取失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 通用文本处理接口
     */
    @PostMapping("/process-text")
    @Operation(summary = "处理文本提示", description = "发送文本提示给Gemini AI进行处理")
    public ResponseEntity<Map<String, Object>> processText(
            @Parameter(description = "文本提示", required = true)
            @RequestBody Map<String, String> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String prompt = request.get("prompt");
            if (prompt == null || prompt.trim().isEmpty()) {
                throw new IllegalArgumentException("提示文本不能为空");
            }
            
            logger.info("收到文本处理请求，提示长度: {} 字符", prompt.length());
            
            String result = geminiService.processTextPrompt(prompt);
            
            response.put("success", true);
            response.put("message", "文本处理成功");
            response.put("data", result);
            response.put("promptLength", prompt.length());
            
            logger.info("文本处理成功，提示长度: {} 字符", prompt.length());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("文本处理失败", e);
            
            response.put("success", false);
            response.put("message", "文本处理失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 通用文件处理接口
     */
    @PostMapping(value = "/process-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "处理文件", description = "上传文件并提供自定义提示进行处理")
    public ResponseEntity<Map<String, Object>> processFile(
            @Parameter(description = "要处理的文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "处理提示", required = true)
            @RequestParam("prompt") String prompt) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (prompt == null || prompt.trim().isEmpty()) {
                throw new IllegalArgumentException("提示文本不能为空");
            }
            
            logger.info("收到文件处理请求，文件名: {}, 大小: {} bytes, 提示长度: {} 字符", 
                    file.getOriginalFilename(), file.getSize(), prompt.length());
            
            // 根据文件类型确定 MIME 类型
            String mimeType = determineMimeType(file);
            
            String result = geminiService.processFileWithPrompt(file.getBytes(), mimeType, prompt);
            
            response.put("success", true);
            response.put("message", "文件处理成功");
            response.put("data", result);
            response.put("filename", file.getOriginalFilename());
            response.put("fileSize", file.getSize());
            response.put("mimeType", mimeType);
            
            logger.info("文件处理成功，文件: {}", file.getOriginalFilename());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("文件处理失败", e);
            
            response.put("success", false);
            response.put("message", "文件处理失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取服务状态", description = "检查Gemini服务的配置和状态")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 测试简单的文本处理
            String testResult = geminiService.processTextPrompt("Hello, please respond with 'Service is working'");
            
            response.put("success", true);
            response.put("message", "服务正常");
            response.put("testResult", testResult);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("服务状态检查失败", e);
            
            response.put("success", false);
            response.put("message", "服务异常: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据文件扩展名确定 MIME 类型
     */
    private String determineMimeType(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename == null) {
            return "application/octet-stream";
        }
        
        String extension = filename.toLowerCase();
        if (extension.endsWith(".pdf")) {
            return "application/pdf";
        } else if (extension.endsWith(".png")) {
            return "image/png";
        } else if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (extension.endsWith(".gif")) {
            return "image/gif";
        } else if (extension.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }
}
