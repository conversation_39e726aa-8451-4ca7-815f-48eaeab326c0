package com.servauto.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "swagger", ignoreInvalidFields = true)
@Data
public class SwaggerProperties {

    private String title;

    private String description;

    private String author;

    private String version;

    private String url;

    private String email;

    private String license;

    private String licenseUrl;
}
