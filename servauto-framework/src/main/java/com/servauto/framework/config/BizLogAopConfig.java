package com.servauto.framework.config;

import com.servauto.framework.aspectj.BizLogAopAspect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class BizLogAopConfig {

    @Bean
    @ConditionalOnMissingBean(BizLogAopAspect.class)
    public BizLogAopAspect traceAspect() {
        return new BizLogAopAspect();
    }


}
