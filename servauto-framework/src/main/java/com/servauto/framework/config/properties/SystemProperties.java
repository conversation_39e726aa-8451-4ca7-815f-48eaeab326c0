package com.servauto.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <p>SystemConfig</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 22:54
 */
@ConfigurationProperties(prefix = "system", ignoreUnknownFields = false)
@Validated
@Data
@Component
public class SystemProperties {

    private String timezone = "Asia/Shanghai";
}
