<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.file.dao.SystemFileContentMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.file.model.SystemFileContent">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.framework.file.model.SystemFileContent">
    <result column="content" jdbcType="LONGVARBINARY" property="content" />
  </resultMap>
  <sql id="Base_Column_List">
    id, config_id, path, create_time, update_time, deleted
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from system_file_content
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_file_content
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.servauto.framework.file.model.SystemFileContent">
    insert into system_file_content (id, config_id, path, 
      create_time, update_time, deleted, 
      content)
    values (#{id,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{path,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, 
      #{content,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.framework.file.model.SystemFileContent">
    insert into system_file_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.file.model.SystemFileContent">
    update system_file_content
    <set>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.servauto.framework.file.model.SystemFileContent">
    update system_file_content
    set config_id = #{configId,jdbcType=BIGINT},
      path = #{path,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      content = #{content,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.framework.file.model.SystemFileContent">
    update system_file_content
    set config_id = #{configId,jdbcType=BIGINT},
      path = #{path,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <delete id="deleteByConfigIdAndPath">
    delete from system_file_content
    where id = #{id,jdbcType=BIGINT} and path = #{path,jdbcType=VARCHAR}
  </delete>

  <select id="selectListByConfigIdAndPath" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from system_file_content
    where config_id = #{id,jdbcType=BIGINT} and path = #{path,jdbcType=VARCHAR}
  </select>


</mapper>