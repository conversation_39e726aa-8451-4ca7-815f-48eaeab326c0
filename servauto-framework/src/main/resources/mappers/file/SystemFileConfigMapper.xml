<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.file.dao.SystemFileConfigMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.file.model.SystemFileConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="storage" jdbcType="INTEGER" property="storage" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="master" jdbcType="BIT" property="master" />
    <result column="config" jdbcType="VARCHAR" property="config" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, storage, remark, master, config, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_file_config
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_file_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.framework.file.model.SystemFileConfig">
    insert into system_file_config (id, name, storage, 
      remark, master, config, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{storage,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{master,jdbcType=BIT}, #{config,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.framework.file.model.SystemFileConfig">
    insert into system_file_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="storage != null">
        storage,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="master != null">
        master,
      </if>
      <if test="config != null">
        config,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="storage != null">
        #{storage,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="master != null">
        #{master,jdbcType=BIT},
      </if>
      <if test="config != null">
        #{config,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.file.model.SystemFileConfig">
    update system_file_config
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="storage != null">
        storage = #{storage,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="master != null">
        master = #{master,jdbcType=BIT},
      </if>
      <if test="config != null">
        config = #{config,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.framework.file.model.SystemFileConfig">
    update system_file_config
    set name = #{name,jdbcType=VARCHAR},
      storage = #{storage,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      master = #{master,jdbcType=BIT},
      config = #{config,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMaster" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_file_config
    where master = 0 and deleted = 0
    order by id asc limit 1
  </select>



</mapper>