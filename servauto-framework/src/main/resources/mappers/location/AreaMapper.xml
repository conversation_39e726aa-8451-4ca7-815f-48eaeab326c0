<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.location.dao.AreaMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.location.model.Area">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, `name`, parent_code, `order`, create_time, update_time
  </sql>

  <select id="selectAreas" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from area
    order by `order` asc
  </select>

  <select id="selectByCodes" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from area
    <choose>
      <when test="codes != null and codes.size() > 0">
        where code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
          #{code,jdbcType=VARCHAR}
        </foreach>
      </when>
      <otherwise>
        where 1=0 -- 如果 codes 为空，返回空结果集
      </otherwise>
    </choose>
  </select>

  <select id="selectByParentCode" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from area
    where parent_code = #{parentCode}
    order by `order` asc
  </select>


</mapper>