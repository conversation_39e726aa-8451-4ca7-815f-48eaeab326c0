<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.sms.dao.SystemSmsChannelMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.sms.model.SystemSmsChannel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="signature" jdbcType="VARCHAR" property="signature" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="BIGINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_secret" jdbcType="VARCHAR" property="apiSecret" />
    <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, signature, code, status, remark, api_key, api_secret, callback_url, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_sms_channel
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_sms_channel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.framework.sms.model.SystemSmsChannel">
    insert into system_sms_channel (id, signature, code, 
      status, remark, api_key, 
      api_secret, callback_url, create_time, 
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{signature,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{status,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{apiKey,jdbcType=VARCHAR}, 
      #{apiSecret,jdbcType=VARCHAR}, #{callbackUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.framework.sms.model.SystemSmsChannel">
    insert into system_sms_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="signature != null">
        signature,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="apiKey != null">
        api_key,
      </if>
      <if test="apiSecret != null">
        api_secret,
      </if>
      <if test="callbackUrl != null">
        callback_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="signature != null">
        #{signature,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        #{apiKey,jdbcType=VARCHAR},
      </if>
      <if test="apiSecret != null">
        #{apiSecret,jdbcType=VARCHAR},
      </if>
      <if test="callbackUrl != null">
        #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.sms.model.SystemSmsChannel">
    update system_sms_channel
    <set>
      <if test="signature != null">
        signature = #{signature,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        api_key = #{apiKey,jdbcType=VARCHAR},
      </if>
      <if test="apiSecret != null">
        api_secret = #{apiSecret,jdbcType=VARCHAR},
      </if>
      <if test="callbackUrl != null">
        callback_url = #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.framework.sms.model.SystemSmsChannel">
    update system_sms_channel
    set signature = #{signature,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      api_key = #{apiKey,jdbcType=VARCHAR},
      api_secret = #{apiSecret,jdbcType=VARCHAR},
      callback_url = #{callbackUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCode" resultType="com.servauto.framework.sms.model.SystemSmsChannel">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_sms_channel
    where code = #{code,jdbcType=VARCHAR}
  </select>

</mapper>