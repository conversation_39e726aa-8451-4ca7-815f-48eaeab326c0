<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.sms.dao.SystemSmsTemplateMapper">
    <resultMap id="BaseResultMap" type="com.servauto.framework.sms.model.SystemSmsTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="BIGINT" property="type"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="params" jdbcType="VARCHAR" property="params"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="api_template_id" jdbcType="VARCHAR" property="apiTemplateId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , type, status, code, name, content, params, remark, api_template_id, channel_id,
    channel_code, create_time, update_time, deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from system_sms_template
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from system_sms_template
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.servauto.framework.sms.model.SystemSmsTemplate">
        insert into system_sms_template (id, type, status,
                                         code, name, content,
                                         params, remark, api_template_id,
                                         channel_id, channel_code, create_time,
                                         update_time, deleted)
        values (#{id,jdbcType=BIGINT}, #{type,jdbcType=BIGINT}, #{status,jdbcType=BIGINT},
                #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{apiTemplateId,jdbcType=VARCHAR},
                #{channelId,jdbcType=BIGINT}, #{channelCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.servauto.framework.sms.model.SystemSmsTemplate">
        insert into system_sms_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="apiTemplateId != null">
                api_template_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="params != null">
                #{params,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="apiTemplateId != null">
                #{apiTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.sms.model.SystemSmsTemplate">
        update system_sms_template
        <set>
            <if test="type != null">
                type = #{type,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="params != null">
                params = #{params,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="apiTemplateId != null">
                api_template_id = #{apiTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.servauto.framework.sms.model.SystemSmsTemplate">
        update system_sms_template
        set type            = #{type,jdbcType=BIGINT},
            status          = #{status,jdbcType=BIGINT},
            code            = #{code,jdbcType=VARCHAR},
            name            = #{name,jdbcType=VARCHAR},
            content         = #{content,jdbcType=VARCHAR},
            params          = #{params,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            api_template_id = #{apiTemplateId,jdbcType=VARCHAR},
            channel_id      = #{channelId,jdbcType=BIGINT},
            channel_code    = #{channelCode,jdbcType=VARCHAR},
            create_time     = #{createTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            deleted         = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from system_sms_template
        where code = #{code,jdbcType=VARCHAR} and deleted = 0
    </select>


</mapper>