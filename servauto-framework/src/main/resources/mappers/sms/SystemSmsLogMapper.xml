<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.sms.dao.SystemSmsLogMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.sms.model.SystemSmsLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="template_code" jdbcType="VARCHAR" property="templateCode" />
    <result column="template_type" jdbcType="BIGINT" property="templateType" />
    <result column="template_content" jdbcType="VARCHAR" property="templateContent" />
    <result column="template_params" jdbcType="VARCHAR" property="templateParams" />
    <result column="api_template_id" jdbcType="VARCHAR" property="apiTemplateId" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_type" jdbcType="BIGINT" property="userType" />
    <result column="send_status" jdbcType="BIGINT" property="sendStatus" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="api_send_code" jdbcType="VARCHAR" property="apiSendCode" />
    <result column="api_send_msg" jdbcType="LONGNVARCHAR" property="apiSendMsg" />
    <result column="api_request_id" jdbcType="VARCHAR" property="apiRequestId" />
    <result column="api_serial_no" jdbcType="VARCHAR" property="apiSerialNo" />
    <result column="receive_status" jdbcType="BIGINT" property="receiveStatus" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="api_receive_code" jdbcType="VARCHAR" property="apiReceiveCode" />
    <result column="api_receive_msg" jdbcType="LONGNVARCHAR" property="apiReceiveMsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, channel_id, channel_code, template_id, template_code, template_type, template_content, 
    template_params, api_template_id, mobile, user_id, user_type, send_status, send_time, 
    api_send_code, api_send_msg, api_request_id, api_serial_no, receive_status, receive_time, 
    api_receive_code, api_receive_msg, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_sms_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_sms_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.framework.sms.model.SystemSmsLog">
    insert into system_sms_log (id, channel_id, channel_code, 
      template_id, template_code, template_type, 
      template_content, template_params, api_template_id, 
      mobile, user_id, user_type, 
      send_status, send_time, api_send_code, 
      api_send_msg, api_request_id, api_serial_no, 
      receive_status, receive_time, api_receive_code, 
      api_receive_msg, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{channelCode,jdbcType=VARCHAR}, 
      #{templateId,jdbcType=BIGINT}, #{templateCode,jdbcType=VARCHAR}, #{templateType,jdbcType=BIGINT}, 
      #{templateContent,jdbcType=VARCHAR}, #{templateParams,jdbcType=VARCHAR}, #{apiTemplateId,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userType,jdbcType=BIGINT}, 
      #{sendStatus,jdbcType=BIGINT}, #{sendTime,jdbcType=TIMESTAMP}, #{apiSendCode,jdbcType=VARCHAR}, 
      #{apiSendMsg,jdbcType=VARCHAR}, #{apiRequestId,jdbcType=VARCHAR}, #{apiSerialNo,jdbcType=VARCHAR}, 
      #{receiveStatus,jdbcType=BIGINT}, #{receiveTime,jdbcType=TIMESTAMP}, #{apiReceiveCode,jdbcType=VARCHAR}, 
      #{apiReceiveMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.framework.sms.model.SystemSmsLog" useGeneratedKeys="true" keyProperty="id">
    insert into system_sms_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelCode != null">
        channel_code,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateCode != null">
        template_code,
      </if>
      <if test="templateType != null">
        template_type,
      </if>
      <if test="templateContent != null">
        template_content,
      </if>
      <if test="templateParams != null">
        template_params,
      </if>
      <if test="apiTemplateId != null">
        api_template_id,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="apiSendCode != null">
        api_send_code,
      </if>
      <if test="apiSendMsg != null">
        api_send_msg,
      </if>
      <if test="apiRequestId != null">
        api_request_id,
      </if>
      <if test="apiSerialNo != null">
        api_serial_no,
      </if>
      <if test="receiveStatus != null">
        receive_status,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="apiReceiveCode != null">
        api_receive_code,
      </if>
      <if test="apiReceiveMsg != null">
        api_receive_msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateCode != null">
        #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        #{templateType,jdbcType=BIGINT},
      </if>
      <if test="templateContent != null">
        #{templateContent,jdbcType=VARCHAR},
      </if>
      <if test="templateParams != null">
        #{templateParams,jdbcType=VARCHAR},
      </if>
      <if test="apiTemplateId != null">
        #{apiTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=BIGINT},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=BIGINT},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiSendCode != null">
        #{apiSendCode,jdbcType=VARCHAR},
      </if>
      <if test="apiSendMsg != null">
        #{apiSendMsg,jdbcType=VARCHAR},
      </if>
      <if test="apiRequestId != null">
        #{apiRequestId,jdbcType=VARCHAR},
      </if>
      <if test="apiSerialNo != null">
        #{apiSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveStatus != null">
        #{receiveStatus,jdbcType=BIGINT},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiReceiveCode != null">
        #{apiReceiveCode,jdbcType=VARCHAR},
      </if>
      <if test="apiReceiveMsg != null">
        #{apiReceiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.sms.model.SystemSmsLog">
    update system_sms_log
    <set>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelCode != null">
        channel_code = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateCode != null">
        template_code = #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        template_type = #{templateType,jdbcType=BIGINT},
      </if>
      <if test="templateContent != null">
        template_content = #{templateContent,jdbcType=VARCHAR},
      </if>
      <if test="templateParams != null">
        template_params = #{templateParams,jdbcType=VARCHAR},
      </if>
      <if test="apiTemplateId != null">
        api_template_id = #{apiTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=BIGINT},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=BIGINT},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiSendCode != null">
        api_send_code = #{apiSendCode,jdbcType=VARCHAR},
      </if>
      <if test="apiSendMsg != null">
        api_send_msg = #{apiSendMsg,jdbcType=VARCHAR},
      </if>
      <if test="apiRequestId != null">
        api_request_id = #{apiRequestId,jdbcType=VARCHAR},
      </if>
      <if test="apiSerialNo != null">
        api_serial_no = #{apiSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveStatus != null">
        receive_status = #{receiveStatus,jdbcType=BIGINT},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiReceiveCode != null">
        api_receive_code = #{apiReceiveCode,jdbcType=VARCHAR},
      </if>
      <if test="apiReceiveMsg != null">
        api_receive_msg = #{apiReceiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.framework.sms.model.SystemSmsLog">
    update system_sms_log
    set channel_id = #{channelId,jdbcType=BIGINT},
      channel_code = #{channelCode,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=BIGINT},
      template_code = #{templateCode,jdbcType=VARCHAR},
      template_type = #{templateType,jdbcType=BIGINT},
      template_content = #{templateContent,jdbcType=VARCHAR},
      template_params = #{templateParams,jdbcType=VARCHAR},
      api_template_id = #{apiTemplateId,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_type = #{userType,jdbcType=BIGINT},
      send_status = #{sendStatus,jdbcType=BIGINT},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      api_send_code = #{apiSendCode,jdbcType=VARCHAR},
      api_send_msg = #{apiSendMsg,jdbcType=VARCHAR},
      api_request_id = #{apiRequestId,jdbcType=VARCHAR},
      api_serial_no = #{apiSerialNo,jdbcType=VARCHAR},
      receive_status = #{receiveStatus,jdbcType=BIGINT},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      api_receive_code = #{apiReceiveCode,jdbcType=VARCHAR},
      api_receive_msg = #{apiReceiveMsg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>