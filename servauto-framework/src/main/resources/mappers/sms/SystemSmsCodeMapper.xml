<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.framework.sms.dao.SystemSmsCodeMapper">
  <resultMap id="BaseResultMap" type="com.servauto.framework.sms.model.SystemSmsCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="create_ip" jdbcType="VARCHAR" property="createIp" />
    <result column="scene" jdbcType="BIGINT" property="scene" />
    <result column="today_index" jdbcType="BIGINT"  property="todayIndex" />
    <result column="used" jdbcType="BIT" property="used" />
    <result column="used_time" jdbcType="TIMESTAMP" property="usedTime" />
    <result column="used_ip" jdbcType="VARCHAR" property="usedIp" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mobile, code, create_ip, scene, today_index, used, used_time, used_ip, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_sms_code
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_sms_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.framework.sms.model.SystemSmsCode">
    insert into system_sms_code (id, mobile, code, 
      create_ip, scene, today_index, 
      used, used_time, used_ip, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{mobile,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{createIp,jdbcType=VARCHAR}, #{scene,jdbcType=BIGINT}, #{todayIndex,jdbcType=BIGINT}, 
      #{used,jdbcType=BIT}, #{usedTime,jdbcType=TIMESTAMP}, #{usedIp,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.framework.sms.model.SystemSmsCode" useGeneratedKeys="true" keyProperty="id">
    insert into system_sms_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="createIp != null">
        create_ip,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="todayIndex != null">
        today_index,
      </if>
      <if test="used != null">
        used,
      </if>
      <if test="usedTime != null">
        used_time,
      </if>
      <if test="usedIp != null">
        used_ip,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="createIp != null">
        #{createIp,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=BIGINT},
      </if>
      <if test="todayIndex != null">
        #{todayIndex,jdbcType=BIGINT},
      </if>
      <if test="used != null">
        #{used,jdbcType=BIT},
      </if>
      <if test="usedTime != null">
        #{usedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usedIp != null">
        #{usedIp,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.framework.sms.model.SystemSmsCode">
    update system_sms_code
    <set>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="createIp != null">
        create_ip = #{createIp,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=BIGINT},
      </if>
      <if test="todayIndex != null">
        today_index = #{todayIndex,jdbcType=BIGINT},
      </if>
      <if test="used != null">
        used = #{used,jdbcType=BIT},
      </if>
      <if test="usedTime != null">
        used_time = #{usedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usedIp != null">
        used_ip = #{usedIp,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.framework.sms.model.SystemSmsCode">
    update system_sms_code
    set mobile = #{mobile,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      create_ip = #{createIp,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=BIGINT},
      today_index = #{todayIndex,jdbcType=BIGINT},
      used = #{used,jdbcType=BIT},
      used_time = #{usedTime,jdbcType=TIMESTAMP},
      used_ip = #{usedIp,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectLastByMobile" resultType="com.servauto.framework.sms.model.SystemSmsCode">
    select
    <include refid="Base_Column_List" />
    from system_sms_code
    where mobile = #{mobile,jdbcType=VARCHAR}
      <if test="code != null and code != '' " >
        and code = #{code,jdbcType=VARCHAR}
      </if>
      <if test="scene != null and scene != '' " >
        and scene = #{scene,jdbcType=BIGINT}
      </if>
    and deleted = 0
    order by id desc
    limit 1
  </select>

</mapper>