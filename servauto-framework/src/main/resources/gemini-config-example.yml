# Gemini API 配置示例
# 请将此文件复制到你的 application.yml 中，并填入正确的配置值

gemini:
  # Gemini API Key - 从 Google AI Studio 获取
  # 获取地址: https://aistudio.google.com/app/apikey
  api-key: your-gemini-api-key-here
  
  # 默认使用的模型
  # 可选值: gemini-2.5-flash, gemini-2.5-pro, gemini-1.5-flash, gemini-1.5-pro
  default-model: gemini-2.5-flash
  
  # 请求超时时间（秒）
  timeout-seconds: 60
  
  # 最大文件大小（MB）
  max-file-size-mb: 20
  
  # 支持的文件类型
  supported-file-types:
    - pdf
    - png
    - jpg
    - jpeg
    - gif
    - webp

# 使用环境变量的方式（推荐）
# 在系统环境变量中设置：
# export GEMINI_API_KEY=your-actual-api-key-here

# 或者在 Docker 中：
# docker run -e GEMINI_API_KEY=your-actual-api-key-here your-app

# 或者在 application.yml 中使用环境变量：
# gemini:
#   api-key: ${GEMINI_API_KEY:}
