### **ServAuto 消息推送模块文档**

#### **概述**

ServAuto 消息推送模块（`servauto-framework/sms`）提供了一套统一的接口，用于发送短信和 WhatsApp 消息。该模块通过抽象不同的消息通道，实现了灵活的配置和扩展性。

#### **核心组件**

1.  **`SmsClient` 和 `SmsClientFactory`**
    *   `SmsClient` (接口): 定义了短信发送和状态回调解析的通用行为。
    *   `SmsClientFactory` (接口): 定义了创建 `SmsClient` 实例的工厂方法。
    *   `SmsClientFactoryImpl` (实现): 负责根据配置创建具体的 `SmsClient` 实例（例如 `YunCloudSmsClient` 或 `YunCloudWhatsappClient`）。

2.  **`AbstractSmsClient`**
    *   抽象类，实现了 `SmsClient` 接口的部分通用逻辑，并持有了 `SmsChannelProperties`。

3.  **具体消息客户端**
    *   **`YunCloudSmsClient`**: 负责通过 YCloud 平台发送普通短信。
        *   **依赖**: `com.ycloud.client` (YCloud API 客户端库)。
        *   **认证**: 使用 `api_key` 进行认证。
        *   **消息类型**: **只支持纯文本短信内容发送，不支持通过 API 模板发送短信。**
        *   **手机号限制**: **不支持向 +86 开头的中国大陆手机号发送短信。**
        *   **外部ID**: `logId` 作为 `externalId` 传递给 YCloud，用于消息追踪。
        *   **回调 URL**: 可配置 `callbackUrl` 以接收短信状态报告。
    *   **`YunCloudWhatsappClient`**: 负责通过 YCloud 平台发送 WhatsApp 消息。
        *   **依赖**: `com.ycloud.client` (YCloud API 客户端库)。
        *   **认证**: 使用 `api_key` 进行认证。
        *   **消息类型**: 支持 `TEMPLATE` (模板消息) 和 `TEXT` (纯文本消息)。
        *   **模板消息参数**: `templateParams` 用于填充模板中的变量。
        *   **外部ID**: `logId` 作为 `externalId` 传递给 YCloud，用于消息追踪。

4.  **API 服务接口**
    *   **`SmsSendApi`**: 对外提供短信发送的核心接口。
    *   **`SmsCodeApi`**: 对外提供短信验证码的发送、使用和验证接口。

5.  **消息队列机制**
    *   **`SmsProducer`**: 负责将短信发送请求封装成 `SmsSendMessage` 对象，并发送到消息队列。
    *   **`SmsSendConsumer`**: 监听消息队列，异步消费 `SmsSendMessage`，并调用具体的 `SmsClient` 进行发送。这有助于提高系统响应速度和解耦。

6.  **数据模型和持久化**
    *   **`SystemSmsChannel`**: 短信通道配置信息（例如 YCloud 的 API Key、Secret 等）。
    *   **`SystemSmsTemplate`**: 短信模板信息（例如模板内容、模板ID等）。
    *   **`SystemSmsLog`**: 短信发送日志，记录每次发送的详细信息。
    *   **`SystemSmsCode`**: 短信验证码的存储和管理。
    *   对应的 `Mapper` 接口和 XML 文件用于与数据库交互。

7.  **配置类**
    *   **`SmsConfiguration`**: 短信模块的 Spring 配置类，负责初始化相关 Bean。
    *   **`SmsChannelProperties`**: 短信通道的配置属性类，包含 `apiKey`, `remark` 等。
    *   **`SmsCodeProperties`**: 短信验证码相关的配置属性。

#### **短信发送流程 (以普通短信为例)**

1.  **调用 `SmsSendApi`**: 业务层调用 `SmsSendApi` 的发送方法。
2.  **封装消息**: `SmsSendApiImpl` 将发送请求封装成 `SmsSendMessage`。
3.  **发送到消息队列**: `SmsProducer` 将 `SmsSendMessage` 发送到消息队列（例如 Redis Stream 或 Kafka）。
4.  **异步消费**: `SmsSendConsumer` 从消息队列中获取 `SmsSendMessage`。
5.  **获取 `SmsClient`**: `SmsClientFactoryImpl` 根据配置的通道类型（例如 `SMS_CHANNEL_YUNCLOUD`）创建或获取 `YunCloudSmsClient` 实例。
6.  **实际发送**: `YunCloudSmsClient` 调用 YCloud 的 API 进行短信发送。
    *   **注意**: 只能发送纯文本内容，不支持模板。
    *   **注意**: 不支持 +86 手机号。
7.  **记录日志**: `SystemSmsLog` 记录发送结果。
8.  **状态回调**: YCloud 将短信发送状态（成功、失败、送达等）通过回调通知系统，由 `parseSmsReceiveStatus` 方法处理并更新 `SystemSmsLog`。

#### **WhatsApp 消息推送流程**

1.  **调用 `SmsSendApi`**: 业务层调用 `SmsSendApi` 的发送方法，指定 WhatsApp 通道。
2.  **封装消息**: `SmsSendApiImpl` 封装 `SmsSendMessage`。
3.  **发送到消息队列**: `SmsProducer` 发送消息。
4.  **异步消费**: `SmsSendConsumer` 消费消息。
5.  **获取 `SmsClient`**: `SmsClientFactoryImpl` 根据配置的通道类型（例如 `SMS_CHANNEL_WHATSAPP_YUNCLOUD`）创建或获取 `YunCloudWhatsappClient` 实例。
6.  **实际发送**: `YunCloudWhatsappClient` 调用 YCloud 的 WhatsApp API 进行消息发送。
    *   如果是模板消息，需要提供 `apiTemplateId` 和 `templateParams`。
    *   如果是纯文本消息，直接提供 `content`。
7.  **记录日志**: `SystemSmsLog` 记录发送结果。
8.  **状态回调**: YCloud 将 WhatsApp 消息状态通过回调通知系统，由 `YunCloudWhatsappClient.parseSmsReceiveStatus` 方法处理并更新 `SystemSmsLog`。

#### **配置说明**

短信和 WhatsApp 通道配置存储在数据库的 `system_sms_channel` 表中，并通过 `SystemSmsChannel` 实体类进行管理。关键配置项包括：

*   `id`: 通道ID。
*   `code`: 通道编码（例如 `YUN_CLOUD_SMS`, `YUN_CLOUD_WHATSAPP`）。
*   `apiKey`: YCloud 平台的 API Key。
*   `remark`: 发送方标识（对于 WhatsApp 可能是发送号码）。
*   `status`: 通道状态（启用/禁用）。
*   `callbackUrl`: (可选) 回调 URL，用于接收短信状态报告。

**示例配置 (伪代码，实际通过数据库管理):**

```yaml
# application.yml (示例，实际配置在数据库)
servauto:
  sms:
    channels:
      yun_cloud_sms:
        apiKey: your_yuncloud_sms_api_key
        remark: YourSMSBrand
        callbackUrl: your_sms_callback_url
      yun_cloud_whatsapp:
        apiKey: your_yuncloud_whatsapp_api_key
        remark: YourWhatsappNumber
```

#### **使用示例**

**1. 发送普通短信 (通过 `SmsSendApi`)**

```java
@Resource
private SmsSendApi smsSendApi;

public void sendVerificationCode(String mobile, String code) {
    SmsSendContentReqDTO reqDTO = new SmsSendContentReqDTO();
    reqDTO.setMobile(mobile);
    reqDTO.setContent("您的验证码是：" + code); // 直接设置内容
    reqDTO.setChannelCode("YUN_CLOUD_SMS"); // 指定短信通道

    smsSendApi.sendSms(reqDTO);
}
```

**2. 发送 WhatsApp 模板消息 (通过 `SmsSendApi`)**

```java
@Resource
private SmsSendApi smsSendApi;

public void sendOrderConfirmationWhatsapp(String mobile, String orderId, String productName) {
    SmsSendContentReqDTO reqDTO = new SmsContentReqDTO();
    reqDTO.setMobile(mobile);
    reqDTO.setTemplateCode("ORDER_CONFIRMATION_WHATSAPP_TEMPLATE"); // WhatsApp 模板ID
    Map<String, Object> templateParams = new HashMap<>();
    templateParams.put("orderId", orderId);
    templateParams.put("productName", productName);
    reqDTO.setTemplateParams(templateParams);
    reqDTO.setChannelCode("YUN_CLOUD_WHATSAPP"); // 指定 WhatsApp 通道

    smsSendApi.sendSms(reqDTO);
}
```

**3. 发送 WhatsApp 纯文本消息 (通过 `SmsSendApi`)**

```java
@Resource
private SmsSendApi smsSendApi;

public void sendSimpleWhatsappMessage(String mobile, String messageContent) {
    SmsSendContentReqDTO reqDTO = new SmsSendContentReqDTO();
    reqDTO.setMobile(mobile);
    reqDTO.setContent(messageContent); // 直接设置内容
    reqDTO.setChannelCode("YUN_CLOUD_WHATSAPP"); // 指定 WhatsApp 通道

    smsSendApi.sendSms(reqDTO);
}
```

#### **异常处理和日志**

*   模块内部使用了 `try-catch` 块来捕获发送过程中的异常，并记录日志 (`log.error`)。
*   `SmsSendRespDTO` 返回发送结果，包括 `success` 状态、`apiCode` 和 `apiMsg`，业务层可以根据这些信息判断发送是否成功。
*   短信发送日志 (`SystemSmsLog`) 记录了每次发送的详细信息，包括发送状态、错误码等，便于排查问题。

---