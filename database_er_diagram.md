```mermaid
erDiagram
    %% =================================
    %% System & User Management Tables
    %% (系统与用户管理表)
    %% =================================
    sys_user {
        bigint user_id PK "User ID"
        varchar(30) user_name "User Account"
        varchar(30) nick_name "User Nickname"
        varchar(50) email "User Email"
        varchar(11) mobile "Mobile Phone Number"
        char(1) sex "Gender (0:M, 1:F, 2:?)"
        char(1) status "Status (0:OK, 1:Disabled)"
    }

    sys_role {
        bigint role_id PK "Role ID"
        varchar(30) role_name "Role Name"
        varchar(100) role_key "Role Permission String"
        char(1) status "Status (0:OK, 1:Disabled)"
    }

    sys_menu {
        bigint menu_id PK "Menu ID"
        varchar(50) menu_name "Menu Name"
        bigint parent_id "Parent Menu ID"
        varchar(200) path "Routing Address"
        char(1) menu_type "Menu Type (M, C, F)"
    }

    sys_user_role {
        bigint user_id PK,FK "User ID"
        bigint role_id PK,FK "Role ID"
    }

    sys_role_menu {
        bigint role_id PK,FK "Role ID"
        bigint menu_id PK,FK "Menu ID"
    }

    sys_role_workshop {
        bigint role_id PK,FK "Role ID"
        bigint workshop_id PK,FK "Workshop ID"
    }

    %% =================================
    %% Customer & Order Tables
    %% (客户与订单表)
    %% =================================
    customer_info {
        bigint id PK "Customer ID"
        varchar(64) real_name "Real Name"
        varchar(16) mobile "Mobile (Unique)"
        char(1) gender "Gender"
    }

    customer_shipping_address {
        bigint id PK "Address ID"
        bigint customer_id FK "Customer ID"
        varchar(64) name "Consignee Name"
        varchar(16) mobile "Consignee Mobile"
        varchar(1024) address "Address"
    }

    "order" {
        bigint id PK "Order ID"
        varchar(64) order_no UK "Order No"
        bigint customer_id FK "Customer ID"
        varchar(20) type "Order Type"
        varchar(255) status "Order Status"
        decimal(10,2) grand_total "Grand Total"
        datetime order_time "Order Time"
        bigint workshop_id FK "Workshop ID"
        bigint service_id FK "Service ID"
        bigint package_id FK "Package ID"
    }

    order_product {
        bigint id PK
        varchar(64) order_no FK "Order No"
        bigint product_id FK "Product ID"
        varchar(64) product_name "Product Name"
        int quantity "Quantity"
        decimal(10,2) actual_price "Actual Price"
    }

    order_delivery {
        bigint id PK
        varchar(64) order_no UK,FK "Order No"
        varchar(64) name "Consignee Name"
        varchar(64) mobile "Consignee Mobile"
        varchar(1024) address "Shipping Address"
    }

    order_pay {
        bigint id PK
        varchar(64) order_no UK,FK "Order No"
        varchar(64) pay_no UK "Payment No"
        decimal(10,2) grand_total "Pay Total"
        varchar(64) pay_method "Payment Method"
    }

    order_status_log {
        bigint id PK
        varchar(64) order_no FK "Order No"
        varchar(20) status "Status"
        varchar(255) remark "Remark"
    }

    %% =================================
    %% Product & Service Tables
    %% (产品与服务表)
    %% =================================
    product {
        bigint id PK "Product ID"
        varchar(255) name "Name"
        int status "Status (1:Listed, 2:Unlisted)"
        bigint category_id FK "Category ID"
        bigint brand_id FK "Brand ID"
        decimal(10,2) price "Price"
        bigint service_id FK "Service ID"
    }

    product_category {
        bigint id PK "Category ID"
        varchar(255) name "Name"
        bigint parent_id "Parent Category ID"
    }

    product_brand {
        bigint id PK "Brand ID"
        varchar(255) name "Name"
    }

    service_info {
        bigint id PK "Service ID"
        varchar(255) name "Name"
        decimal(10,2) fee "Service Fee"
    }

    package_info {
        bigint id PK "Package ID"
        varchar(255) name "Name"
        bigint service_id FK "Service ID"
        decimal(10,2) price "Price"
    }

    package_product {
        bigint id PK
        bigint package_id FK "Package ID"
        bigint product_id FK "Product ID"
    }

    %% =================================
    %% Workshop Tables
    %% (修理车间表)
    %% =================================
    workshop {
        bigint id PK "Workshop ID"
        varchar(255) name UK "Name"
        int status "Status (1:Active, 2:Inactive)"
        varchar(1024) address "Address"
    }

    service_workshop {
        bigint id PK
        bigint service_id FK "Service ID"
        bigint workshop_id FK "Workshop ID"
    }

    %% =================================
    %% Content Management Tables
    %% (内容管理表)
    %% =================================
    content_template {
        bigint id PK
        varchar(64) code UK "Template Code"
        varchar(64) title "Title"
    }

    content_layout {
        bigint id PK
        varchar(64) template_code FK "Template Code"
        varchar(64) title "Layout Title"
        bigint seq "Sequence"
    }

    content_resource {
        bigint id PK
        varchar(64) title "Resource Title"
        varchar(255) pict_uri "Picture URI"
        varchar(255) entry_uri "Entry URI"
    }

    content_layout_resource {
        bigint id PK
        bigint layout_id FK "Layout ID"
        bigint resource_id FK "Resource ID"
    }


    %% =================================
    %% Relationships
    %% (关系)
    %% =================================
    sys_user ||--|{ sys_user_role : "has"
    sys_role ||--|{ sys_user_role : "has"
    sys_role ||--|{ sys_role_menu : "defines"
    sys_menu ||--|{ sys_role_menu : "is_defined_by"
    sys_role ||--|{ sys_role_workshop : "assigned_to"
    workshop ||--|{ sys_role_workshop : "has_roles"

    customer_info ||--o{ customer_shipping_address : "has"
    customer_info ||--o{ "order" : "places"

    "order" ||--|{ order_product : "contains"
    "order" ||--o{ order_delivery : "ships_to"
    "order" ||--o{ order_pay : "is_paid_by"
    "order" ||--|{ order_status_log : "has_history"

    product_category ||--o{ product : "belongs_to"
    product_brand ||--o{ product : "belongs_to"
    service_info ||--o{ product : "related_to"
    service_info ||--o{ package_info : "can_be"
    service_info ||--|{ service_workshop : "provided_by"
    workshop ||--|{ service_workshop : "provides"

    package_info ||--|{ package_product : "contains"
    product ||--|{ package_product : "is_part_of"

    workshop ||--o{ "order" : "fulfills"

    content_template ||--|{ content_layout : "has"
    content_layout ||--|{ content_layout_resource : "maps_to"
    content_resource ||--|{ content_layout_resource : "is_mapped_by"
```