package com.servauto.admin.service;

import cn.hutool.core.lang.Assert;
import com.servauto.admin.BaseTest;
import com.servauto.framework.file.api.FileApi;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class FileServiceTest extends BaseTest {

    @Resource
    private FileApi fileApi;

    @Test
    public void uploadTest() throws IOException {
        String home = System.getProperty("user.home");
        File file = new File(home + "/Downloads/WechatIMG8.jpeg");
        byte[] content = fileToByteArrayUsingFileInputStream(file);
        String path = fileApi.createFile("test", "123/456/WechatIMG8.jpeg", content);
        Assert.notBlank(path);
        System.out.println(path);
    }

    public static byte[] fileToByteArrayUsingFileInputStream(File file) throws IOException {
        byte[] buffer = new byte[(int) file.length()];
        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(buffer);
        }
        return buffer;
    }

}
