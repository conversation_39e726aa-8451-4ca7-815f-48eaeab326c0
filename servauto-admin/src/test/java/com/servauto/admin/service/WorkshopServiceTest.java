package com.servauto.admin.service;

import com.alibaba.fastjson2.JSONObject;
import com.servauto.admin.BaseTest;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.service.workshop.WorkshopService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

public class WorkshopServiceTest extends BaseTest {

    @Resource
    private WorkshopService workshopService;

    @Test
    public void test() throws Throwable {
        WorkshopDetailDTO workshopDetail = workshopService.getWorkshopDetail(1L);
        System.out.println(JSONObject.from(workshopDetail));
    }

}
