package com.servauto.admin.service;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.servauto.admin.BaseTest;
import com.servauto.admin.constants.RedisKeyConstants;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.parse.JacksonSerializer;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

public class LocationTest extends BaseTest {

    @Resource
    private LocationService locationService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    String AREA_KEY = "LOCATIONS:AREAS-NEW";
    String STATE_KEY = "LOCATIONS:STATES-NEW";


    @Test
    public void testGetAreas() {
        List<AreaDTO> areaList = locationService.getAreasByParentCode("melaka");
        Assert.notNull(areaList);

        Object value = redisTemplate.opsForValue().get(String.format(AREA_KEY + ":%s", "melaka"));
        Assert.notNull(value);
    }


    @Test
    public void testGetStates() {
        StateDTO state= locationService.getStateByCode("melaka");
        Assert.notNull(state);

        Object value = redisTemplate.opsForValue().get(String.format(STATE_KEY + ":%s", "melaka"));
        Assert.notNull(value);

        StateDTO stateCache = JacksonSerializer.deSerialize(JacksonSerializer.serialize(value), new TypeReference<>() {
        });
        Assert.notNull(stateCache);
    }


}
