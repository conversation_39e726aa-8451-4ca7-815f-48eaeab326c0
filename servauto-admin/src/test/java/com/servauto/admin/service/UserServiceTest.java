package com.servauto.admin.service;

import com.servauto.admin.BaseTest;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.service.TokenService;
import com.servauto.admin.service.system.SysUserService;
import io.jsonwebtoken.Jwts;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

public class UserServiceTest extends BaseTest {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private RedissonClient redissonClient;


    @Test
    public void queryUserTest() {
        SysUser sysUser = sysUserService.selectUserById(1L);
        Assertions.assertNotNull(sysUser);
        System.out.println(sysUser);
        RLock lock = redissonClient.getLock(sysUser.getUserName());
        try {
            lock.lock(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    @Test
    public void testUserToken() {
        String token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjE0MTAzNTIzNTQ2NTM5MDA4MDAifQ.PPMv3tqG3mIrdC3N3sQOXYd6OyNihYdtJzVFEbeE7VzjmA2rqeWEp6mVwtjQ0ahaH5or7IaK9OpCx3l7OiJsUA";
        String signingKey = "abcdefghijklmnopqrstuvwxy1";
        System.out.println(Jwts.parser().setSigningKey(signingKey).parseClaimsJws(token).getBody());
    }

}
