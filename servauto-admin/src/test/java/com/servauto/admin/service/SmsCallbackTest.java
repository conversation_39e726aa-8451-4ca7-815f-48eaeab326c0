package com.servauto.admin.service;

import com.servauto.admin.BaseTest;
import com.servauto.framework.sms.enums.SmsChannelEnum;
import com.servauto.framework.sms.service.SmsSendService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

/**
 * <p>SmsCallbackTest</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/12 17:25
 */
public class SmsCallbackTest extends BaseTest {

    @Resource
    private SmsSendService smsSendService;

    @Test
    public void testReceiveYCloudSmsStatus() throws Throwable {
        String body = "{\n" +
                "  \"id\": \"evt_6821bb7e31e170171871ae39\",\n" +
                "  \"type\": \"whatsapp.message.updated\",\n" +
                "  \"apiVersion\": \"v2\",\n" +
                "  \"createTime\": \"2025-05-12T09:12:30.118Z\",\n" +
                "  \"whatsappMessage\": {\n" +
                "    \"id\": \"6821bb7addd76f030673c5fd\",\n" +
                "    \"wamid\": \"wamid.HBgLNjAxMDI3MTgzMjkVAgARGBJFMDM4MzdFQzM3MzBGQTUyQkQA\",\n" +
                "    \"status\": \"delivered\",\n" +
                "    \"from\": \"+601128407382\",\n" +
                "    \"to\": \"+60102718329\",\n" +
                "    \"wabaId\": \"393121193881329\",\n" +
                "    \"externalId\": \"1605\",\n" +
                "    \"type\": \"template\",\n" +
                "    \"template\": {\n" +
                "      \"name\": \"template_authentication_login\",\n" +
                "      \"language\": {\n" +
                "        \"code\": \"en_US\",\n" +
                "        \"policy\": \"deterministic\"\n" +
                "      },\n" +
                "      \"components\": [\n" +
                "        {\n" +
                "          \"type\": \"body\",\n" +
                "          \"parameters\": [\n" +
                "            {\n" +
                "              \"type\": \"text\",\n" +
                "              \"text\": \"999999\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"type\": \"button\",\n" +
                "          \"sub_type\": \"url\",\n" +
                "          \"index\": 0,\n" +
                "          \"parameters\": [\n" +
                "            {\n" +
                "              \"type\": \"text\",\n" +
                "              \"text\": \"999999\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"conversation\": {\n" +
                "      \"id\": \"1985184dd0d8e09ecb2901d0f2255735\",\n" +
                "      \"type\": \"REGULAR\",\n" +
                "      \"originType\": \"authentication\",\n" +
                "      \"expireTime\": \"2025-05-13T08:51:07.000Z\"\n" +
                "    },\n" +
                "    \"createTime\": \"2025-05-12T09:12:26.058Z\",\n" +
                "    \"updateTime\": \"2025-05-12T09:12:30.109Z\",\n" +
                "    \"sendTime\": \"2025-05-12T09:12:27.000Z\",\n" +
                "    \"deliverTime\": \"2025-05-12T09:12:29.000Z\",\n" +
                "    \"totalPrice\": 0,\n" +
                "    \"pricingCategory\": \"authentication\",\n" +
                "    \"currency\": \"USD\",\n" +
                "    \"regionCode\": \"MY\",\n" +
                "    \"bizType\": \"whatsapp\"\n" +
                "  }\n" +
                "}";
        smsSendService.receiveSmsStatus(SmsChannelEnum.YCloud_SMS.getCode(), "", body);
    }
}
