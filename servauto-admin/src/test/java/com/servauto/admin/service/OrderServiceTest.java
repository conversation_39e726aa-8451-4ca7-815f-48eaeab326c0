package com.servauto.admin.service;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.BaseTest;
import com.servauto.admin.enums.order.OrderDeliveryTypeEnum;
import com.servauto.admin.enums.order.OrderStatusEnum;
import com.servauto.admin.model.dto.request.order.DeliveryOrderReqDTO;
import com.servauto.admin.model.dto.request.order.PageOrderReqDTO;
import com.servauto.admin.model.dto.request.order.RescheduleReqDTO;
import com.servauto.admin.model.dto.response.order.OrderDTO;
import com.servauto.admin.model.dto.response.order.PageOrderDTO;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.service.order.OrderService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;

/**
 * <p>OrderServiceTest</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 10:52
 */
public class OrderServiceTest extends BaseTest {

    @Resource
    private OrderService orderService;

    @Test
    public void testPageOrders() {
        PageInfo<PageOrderDTO> pageInfo = orderService.pageOrders(1, 10, PageOrderReqDTO.builder()
                .deliveryType(OrderDeliveryTypeEnum.PICKUP.getCode())
                .workShopIds(List.of(1L))
                .status(OrderStatusEnum.PENDING_PAY.getCode())
                .createBeginTime(new Date(1745211221000L))
                .createEndTime(new Date(System.currentTimeMillis()))
                .build());
        Assertions.assertTrue(CollectionUtils.isNotEmpty(pageInfo.getList()));
        System.out.println(pageInfo.getList());
    }

    @Test
    public void testQueryOrderByOrderNo() {
        OrderDTO orderDTO = orderService.queryOrderByOrderNo("1397556269191790592");
        Assertions.assertNotNull(orderDTO);
        System.out.println(orderDTO);
    }

    @Test
    public void testDeliveryOrder() {
        SysUser sysUser = new SysUser();
        sysUser.setUserName("admin");
        LoginUser user = new LoginUser(1L, sysUser, null);
        orderService.deliveryOrder(user, DeliveryOrderReqDTO.builder()
                .orderNo("1397626408759197696")
                .deliveryTime(System.currentTimeMillis())
                .remark("hello delivery")
                .images(List.of("http://39.99.196.38:8080/file/api/29/get/83b2636e85f5c300bc34e98544e80015d6c6506f96d01f479bdf7d07d78e5cc0.png"))
                .build());
    }

    @Test
    public void testReschedule() {
        SysUser sysUser = new SysUser();
        sysUser.setUserName("admin");
        LoginUser user = new LoginUser(1L, sysUser, null);
        orderService.reschedule(user, RescheduleReqDTO.builder()
                .orderNo("1397897310377869312")
                .reservationTime(1745942400000L)
                .workshopId(7L)
                .build()
        );
    }

}
