package com.servauto.admin;

import com.github.pagehelper.PageInfo;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.parse.JacksonSerializer;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;


@SpringBootTest
@ActiveProfiles("local")
public class BaseTest {

    public static void main(String[] args) {
        System.out.println( JacksonSerializer.serialize(CommonResult.success(PageInfo.of(new ArrayList<>()))));
    }



}
