spring.profiles.active=dev
server.port=8080
system.timezone=Asia/Shanghai
logging.level.com.servauto=debug
logging.level.org.springframework=warn

user.password.maxRetryCount=5
user.password.lockTime=10

spring.datasource.druid.master.url=**********************************************************************************************************************************************************************
spring.datasource.druid.master.username=servauto
spring.datasource.druid.master.password=servauto@123
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.login-username=
spring.datasource.druid.stat-view-servlet.login-password=
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=100


spring.data.redis.host=************
spring.data.redis.port=6679
spring.data.redis.database=1

# token配置
token.header=Authorization
token.secret=abcdefghijklmnopqrstuvwxyz
token.expireTime=30

# Springdoc配置
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui
springdoc.default-flat-param-object=true
knife4j.enable=true
knife4j.setting.language=EN
swagger.author=servauto
swagger.description=admin system interface
swagger.title=Servauto swagger platform
swagger.url=http://127.0.0.1:8080
swagger.email=<EMAIL>
swagger.license-url=http://127.0.0.1:8080
swagger.license=MIT
swagger.version=1.0

webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.confirm.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.book.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.completed.secret=2FslWuOqM6AjPxU3rvzXCc