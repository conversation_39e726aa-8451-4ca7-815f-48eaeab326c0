spring.profiles.active=uat
server.port=8080
system.timezone=Asia/Shanghai

# log config
logging.level.com.servauto=info
logging.level.org.springframework=warn

# password config
user.password.maxRetryCount=5
user.password.lockTime=10

# druid config
spring.datasource.druid.master.url=**************************************************************************************************************************
spring.datasource.druid.master.username=test
spring.datasource.druid.master.password=123456

# redis config
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=0

# token config
token.header=Authorization
token.secret=abcdefghijklmnopqrstuvwxyz
token.expireTime=30

# Springdoc config
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
knife4j.enable=false
knife4j.production=true


webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.confirm.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.book.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.completed.secret=2FslWuOqM6AjPxU3rvzXCc