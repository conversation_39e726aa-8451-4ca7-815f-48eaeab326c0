spring.profiles.active=test
server.port=8080
system.timezone=Asia/Shanghai

# log config
logging.level.com.servauto=debug
logging.level.org.springframework=warn

# password config
user.password.maxRetryCount=5
user.password.lockTime=10

# druid config
spring.datasource.druid.master.url=***********************************************************************************************************************************************************************
spring.datasource.druid.master.username=servauto
spring.datasource.druid.master.password=servauto@123
# just test env
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.login-username=admin
spring.datasource.druid.stat-view-servlet.login-password=admin123
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=100

# redis config
spring.data.redis.host=************
spring.data.redis.port=6679
spring.data.redis.database=0

# token config
token.header=Authorization
token.secret=abcdefghijklmnopqrstuvwxy1
token.expireTime=1440

# Springdoc config
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
knife4j.production=true
knife4j.enable=false

sms-code.enable=false

webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.confirm.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.book.secret=2FslWuOqM6AjPxU3rvzXCc

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/fb7fe9c4-d4a8-49b6-94ce-6ef29fcc15ab
webHooks.completed.secret=2FslWuOqM6AjPxU3rvzXCc
