spring.profiles.active=prod
server.port=8080
system.timezone=Asia/Shanghai

# log config
logging.level.com.servauto=info
logging.level.org.springframework=error

# password config
user.password.maxRetryCount=5
user.password.lockTime=10

# druid config
spring.datasource.druid.master.url=**************************************************************************************************************************
spring.datasource.druid.master.username=test
spring.datasource.druid.master.password=123456

# redis config
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.password=

# token config
token.header=Authorization
token.secret=abcdefghij123mnopqrstuvwxyz
token.expireTime=30

# Springdoc config
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
knife4j.production=true
knife4j.enable=false


#Lark config
webHooks.confirm.url=https://open.larksuite.com/open-apis/bot/v2/hook/c7abd825-5272-4a19-97e2-a3166e935542
webHooks.confirm.secret=GZiVlY23talducp0g6Q5Jh

webHooks.book.url=https://open.larksuite.com/open-apis/bot/v2/hook/574e63c5-8e2d-4f67-8460-016a673913a3
webHooks.book.secret=6tSjOixuuPEVuPdGN4XsSh

webHooks.completed.url=https://open.larksuite.com/open-apis/bot/v2/hook/5a5be35e-4352-4629-9fa7-3044d894e1a9
webHooks.completed.secret= o7eJwLwEWZDE3UfpU3CZsh
