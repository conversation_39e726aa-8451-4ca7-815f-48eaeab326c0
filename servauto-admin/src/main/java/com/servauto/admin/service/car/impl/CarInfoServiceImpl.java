package com.servauto.admin.service.car.impl;

import com.servauto.admin.dao.car.CarExtraMapper;
import com.servauto.admin.dao.car.CarInfoMapper;
import com.servauto.admin.dao.car.CarLibraryMapper;
import com.servauto.admin.model.dto.response.car.CarInfoDTO;
import com.servauto.admin.model.entity.car.*;
import com.servauto.admin.service.car.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class CarInfoServiceImpl implements CarInfoService {

    @Resource
    private CarInfoMapper carInfoMapper;
    @Resource
    private CarExtraMapper carExtraMapper;
    @Resource
    private CarLibraryMapper carLibraryMapper;

    @Resource
    private CarBrandsService carBrandsService;
    @Resource
    private CarModelsService carModelsService;
    @Resource
    private CarYearsService carYearsService;
    @Resource
    private CarExtraService carExtraService;
    // 2. 定义字段名常量，减少硬编码
    final String BRAND_IDS = "brandIds";
    final String MODEL_IDS = "modelIds";
    final String YEAR_IDS = "yearIds";
    final String VARIANT_IDS = "variantIds";

    @Override
    public List<CarInfo> selectByCustomerId(Long customerId) {
        return carInfoMapper.selectByCustomerId(customerId);
    }

    @Override
    public int deleteByCustomerId(Long customerId) {
        return carInfoMapper.deleteByCustomerId(customerId);
    }


    @Override
    public List<CarInfoDTO> carInfoList(Long customerId) {
        // 查询 carInfo 列表
        List<CarInfo> carInfoList = carInfoMapper.selectByCustomerId(customerId);
        if (carInfoList == null || carInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            // 提取 idMaps 和构建 CarInfoDTO
            return buildCarInfoDTOList(carInfoList);
        } catch (Exception e) {
            // 异常处理
            return Collections.emptyList();
        }
    }


    private Map<String, List<Long>> extractIds(List<CarInfo> carInfoList) {
        // 1. 非空校验，防止 NullPointerException
        if (carInfoList == null) {
            throw new IllegalArgumentException("Input carInfoList cannot be null");
        }

        // 3. 初始化 Map 和字段对应的 Set（用于去重）
        Map<String, List<Long>> idMaps = new HashMap<>();
        Set<Long> brandIdsSet = new HashSet<>();
        Set<Long> modelIdsSet = new HashSet<>();
        Set<Long> yearIdsSet = new HashSet<>();
        Set<Long> variantIdsSet = new HashSet<>();

        // 4. 使用 Stream API 提高代码简洁性和性能
        carInfoList.forEach(carInfo -> {
            if (carInfo.getBrandId() != null) {
                brandIdsSet.add(carInfo.getBrandId());
            }
            if (carInfo.getModelId() != null) {
                modelIdsSet.add(carInfo.getModelId());
            }
            if (carInfo.getYearId() != null) {
                yearIdsSet.add(carInfo.getYearId());
            }
            if (carInfo.getVariantId() != null) {
                variantIdsSet.add(carInfo.getVariantId());
            }
        });

        // 5. 将 Set 转换为 List 并添加到 Map 中
        idMaps.put(BRAND_IDS, new ArrayList<>(brandIdsSet));
        idMaps.put(MODEL_IDS, new ArrayList<>(modelIdsSet));
        idMaps.put(YEAR_IDS, new ArrayList<>(yearIdsSet));
        idMaps.put(VARIANT_IDS, new ArrayList<>(variantIdsSet));

        return idMaps;
    }

    // 辅助方法：获取 ID 列表并处理空值
    private List<Long> getIdList(Map<String, List<Long>> idMaps, String key) {
        List<Long> idList = idMaps.get(key);
        return (idList != null) ? idList : Collections.emptyList();
    }
    // 辅助方法：构建 CarInfoDTO 列表
    private List<CarInfoDTO> buildCarInfoDTOList(List<CarInfo> carInfoList) {
        // 提取 carLibIds 和 brandIds
        Map<String, List<Long>> idMaps = extractIds(carInfoList);
        List<Long> brandIds = getIdList(idMaps, BRAND_IDS);
        List<Long> modelIds = getIdList(idMaps, MODEL_IDS);
        List<Long> yearIds = getIdList(idMaps, YEAR_IDS);
        List<Long> variantIds = getIdList(idMaps, VARIANT_IDS);

        Map<Long, CarBrands> carBrandsMap = carBrandsService.selectCarBrandsMap(brandIds);
        Map<Long, CarModels> carModelsMap = carModelsService.selectCarModelsMap(modelIds);
        Map<Long, CarYears> carYearsMap = carYearsService.selectCarYearsMap(yearIds);
        Map<Long, CarExtra> carExtrasMap = carExtraService.selectVariantsMap(variantIds);

        // 拼装数据
        return carInfoList.stream().map(carInfo -> {
            CarBrands carBrand = carBrandsMap.get(carInfo.getBrandId());
            CarModels carModels = carModelsMap.get(carInfo.getModelId());
            CarYears carYears = carYearsMap.get(carInfo.getYearId());
            CarExtra carExtra = carExtrasMap.get(carInfo.getVariantId());
            CarInfoDTO carInfoDTO = new CarInfoDTO();
            carInfoDTO.setBrandId(carInfo.getBrandId());
            carInfoDTO.setModelId(carInfo.getModelId());
            carInfoDTO.setYearId(carInfo.getYearId());
            carInfoDTO.setVariantId(carInfo.getVariantId());
            carInfoDTO.setLicensePlate(carInfo.getLicensePlate());
            carInfoDTO.setCarMileage(carInfo.getCarMileage());
            carInfoDTO.setCarId(carInfo.getId());
            carInfoDTO.setCarName(carBrand.getBrandName() + " " + carModels.getModelName() + " " + carYears.getYearValue() + " " + carExtra.getVariantName());
            return carInfoDTO;
        }).toList();
    }
}
