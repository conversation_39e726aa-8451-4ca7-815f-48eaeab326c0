package com.servauto.admin.service.product.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.dao.packageinfo.PackageProductMapper;
import com.servauto.admin.dao.product.*;
import com.servauto.admin.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.admin.enums.DeliveryMode;
import com.servauto.admin.enums.ProductImageType;
import com.servauto.admin.factory.product.ProductAttributeFactory;
import com.servauto.admin.factory.product.ProductFactory;
import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.dto.request.product.UpdateProductDTO;
import com.servauto.admin.model.dto.response.product.*;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.system.TypeNameDTO;
import com.servauto.admin.model.entity.product.*;
import com.servauto.admin.model.entity.serviceinfo.ServiceInfo;
import com.servauto.admin.service.product.ProductService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.servauto.admin.enums.ServiceSupportedOn.PRODUCT;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Resource
    private ProductAttributeMapper productAttributeMapper;

    @Resource
    private ProductAttributeOptionMapper productAttributeOptionMapper;

    @Resource
    private ProductAttributeDisplayMapper productAttributeDisplayMapper;

    @Resource
    private ProductAttributeValueMapper productAttributeValueMapper;

    @Resource
    private ProductBrandMapper productBrandMapper;

    @Resource
    private ProductDetailImageMapper productDetailImageMapper;

    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private PackageProductMapper packageProductMapper;

    @Resource
    private SysUserService sysUserService;

    public ProductDTO getProduct(long id) {
        if (id == 0) {
            throw new RuntimeException("id is 0");
        }

        // build product base info
        var productInfo = productMapper.selectByPrimaryKey(id);
        if (productInfo == null || productInfo.getDeleted()) {
            throw new RuntimeException("product not found");
        }
        var productDTO = ProductFactory.convert(productInfo);

        // build product details
        setProductDetails(productDTO);

        // build product attribute values
        setAttributeValues(List.of(productDTO));

        // build other info
        if (!productInfo.getFeaturedTags().isEmpty()) {
            productDTO.setFeaturedTags(Arrays.stream(productInfo.getFeaturedTags().split(",")).toList());
        }
        productDTO.setDeliveryModes(getDeliveryModeString(productInfo));

        // build package info
        var packageProductList = packageProductMapper.selectByProductId(id);
        if (CollectionUtils.isNotEmpty(packageProductList)) {
            productDTO.setAssociatePackage(true);
        }
        return productDTO;
    }

    public List<ProductBaseDTO> getProducts(QueryProductsDTO reqDTO) {
        validateQueryProductsDTO(reqDTO);

        // build product base info
        var products = productMapper.selectByConditions(reqDTO);
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }
        List<ProductBaseDTO> baseDTOs = products.stream().map(ProductFactory::convertBase).toList();

        // build product details
        setProductDetailsToBaseDTO(baseDTOs);

        // build attribute value
        if (Boolean.TRUE.equals(reqDTO.getIsNeedAttributeValue())) {
            setAttributeValuesToBaseDTO(baseDTOs);
        }

        // build other info
        HashMap<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, product -> product, (existing, replacement) -> existing, HashMap::new));
        for (var dst : baseDTOs) {
            var src = productMap.get(dst.getId());
            if (!src.getFeaturedTags().isEmpty()) {
                dst.setFeaturedTags(Arrays.stream(src.getFeaturedTags().split(",")).toList());
            }
            dst.setDeliveryModes(getDeliveryModes(src));
            if (Boolean.TRUE.equals(reqDTO.getIsNeedUpdaterName()) && dst.getUpdatedBy() > 0) {
                dst.setUpdaterName(sysUserService.selectUserById(dst.getUpdatedBy()).getUserName());
            }
        }
        return baseDTOs;
    }

    public PageInfo<ProductBaseDTO> pageProducts(QueryProductsDTO reqDTO) {
        validateQueryProductsDTO(reqDTO);

        // query products
        PageSupport.startPage(reqDTO.getPageNo(), reqDTO.getPageSize());
        var products = productMapper.selectByConditions(reqDTO);
        PageInfo<Product> pageInfo = PageInfo.of(products);

        if (CollectionUtils.isEmpty(products)) {
            PageInfo<ProductBaseDTO> result = new PageInfo<>();
            result.setPageNum(pageInfo.getPageNum());
            result.setPages(pageInfo.getPages());
            result.setTotal(pageInfo.getTotal());
            return result;
        }

        // build product base info
        List<ProductBaseDTO> baseDTOs = products.stream().map(ProductFactory::convertBase).toList();

        // build product details
        setProductDetailsToBaseDTO(baseDTOs);

        // build attribute value
        if (Boolean.TRUE.equals(reqDTO.getIsNeedAttributeValue())) {
            setAttributeValuesToBaseDTO(baseDTOs);
        }

        // build other info
        HashMap<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, product -> product, (existing, replacement) -> existing, HashMap::new));
        for (var dst : baseDTOs) {
            var src = productMap.get(dst.getId());
            if (!src.getFeaturedTags().isEmpty()) {
                dst.setFeaturedTags(Arrays.stream(src.getFeaturedTags().split(",")).toList());
            }
            dst.setDeliveryModes(getDeliveryModes(src));
            if (Boolean.TRUE.equals(reqDTO.getIsNeedUpdaterName()) && dst.getUpdatedBy() > 0) {
                dst.setUpdaterName(sysUserService.selectUserById(dst.getUpdatedBy()).getUserName());
            }
        }

        // build page info
        PageInfo<ProductBaseDTO> result = PageInfo.of(baseDTOs);
        result.setPageNum(pageInfo.getPageNum());
        result.setPages(pageInfo.getPages());
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    public void updateProduct(UpdateProductDTO updateProductDTO) {
        if (updateProductDTO.getId() == 0) {
            throw new RuntimeException("id is 0");
        }
        validateCorrelationId(updateProductDTO);

        Product info = buildProductByUpdateProductDTO(updateProductDTO);

        int rows = productMapper.updateByPrimaryKeySelective(info);
        if (rows == 0) {
            throw BusinessException.of("update product failed");
        }

        productDetailImageMapper.deleteByProductId(updateProductDTO.getId());
        productAttributeValueMapper.deleteByProductId(updateProductDTO.getId());
        handleDetailImagesAndAttributeValues(info.getId(), updateProductDTO);
    }

    public void insertProduct(UpdateProductDTO updateProductDTO) {
        validateCorrelationId(updateProductDTO);

        Product info = buildProductByUpdateProductDTO(updateProductDTO);

        int rows = productMapper.insertSelective(info);
        if (rows == 0) {
            throw BusinessException.of("insert product failed");
        }

        handleDetailImagesAndAttributeValues(info.getId(), updateProductDTO);
    }

    public List<IdNameDTO> getProductCategories() {
        List<ProductCategory> productCategories = productCategoryMapper.selectAll();
        List<IdNameDTO> idNameDTOList = new ArrayList<>();
        if (productCategories != null) {
            idNameDTOList = productCategories.stream().map(e -> IdNameDTO.builder().id(e.getId()).name(e.getName()).build()).toList();
        }
        return idNameDTOList;
    }

    public List<IdNameDTO> getProductBrands() {
        List<ProductBrand> productBrands = productBrandMapper.selectAll();
        List<IdNameDTO> idNameDTOList = new ArrayList<>();
        if (productBrands != null) {
            idNameDTOList = productBrands.stream().map(e -> IdNameDTO.builder().id(e.getId()).name(e.getName()).build()).toList();
        }
        return idNameDTOList;
    }

    public List<ProductAttributeDTO> getProductAttributes(String displayType, long categoryId) {
        List<ProductAttribute> attributes = productAttributeMapper.selectByCategoryId(categoryId);
        List<ProductAttributeDTO> attributeDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(attributes)) {
            return attributeDTOS;
        }
        if (displayType == null || displayType.isEmpty() || displayType.equals("product")) {
            var totalAttributes = attributes.stream().map(ProductAttributeFactory::convert).toList();
            setAttributeOptions(totalAttributes);
            return totalAttributes;
        }

        HashMap<Long, ProductAttribute> attrHashMap = attributes.stream().collect(Collectors.toMap(ProductAttribute::getId, attribute -> attribute, (existing, replacement) -> existing, HashMap::new));
        var attributeDisplay = productAttributeDisplayMapper.selectByTypeAndCategoryId(displayType, categoryId);
        if (attributeDisplay == null) {
           return attributeDTOS;
        }

        List<Long> attributeIds = Arrays.stream(attributeDisplay.getAttributeIds().split(",")).map(Long::parseLong).toList();
        for (var id : attributeIds) {
            ProductAttribute attr = attrHashMap.get(id);
            if (attr == null) {
                throw BusinessException.of("product attribute not found: ", id.toString());
            }
            attributeDTOS.add(ProductAttributeFactory.convert(attr));
        }
        attributeDTOS.sort(Comparator.comparing(ProductAttributeDTO::getOrder));
        setAttributeOptions(attributeDTOS);
        return attributeDTOS;
    }

    private void validateQueryProductsDTO(QueryProductsDTO reqDTO) {
        if (reqDTO.getCategoryId() != null && reqDTO.getCategoryId() != 0) {
            if (reqDTO.getCategoryIds() == null) {
                reqDTO.setCategoryIds(new ArrayList<>());
            }
            reqDTO.getCategoryIds().add(reqDTO.getCategoryId());
            reqDTO.setCategoryIds(reqDTO.getCategoryIds().stream().distinct().toList());
        }

        if (reqDTO.getBrandId() != null && reqDTO.getBrandId() != 0) {
            if (reqDTO.getBrandIds() == null) {
                reqDTO.setBrandIds(new ArrayList<>());
            }
            reqDTO.getBrandIds().add(reqDTO.getBrandId());
            reqDTO.setBrandIds(reqDTO.getBrandIds().stream().distinct().toList());
        }
    }

    private void setAttributeOptions(List<ProductAttributeDTO> attributes) {
        List<ProductAttributeOption> attributeOptions = productAttributeOptionMapper.selectByAttributeIds(attributes.stream().map(ProductAttributeDTO::getId).distinct().toList());

        HashMap<Long, List<ProductAttributeOption>> attributeOptionMap = new HashMap<>();
        for (ProductAttributeOption option : attributeOptions) {
            attributeOptionMap.computeIfAbsent(option.getAttributeId(), k -> new ArrayList<>()).add(option);
        }

        for (ProductAttributeDTO attr : attributes) {
            List<ProductAttributeOptionDTO> options = attributeOptionMap.getOrDefault(attr.getId(), new ArrayList<>())
                    .stream()
                    .map(option -> ProductAttributeOptionDTO.builder()
                            .value(option.getValue())
                            .label(option.getLabel())
                            .order(option.getSortOrder())
                            .build())
                    .sorted((o1, o2) -> Integer.compare(o1.getOrder(), o2.getOrder())) // Sort by order
                    .collect(Collectors.toList());

            attr.setOptions(options);
        }
    }

    private void setProductDetails(ProductDTO productDTO) {
        // build product category name
        var categoryInfo = productCategoryMapper.selectByPrimaryKey(productDTO.getCategoryId());
        if (categoryInfo == null) {
            throw new RuntimeException("product category not found");
        }
        productDTO.setCategoryName(categoryInfo.getName());

        // build product brand name
        var brandInfo = productBrandMapper.selectByPrimaryKey(productDTO.getBrandId());
        if (brandInfo == null) {
            throw new RuntimeException("product brand not found");
        }
        productDTO.setBrandName(brandInfo.getName());

        // build service name
        if (productDTO.getServiceId() != null && productDTO.getServiceId() > 0) {
            var serviceInfo = serviceInfoMapper.selectByPrimaryKey(productDTO.getServiceId());
            if (serviceInfo != null) {
                productDTO.setServiceName(serviceInfo.getName());
            }
        }

        // build product detail images
        var detailImages = productDetailImageMapper.selectByProductId(productDTO.getId());
        if (CollectionUtils.isNotEmpty(detailImages)) {
            productDTO.setCoverImages(new ArrayList<>());
            productDTO.setH5DetailImages(new ArrayList<>());
            for (var image : detailImages) {
                switch (ProductImageType.getByCode(image.getType())) {
                    case COVER_IMAGE:
                        productDTO.getCoverImages().add(image.getImage());
                        break;
                    case H5_DETAIL_IMAGE:
                        productDTO.getH5DetailImages().add(image.getImage());
                        break;
                    default:
                        log.warn("unknown product image type: {}", image.getType());
                        break;
                }
            }
        }
    }

    private void setProductDetailsToBaseDTO(List<ProductBaseDTO> dtos) {
        // build product category name
        List<Long> categoryIds = dtos.stream().map(ProductBaseDTO::getCategoryId).distinct().toList();
        HashMap<Long, ProductCategory> categoryMap = productCategoryMapper.selectByIds(categoryIds).stream().collect(Collectors.toMap(ProductCategory::getId, category -> category, (existing, replacement) -> existing, HashMap::new));
        for (var v : dtos) {
            v.setCategoryName(categoryMap.get(v.getCategoryId()).getName());
        }

        // build product brand name
        List<Long> brandIds = dtos.stream().map(ProductBaseDTO::getBrandId).distinct().toList();
        HashMap<Long, ProductBrand> brandMap = productBrandMapper.selectByIds(brandIds).stream().collect(Collectors.toMap(ProductBrand::getId, brand -> brand, (existing, replacement) -> existing, HashMap::new));
        for (var v : dtos) {
            v.setBrandName(brandMap.get(v.getBrandId()).getName());
        }

        // build service name
        List<Long> serviceIds = dtos.stream().map(ProductBaseDTO::getServiceId).distinct().filter(Objects::nonNull).filter(serviceId -> serviceId > 0).toList();
        if (!serviceIds.isEmpty()) {
            HashMap<Long, ServiceInfo> serviceInfoMap = serviceInfoMapper.selectByIds(serviceIds).stream().collect(Collectors.toMap(ServiceInfo::getId, serviceInfo -> serviceInfo, (existing, replacement) -> existing, HashMap::new));
            for (var v : dtos) {
                if (v.getServiceId() == null || v.getServiceId() == 0) {
                    continue;
                }
                v.setServiceName(serviceInfoMap.get(v.getServiceId()).getName());
            }
        }
    }

    private void setAttributeValues(List<ProductDTO> productDTOs) {
        if (CollectionUtils.isEmpty(productDTOs)) {
            return;
        }
        // Collect all product IDs
        List<Long> productIds = productDTOs.stream()
                .map(ProductDTO::getId)
                .distinct()
                .toList();

        // Batch query product attributes
        List<Long> categoryIds = productDTOs.stream()
                .map(ProductDTO::getCategoryId)
                .distinct()
                .toList();
        List<ProductAttribute> attributes = productAttributeMapper.selectByCategoryIds(categoryIds);

        if (CollectionUtils.isEmpty(attributes)) {
            return;
        }

        // Batch query product attribute values
        List<ProductAttributeValue> attributeValues = productAttributeValueMapper.selectByProductIds(productIds);

        // Batch query product attribute options
        List<Long> attributeIds = attributes.stream()
                .map(ProductAttribute::getId)
                .distinct()
                .toList();
        List<ProductAttributeOption> attributeOptions = productAttributeOptionMapper.selectByAttributeIds(attributeIds);

        // Build attribute map
        HashMap<Long, ProductAttribute> attributeMap = attributes.stream()
                .collect(Collectors.toMap(ProductAttribute::getId, attr -> attr, (existing, replacement) -> existing, HashMap::new));

        // Build attribute value map
        HashMap<Long, List<ProductAttributeValue>> attributeValueMap = new HashMap<>();
        for (ProductAttributeValue value : attributeValues) {
            attributeValueMap.computeIfAbsent(value.getProductId(), k -> new ArrayList<>()).add(value);
        }

        // Build attribute option map
        HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap = new HashMap<>();
        for (ProductAttributeOption option : attributeOptions) {
            attributeOptionMap.computeIfAbsent(option.getAttributeId(), k -> new HashMap<>()).put(option.getValue(), option);
        }

        // Set attribute values to product DTOs
        for (ProductDTO productDTO : productDTOs) {
            productDTO.setAttributeValues(getAttributeValues(productDTO.getId(), attributeMap, attributeValueMap, attributeOptionMap));
        }
    }

    private void setAttributeValuesToBaseDTO(List<ProductBaseDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        // Collect all product IDs
        List<Long> productIds = dtos.stream()
                .map(ProductBaseDTO::getId)
                .distinct()
                .toList();

        // Batch query product attributes
        List<Long> categoryIds = dtos.stream()
                .map(ProductBaseDTO::getCategoryId)
                .distinct()
                .toList();
        List<ProductAttribute> attributes = productAttributeMapper.selectByCategoryIds(categoryIds);

        if (CollectionUtils.isEmpty(attributes)) {
            return;
        }

        // Batch query product attribute values
        List<ProductAttributeValue> attributeValues = productAttributeValueMapper.selectByProductIds(productIds);

        // Batch query product attribute options
        List<Long> attributeIds = attributes.stream()
                .map(ProductAttribute::getId)
                .distinct()
                .toList();
        List<ProductAttributeOption> attributeOptions = productAttributeOptionMapper.selectByAttributeIds(attributeIds);

        // Build attribute map
        HashMap<Long, ProductAttribute> attributeMap = attributes.stream()
                .collect(Collectors.toMap(ProductAttribute::getId, attr -> attr, (existing, replacement) -> existing, HashMap::new));

        // Build attribute value map
        HashMap<Long, List<ProductAttributeValue>> attributeValueMap = new HashMap<>();
        for (ProductAttributeValue value : attributeValues) {
            attributeValueMap.computeIfAbsent(value.getProductId(), k -> new ArrayList<>()).add(value);
        }

        // Build attribute option map
        HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap = new HashMap<>();
        for (ProductAttributeOption option : attributeOptions) {
            attributeOptionMap.computeIfAbsent(option.getAttributeId(), k -> new HashMap<>()).put(option.getValue(), option);
        }

        // Set attribute values to product DTOs
        for (var productDTO : dtos) {
            var attrValues = getAttributeValues(productDTO.getId(), attributeMap, attributeValueMap, attributeOptionMap);
            HashMap<Long, List<String>> attrValueMap = attrValues.stream().collect(Collectors.toMap(ProductAttributeValueDTO::getId, ProductAttributeValueDTO::getLabels, (existing, replacement) -> existing, HashMap::new));
            productDTO.setAttributeValueMap(attrValueMap);
        }
    }

    private List<ProductAttributeValueDTO> getAttributeValues(
            Long productId,
            HashMap<Long, ProductAttribute> attributeMap,
            HashMap<Long, List<ProductAttributeValue>> attributeValueMap,
            HashMap<Long, HashMap<String, ProductAttributeOption>> attributeOptionMap) {
        List<ProductAttributeValueDTO> res = new ArrayList<>();
        List<ProductAttributeValue> attrValues = attributeValueMap.getOrDefault(productId, new ArrayList<>());
        HashMap<Long, List<ProductAttributeValue>> attrIdToValues = new HashMap<>();
        for (ProductAttributeValue v : attrValues) {
            attrIdToValues.computeIfAbsent(v.getAttributeId(), k -> new ArrayList<>()).add(v);
        }
        for (var list : attrIdToValues.values()) {
            ProductAttribute attr = attributeMap.get(list.get(0).getAttributeId());
            if (attr == null) {
                continue;
            }
            HashMap<String, ProductAttributeOption> optionSubMap = attributeOptionMap.get(attr.getId());
            List<String> labels = new ArrayList<>();
            List<String> values = new ArrayList<>();
            List<ProductAttributeOptionDTO> options = new ArrayList<>();
            for (ProductAttributeValue value : list) {
                String label = value.getValue();
                if (optionSubMap != null) {
                    for (Map.Entry<String, ProductAttributeOption> entry : optionSubMap.entrySet()) {
                        options.add(ProductAttributeOptionDTO.builder()
                                .value(entry.getKey())
                                .label(entry.getValue().getLabel())
                                .order(entry.getValue().getSortOrder())
                                .build());
                        if (entry.getKey().equals(value.getValue())) {
                            label = entry.getValue().getLabel();
                        }
                    }
                    options.sort((o1, o2) -> o1.getOrder() - o2.getOrder());
                }
                labels.add(label);
                values.add(value.getValue());
            }
            res.add(ProductAttributeValueDTO.builder()
                    .id(attr.getId())
                    .type(attr.getType())
                    .name(attr.getName())
                    .values(values)
                    .labels(labels)
                    .options(options)
                    .order(attr.getOrder())
                    .isRequired(attr.getIsRequired() == 1)
                    .build());
        }
        res.sort((o1, o2) -> o1.getOrder() - o2.getOrder());
        return res;
    }

    private List<TypeNameDTO> getDeliveryModes(Product src) {
        List<TypeNameDTO> list = new ArrayList<>();
        for (int i = 0; (1 << i) < DeliveryMode.MAX_ENUM.getCode(); i++) {
            int j = 1 << i;
            if ((src.getDeliveryModes() & j) > 0) {
                list.add(TypeNameDTO.builder().type(j).name(DeliveryMode.getByCode(j).getDesc()).build());
            }
        }
        return list;
    }

    private List<String> getDeliveryModeString(Product src) {
        List<String> list = new ArrayList<>();
        for (int i = 0; (1 << i) < DeliveryMode.MAX_ENUM.getCode(); i++) {
            Integer j = 1 << i;
            if ((src.getDeliveryModes() & j) > 0) {
                list.add(j.toString());
            }
        }
        return list;
    }

    private void validateAndSetDeliveryModes(UpdateProductDTO src, Product dst) {
        int deliveryModes = 0;
        if (CollectionUtils.isEmpty(src.getDeliveryModes())) {
            dst.setDeliveryModes(deliveryModes);
            return;
        }
        for (var typ : src.getDeliveryModes()) {
            if (typ == null) {
                throw BusinessException.of("delivery mode can not be null");
            }
            deliveryModes |= typ;
        }
        dst.setDeliveryModes(deliveryModes);
    }

    private void validateCorrelationId(UpdateProductDTO updateProductDTO) {
        var categoryInfo = productCategoryMapper.selectByPrimaryKey(updateProductDTO.getCategoryId());
        if (categoryInfo == null) {
            throw BusinessException.of("category id is invalid");
        }

        if (updateProductDTO.getServiceId() != null && updateProductDTO.getServiceId() > 0) {
            var serviceInfo = serviceInfoMapper.selectByPrimaryKey(updateProductDTO.getServiceId());
            if (serviceInfo == null) {
                throw BusinessException.of("service id is invalid");
            }
            if (!serviceInfo.getSupportedOn().equals(PRODUCT.getCode())) {
                throw BusinessException.of("service does not support the product");
            }
        }

        var brandInfo = productBrandMapper.selectByPrimaryKey(updateProductDTO.getBrandId());
        if (brandInfo == null) {
            throw BusinessException.of("brand id is invalid");
        }

        if (CollectionUtils.isNotEmpty(updateProductDTO.getAttributeValues())) {
            for (var attr : updateProductDTO.getAttributeValues()) {
                var attributeInfo = productAttributeMapper.selectByPrimaryKey(attr.getId());
                if (attributeInfo == null) {
                    throw BusinessException.of("attribute id is invalid");
                }
                if (!attributeInfo.getCategoryId().equals(updateProductDTO.getCategoryId())) {
                    throw BusinessException.of("product attribute error");
                }
            }
        }
    }

    private void handleDetailImagesAndAttributeValues(Long productId, UpdateProductDTO updateProductDTO) {
        if (CollectionUtils.isNotEmpty(updateProductDTO.getCoverImages())) {
            for (var image : updateProductDTO.getCoverImages()) {
                ProductDetailImage productDetailImage = new ProductDetailImage();
                productDetailImage.setProductId(productId);
                productDetailImage.setType(ProductImageType.COVER_IMAGE.getCode());
                productDetailImage.setImage(image);
                productDetailImageMapper.insertSelective(productDetailImage);
            }
        }

        if (CollectionUtils.isNotEmpty(updateProductDTO.getH5DetailImages())) {
            for (var image : updateProductDTO.getH5DetailImages()) {
                ProductDetailImage productDetailImage = new ProductDetailImage();
                productDetailImage.setProductId(productId);
                productDetailImage.setType(ProductImageType.H5_DETAIL_IMAGE.getCode());
                productDetailImage.setImage(image);
                productDetailImageMapper.insertSelective(productDetailImage);
            }
        }

        if (CollectionUtils.isNotEmpty(updateProductDTO.getAttributeValues())) {
            for (var attrValues : updateProductDTO.getAttributeValues()) {
                if (attrValues.getValues() == null) {
                    continue;
                }
                for (var value : attrValues.getValues()) {
                    ProductAttributeValue productAttributeValueInfo = new ProductAttributeValue();
                    productAttributeValueInfo.setProductId(productId);
                    productAttributeValueInfo.setAttributeId(attrValues.getId());
                    productAttributeValueInfo.setValue(value);
                    productAttributeValueMapper.insertSelective(productAttributeValueInfo);
                }
            }
        }
    }

    private Product buildProductByUpdateProductDTO(UpdateProductDTO updateProductDTO) {
        Product info = new Product();
        BeanUtils.copyProperties(updateProductDTO, info, "featuredTags", "deliveryModes", "price");

        info.setPrice(new BigDecimal(updateProductDTO.getPrice()));
        if (updateProductDTO.getNetContent() == null || updateProductDTO.getNetContent() == 0) {
            info.setNetContent(1);
        }
        if (CollectionUtils.isNotEmpty(updateProductDTO.getFeaturedTags())) {
            info.setFeaturedTags(String.join(",", updateProductDTO.getFeaturedTags()));
        }
        validateAndSetDeliveryModes(updateProductDTO, info);
        if (updateProductDTO.getServiceId() == null) {
            info.setServiceId(0L);
        }
        return info;
    }
}
