package com.servauto.admin.service.notice;

import com.servauto.admin.model.entity.order.Order;

/**
 * <p>NoticeService</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/16 19:40
 */
public interface NoticeService {
    /**
     * AppointmentChanged
     *
     * @param order order
     */
    void onRescheduleSendWhatsapp(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void onRescheduleSendWhatsappToCustomer(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void advanceReminderOneDay(Order order);

    /**
     * changed appointment info
     *
     * @param order order
     */
    void advanceReminderTwoHour(Order order);

    /**
     * AppointmentChanged
     *
     * @param order order
     */
    void onWorkshopRescheduleSendLark(Order order);
}
