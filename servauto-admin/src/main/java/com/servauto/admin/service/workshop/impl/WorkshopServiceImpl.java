package com.servauto.admin.service.workshop.impl;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.servauto.admin.dao.workshop.WorkshopMapper;
import com.servauto.admin.dao.workshop.WorkshopServiceTimeMapper;
import com.servauto.admin.factory.workshop.WorkshopFactory;
import com.servauto.admin.factory.workshop.WorkshopServiceTimeFactory;
import com.servauto.admin.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.admin.model.dto.request.workshop.UpdateWorkshopDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopServiceTimeDTO;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.model.entity.workshop.Workshop;
import com.servauto.admin.model.entity.workshop.WorkshopServiceTime;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.admin.service.workshop.WorkshopService;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkshopServiceImpl implements WorkshopService {
    @Resource
    private WorkshopMapper workshopMapper;

    @Resource
    private WorkshopServiceTimeMapper workshopServiceTimeMapper;

    @Resource
    private LocationService locationService;

    @Resource
    private SysUserService sysUserService;

    static final int MAX_PHOTO_NUMS = 10;

    public PageInfo<WorkshopDTO> pageWorkshops(QueryWorkshopsDTO queryWorkshopsDTO) {
        PageSupport.startPage();
        List<Workshop> list = workshopMapper.selectByConditions(queryWorkshopsDTO);
        if (CollectionUtils.isEmpty(list)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<Workshop> pageInfo = PageInfo.of(list);

        List<Long> userIds = list.stream().map(Workshop::getOperatorId).distinct().toList();
        List<SysUser> users = sysUserService.selectUserByIds(userIds);
        Map<Long, String> userMap = users.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getUserName));

        var workshopDTOS = list.stream().map(e -> {
            WorkshopDTO workshopDTO = WorkshopFactory.convert(e);
            workshopDTO.setOperatorName(userMap.get(e.getOperatorId()));
            return workshopDTO;
        }).toList();

        Map<String, StateDTO> stateMap = locationService.getStateMap();
        Map<String, AreaDTO> cityMap = locationService.getAreaMap();
        for (var v : workshopDTOS) {
            v.setStateName(stateMap.get(v.getStateCode()).getName());
            v.setCityName(cityMap.get(v.getCityCode()).getName());
        }

        return PageSupport.copyProperties(pageInfo, workshopDTOS);
    }

    public List<IdNameDTO> getWorkshopNames() {
        List<Workshop> list = workshopMapper.selectByConditions(new QueryWorkshopsDTO());
        List<IdNameDTO> resp = new ArrayList<>();
        for (var v : list) {
            resp.add(IdNameDTO.builder().id(v.getId()).name(v.getName()).build());
        }
        return resp;
    }

    @Override
    public List<IdNameDTO> getWorkshopNames(List<Long> ids) {
        QueryWorkshopsDTO queryWorkshopsDTO = new QueryWorkshopsDTO();
        queryWorkshopsDTO.setIds(ids);
        List<Workshop> list = workshopMapper.selectByConditions(queryWorkshopsDTO);
        List<IdNameDTO> resp = new ArrayList<>();
        for (var v : list) {
            resp.add(IdNameDTO.builder().id(v.getId()).name(v.getName()).build());
        }
        return resp;
    }

    public WorkshopDetailDTO getWorkshopDetail(long id) {
        Workshop info = workshopMapper.selectByPrimaryKey(id);
        var workshopServiceTimes = workshopServiceTimeMapper.selectByWorkshopIds(List.of(id));
        var workshopDTO = WorkshopFactory.convertDetail(info);

        workshopDTO.setOperatorName(sysUserService.selectUserById(workshopDTO.getOperatorId()).getUserName());
        setFeaturedTagsFromWorkshop(info, workshopDTO);
        workshopDTO.setWhatsAppNumber(info.getWhatsAppNumber());

        if (StringUtils.isNotEmpty(workshopDTO.getStateCode())) {
            StateDTO state = locationService.getStateByCode(workshopDTO.getStateCode());
            workshopDTO.setStateName(state.getName());
        }

        if (StringUtils.isNotEmpty(workshopDTO.getCityCode())) {
            AreaDTO area = locationService.getAreaByCode(workshopDTO.getCityCode());
            workshopDTO.setCityName(area.getName());
        }

        if (CollectionUtils.isNotEmpty(workshopServiceTimes)) {
            List<WorkshopServiceTimeDTO> serviceTimeDTOList = workshopServiceTimes.stream().map(WorkshopServiceTimeFactory::convert).toList();
            workshopDTO.setServiceTimeList(serviceTimeDTOList);
        }

        return workshopDTO;
    }

    public void updateWorkshop(UpdateWorkshopDTO updateWorkshopDTO) {
        Workshop info = workshopMapper.selectByPrimaryKey(updateWorkshopDTO.getId());
        BeanUtils.copyProperties(updateWorkshopDTO, info, "photo", "featuredTags");
        info.setPhoto(String.join(",", updateWorkshopDTO.getPhoto()));
        info.setPhoneNumber(updateWorkshopDTO.getContactNumber());

        if(StringUtils.isNotBlank(updateWorkshopDTO.getWhatsAppNumber())){
            info.setWhatsAppNumber(String.join(";",updateWorkshopDTO.getWhatsAppNumber().split(";")));
        }
        setFeaturedTagsFromUpdateWorkshop(updateWorkshopDTO, info);
        checkPhotoNums(updateWorkshopDTO);
        checkStateAndCity(info);

        int rows = workshopMapper.updateByPrimaryKeySelective(info);
        if (rows == 0) {
            throw BusinessException.of("update workshop failed");
        }

        workshopServiceTimeMapper.deleteByWorkshopId(info.getId());

        handlePackagesAndServiceTimes(info.getId(), updateWorkshopDTO);
    }

    public void insertWorkshop(UpdateWorkshopDTO updateWorkshopDTO) {
        Workshop info = new Workshop();
        BeanUtils.copyProperties(updateWorkshopDTO, info, "photo", "featuredTags");
        info.setCountryCode("");
        info.setPhoto(String.join(",", updateWorkshopDTO.getPhoto()));
        info.setPhoneNumber(updateWorkshopDTO.getContactNumber());
        if(StringUtils.isNotBlank(updateWorkshopDTO.getWhatsAppNumber())){
            info.setWhatsAppNumber(String.join(";",updateWorkshopDTO.getWhatsAppNumber().split(";")));
        }
        setFeaturedTagsFromUpdateWorkshop(updateWorkshopDTO, info);
        checkPhotoNums(updateWorkshopDTO);
        checkStateAndCity(info);

        int rows = workshopMapper.insertSelective(info);
        if (rows == 0) {
            throw BusinessException.of("insert workshop failed");
        }

        handlePackagesAndServiceTimes(info.getId(), updateWorkshopDTO);
    }

    private void setFeaturedTagsFromWorkshop(Workshop src, WorkshopDetailDTO dst) {
        dst.setFeaturedTags(Arrays.stream(src.getFeaturedTags().split(",")).toList());
    }

    private void setFeaturedTagsFromUpdateWorkshop(UpdateWorkshopDTO src, Workshop dst) {
        dst.setFeaturedTags(String.join(",",src.getFeaturedTags()));
    }

    private void checkPhotoNums(UpdateWorkshopDTO updateWorkshopDTO) {
        if (updateWorkshopDTO.getPhoto().size() > MAX_PHOTO_NUMS) {
            throw BusinessException.of("photo nums must be less than " + MAX_PHOTO_NUMS);
        }
    }

    private void checkStateAndCity(Workshop info) {
        StateDTO state = locationService.getStateByCode(info.getStateCode());
        if (state == null) {
            throw BusinessException.of("state not found");
        }

        AreaDTO areas = locationService.getAreaByCode(info.getCityCode());
        if (areas == null) {
            throw BusinessException.of("area not found");
        }
    }

    private void handlePackagesAndServiceTimes(Long workshopId, UpdateWorkshopDTO updateWorkshopDTO) {
        if (updateWorkshopDTO.getServiceTimeList() != null) {
            for (var v : updateWorkshopDTO.getServiceTimeList()) {
                WorkshopServiceTime workshopServiceTime = new WorkshopServiceTime();
                workshopServiceTime.setWorkshopId(workshopId);
                workshopServiceTime.setDay(v.getDay());
                workshopServiceTime.setStartTime(new Date(v.getStartTime()));
                workshopServiceTime.setEndTime(new Date(v.getEndTime()));
                workshopServiceTimeMapper.insertSelective(workshopServiceTime);
            }
        }
    }
}
