package com.servauto.admin.service.car;

import com.servauto.admin.model.dto.response.car.CarInfoDTO;
import com.servauto.admin.model.entity.car.CarInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CarInfoService {

    List<CarInfo> selectByCustomerId(@Param("customerId") Long customerId);
    int deleteByCustomerId(@Param("customerId") Long customerId);
    List<CarInfoDTO> carInfoList(@Param("customerId") Long customerId);

}