package com.servauto.admin.service.order.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.servauto.admin.dao.order.*;
import com.servauto.admin.enums.order.OrderDeliveryTypeEnum;
import com.servauto.admin.enums.order.OrderStatusEnum;
import com.servauto.admin.event.producer.OrderProducer;
import com.servauto.admin.factory.order.OrderDeliveryFactory;
import com.servauto.admin.factory.order.OrderFactory;
import com.servauto.admin.factory.order.OrderProductFactory;
import com.servauto.admin.factory.order.OrderStatusLogFactory;
import com.servauto.admin.model.dto.request.order.*;
import com.servauto.admin.model.dto.response.order.OrderDTO;
import com.servauto.admin.model.dto.response.order.OrderStatusLogDTO;
import com.servauto.admin.model.dto.response.order.OrderWorkshopDTO;
import com.servauto.admin.model.dto.response.order.PageOrderDTO;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.model.entity.order.*;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.service.order.OrderService;
import com.servauto.admin.service.serviceinfo.ServiceInfoService;
import com.servauto.admin.service.workshop.WorkshopService;
import com.servauto.admin.support.utils.ScheduleGenerator;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.DateUtils;
import com.servauto.framework.utils.PageSupport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderProductMapper orderProductMapper;

    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;

    @Resource
    private OrderStatusLogMapper orderStatusLogMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderPayMapper orderPayMapper;

    @Resource
    private WorkshopService workshopService;

    @Resource
    private ServiceInfoService serviceInfoService;

    @Resource
    private OrderProducer orderProducer;

    @Override
    public PageInfo<PageOrderDTO> pageOrders(Integer pageNo, Integer pageSize, PageOrderReqDTO reqDTO) {

        PageSupport.startPage(pageNo, pageSize);
        List<Order> orderList = orderMapper.selectByPage(reqDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<Order> pageInfo = PageInfo.of(orderList);
        List<String> orderNos = pageInfo.getList().stream().map(Order::getOrderNo).toList();

        OrderProductExample example = new OrderProductExample();
        example.createCriteria().andOrderNoIn(orderNos);
        List<OrderProduct> products = orderProductMapper.selectByExample(example);
        Map<String, List<OrderProduct>> productMap = products.stream().collect(
                Collectors.groupingBy(OrderProduct::getOrderNo)
        );

        Map<String, OrderDelivery> deliveryMap = Maps.newHashMap();
        if (OrderDeliveryTypeEnum.isShipping(reqDTO.getDeliveryType())) {
            OrderDeliveryExample eg = new OrderDeliveryExample();
            eg.createCriteria().andOrderNoIn(orderNos);
            List<OrderDelivery> deliveries = orderDeliveryMapper.selectByExample(eg);
            deliveryMap = deliveries.stream().collect(
                    Collectors.toMap(OrderDelivery::getOrderNo, k -> k)
            );
        }

        List<PageOrderDTO> orders = Lists.newArrayList();
        for (Order order : pageInfo.getList()) {

            PageOrderDTO.PageOrderDTOBuilder builder = PageOrderDTO.builder()
                    .orderNo(order.getOrderNo())
                    .status(order.getStatus())
                    .statusName(OrderStatusEnum.getByCode(order.getStatus()).getMsg())
                    .workShopId(order.getWorkshopId())
                    .workShopName(order.getWorkshopName())
                    .packageId(order.getPackageId())
                    .packageName(order.getPackageName())
                    .creatorName(order.getCreateBy())
                    .customerName(order.getCustomerName())
                    .customerMobile(order.getCustomerMobile())
                    .licensePlate(order.getLicensePlate())
                    .carInfo(OrderFactory.formatCarInfo(order))
                    .originalAmount(order.getOriginalAmount())
                    .grandTotal(order.getGrandTotal())
                    .reservationTime(OrderFactory.formatDate(order.getReservationTime()))
                    .updaterName(order.getUpdateBy())
                    .orderTime(order.getOrderTime())
                    .updateTime(order.getUpdateTime())
                    .remark(order.getRemark())
                    .printInfo(OrderFactory.formatTemplate(order))
                    .suggestionTime(order.getSuggestionTime());

            OrderDeliveryTypeEnum deliveryTypeEnum = OrderDeliveryTypeEnum.getByCode(order.getDeliveryType());
            assert deliveryTypeEnum != null;
            builder.allowCancel(OrderStatusEnum.canCancel(order.getStatus())).allowDelivery(OrderStatusEnum.canDelivery(order.getDeliveryType(), order.getStatus()));

            switch (deliveryTypeEnum) {
                case PICKUP:
                    break;
                case SHIPPING:
                    builder.allowShipping(OrderStatusEnum.canShipping(order.getStatus()));
                    break;
                case WORKSHOP:
                    builder.allowReschedule(OrderStatusEnum.canReschedule(order.getStatus()));
                    builder.allowWorkshopReschedule(OrderStatusEnum.canWorkshopReschedule(order.getStatus()));
                    builder.allowConfirm(OrderStatusEnum.canWorkshopReschedule(order.getStatus()));
                    break;
            }

            List<OrderProduct> orderProducts = productMap.get(order.getOrderNo());
            builder.products(orderProducts.stream().map(OrderProductFactory::convert).toList());

            if (OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
                OrderDelivery orderDelivery = deliveryMap.get(order.getOrderNo());
                if (Objects.nonNull(orderDelivery)) {
                    builder.deliveryAddress(OrderDeliveryFactory.formatAddress(orderDelivery)).trackingNo(orderDelivery.getTrackingNo());
                }
            }

            orders.add(builder.build());
        }

        PageInfo<PageOrderDTO> result = PageInfo.of(orders);
        result.setPageNum(pageInfo.getPageNum());
        result.setPages(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        return result;
    }


    @Override
    public OrderDTO queryOrderByOrderNo(String orderNo) {
        Order tblOrder = orderMapper.selectByOrderNo(orderNo);
        if (tblOrder == null) {
            return OrderDTO.builder().build();
        }

        OrderProductExample example = new OrderProductExample();
        example.createCriteria().andOrderNoEqualTo(tblOrder.getOrderNo());
        List<OrderProduct> products = orderProductMapper.selectByExample(example);

        OrderPayExample ope = new OrderPayExample();
        ope.createCriteria().andOrderNoEqualTo(orderNo);
        List<OrderPay> orderPays = orderPayMapper.selectByExample(ope);

        OrderDelivery orderDelivery = null;
        if (OrderDeliveryTypeEnum.isShipping(tblOrder.getDeliveryType())) {
            OrderDeliveryExample eg = new OrderDeliveryExample();
            eg.createCriteria().andOrderNoEqualTo(tblOrder.getOrderNo());
            List<OrderDelivery> deliveries = orderDeliveryMapper.selectByExample(eg);
            Map<String, OrderDelivery> deliveryMap = deliveries.stream().collect(
                    Collectors.toMap(OrderDelivery::getOrderNo, k -> k)
            );
            orderDelivery = deliveryMap.get(orderNo);
        }
        final OrderDelivery delivery = orderDelivery;

        OrderStatusLogExample logExample = new OrderStatusLogExample();
        logExample.createCriteria().andOrderNoEqualTo(tblOrder.getOrderNo());
        List<OrderStatusLog> orderStatusLogs = orderStatusLogMapper.selectByExample(logExample);
        List<OrderStatusLogDTO> logs = orderStatusLogs.stream()
                .map(e -> OrderStatusLogFactory.convert(e, delivery)).toList();

        OrderDTO orderDTO = OrderFactory.buildOrderDTO(tblOrder, orderPays.stream().findFirst().orElse(new OrderPay()), products, delivery);
        orderDTO.setOrderStatusLogs(logs);
        return orderDTO;
    }

    @Override
    public void cancelOrder(LoginUser operator, CancelOrderReqDTO reqDTO) {
        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!OrderStatusEnum.canCancel(order.getStatus())) {
            throw BusinessException.of(ResponseCode.ERROR, "Order status has been changed!");
        }

        Order tblOrder = new Order();
        tblOrder.setOrderNo(order.getOrderNo());
        tblOrder.setStatus(OrderStatusEnum.CANCELED.getCode());
        tblOrder.setUpdateBy(operator.getUsername());

        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(),
                OrderStatusEnum.CANCELED, tblOrder.getUpdateBy(), reqDTO.getImages(), reqDTO.getRemark());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder,
                        Arrays.asList(OrderStatusEnum.PENDING_PAY.getCode(), OrderStatusEnum.PENDING_DELIVERY.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
                orderStatusLogMapper.insertSelective(orderStatusLog);
            }
        });
    }

    @Override
    public void deliveryOrder(LoginUser operator, DeliveryOrderReqDTO reqDTO) {
        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!OrderStatusEnum.canDelivery(order.getDeliveryType(), order.getStatus())) {
            throw BusinessException.of(ResponseCode.ERROR, "Order status has been changed!");
        }

        Order tblOrder = new Order();
        tblOrder.setOrderNo(order.getOrderNo());
        tblOrder.setStatus(OrderStatusEnum.COMPLETED.getCode());
        tblOrder.setUpdateBy(operator.getUsername());
        tblOrder.setCompletedTime(DateUtils.getNowDate());

        Long deliveryTime = reqDTO.getDeliveryTime() == null || reqDTO.getDeliveryTime() <= 0 ? DateUtils.getNowDate().getTime() : reqDTO.getDeliveryTime();
        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(),
                OrderStatusEnum.COMPLETED, tblOrder.getUpdateBy(), reqDTO.getImages(), reqDTO.getRemark(), deliveryTime);

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder,
                        Arrays.asList(OrderStatusEnum.PENDING_DELIVERY.getCode(), OrderStatusEnum.DESPATCHED.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
                orderStatusLogMapper.insertSelective(orderStatusLog);
            }
        });
    }

    @Override
    public void shippingOrder(LoginUser operator, ShippingOrderReqDTO reqDTO) {

        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null || !OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!OrderStatusEnum.canShipping(order.getStatus())) {
            throw BusinessException.of(ResponseCode.ERROR, "Order status has been changed!");
        }

        Order tblOrder = new Order();
        tblOrder.setStatus(OrderStatusEnum.DESPATCHED.getCode());
        tblOrder.setUpdateBy(operator.getUsername());

        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(),
                OrderStatusEnum.DESPATCHED, tblOrder.getUpdateBy(), null, reqDTO.getRemark());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {

                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder,
                        Collections.singletonList(OrderStatusEnum.PENDING_DELIVERY.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                rows = orderDeliveryMapper.updateTrackingNoByOrderNo(order.getOrderNo(), reqDTO.getTrackingNo());
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                orderStatusLogMapper.insertSelective(orderStatusLog);
            }
        });
    }

    @Override
    public void reschedule(LoginUser operator, RescheduleReqDTO reqDTO) {
        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null || OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!OrderStatusEnum.canReschedule(order.getStatus())) {
            throw BusinessException.of(ResponseCode.ERROR, "Order status has been changed!");
        }

        WorkshopDetailDTO workshop = workshopService.getWorkshopDetail(reqDTO.getWorkshopId());
        Preconditions.checkNotNull(workshop);

        boolean confirm = OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())
                && OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode().equals(order.getStatus());

        Order tblOrder = new Order();
        tblOrder.setWorkshopId(reqDTO.getWorkshopId());
        tblOrder.setWorkshopName(workshop.getName());
        tblOrder.setReservationTime(new Date(reqDTO.getReservationTime()));
        tblOrder.setUpdateBy(operator.getUsername());
        if (confirm) {
            tblOrder.setStatus(OrderStatusEnum.PENDING_DELIVERY.getCode());
        }

        List<String> statusList = confirm ?
                Collections.singletonList(OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode()) : Collections.singletonList(OrderStatusEnum.PENDING_DELIVERY.getCode());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {

                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, statusList);
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }

                if (confirm) {
                    OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(), OrderStatusEnum.PENDING_DELIVERY, tblOrder.getUpdateBy());
                    orderStatusLogMapper.insertSelective(orderStatusLog);
                }

                orderProducer.sendRescheduleEvent(order.getOrderNo());
            }
        });
    }

    @Override
    public void pickup(LoginUser operator, PickupOrderReqDTO reqDTO) {
        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        if (!reqDTO.getPickupCode().equals(order.getPickupCode())) {
            throw BusinessException.of("Pickup Code is incorrect,please re-enter it.");
        }

        if (!OrderDeliveryTypeEnum.isPickup(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        Order tblOrder = new Order();
        tblOrder.setOrderNo(order.getOrderNo());
        tblOrder.setStatus(OrderStatusEnum.COMPLETED.getCode());
        tblOrder.setUpdateBy(operator.getUsername());
        tblOrder.setCompletedTime(DateUtils.getNowDate());

        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(),
                OrderStatusEnum.COMPLETED, tblOrder.getUpdateBy(), reqDTO.getImages(), reqDTO.getRemark(), DateUtils.getNowDate().getTime());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, Collections.singletonList(OrderStatusEnum.PENDING_DELIVERY.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
                orderStatusLogMapper.insertSelective(orderStatusLog);
            }
        });
    }

    @Override
    public void finishOrders() {

    }

    @Override
    public List<IdNameDTO> queryOrderWorkshops(String orderNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        Preconditions.checkNotNull(order);
        Preconditions.checkArgument(Objects.nonNull(order.getServiceId()));

        ServiceInfoDTO serviceInfo = serviceInfoService.getService(order.getServiceId());
        Preconditions.checkNotNull(serviceInfo);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(serviceInfo.getWorkshopIds()));

        return workshopService.getWorkshopNames(serviceInfo.getWorkshopIds());
    }

    @Override
    public void confirm(LoginUser operator, String orderNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        Order tblOrder = new Order();
        tblOrder.setOrderNo(order.getOrderNo());
        tblOrder.setStatus(OrderStatusEnum.PENDING_DELIVERY.getCode());
        tblOrder.setUpdateBy(operator.getUsername());
        tblOrder.setConfirmTime(DateUtils.getNowDate());

        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(), OrderStatusEnum.PENDING_DELIVERY, tblOrder.getUpdateBy());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, Collections.singletonList(OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
                orderStatusLogMapper.insertSelective(orderStatusLog);
            }
        });
    }

    @Override
    public void workshopReschedule(LoginUser operator, WorkshopRescheduleReqDTO reqDTO) {
        Order order = orderMapper.selectByOrderNo(reqDTO.getOrderNo());
        if (order == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }
        if (!OrderStatusEnum.canWorkshopReschedule(order.getStatus())) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        Order tblOrder = new Order();
        tblOrder.setOrderNo(order.getOrderNo());
        tblOrder.setSuggestionTime(new Date(reqDTO.getReservationTime()));
        tblOrder.setUpdateBy(operator.getUsername());
        tblOrder.setStatus(OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode());

        OrderStatusLog orderStatusLog = OrderStatusLogFactory.build(order.getOrderNo(), OrderStatusEnum.PENDING_HQOPS_CONFIRM, tblOrder.getUpdateBy());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                int rows = orderMapper.casByOrderNoAndStatusList(order.getOrderNo(), tblOrder, Collections.singletonList(OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode()));
                if (rows == 0) {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
                orderStatusLogMapper.insertSelective(orderStatusLog);

                orderProducer.sendWorkshopRescheduleEvent(order.getOrderNo());
            }
        });
    }

    @Override
    public OrderWorkshopDTO queryOrderWorkshopOpeningHours(String orderNo, Long workshopId) {
        WorkshopDetailDTO workshopInfo = workshopService.getWorkshopDetail(workshopId);
        Preconditions.checkNotNull(workshopInfo);

        List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(workshopInfo.getServiceTimeList())) {
            serviceTimes = ScheduleGenerator.generateServicesTimes(workshopInfo.getServiceTimeList());
        }

        return OrderFactory.buildWorkshop(workshopInfo, serviceTimes);
    }


}
