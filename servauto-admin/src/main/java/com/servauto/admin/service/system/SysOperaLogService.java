package com.servauto.admin.service.system;

import com.servauto.admin.model.entity.system.SysOperLog;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 操作日志 服务层
 *
 * <AUTHOR>
 */
public interface SysOperaLogService {

    void recordLoginInfo(HttpServletRequest request, final String username, final String status, final String message,
                         final Object... args);

    void recordOperaLog(final SysOperLog operaLog);
}
