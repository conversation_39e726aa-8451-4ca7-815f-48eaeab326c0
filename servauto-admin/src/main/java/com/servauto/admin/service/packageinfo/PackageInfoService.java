package com.servauto.admin.service.packageinfo;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.admin.model.dto.request.packageinfo.UpdatePackageInfoDTO;
import com.servauto.admin.model.dto.response.packageinfo.PackageInfoDTO;

import java.util.List;

public interface PackageInfoService {
    /**
     * get package
     *
     * @param id id
     * @return PackageInfoDTO
     */
    PackageInfoDTO getPackage(long id);

    /**
     * get packages
     *
     * @param reqDTO reqDTO
     * @return List<PackageInfoDTO>
     */
    List<PackageInfoDTO> getPackages(QueryPackagesDTO reqDTO);

    /**
     * page packages
     *
     * @param reqDTO reqDTO
     * @return PageInfo<PackageInfoDTO>
     */
    PageInfo<PackageInfoDTO> pagePackages(QueryPackagesDTO reqDTO);

    /**
     * update package info
     *
     * @param updatePackageInfoDTO updatePackageInfoDTO
     */
    void updatePackage(UpdatePackageInfoDTO updatePackageInfoDTO);

    /**
     * insert package info
     *
     * @param updatePackageInfoDTO updatePackageInfoDTO
     */
    void insertPackage(UpdatePackageInfoDTO updatePackageInfoDTO);
}
