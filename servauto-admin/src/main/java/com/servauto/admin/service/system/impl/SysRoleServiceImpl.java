package com.servauto.admin.service.system.impl;

import com.servauto.admin.dao.system.SysRoleMapper;
import com.servauto.admin.dao.system.SysRoleMenuMapper;
import com.servauto.admin.dao.system.SysRoleWorkshopMapper;
import com.servauto.admin.dao.system.SysUserRoleMapper;
import com.servauto.admin.model.dto.request.system.DataPermissionReqDTO;
import com.servauto.admin.model.dto.request.system.QueryRoleReqDTO;
import com.servauto.admin.model.entity.system.*;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysRoleServiceImpl implements SysRoleService {

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysRoleWorkshopMapper sysRoleWorkshopMapper;

    @Override
    public List<SysRole> selectRoleList(QueryRoleReqDTO role) {
        return sysRoleMapper.selectRoleList(role);
    }

    @Override
    public List<SysRole> selectRoles(Long userId) {
        List<SysRole> userRoles = sysRoleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleList(new QueryRoleReqDTO());
        for (SysRole role : roles) {
            for (SysRole userRole : userRoles) {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        return sysRoleMapper.selectRolePermissionByUserId(userId);
    }

    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = sysRoleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public List<SysRole> selectRolePermissionRoleByUserId(Long userId) {
        return sysRoleMapper.selectRolePermissionByUserId(userId);
    }

    @Override
    public SysRole selectRoleById(Long roleId) {
        return sysRoleMapper.selectRoleById(roleId);
    }

    @Override
    public boolean checkRoleNameUnique(SysRole role) {
        long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = sysRoleMapper.checkRoleNameUnique(role.getRoleName());
        return StringUtils.isNull(info) || info.getRoleId() == roleId;
    }

    @Override
    public boolean checkRoleKeyUnique(SysRole role) {
        long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = sysRoleMapper.checkRoleKeyUnique(role.getRoleKey());
        return StringUtils.isNull(info) || info.getRoleId() == roleId;
    }

    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return sysUserRoleMapper.countUserRoleByRoleId(roleId);
    }

    @Override
    @Transactional
    public int insertRole(SysRole role) {
        sysRoleMapper.insertRole(role);
        return insertRoleMenu(role);
    }


    @Override
    @Transactional
    public int updateRole(SysRole role) {
        if (!SecurityUtils.isRoleAdmin(role.getRoleId())) {
            sysRoleMapper.updateRole(role);
        }
        sysRoleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        return insertRoleMenu(role);
    }

    @Override
    public int updateRoleStatus(SysRole role) {
        return sysRoleMapper.updateRole(role);
    }

    public int insertRoleMenu(SysRole role) {
        int rows = 1;
        List<SysRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            rows = sysRoleMenuMapper.batchRoleMenu(list);
        }
        return rows;
    }


    @Override
    @Transactional
    public int deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            SecurityUtils.checkRoleAdmin(roleId);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw BusinessException.of(String.format("%1$s has been allocated and cannot be deleted", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        sysRoleMenuMapper.deleteRoleMenu(roleIds);
        // 删除数据权限
        sysRoleWorkshopMapper.deleteByRoleIds(List.of(roleIds));
        return sysRoleMapper.deleteRoleByIds(roleIds);
    }

    @Override
    public int deleteAuthUser(SysUserRole userRole) {
        return sysUserRoleMapper.deleteUserRoleInfo(userRole);
    }

    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds) {
        return sysUserRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    @Override
    public int insertAuthUsers(Long roleId, Long[] userIds) {
        List<SysUserRole> list = Arrays.stream(userIds).map(e -> {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(e);
            ur.setRoleId(roleId);
            return ur;
        }).collect(Collectors.toList());
        return sysUserRoleMapper.batchUserRole(list);
    }

    @Override
    public List<Long> getWorkshopIdsByUserId(Long userId) {
        List<SysRoleWorkshop> sysRoleWorkshops = sysRoleWorkshopMapper.selectByUserId(userId);
        return sysRoleWorkshops.stream().map(SysRoleWorkshop::getWorkshopId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> getWorkshopIdsByRoleId(Long roleId) {
        List<SysRoleWorkshop> sysRoleWorkshops = sysRoleWorkshopMapper.selectByRoleId(roleId);
        return sysRoleWorkshops.stream().map(SysRoleWorkshop::getWorkshopId).distinct().collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void authDataScope(DataPermissionReqDTO reqDTO) {

        List<SysRoleWorkshop> sysRoleWorkshops = sysRoleWorkshopMapper.selectByRoleId(reqDTO.getRoleId());
        if (CollectionUtils.isNotEmpty(sysRoleWorkshops)) {
            sysRoleWorkshopMapper.deleteByRoleIds(Collections.singletonList(reqDTO.getRoleId()));
        }

        if (CollectionUtils.isNotEmpty(reqDTO.getWorkshopIds())) {
            reqDTO.getWorkshopIds().stream().map(e -> {
                SysRoleWorkshop sysRoleWorkshop = new SysRoleWorkshop();
                sysRoleWorkshop.setWorkshopId(e);
                sysRoleWorkshop.setRoleId(reqDTO.getRoleId());
                return sysRoleWorkshop;
            }).forEach(ws -> sysRoleWorkshopMapper.insertSelective(ws));
        }
    }

}
