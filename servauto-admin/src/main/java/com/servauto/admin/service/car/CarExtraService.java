package com.servauto.admin.service.car;

import com.servauto.admin.model.dto.response.car.CarExtraDTO;
import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.entity.car.CarExtra;

import java.util.List;
import java.util.Map;

public interface CarExtraService {

    List<CarExtraDTO> getCarExtraByYearId(Long id);

    void insertCarExtraIfNotExists(List<CarExtra> collect);

    void batchInsertCarExtra(List<CarExtra> collect);

    List<CarKeyValueDTO> getCarExtraByYearIdKv(Long yearId);

    Map<Long, CarExtra> selectVariantsMap(List<Long> variantIds);

}