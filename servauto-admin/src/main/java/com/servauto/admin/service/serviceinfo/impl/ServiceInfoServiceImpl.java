package com.servauto.admin.service.serviceinfo.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.dao.packageinfo.PackageInfoMapper;
import com.servauto.admin.dao.product.ProductMapper;
import com.servauto.admin.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.admin.dao.serviceinfo.ServiceWorkshopMapper;
import com.servauto.admin.enums.ServiceSupportedOn;
import com.servauto.admin.factory.serviceinfo.ServiceInfoFactory;
import com.servauto.admin.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.admin.model.dto.request.serviceinfo.UpdateServiceInfoDTO;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.admin.model.entity.packageinfo.PackageInfo;
import com.servauto.admin.model.entity.product.Product;
import com.servauto.admin.model.entity.serviceinfo.ServiceInfo;
import com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop;
import com.servauto.admin.service.serviceinfo.ServiceInfoService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ServiceInfoServiceImpl implements ServiceInfoService {
    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private PackageInfoMapper packageInfoMapper;

    @Resource
    private ServiceWorkshopMapper serviceWorkshopMapper;

    @Resource
    private SysUserService sysUserService;

    public ServiceInfoDTO getService(long id) {
        if (id == 0) {
            throw BusinessException.of("Invalid service id");
        }
        var serviceInfo = serviceInfoMapper.selectByPrimaryKey(id);
        if (serviceInfo == null) {
            throw BusinessException.of("Invalid service id");
        }
        var serviceInfoDTO = ServiceInfoFactory.convert(serviceInfo);

        List<Long> workshopIds = serviceWorkshopMapper.selectByServiceId(id).stream().map(ServiceWorkshop::getWorkshopId).toList();
        serviceInfoDTO.setWorkshopIds(workshopIds);

        switch (ServiceSupportedOn.getByCode(serviceInfo.getSupportedOn())) {
            case UNKNOWN:
                break;
            case PRODUCT:
                var products = productMapper.selectByConditions(QueryProductsDTO.builder().serviceId(id).build());
                if (CollectionUtils.isNotEmpty(products)) {
                    serviceInfoDTO.setProductIds(products.stream().map(Product::getId).toList());
                }
                break;
            case PACKAGE:
                var packages = packageInfoMapper.selectByConditions(QueryPackagesDTO.builder().serviceId(id).build());
                if (CollectionUtils.isNotEmpty(packages)) {
                    serviceInfoDTO.setPackageIds(packages.stream().map(PackageInfo::getId).toList());
                }
                break;
            default:
                log.warn("unknown service supported on: {}", serviceInfo.getSupportedOn());
                break;
        }
        return serviceInfoDTO;
    }

    public List<ServiceInfoDTO> getServices(QueryServicesDTO reqDTO) {
        if (reqDTO.getWorkshopId() != null && reqDTO.getWorkshopId() > 0) {
            var serviceWorkshops = serviceWorkshopMapper.selectByServiceId(reqDTO.getWorkshopId());
            if (CollectionUtils.isNotEmpty(serviceWorkshops)) {
                reqDTO.setIds(serviceWorkshops.stream().map(ServiceWorkshop::getServiceId).distinct().toList());
            }
        }
        var serviceInfos = serviceInfoMapper.selectByConditions(reqDTO);
        List<ServiceInfoDTO> resp = serviceInfos.stream().map(ServiceInfoFactory::convert).toList();
        if (reqDTO.isNeedUpdaterName()) {
            for (var info : resp) {
                if (info.getUpdatedBy() > 0) {
                    info.setUpdaterName(sysUserService.selectUserById(info.getUpdatedBy()).getUserName());
                }
            }
        }
        return resp;
    }

    public PageInfo<ServiceInfoDTO> pageServices(QueryServicesDTO reqDTO) {
        if (reqDTO.getWorkshopId() != null && reqDTO.getWorkshopId() > 0) {
            var serviceWorkshops = serviceWorkshopMapper.selectByServiceId(reqDTO.getWorkshopId());
            if (CollectionUtils.isNotEmpty(serviceWorkshops)) {
                reqDTO.setIds(serviceWorkshops.stream().map(ServiceWorkshop::getServiceId).distinct().toList());
            }
        }
        PageSupport.startPage();
        var serviceInfos = serviceInfoMapper.selectByConditions(reqDTO);
        PageInfo<ServiceInfo> pageInfo = PageInfo.of(serviceInfos);

        List<ServiceInfoDTO> dos = serviceInfos.stream().map(ServiceInfoFactory::convert).toList();
        if (reqDTO.isNeedUpdaterName()) {
            for (var info : dos) {
                if (info.getUpdatedBy() > 0) {
                    info.setUpdaterName(sysUserService.selectUserById(info.getUpdatedBy()).getUserName());
                }
            }
        }

        return PageSupport.copyProperties(pageInfo, dos);
    }


    public void updateServiceInfo(UpdateServiceInfoDTO updateServiceInfoDTO) {
        if (updateServiceInfoDTO.getId() == 0) {
            throw BusinessException.of("Invalid service id");
        }
        validateUpdateServiceInfoDTO(updateServiceInfoDTO);

        ServiceInfo info = new ServiceInfo();
        BeanUtils.copyProperties(updateServiceInfoDTO, info);

        int rows = serviceInfoMapper.updateByPrimaryKeySelective(info);
        if (rows == 0) {
            throw BusinessException.of("Update service failed");
        }

        // update service product and workshop
        serviceWorkshopMapper.deleteByServiceId(info.getId());
        productMapper.updateServiceIdByServiceId(info.getId(), 0L);
        packageInfoMapper.updateServiceIdByServiceId(info.getId(), 0L);
        handleServiceProductAndWorkshop(info.getId(), updateServiceInfoDTO);
    }


    public void insertServiceInfo(UpdateServiceInfoDTO updateServiceInfoDTO) {
        validateUpdateServiceInfoDTO(updateServiceInfoDTO);

        ServiceInfo info = new ServiceInfo();
        BeanUtils.copyProperties(updateServiceInfoDTO, info);

        int rows = serviceInfoMapper.insertSelective(info);
        if (rows == 0) {
            throw BusinessException.of("Insert service failed");
        }

        handleServiceProductAndWorkshop(info.getId(), updateServiceInfoDTO);
    }

    private void validateUpdateServiceInfoDTO(UpdateServiceInfoDTO updateServiceInfoDTO) {
        if (CollectionUtils.isEmpty(updateServiceInfoDTO.getWorkshopIds())) {
            throw BusinessException.of("Invalid workshop id");
        }
    }

    private void handleServiceProductAndWorkshop(Long serviceId, UpdateServiceInfoDTO updateServiceInfoDTO) {
        if (CollectionUtils.isNotEmpty(updateServiceInfoDTO.getProductIds())) {
            List<Long> productIds = updateServiceInfoDTO.getProductIds().stream().distinct().toList();
            int row = productMapper.updateServiceIdByIds(productIds, serviceId);
            if (row != productIds.size()) {
                throw BusinessException.of("Update product service id failed");
            }
        }

        if (CollectionUtils.isNotEmpty(updateServiceInfoDTO.getPackageIds())) {
            List<Long> packageIds = updateServiceInfoDTO.getPackageIds().stream().distinct().toList();
            int row = packageInfoMapper.updateServiceIdByIds(packageIds, serviceId);
            if (row != packageIds.size()) {
                throw BusinessException.of("Update package service id failed");
            }
        }

        if (CollectionUtils.isNotEmpty(updateServiceInfoDTO.getWorkshopIds())) {
            List<Long> workshopIds = updateServiceInfoDTO.getWorkshopIds().stream().distinct().toList();
            for (var workshopId : workshopIds) {
                ServiceWorkshop serviceWorkshop = new ServiceWorkshop();
                serviceWorkshop.setServiceId(serviceId);
                serviceWorkshop.setWorkshopId(workshopId);
                int row = serviceWorkshopMapper.insertSelective(serviceWorkshop);
                if (row == 0) {
                    throw BusinessException.of("Insert service workshop failed");
                }
            }
        }
    }
}
