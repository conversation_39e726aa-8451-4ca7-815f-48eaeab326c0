package com.servauto.admin.service.customer.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.servauto.admin.dao.car.CarExtraMapper;
import com.servauto.admin.dao.car.CarInfoMapper;
import com.servauto.admin.dao.customer.CustomerInfoMapper;
import com.servauto.admin.dao.customer.CustomerShippingAddressMapper;
import com.servauto.admin.factory.customer.CustomerFactory;
import com.servauto.admin.model.dto.request.customer.EditCustomerCarInfoReqDTO;
import com.servauto.admin.model.dto.request.customer.EditCustomerReqDTO;
import com.servauto.admin.model.dto.request.customer.QueryCustomersDTO;
import com.servauto.admin.model.dto.response.car.CarInfoDTO;
import com.servauto.admin.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.admin.model.dto.response.customer.CustomerInfoDTO;
import com.servauto.admin.model.entity.car.CarExtra;
import com.servauto.admin.model.entity.car.CarInfo;
import com.servauto.admin.model.entity.customer.CustomerInfo;
import com.servauto.admin.model.entity.customer.CustomerShippingAddress;
import com.servauto.admin.service.car.CarInfoService;
import com.servauto.admin.service.customer.CustomerService;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.location.api.LocationService;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private CustomerShippingAddressMapper customerShippingAddressMapper;

    @Resource
    private CarInfoMapper carInfoMapper;

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private CarExtraMapper carExtraMapper;

    @Resource
    private LocationService locationService;


    @Override
    public PageInfo<CustomerInfoDTO> pageCustomers(QueryCustomersDTO queryCustomersDTO) {
        PageSupport.startPage();
        List<CustomerInfo> customerInfos = customerInfoMapper.selectByConditions(queryCustomersDTO);
        PageInfo<CustomerInfo> pageInfo = PageInfo.of(customerInfos);

        List<Long> customerIds = customerInfos.stream().map(CustomerInfo::getId).toList();
        Map<Long, List<CustomerAddressDTO>> customerAddressMap = this.getCustomerAddressesByCustomerIds(customerIds);
        List<CustomerInfoDTO> list = customerInfos.stream().map(e -> {
            CustomerInfoDTO customerInfoDTO = CustomerFactory.convert(e);
            customerInfoDTO.setAddresses(customerAddressMap.get(e.getId()));
            return customerInfoDTO;
        }).collect(Collectors.toList());

        return PageSupport.copyProperties(pageInfo, list);
    }

    @Override
    public Map<Long, List<CustomerAddressDTO>> getCustomerAddressesByCustomerIds(List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Maps.newHashMap();
        }

        Map<Long, List<CustomerAddressDTO>> customerAddressMap = Maps.newHashMap();
        List<CustomerShippingAddress> addresses = customerShippingAddressMapper.selectByCustomerIds(customerIds);
        if (CollectionUtils.isNotEmpty(addresses)) {
            Map<String, AreaDTO> areaMap = locationService.getAreaMap();
            Map<String, StateDTO> stateMap = locationService.getStateMap();
            List<CustomerAddressDTO> customerAddresses = addresses.stream().map(e -> CustomerFactory.convert(e, stateMap, areaMap)).toList();
            customerAddressMap = customerAddresses.stream().collect(Collectors.groupingBy(CustomerAddressDTO::getCustomerId));
        }

        return customerAddressMap;
    }


    @Override
    public CustomerInfoDTO getCustomerById(Long customerId) {
        CustomerInfo customerInfo = customerInfoMapper.selectByPrimaryKey(customerId);
        if (customerInfo == null) {
            throw BusinessException.of(ResponseCode.ERROR);
        }

        // customer info
        CustomerInfoDTO customerInfoDTO = CustomerFactory.convert(customerInfo);

        // customer car info
        List<CarInfoDTO> carInfoDTOList = carInfoService.carInfoList(customerInfo.getId());
        customerInfoDTO.setCarInfos(carInfoDTOList);

        // customer address
        Map<Long, List<CustomerAddressDTO>> addresses = this.getCustomerAddressesByCustomerIds(Collections.singletonList(customerId));
        customerInfoDTO.setAddresses(addresses.get(customerInfo.getId()));
        return customerInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerInfo(EditCustomerReqDTO reqDTO) {
        CustomerInfo customerInfo = new CustomerInfo();
        BeanUtils.copyProperties(reqDTO, customerInfo);

        customerInfo.setId(reqDTO.getCustomerId());
        if (reqDTO.getBirthday() != null && reqDTO.getBirthday() > 0) {
            customerInfo.setBirthday(new Date(reqDTO.getBirthday()));
        }

        int rows = customerInfoMapper.updateByPrimaryKeySelective(customerInfo);
        if (rows == 0) {
            throw BusinessException.of("Update customer failed");
        }

        long defaultCount = reqDTO.getAddresses().stream().filter(e -> e.getIsDefault() != null && e.getIsDefault()).count();
        if (defaultCount > 1) {
            throw BusinessException.of("Only one default address is allowed");
        }

        List<CustomerShippingAddress> addresses = customerShippingAddressMapper.selectByCustomerId(customerInfo.getId());
        if (CollectionUtils.isNotEmpty(addresses)) {
            rows = customerShippingAddressMapper.deleteByCustomerId(reqDTO.getCustomerId());
            if (rows == 0) {
                throw BusinessException.of("Update customer address failed");
            }
        }

        if (CollectionUtils.isNotEmpty(reqDTO.getAddresses())) {
            addresses = reqDTO.getAddresses().stream().map(e -> {
                CustomerShippingAddress customerShippingAddress = new CustomerShippingAddress();
                BeanUtils.copyProperties(e, customerShippingAddress);
                customerShippingAddress.setCustomerId(reqDTO.getCustomerId());
                customerShippingAddress.setIsDefault(e.getIsDefault() != null && e.getIsDefault());
                return customerShippingAddress;
            }).toList();
            customerShippingAddressMapper.batchInsert(addresses);
        }

        List<CarInfo> carInfoList = carInfoMapper.selectByCustomerId(customerInfo.getId());
        if (CollectionUtils.isNotEmpty(carInfoList)) {
            rows = carInfoMapper.deleteByCustomerId(reqDTO.getCustomerId());
            if (rows == 0) {
                throw BusinessException.of("Update car Info failed");
            }
        }
        if (CollectionUtils.isNotEmpty(reqDTO.getCarInfo())) {
            // 根据variantID 查询carExtra 并根据VariantId放到map中
            List<CarExtra> carExtras = carExtraMapper.selectCarExtraByIds(reqDTO.getCarInfo().stream().map(EditCustomerCarInfoReqDTO::getVariantId).toList());
            Map<Long, CarExtra> carExtraMap = new HashMap<>();
            for (CarExtra carExtra : carExtras) {
                carExtraMap.put(carExtra.getId(), carExtra);
            }

            carInfoList = reqDTO.getCarInfo().stream().map(e -> {
                CarInfo carInfo = new CarInfo();

                carInfo.setCustomerId(reqDTO.getCustomerId());
                carInfo.setBrandId(e.getBrandId());
                carInfo.setModelId(e.getModelId());
                carInfo.setYearId(e.getYearId());
                carInfo.setLicensePlate(e.getLicensePlate());
                carInfo.setCarMileage(e.getCarMileage());
                carInfo.setStatus((byte) 1);

                CarExtra carExtra = carExtraMap.get(e.getVariantId());
                if (carExtra != null) {
                    // 设置carInfo的相关属性，
                    carInfo.setCarLibId(carExtra.getCarLibId());
                    carInfo.setVariantId(carExtra.getId());

                }
                return carInfo;
            }).toList();
            carInfoMapper.batchInsert(carInfoList);
        }
    }
}
