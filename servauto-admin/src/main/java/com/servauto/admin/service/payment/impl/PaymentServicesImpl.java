package com.servauto.admin.service.payment.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.dao.payment.PaymentMapper;
import com.servauto.admin.factory.payment.PaymentFactory;
import com.servauto.admin.model.dto.request.payment.QueryPaymentDTO;
import com.servauto.admin.model.dto.response.payment.PaymentDTO;
import com.servauto.admin.model.entity.payment.Payment;
import com.servauto.admin.service.payment.PaymentServices;
import com.servauto.framework.utils.PageSupport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PaymentServicesImpl implements PaymentServices {
    @Resource
    private PaymentMapper paymentMapper;

    @Override
    public PaymentDTO queryPayment(Long sourceId) {
        Payment payment = paymentMapper.selectByPrimaryKey(sourceId);
        return PaymentFactory.convert(payment);
    }

    @Override
    public PageInfo<PaymentDTO> pagePayments(QueryPaymentDTO queryPaymentDTO) {
        PageSupport.startPage();
        List<Payment> payments = paymentMapper.selectByConditions(queryPaymentDTO);
        PageInfo<Payment> pageInfo = PageInfo.of(payments);
        List<PaymentDTO> list = payments.stream().map(PaymentFactory::convert).toList();
        return PageSupport.copyProperties(pageInfo, list);
    }
}
