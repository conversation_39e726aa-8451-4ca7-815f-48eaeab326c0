package com.servauto.admin.service.car.impl;

import com.servauto.admin.dao.car.CarYearsMapper;
import com.servauto.admin.factory.car.CarYearsFactory;
import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.dto.response.car.CarModelsDTO;
import com.servauto.admin.model.dto.response.car.CarYearsDTO;
import com.servauto.admin.model.entity.car.CarYears;
import com.servauto.admin.service.car.CarYearsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CarYearsServiceImpl implements CarYearsService {

    @Resource
    private CarYearsMapper carYearsMapper;

    //根据modelId查询CarYearsDTO
    @Override
    public List<CarYearsDTO> getCarYearsByModelId(Long modelId) {
        List<CarYears> carYearsList = carYearsMapper.selectByModelId(modelId);
        return carYearsList.stream().map(CarYearsFactory::convert).collect(Collectors.toList());
    }

    @Override
    public void insertCarYearsIfNotExists(List<CarYears> collect) {
        collect.forEach(carYears -> {
            carYearsMapper.insertCarYearsIfNotExists(carYears);
        });
    }

    @Override
    public void batchInsertCarYears(List<CarYears> collect) {
        carYearsMapper.batchInsertCarYears(collect);
    }

    @Override
    public List<CarYearsDTO> selectAllYearsByModelIdAndModels(Long modelId, List<String> years) {
        List<CarYears> carYearsList = carYearsMapper.selectByModelIdAndYears(modelId, years);
        return carYearsList.stream().map(CarYearsFactory::convert).collect(Collectors.toList());
    }

    @Override
    public List<CarKeyValueDTO> getCarYearsByModelIdKv(Long modelId) {
        List<CarYears> carYearsList = carYearsMapper.selectByModelId(modelId);
        return carYearsList.stream().map(CarYearsFactory::convertKv).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CarYears> selectCarYearsMap(List<Long> yearsIds) {
        // 如果输入为空，直接返回空Map
        if (yearsIds == null || yearsIds.isEmpty()) {
            // 使用不可变的空Map，避免不必要的对象创建
            return Collections.emptyMap();
        }

        // 调用数据库查询方法，并检查返回值是否为null
        List<CarYears> carYears = carYearsMapper.selectByPrimaryKeys(yearsIds);
        if (carYears == null) {
            // 数据库查询结果为空时，返回空Map
            return Collections.emptyMap();
        }

        // 使用Stream API构建Map，确保id不为null 过滤掉无效的carYear
        return carYears.stream().filter(carYear -> carYear != null && carYear.getId() != null).collect(Collectors.toMap(CarYears::getId, carYear -> carYear, (existing, replacement) -> existing));
    }
}