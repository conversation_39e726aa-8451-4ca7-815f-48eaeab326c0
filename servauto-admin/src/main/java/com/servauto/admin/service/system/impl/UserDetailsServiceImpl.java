package com.servauto.admin.service.system.impl;

import com.servauto.admin.model.entity.system.SysRole;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.service.SysPasswordService;
import com.servauto.admin.security.service.SysPermissionService;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.enums.UserStatus;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.MessageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysPasswordService sysPasswordService;

    @Resource
    private SysPermissionService sysPermissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = sysUserService.selectUserByUserName(username);

        if (StringUtils.isNull(user)) {
            log.info("Login user: {} does not exist.", username);
            throw BusinessException.of(MessageUtils.message("user.not.exists"));
        }

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("Logged in user: {} has been deleted.", username);
            throw BusinessException.of(MessageUtils.message("user.password.delete"));
        }

        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("Logged in user: {} has been deactivated.", username);
            throw BusinessException.of(MessageUtils.message("user.blocked"));
        }

        sysPasswordService.validate(user);
        List<SysRole> userRoles = sysRoleService.selectRolesByUserId(user.getUserId());
        if (CollectionUtils.isNotEmpty(userRoles)) {
            user.setRoles(userRoles);
            user.setRoleIds(userRoles.stream().map(SysRole::getRoleId).toArray(Long[]::new));
        }
        Set<String> permissions = sysPermissionService.getMenuPermission(user);
        log.info("Logged in user: {} roles: {} , permissions {}", username, userRoles, permissions);
        return new LoginUser(user.getUserId(), user, permissions);
    }

}
