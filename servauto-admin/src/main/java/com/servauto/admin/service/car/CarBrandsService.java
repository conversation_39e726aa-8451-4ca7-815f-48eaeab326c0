package com.servauto.admin.service.car;

import com.servauto.admin.model.dto.response.car.CarBrandsDTO;
import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.entity.car.CarBrands;

import java.util.List;
import java.util.Map;

public interface CarBrandsService {
    void batchInsertCarBrands(List<CarBrands> carBrandsList);

    // 新增方法：查询所有有效的brands
    List<CarKeyValueDTO> selectAllValidBrandsKv();

    // 新增方法：查询所有有效的brands
    List<CarBrandsDTO> selectAllValidBrands();

    // 新增方法：查询brands by brandNames
    List<CarBrandsDTO> selectBrandsByBrandNames(List<String> brandNames);

    Map<Long, CarBrands> selectCarBrandsMap(List<Long> brandIds);

}