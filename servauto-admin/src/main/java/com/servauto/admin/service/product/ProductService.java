package com.servauto.admin.service.product;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.dto.request.product.UpdateProductDTO;
import com.servauto.admin.model.dto.response.product.ProductAttributeDTO;
import com.servauto.admin.model.dto.response.product.ProductBaseDTO;
import com.servauto.admin.model.dto.response.product.ProductDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;

import java.util.List;

public interface ProductService {

    /**
     * get product by id
     *
     * @param id id
     * @return ProductDTO
     */
    ProductDTO getProduct(long id);

    /**
     * get products by reqDTO
     *
     * @param reqDTO reqDTO
     * @return List<ProductBaseDTO>
     */
    List<ProductBaseDTO> getProducts(QueryProductsDTO reqDTO);

    /**
     * page products by reqDTO
     *
     * @param reqDTO reqDTO
     * @return PageInfo<ProductBaseDTO>
     */
    PageInfo<ProductBaseDTO> pageProducts(QueryProductsDTO reqDTO);

    /**
     * update product
     *
     * @param updateProductDTO updateProductDTO
     */
    void updateProduct(UpdateProductDTO updateProductDTO);

    /**
     * insert product
     *
     * @param updateProductDTO updateWorkshopDTO
     */
    void insertProduct(UpdateProductDTO updateProductDTO);

    /**
     * get product categories
     *
     * @return List<IdNameDTO>
     */
    List<IdNameDTO> getProductCategories();

    /**
     * get product brands
     *
     * @return List<IdNameDTO>
     */
    List<IdNameDTO> getProductBrands();

    /**
     * get product attributes
     *
     * @return List<ProductAttributeDTO>
     */
    List<ProductAttributeDTO> getProductAttributes(String displayType, long categoryId);
}
