package com.servauto.admin.service.notice.impl;

import com.google.common.base.Preconditions;
import com.servauto.admin.enums.order.OrderDeliveryTypeEnum;
import com.servauto.admin.model.dto.response.customer.CustomerInfoDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.model.entity.customer.CustomerInfo;
import com.servauto.admin.model.entity.order.Order;
import com.servauto.admin.service.customer.CustomerService;
import com.servauto.admin.service.notice.NoticeService;
import com.servauto.admin.service.workshop.WorkshopService;
import com.servauto.common.enums.TriggerType;
import com.servauto.common.utils.DateUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.lark.MessageTriggerStrategy;
import com.servauto.framework.lark.MessageTriggerStrategyFactory;
import com.servauto.framework.lark.TriggerMessageReq;
import com.servauto.framework.sms.api.dto.SmsSendContentReqDTO;
import com.servauto.framework.sms.api.service.SmsSendApi;
import com.servauto.framework.sms.enums.SceneTemplateEnum;
import com.servauto.framework.sms.enums.UserTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import static com.servauto.common.utils.DateUtils.YYYY_MM_DD_HH_MM_PATTERN;

/**
 * <p>NoticeServiceImpl</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/16 19:40
 */
@Service
@Slf4j
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private SmsSendApi noticeApi;

    @Resource
    private WorkshopService workshopService;

    @Resource
    private CustomerService customerService;

    @Resource
    private Environment environment;

    /**
     * You have an order service time that has been changed. Please handle it on time：
     * #Order ID：{Order ID}
     * #Order Type：{Order Type}
     * #Car Plate Number：{Car Plate Number}
     * #Service Name：{Service Name}
     * #Appointment Info：
     * - Store：{Store Name}
     * - Time：{Appointment Time（yyyy-mm-dd hh：mm）}
     *
     * @param order order
     */
    @Override
    public void onRescheduleSendWhatsapp(Order order) {
        log.info("the order {} appointment changed pending send whatsapp message", order.getOrderNo());
        if (!OrderDeliveryTypeEnum.isInWorkshop(order.getDeliveryType())) {
            log.info("the order {} appointment changed delivery type is {} skip send whatsapp message", order.getOrderNo(), order.getDeliveryType());
            return;
        }

        if (order.getWorkshopId() == null || order.getReservationTime().getTime() <= 0) {
            log.info("the order {} appointment changed  workshop {} is null || order reservation {} is null skip send whatsapp message", order.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
            return;
        }

        WorkshopDetailDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());
        if (StringUtils.isEmpty(workshopInfo.getWhatsAppNumber())) {
            return;
        }

        // ["orderNo","orderType","licensePlate","serviceName","workshopName","time"]
        for (String number : workshopInfo.getWhatsAppNumber().split(";")) {
            Map<String, Object> templateParams = new LinkedHashMap<>();
            templateParams.put("orderNo", order.getOrderNo());
            templateParams.put("orderType", OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
            templateParams.put("licensePlate", order.getLicensePlate());
            templateParams.put("serviceName", order.getServiceName());
            templateParams.put("workshopName", order.getWorkshopName());
            templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));

            noticeApi.sendContent(SmsSendContentReqDTO.builder()
                    .mobile(number)
                    .templateCode(SceneTemplateEnum.RESCHEDULE.getTemplateCode())
                    .userId(order.getCustomerId())
                    .userType(UserTypeEnum.MEMBER.getValue())
                    .templateParams(templateParams)
                    .build());
        }

        log.info("the order {} appointment changed send whatsapp message succeed", order.getOrderNo());
    }

    /**
     * Dear Customer, your service appointment has been rescheduled.We sincerely apologize for any inconvenience caused by the adjustment to your appointment. We’ve updated your service booking details as follows:
     * - New Date & Time: [Month Day, Year]  [HH:MM AM/PM]
     * - Service Store: [Store Name][Store Address]
     *
     * @param order order
     */
    @Override
    public void onRescheduleSendWhatsappToCustomer(Order order) {
        CustomerInfoDTO customerInfo = customerService.getCustomerById(order.getCustomerId());
        WorkshopDetailDTO workshopInfo = workshopService.getWorkshopDetail(order.getWorkshopId());

        LocalDateTime ldt = order.getReservationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        String date = ldt.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH));
        String time = ldt.format(DateTimeFormatter.ofPattern("hh:mm a", Locale.ENGLISH));

        // ["date","time","workshopName","address"]
        Map<String, Object> templateParams = new LinkedHashMap<>();
        templateParams.put("date", date);
        templateParams.put("time", time);
        templateParams.put("workshopName", order.getWorkshopName());
        templateParams.put("address", String.format("%s %s %s ", workshopInfo.getCityName(), workshopInfo.getStateName(), workshopInfo.getAddress()));

        String mobile = "test".equals(environment.getProperty("spring.profiles.active")) ? "8618210378280" : customerInfo.getMobile();

        noticeApi.sendContent(SmsSendContentReqDTO.builder()
                .mobile(mobile)
                .templateCode(SceneTemplateEnum.RESCHEDULE_CUSTOMER.getTemplateCode())
                .userId(order.getCustomerId())
                .userType(UserTypeEnum.MEMBER.getValue())
                .templateParams(templateParams)
                .build());
    }

    @Override
    public void advanceReminderOneDay(Order order) {

        CustomerInfoDTO customerInfo = customerService.getCustomerById(order.getCustomerId());
        Preconditions.checkNotNull(customerInfo);

        WorkshopDetailDTO workshopDetail = workshopService.getWorkshopDetail(order.getWorkshopId());
        Preconditions.checkNotNull(workshopDetail);

        // ["serviceName","time","workshopName","address","contactNumber"]
        Map<String, Object> templateParams = new LinkedHashMap<>();
        templateParams.put("serviceName", order.getServiceName());
        templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));
        templateParams.put("workshopName", order.getWorkshopName());
        templateParams.put("address", workshopDetail.getCityName() + "," + workshopDetail.getStateName() + "," + workshopDetail.getAddress());
        templateParams.put("contactNumber", workshopDetail.getContactNumber());

        String mobile = "test".equals(environment.getProperty("spring.profiles.active")) ? "8618210378280" : customerInfo.getMobile();

        noticeApi.sendContent(SmsSendContentReqDTO.builder()
                .mobile(mobile)
                .templateCode(SceneTemplateEnum.ONE_DAY_ADVANCE_REMINDER.getTemplateCode())
                .userId(customerInfo.getCustomerId())
                .userType(UserTypeEnum.MEMBER.getValue())
                .templateParams(templateParams)
                .build());
    }

    @Override
    public void advanceReminderTwoHour(Order order) {
        CustomerInfoDTO customerInfo = customerService.getCustomerById(order.getCustomerId());
        Preconditions.checkNotNull(customerInfo);

        WorkshopDetailDTO workshopDetail = workshopService.getWorkshopDetail(order.getWorkshopId());
        Preconditions.checkNotNull(workshopDetail);

        // ["serviceName","workshopName","address","time","contactNumber"]
        Map<String, Object> templateParams = new LinkedHashMap<>();
        templateParams.put("serviceName", order.getServiceName());
        templateParams.put("workshopName", order.getWorkshopName());
        templateParams.put("address", workshopDetail.getCityName() + "," + workshopDetail.getStateName() + "," + workshopDetail.getAddress());
        templateParams.put("time", DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));
        templateParams.put("contactNumber", workshopDetail.getContactNumber());

        String mobile = "test".equals(environment.getProperty("spring.profiles.active")) ? "8618210378280" : customerInfo.getMobile();

        noticeApi.sendContent(SmsSendContentReqDTO.builder()
                .mobile(mobile)
                .templateCode(SceneTemplateEnum.TWO_HOUR_ADVANCE_REMINDER.getTemplateCode())
                .userId(customerInfo.getCustomerId())
                .userType(UserTypeEnum.MEMBER.getValue())
                .templateParams(templateParams)
                .build());
    }

    @Override
    public void onWorkshopRescheduleSendLark(Order order) {
        log.info("the order {} pending send lark message", order.getOrderNo());
        MessageTriggerStrategy strategy = MessageTriggerStrategyFactory.getStrategy(TriggerType.CONFIRM);

        TriggerMessageReq req = new TriggerMessageReq();
        req.setOrderId(order.getOrderNo());
        req.setOrderType(OrderDeliveryTypeEnum.getByCode(order.getDeliveryType()).getMsg());
        req.setCarPlateNumber(order.getLicensePlate());
        req.setServiceName(order.getServiceName());
        req.setStoreName(order.getWorkshopName());
        req.setTime(DateUtils.format(order.getReservationTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));
        req.setPreferTime(DateUtils.format(order.getSuggestionTime(), YYYY_MM_DD_HH_MM_PATTERN, ZoneId.systemDefault()));
        strategy.doProcess(req);
        log.info("the order {} pending send lark succeed", order.getOrderNo());
    }

}
