package com.servauto.admin.service.workshop;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.admin.model.dto.request.workshop.UpdateWorkshopDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;

import java.util.List;

public interface WorkshopService {

    /**
     * page workshops by conditions
     *
     * @param queryWorkshopsDTO reqDTO
     * @return PageInfo<WorkshopDTO>
     */
    PageInfo<WorkshopDTO> pageWorkshops(QueryWorkshopsDTO queryWorkshopsDTO);

    /**
     * get workshop detail by id
     *
     * @param id id
     * @return WorkshopDetailDTO
     */
    WorkshopDetailDTO getWorkshopDetail(long id);

    /**
     * update workshop
     *
     * @param updateWorkshopDTO updateWorkshopDTO
     */
    void updateWorkshop(UpdateWorkshopDTO updateWorkshopDTO);

    /**
     * insert workshop
     *
     * @param updateWorkshopDTO updateWorkshopDTO
     */
    void insertWorkshop(UpdateWorkshopDTO updateWorkshopDTO);

    /**
     * get workshop names
     *
     * @return List<IDNameDTO>
     */
    List<IdNameDTO> getWorkshopNames();
    List<IdNameDTO> getWorkshopNames(List<Long> ids);
}
