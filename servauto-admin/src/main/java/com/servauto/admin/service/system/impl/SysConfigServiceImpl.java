package com.servauto.admin.service.system.impl;

import com.servauto.admin.dao.system.SysConfigMapper;
import com.servauto.admin.model.entity.system.SysConfig;
import com.servauto.admin.service.system.SysConfigService;
import com.servauto.common.constant.CacheConstants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SysConfigServiceImpl implements SysConfigService {

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Override
    @Cacheable(cacheNames = CacheConstants.SYS_CONFIG_KEY, key = "#configKey", unless = "#result == null")
    public String selectConfigByKey(String configKey) {
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = sysConfigMapper.selectConfig(config);
        return retConfig != null ? retConfig.getConfigValue() : null;
    }

}
