package com.servauto.admin.service.serviceinfo;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.admin.model.dto.request.serviceinfo.UpdateServiceInfoDTO;
import com.servauto.admin.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceInfoDTO;

import java.util.List;

public interface ServiceInfoService {

    /**
     * get service info by id
     *
     * @param id id
     * @return ServiceInfoDTO
     */
    ServiceInfoDTO getService(long id);

    /**
     * get services
     *
     * @param reqDTO reqDTO
     * @return List<ServiceInfoDTO>
     */
    List<ServiceInfoDTO> getServices(QueryServicesDTO reqDTO);

    /**
     * page services
     *
     * @param reqDTO reqDTO
     * @return PageInfo<ServiceInfoDTO>
     */
    PageInfo<ServiceInfoDTO> pageServices(QueryServicesDTO reqDTO);

    /**
     * update service info
     *
     * @param updateServiceInfoDTO updateServiceInfoDTO
     */
    void updateServiceInfo(UpdateServiceInfoDTO updateServiceInfoDTO);

    /**
     * insert service info
     *
     * @param updateServiceInfoDTO updateServiceInfoDTO
     */
    void insertServiceInfo(UpdateServiceInfoDTO updateServiceInfoDTO);

}
