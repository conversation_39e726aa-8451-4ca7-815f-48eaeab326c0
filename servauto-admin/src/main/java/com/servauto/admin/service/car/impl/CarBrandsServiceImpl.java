package com.servauto.admin.service.car.impl;

import com.servauto.admin.dao.car.CarBrandsMapper;
import com.servauto.admin.factory.car.CarBrandsFactory;
import com.servauto.admin.model.dto.response.car.CarBrandsDTO;
import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.entity.car.CarBrands;
import com.servauto.admin.service.car.CarBrandsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CarBrandsServiceImpl implements CarBrandsService {

    @Resource
    private CarBrandsMapper carBrandsMapper;


    // 新增方法：查询所有有效的brands
    @Override
    public List<CarBrandsDTO> selectAllValidBrands() {
        List<CarBrands> carBrandsList = carBrandsMapper.selectAllValidBrands();
        return carBrandsList.stream().map(CarBrandsFactory::convert).collect(Collectors.toList());
    }

    @Override
    public List<CarBrandsDTO> selectBrandsByBrandNames(List<String> brandNames) {

        return carBrandsMapper.selectBrandsByBrandNames(brandNames);
    }

    // 新增方法：批量插入品牌，如果已存在则跳过
    @Override
    public void batchInsertCarBrands(List<CarBrands> carBrandsList) {
        carBrandsMapper.batchInsertCarBrands(carBrandsList);
    }

    @Override
    public List<CarKeyValueDTO> selectAllValidBrandsKv() {
        List<CarBrands> carBrandsList = carBrandsMapper.selectAllValidBrands();
        return carBrandsList.stream().map(CarBrandsFactory::convertKv).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CarBrands> selectCarBrandsMap(List<Long> brandIds) {
        // 如果输入为空，直接返回空Map
        if (brandIds == null || brandIds.isEmpty()) {
            // 使用不可变的空Map，避免不必要的对象创建
            return Collections.emptyMap();
        }

        // 调用数据库查询方法，并检查返回值是否为null
        List<CarBrands> carBrands = carBrandsMapper.selectByPrimaryKeys(brandIds);
        if (carBrands == null) {
            // 数据库查询结果为空时，返回空Map
            return Collections.emptyMap();
        }

        // 使用Stream API构建Map，确保id不为null 过滤掉无效的CarBrands
        return carBrands.stream().filter(carBrand -> carBrand != null && carBrand.getId() != null).collect(Collectors.toMap(CarBrands::getId, carBrand -> carBrand, (existing, replacement) -> existing));
    }

}