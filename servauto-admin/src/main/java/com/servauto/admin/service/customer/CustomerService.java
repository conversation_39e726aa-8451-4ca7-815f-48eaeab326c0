package com.servauto.admin.service.customer;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.customer.EditCustomerReqDTO;
import com.servauto.admin.model.dto.request.customer.QueryCustomersDTO;
import com.servauto.admin.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.admin.model.dto.response.customer.CustomerInfoDTO;

import java.util.List;
import java.util.Map;

public interface CustomerService {

    /**
     * get customer info by mobile
     *
     * @param queryCustomersDTO reqDTO
     * @return List<CustomerInfoDTO>
     */
    PageInfo<CustomerInfoDTO> pageCustomers(QueryCustomersDTO queryCustomersDTO);

    /**
     * get customer info by id
     *
     * @param customerId id
     * @return CustomerInfoDTO
     */
    CustomerInfoDTO getCustomerById(Long customerId);

    /**
     * edit customer info
     *
     * @param reqDTO req
     */
    void updateCustomerInfo(EditCustomerReqDTO reqDTO);

    /**
     * Get customer locations
     *
     * @param customerIds customerIds
     * @return Map<Long, List < CustomerAddressDTO>>
     */
    Map<Long, List<CustomerAddressDTO>> getCustomerAddressesByCustomerIds(List<Long> customerIds);
}
