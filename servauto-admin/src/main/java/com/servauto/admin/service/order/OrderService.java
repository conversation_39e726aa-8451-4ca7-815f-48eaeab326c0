package com.servauto.admin.service.order;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.order.*;
import com.servauto.admin.model.dto.response.order.OrderDTO;
import com.servauto.admin.model.dto.response.order.OrderWorkshopDTO;
import com.servauto.admin.model.dto.response.order.PageOrderDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.security.LoginUser;

import java.util.List;

public interface OrderService {

    /**
     * query orders
     *
     * @param current  pageNo
     * @param pageSize pageSize
     * @param reqDTO   req
     * @return PageInfo<PageOrderDTO>
     */
    PageInfo<PageOrderDTO> pageOrders(Integer pageNo, Integer pageSize, PageOrderReqDTO reqDTO);

    /**
     * query order
     *
     * @param orderNo orderNo
     * @return OrderDTO
     */
    OrderDTO queryOrderByOrderNo(String orderNo);


    /**
     * cancel order
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void cancelOrder(LoginUser operator, CancelOrderReqDTO reqDTO);

    /**
     * delivery order
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void deliveryOrder(LoginUser operator, DeliveryOrderReqDTO reqDTO);

    /**
     * shipping order
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void shippingOrder(LoginUser operator, ShippingOrderReqDTO reqDTO);

    /**
     * reschedule workshop
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void reschedule(LoginUser operator, RescheduleReqDTO reqDTO);

    /**
     * pickup order
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void pickup(LoginUser operator, PickupOrderReqDTO reqDTO);

    /**
     * finish order
     */
    void finishOrders();

    /**
     * query order workshops
     *
     * @param orderNo orderNo
     * @return support workshops
     */
    List<IdNameDTO> queryOrderWorkshops(String orderNo);

    /**
     * confirm order
     *
     * @param operator LoginUser
     * @param orderNo  orderNo
     */
    void confirm(LoginUser operator, String orderNo);

    /**
     * workshop reschedule
     *
     * @param operator LoginUser
     * @param reqDTO   reqDTO
     */
    void workshopReschedule(LoginUser operator, WorkshopRescheduleReqDTO reqDTO);

    /**
     * query order workshop opening hours
     *
     * @param orderNo    orderNo
     * @param workshopId workshopId
     * @return OrderWorkshopDTO
     */
    OrderWorkshopDTO queryOrderWorkshopOpeningHours(String orderNo, Long workshopId);
}

