package com.servauto.admin.service.packageinfo.impl;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.dao.packageinfo.PackageInfoMapper;
import com.servauto.admin.dao.packageinfo.PackageProductMapper;
import com.servauto.admin.dao.product.ProductMapper;
import com.servauto.admin.dao.serviceinfo.ServiceInfoMapper;
import com.servauto.admin.enums.DeliveryMode;
import com.servauto.admin.enums.SaleStatus;
import com.servauto.admin.enums.YesOrNo;
import com.servauto.admin.factory.packageinfo.PackageInfoFactory;
import com.servauto.admin.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.admin.model.dto.request.packageinfo.UpdatePackageInfoDTO;
import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.admin.model.entity.packageinfo.PackageInfo;
import com.servauto.admin.model.entity.packageinfo.PackageProduct;
import com.servauto.admin.model.entity.product.Product;
import com.servauto.admin.model.entity.serviceinfo.ServiceInfo;
import com.servauto.admin.service.packageinfo.PackageInfoService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.utils.PageSupport;
import com.servauto.framework.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.servauto.admin.enums.ServiceSupportedOn.PACKAGE;

@Slf4j
@Service
public class PackageInfoServiceImpl implements PackageInfoService {
    @Resource
    private PackageInfoMapper packageInfoMapper;

    @Resource
    private PackageProductMapper packageProductMapper;

    @Resource
    private ServiceInfoMapper serviceInfoMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private SysUserService sysUserService;

    public PackageInfoDTO getPackage(long id) {
        if (id == 0) {
            throw BusinessException.of("Invalid package id");
        }
        var packageInfo = packageInfoMapper.selectByPrimaryKey(id);
        if (packageInfo == null) {
            throw BusinessException.of("Invalid package id");
        }
        var packageInfoDTO = PackageInfoFactory.convert(packageInfo);
        List<Long> productIds = packageProductMapper.selectByPackageId(id).stream().map(PackageProduct::getProductId).toList();
        if (CollectionUtils.isNotEmpty(productIds)) {
            var products = productMapper.selectByConditions(QueryProductsDTO.builder().ids(productIds).status(SaleStatus.ACTIVE.getCode()).build());
            if (CollectionUtils.isNotEmpty(products)) {
                packageInfoDTO.setProductIds(products.stream().map(Product::getId).toList());
            }
        }
        packageInfoDTO.setDeliveryModes(getDeliveryModes(packageInfo));
        return packageInfoDTO;
    }

    public List<PackageInfoDTO> getPackages(QueryPackagesDTO reqDTO) {
        var packageInfos = packageInfoMapper.selectByConditions(reqDTO);
        List<PackageInfoDTO> resp = packageInfos.stream().map(PackageInfoFactory::convert).toList();
        List<Long> serviceIds = packageInfos.stream().map(PackageInfo::getServiceId).toList();
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            HashMap<Long, ServiceInfo> serviceInfoMap = serviceInfoMapper.selectByIds(serviceIds).stream().collect(Collectors.toMap(ServiceInfo::getId, serviceInfo -> serviceInfo, (existing, replacement) -> existing, HashMap::new));
            for (var info : resp) {
                if (info.getServiceId() > 0) {
                    info.setServiceName(serviceInfoMap.get(info.getServiceId()).getName());
                }
            }
        }
        for (var info : resp) {
            if (info.getUpdatedBy() > 0) {
                info.setUpdaterName(sysUserService.selectUserById(info.getUpdatedBy()).getUserName());
            }
        }
        return resp;
    }

    public PageInfo<PackageInfoDTO> pagePackages(QueryPackagesDTO reqDTO) {
        PageSupport.startPage();
        var packageInfos = packageInfoMapper.selectByConditions(reqDTO);
        PageInfo<PackageInfo> pageInfo = PageInfo.of(packageInfos);

        List<PackageInfoDTO> dtos = packageInfos.stream().map(PackageInfoFactory::convert).toList();
        List<Long> serviceIds = packageInfos.stream().map(PackageInfo::getServiceId).toList();
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            HashMap<Long, ServiceInfo> serviceInfoMap = serviceInfoMapper.selectByIds(serviceIds).stream().collect(Collectors.toMap(ServiceInfo::getId, serviceInfo -> serviceInfo, (existing, replacement) -> existing, HashMap::new));
            for (var info : dtos) {
                if (info.getServiceId() > 0) {
                    info.setServiceName(serviceInfoMap.get(info.getServiceId()).getName());
                }
            }
        }
        for (var info : dtos) {
            if (info.getUpdatedBy() > 0) {
                info.setUpdaterName(sysUserService.selectUserById(info.getUpdatedBy()).getUserName());
            }
        }

        return PageSupport.copyProperties(pageInfo, dtos);
    }

    public void updatePackage(UpdatePackageInfoDTO updatePackageInfoDTO) {
        if (updatePackageInfoDTO.getId() == 0) {
            throw BusinessException.of("Invalid package id");
        }
        validateUpdatePackageInfoDTO(updatePackageInfoDTO);

        PackageInfo info = new PackageInfo();
        BeanUtils.copyProperties(updatePackageInfoDTO, info, "deliveryModes");
        setIsFixedPrice(updatePackageInfoDTO, info);
        validateAndSetDeliveryModes(updatePackageInfoDTO, info);

        int rows = packageInfoMapper.updateByPrimaryKeySelective(info);
        if (rows == 0) {
            throw BusinessException.of("Update package failed");
        }

        packageProductMapper.deleteByPackageId(info.getId());
        handlePackageProduct(info.getId(), updatePackageInfoDTO);
    }

    public void insertPackage(UpdatePackageInfoDTO updatePackageInfoDTO) {
        validateUpdatePackageInfoDTO(updatePackageInfoDTO);

        PackageInfo info = new PackageInfo();
        BeanUtils.copyProperties(updatePackageInfoDTO, info, "deliveryModes");
        setIsFixedPrice(updatePackageInfoDTO, info);
        validateAndSetDeliveryModes(updatePackageInfoDTO, info);

        int rows = packageInfoMapper.insertSelective(info);
        if (rows == 0) {
            throw BusinessException.of("Insert package failed");
        }

        handlePackageProduct(info.getId(), updatePackageInfoDTO);
    }

    private List<String> getDeliveryModes(PackageInfo src) {
        List<String> list = new ArrayList<>();
        for (int i = 0; (1 << i) < DeliveryMode.MAX_ENUM.getCode(); i++) {
            Integer j = 1 << i;
            if ((src.getDeliveryModes() & j) > 0) {
                list.add(j.toString());
            }
        }
        return list;
    }

    private void setIsFixedPrice(UpdatePackageInfoDTO src, PackageInfo dst) {
        dst.setIsFixedPrice(
                (src.getPrice() == null || src.getPrice().isEmpty())
                        ? YesOrNo.NO.getCode()
                        : YesOrNo.YES.getCode()
        );
        if (YesOrNo.yes(dst.getIsFixedPrice())) {
            dst.setPrice(new BigDecimal(src.getPrice()));
        }
    }

    private void validateAndSetDeliveryModes(UpdatePackageInfoDTO src, PackageInfo dst) {
        int deliveryModes = 0;
        if (CollectionUtils.isEmpty(src.getDeliveryModes())) {
            dst.setDeliveryModes(deliveryModes);
            return;
        }
        for (var typ : src.getDeliveryModes()) {
            if (typ == null) {
                throw BusinessException.of("delivery mode can not be null");
            }
            deliveryModes |= typ;
        }
        dst.setDeliveryModes(deliveryModes);
    }

    private void validateUpdatePackageInfoDTO(UpdatePackageInfoDTO updatePackageInfoDTO) {
        if (CollectionUtils.isEmpty(updatePackageInfoDTO.getProductIds())) {
            throw BusinessException.of("Invalid product id");
        }

        if (updatePackageInfoDTO.getServiceId() != null && updatePackageInfoDTO.getServiceId() > 0) {
            var serviceInfo = serviceInfoMapper.selectByPrimaryKey(updatePackageInfoDTO.getServiceId());
            if (serviceInfo == null) {
                throw BusinessException.of("Invalid service id");
            }
            if (!serviceInfo.getSupportedOn().equals(PACKAGE.getCode())) {
                throw BusinessException.of("service does not support the product");
            }
        }

        var products = productMapper.selectByConditions(QueryProductsDTO.builder().ids(updatePackageInfoDTO.getProductIds()).build());
        if (products.size() != updatePackageInfoDTO.getProductIds().size()) {
            throw BusinessException.of("Invalid product id");
        }
        for (var product : products) {
            if (!SaleStatus.active(product.getStatus())) {
                throw BusinessException.of("Including unlisted products");
            }
        }
    }

    private void handlePackageProduct(Long packageId, UpdatePackageInfoDTO updatePackageInfoDTO) {
        for (var productId : updatePackageInfoDTO.getProductIds()) {
            var packageProduct = new PackageProduct();
            packageProduct.setPackageId(packageId);
            packageProduct.setProductId(productId);
            int rows = packageProductMapper.insertSelective(packageProduct);
            if (rows == 0) {
                throw BusinessException.of("Insert package product failed");
            }
        }
    }
}
