package com.servauto.admin.service.system.impl;

import com.servauto.admin.dao.system.SysLoginInfoMapper;
import com.servauto.admin.dao.system.SysOperLogMapper;
import com.servauto.admin.model.entity.system.SysLoginInfo;
import com.servauto.admin.model.entity.system.SysOperLog;
import com.servauto.admin.service.system.SysOperaLogService;
import com.servauto.common.constant.Constants;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.ip.AddressUtils;
import com.servauto.framework.utils.ip.IpUtils;
import eu.bitwalker.useragentutils.UserAgent;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysOperaLogServiceImpl implements SysOperaLogService {
    
    @Resource
    private SysOperLogMapper sysOperLogMapper;


    @Resource
    private SysLoginInfoMapper sysLoginInfoMapper;

    public void recordLoginInfo(HttpServletRequest request, final String username, final String status, final String message,
                                final Object... args) {
        final UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        final String ip = IpUtils.getIpAddr(request);
        String address = AddressUtils.getRealAddressByIP(ip);
        String s = getBlock(ip) + address + getBlock(username) + getBlock(status) + getBlock(message);

        log.info(s, args);
        String os = userAgent.getOperatingSystem().getName();
        String browser = userAgent.getBrowser().getName();
        SysLoginInfo loginInfo = new SysLoginInfo();
        loginInfo.setUserName(username);
        loginInfo.setIpaddr(ip);
        loginInfo.setLoginLocation(address);
        loginInfo.setBrowser(browser);
        loginInfo.setOs(os);
        loginInfo.setMsg(message);

        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            loginInfo.setStatus(Constants.SUCCESS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            loginInfo.setStatus(Constants.FAIL);
        }

        sysLoginInfoMapper.insertLoginInfo(loginInfo);
    }

    private static String getBlock(Object msg) {
        if (msg == null) {
            msg = "";
        }
        return "[" + msg + "]";
    }

    @Async
    public void recordOperaLog(final SysOperLog operaLog) {
        operaLog.setOperLocation(AddressUtils.getRealAddressByIP(operaLog.getOperIp()));
        sysOperLogMapper.insertOperlog(operaLog);
    }


}
