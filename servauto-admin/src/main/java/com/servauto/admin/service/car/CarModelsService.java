package com.servauto.admin.service.car;

import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.dto.response.car.CarModelsDTO;
import com.servauto.admin.model.entity.car.CarModels;

import java.util.List;
import java.util.Map;

public interface CarModelsService {
    // 新增方法：根据品牌ID查询所有有效的车型
    List<CarModelsDTO> selectAllModelsByBrandId(Long brandId);

    // 新增方法：根据品牌ID查询所有有效的车型
    List<CarKeyValueDTO> selectAllModelsByBrandIdKv(Long brandId);

    // 新增方法：根据品牌ID和modelsName 查询所有有效的车型
    List<CarModelsDTO> selectAllModelsByBrandIdAndModels(Long brandId, List<String> modelsNames);

    // 新增方法：查询所有有效的车型
    List<CarModelsDTO> selectAllValidModels();

    void insertCarModelsIfNotExists(List<CarModels> carModel);

    void batchInsertCarModels(List<CarModels> carModel);

    Map<Long, CarModels> selectCarModelsMap(List<Long> modelIds);

}