package com.servauto.admin.service.system.impl;

import com.servauto.admin.dao.system.SysRoleMapper;
import com.servauto.admin.dao.system.SysUserMapper;
import com.servauto.admin.dao.system.SysUserRoleMapper;
import com.servauto.admin.model.dto.request.system.QueryUserReqDTO;
import com.servauto.admin.model.entity.system.SysRole;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.model.entity.system.SysUserRole;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysUserServiceImpl implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysRoleService sysRoleService;

    @Override
    public List<SysUser> selectUserList(QueryUserReqDTO queryUserDTO) {
        return sysUserMapper.selectUserList(queryUserDTO);
    }

    @Override
    public List<SysUser> selectAllocatedList(SysUser user) {
        return sysUserMapper.selectAllocatedList(user);
    }

    @Override
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return sysUserMapper.selectUnallocatedList(user);
    }

    @Override
    public SysUser selectUserByUserName(String userName) {
        return sysUserMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUserById(Long userId) {
        return sysUserMapper.selectUserById(userId);
    }

    @Override
    public List<SysUser> selectUserByIds(List<Long> userIds) {
        return sysUserMapper.selectUserByIds(userIds);
    }

    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = sysRoleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    @Override
    public boolean checkUserNameUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = sysUserMapper.checkUserNameUnique(user.getUserName());
        return Objects.isNull(info) || info.getUserId() == userId;
    }

    @Override
    public boolean checkPhoneUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = sysUserMapper.checkPhoneUnique(user.getMobile());
        return Objects.isNull(info) || info.getUserId() == userId;
    }

    @Override
    public boolean checkEmailUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = sysUserMapper.checkEmailUnique(user.getEmail());
        return Objects.isNull(info) || info.getUserId() == userId;
    }

    @Override
    public void checkUserDataScope(Long operatorId, List<Long> userIds) {
        boolean isOperatorAdmin = userIds.stream().anyMatch(SecurityUtils::isAdmin);
        if (isOperatorAdmin) {
            throw BusinessException.of("Operations by super administrators are not allowed");
        }

        if (!SysUser.isAdmin(operatorId)) {
            QueryUserReqDTO user = new QueryUserReqDTO();
            List<Long> workshopIds = sysRoleService.getWorkshopIdsByUserId(operatorId);
            user.setWorkshopIds(workshopIds);
            List<SysUser> users = selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw BusinessException.of("No permission to access user data！");
            }
            List<Long> permissionIds = users.stream().map(SysUser::getUserId).toList();
            boolean noPermission = userIds.stream().anyMatch(e -> !permissionIds.contains(e));
            if (noPermission) {
                throw BusinessException.of("No permission to access user data！");
            }
        }
    }

    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = sysUserMapper.insertUser(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }


    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        sysUserRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        return sysUserMapper.updateUser(user);
    }

    @Override
    @Transactional
    public void insertUserAuth(Long userId, List<Long> roleIds) {
        sysUserRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    @Override
    public int updateUserStatus(SysUser user) {
        return sysUserMapper.updateUser(user);
    }

    @Override
    public int updateUserProfile(SysUser user) {
        return sysUserMapper.updateUser(user);
    }

    @Override
    public int resetPwd(SysUser user) {
        return sysUserMapper.updateUser(user);
    }

    @Override
    public int resetUserPwd(String userName, String password) {
        return sysUserMapper.resetUserPwd(userName, password);
    }


    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), Arrays.asList(user.getRoleIds()));
    }

    public void insertUserRole(Long userId, List<Long> roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.size());
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            sysUserRoleMapper.batchUserRole(list);
        }
    }

    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        sysUserRoleMapper.deleteUserRole(userIds);
        return sysUserMapper.deleteUserByIds(userIds);
    }

}
