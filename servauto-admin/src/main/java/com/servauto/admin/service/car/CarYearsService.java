package com.servauto.admin.service.car;

import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.dto.response.car.CarYearsDTO;
import com.servauto.admin.model.entity.car.CarYears;

import java.util.List;
import java.util.Map;

public interface CarYearsService {
    // 根据modelId查询CarYearsDTO
    List<CarYearsDTO> getCarYearsByModelId(Long modelId);

    void insertCarYearsIfNotExists(List<CarYears> collect);

    void batchInsertCarYears(List<CarYears> collect);

    List<CarYearsDTO> selectAllYearsByModelIdAndModels(Long modelId, List<String> years);

    List<CarKeyValueDTO> getCarYearsByModelIdKv(Long modelId);

    Map<Long, CarYears> selectCarYearsMap(List<Long> yearsIds);

}