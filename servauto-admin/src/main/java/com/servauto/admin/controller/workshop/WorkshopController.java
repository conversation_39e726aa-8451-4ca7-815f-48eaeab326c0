package com.servauto.admin.controller.workshop;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.admin.model.dto.request.workshop.UpdateWorkshopDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.service.workshop.WorkshopService;
import com.servauto.common.core.domain.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin workshop")
@RestController
@Validated
@Slf4j
public class WorkshopController extends BaseController {

    @Resource
    private WorkshopService workshopService;

    /**
     * 分页查询 workshop
     *
     * @param name name
     * @param type type
     * @param id id
     * @return PageInfo<WorkshopDTO>
     */
    @GetMapping("/admin/workshop/list")
    @Operation(summary = "workshop list")
    public CommonResult<PageInfo<WorkshopDTO>> queryWorkshopList(
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "type", required = false) Integer type,
            @RequestParam(name = "id", required = false) Long id) {
        var workshops = workshopService.pageWorkshops(QueryWorkshopsDTO.builder().id(id).name(name).type(type).build());
        return CommonResult.success(workshops);
    }

    /**
     * 查询 workshop names
     *
     * @return List<IdNameDTO>
     */
    @GetMapping("/admin/workshop/names")
    @Operation(summary = "workshop name list")
    public CommonResult<List<IdNameDTO>> queryWorkshopNames() {
        var workshopNames =workshopService.getWorkshopNames();
        return CommonResult.success(workshopNames);
    }

    /**
     * 查询 workshop detail
     *
     * @param workshopId workshopId
     * @return WorkshopDetailDTO
     */
    @GetMapping("/admin/workshop/{workshopId}")
    @Operation(summary = "workshop detail")
    public CommonResult<WorkshopDetailDTO> queryWorkshopDetail(@PathVariable(name = "workshopId") Long workshopId) {
        WorkshopDetailDTO workshopDetail = workshopService.getWorkshopDetail(workshopId);
        return CommonResult.success(workshopDetail);
    }

    /**
     * 编辑 workshop
     *
     * @param workshopId workshopId
     * @param reqDTO reqDTO
     */
    @PutMapping("/admin/workshop/{workshopId}")
    @Operation(summary = "edit workshop")
    public CommonResult<Object> editWorkshop(@PathVariable(name = "workshopId") Long workshopId, @RequestBody UpdateWorkshopDTO reqDTO) {
        reqDTO.setId(workshopId);
        reqDTO.setOperatorId(getUserId());
        workshopService.updateWorkshop(reqDTO);
        return CommonResult.success();
    }

    /**
     * 新增 workshop
     *
     * @param reqDTO reqDTO
     */
    @PostMapping("/admin/workshop")
    @Operation(summary = "insert workshop")
    public CommonResult<Object> insertWorkshop(@RequestBody UpdateWorkshopDTO reqDTO) {
        reqDTO.setOperatorId(getUserId());
        workshopService.insertWorkshop(reqDTO);
        return CommonResult.success();
    }
}
