package com.servauto.admin.controller.sms;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.servauto.common.annotation.Anonymous;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.sms.enums.SmsChannelEnum;
import com.servauto.framework.sms.service.SmsSendService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>SmsCallbackController</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/24 16:14
 */
@Tag(name = "admin - sms callback")
@RestController
@RequestMapping("/sys/sms/callback")
@Slf4j
public class SmsCallbackController {

    @Resource
    private SmsSendService smsSendService;

    @PostMapping("/ycloud-sms")
    @Anonymous
    public CommonResult<Boolean> receiveYCloudSmsStatus(HttpServletRequest request, HttpServletResponse response) throws Throwable {

        try {
            smsSendService.receiveSmsStatus(SmsChannelEnum.YCloud_SMS.getCode(), request.getHeader("YCloud-Signature"), getBody(request));
        } catch (Exception e) {
            log.error("callback error", e);
            response.setStatus(500);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(e.getMessage());
            return null;
        }
        return CommonResult.success(Boolean.TRUE);
    }

    public static String getBody(HttpServletRequest request) {
        if (StrUtil.startWithIgnoreCase(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)) {
            return JakartaServletUtil.getBody(request);
        }
        return null;
    }
}
