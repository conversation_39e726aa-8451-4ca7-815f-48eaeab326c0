package com.servauto.admin.controller.serviceinfo;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.admin.model.dto.request.serviceinfo.UpdateServiceInfoDTO;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceSimpleDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.service.serviceinfo.ServiceInfoService;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.utils.PageSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "admin service")
@RestController
@Validated
@Slf4j
public class ServiceInfoController extends BaseController {

    @Resource
    private ServiceInfoService serviceInfoService;

    /**
     * 查询 service
     *
     * @param serviceId serviceId
     * @return ServiceInfoDTO
     */
    @GetMapping("/admin/services/{serviceId}")
    @Operation(summary = "service detail")
    public CommonResult<ServiceInfoDTO> queryServiceInfo(@PathVariable(name = "serviceId") Long serviceId) {
        ServiceInfoDTO serviceInfo = serviceInfoService.getService(serviceId);
        return CommonResult.success(serviceInfo);
    }

    /**
     * 分页查询 service
     *
     * @param name name
     * @param supportedOn supportedOn
     * @return PageInfo<ServiceInfoDTO>
     */
    @GetMapping("/admin/services/list")
    @Operation(summary = "service list")
    public CommonResult<PageInfo<ServiceInfoDTO>> queryServices(
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "supportedOn", required = false) Integer supportedOn,
            @RequestParam(name = "workshopId", required = false) Long workshopId) {
        var list = serviceInfoService.pageServices(QueryServicesDTO.builder().name(name).supportedOn(supportedOn).workshopId(workshopId).isNeedUpdaterName(true).build());
        return CommonResult.success(list);
    }


    /**
     * 查询所有 service
     *
     * @return List<ServiceSimpleDTO>
     */
    @GetMapping("/admin/services/all")
    @Operation(summary = "service all")
    public CommonResult<List<ServiceSimpleDTO>> queryServicesAll() {
        List<ServiceInfoDTO> list = serviceInfoService.getServices(QueryServicesDTO.builder().isNeedUpdaterName(false).build());
        List<ServiceSimpleDTO> resp = new ArrayList<>();
        for (var info: list) {
            resp.add(ServiceSimpleDTO.builder().id(info.getId()).name(info.getName()).supportedOn(info.getSupportedOn()).build());
        }
        return CommonResult.success(resp);
    }

    /**
     * 新增 service
     *
     * @param reqDTO reqDTO
     */
    @PostMapping("/admin/services")
    @Operation(summary = "insert service")
    public CommonResult<Object> insertServiceInfo(@RequestBody UpdateServiceInfoDTO reqDTO) {
        reqDTO.setUpdatedBy(getUserId());
        serviceInfoService.insertServiceInfo(reqDTO);
        return CommonResult.success();
    }

    /**
     * 更新 service
     *
     * @param serviceId serviceId
     * @param reqDTO reqDTO
     */
    @PutMapping("/admin/services/{serviceId}")
    @Operation(summary = "update service")
    public CommonResult<Object> updateServiceInfo(@PathVariable(name = "serviceId") Long serviceId, @RequestBody UpdateServiceInfoDTO reqDTO) {
        reqDTO.setId(serviceId);
        reqDTO.setUpdatedBy(getUserId());
        serviceInfoService.updateServiceInfo(reqDTO);
        return CommonResult.success();
    }
}
