package com.servauto.admin.controller.packageinfo;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.admin.model.dto.request.packageinfo.UpdatePackageInfoDTO;
import com.servauto.admin.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.admin.service.packageinfo.PackageInfoService;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.utils.PageSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin package")
@RestController
@Validated
@Slf4j
public class PackageInfoController extends BaseController {
    @Resource
    private PackageInfoService packageInfoService;

    /**
     * 查询 package
     *
     * @param packageId packageId
     * @return ServiceInfoDTO
     */
    @GetMapping("/admin/packages/{packageId}")
    @Operation(summary = "package detail")
    public CommonResult<PackageInfoDTO> queryPackage(@PathVariable(name = "packageId") Long packageId) {
        var info = packageInfoService.getPackage(packageId);
        return CommonResult.success(info);
    }

    /**
     * 分页查询 package
     *
     * @param name name
     * @return PageInfo<PackageInfoDTO>
     */
    @GetMapping("/admin/packages/list")
    @Operation(summary = "package list")
    public CommonResult<PageInfo<PackageInfoDTO>> queryPackages(
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "status", required = false) Integer status) {
        var list = packageInfoService.pagePackages(QueryPackagesDTO.builder().name(name).status(status).build());
        return CommonResult.success(list);
    }

    /**
     * 查询全部 package
     *
     * @param name name
     * @return List<PackageInfoDTO>
     */
    @GetMapping("/admin/packages/all")
    @Operation(summary = "package all")
    public CommonResult<List<PackageInfoDTO>> queryPackagesAll(
            @RequestParam(name = "name", required = false) String name) {
        List<PackageInfoDTO> list = packageInfoService.getPackages(QueryPackagesDTO.builder().name(name).build());
        return CommonResult.success(list);
    }

    /**
     * 新增 package
     *
     * @param reqDTO reqDTO
     */
    @PostMapping("/admin/packages")
    @Operation(summary = "insert package")
    public CommonResult<Object> insertPackageInfo(@RequestBody UpdatePackageInfoDTO reqDTO) {
        reqDTO.setUpdatedBy(getUserId());
        packageInfoService.insertPackage(reqDTO);
        return CommonResult.success();
    }

    /**
     * 更新 package
     *
     * @param packageId packageId
     * @param reqDTO    reqDTO
     */
    @PutMapping("/admin/packages/{packageId}")
    @Operation(summary = "update package")
    public CommonResult<Object> updatePackageInfo(@PathVariable(name = "packageId") Long packageId, @RequestBody UpdatePackageInfoDTO reqDTO) {
        reqDTO.setId(packageId);
        reqDTO.setUpdatedBy(getUserId());
        packageInfoService.updatePackage(reqDTO);
        return CommonResult.success();
    }
}
