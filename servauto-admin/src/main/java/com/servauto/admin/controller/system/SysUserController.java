package com.servauto.admin.controller.system;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.system.AuthRoleReqDTO;
import com.servauto.admin.model.dto.request.system.QueryRoleReqDTO;
import com.servauto.admin.model.dto.request.system.QueryUserReqDTO;
import com.servauto.admin.model.dto.request.system.UserSaveReqDTO;
import com.servauto.admin.model.dto.response.system.AuthRoleDTO;
import com.servauto.admin.model.dto.response.system.ProfileInfoDTO;
import com.servauto.admin.model.dto.response.system.UserDetailDTO;
import com.servauto.admin.model.entity.system.SysRole;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.security.service.TokenService;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.PageSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Tag(name = "admin user")
@RestController
@RequestMapping("/system")
public class SysUserController extends BaseController {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private TokenService tokenService;


    @PreAuthorize("@ss.hasPermission('system:user:list')")
    @PostMapping("/user/list")
    public CommonResult<PageInfo<SysUser>> list(@RequestBody QueryUserReqDTO queryUserDTO) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            List<Long> workshopIds = sysRoleService.getWorkshopIdsByUserId(getUserId());
            if (CollectionUtils.isEmpty(queryUserDTO.getWorkshopIds())) {
                queryUserDTO.setWorkshopIds(workshopIds);
            } else {
                queryUserDTO.setWorkshopIds(queryUserDTO.getWorkshopIds().stream().filter(workshopIds::contains).collect(Collectors.toList()));
            }
        }
        PageSupport.startPage(queryUserDTO.getPageNo(), queryUserDTO.getPageSize());
        List<SysUser> list = sysUserService.selectUserList(queryUserDTO);
        return CommonResult.success(PageInfo.of(list));
    }

    @PreAuthorize("@ss.hasPermission('system:user:query')")
    @GetMapping(value = {"/user/{userId}"})
    public CommonResult<UserDetailDTO> getInfo(@PathVariable(value = "userId") Long userId) {
        sysUserService.checkUserDataScope(getUserId(), Collections.singletonList(userId));
        SysUser sysUser = sysUserService.selectUserById(userId);
        List<SysRole> roleList = SysUser.isAdmin(userId) ? sysRoleService.selectRoleList(new QueryRoleReqDTO()) : sysUser.getRoles();
        List<Long> roleIds = roleList.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        return CommonResult.success(UserDetailDTO.builder()
                .roleIds(roleIds)
                .admin(sysUser.isAdmin())
                .userId(sysUser.getUserId())
                .userName(sysUser.getUserName())
                .sex(sysUser.getSex())
                .email(sysUser.getEmail())
                .mobile(sysUser.getMobile())
                .password(sysUser.getPassword())
                .status(sysUser.getStatus())
                .avatar(sysUser.getAvatar())
                .build());
    }

    @Operation(summary = "Create User")
    @PreAuthorize("@ss.hasPermission('system:user:add')")
    @Log(title = "userInfo", businessType = BusinessType.INSERT)
    @PostMapping("/user")
    public CommonResult<Object> add(@Validated @RequestBody UserSaveReqDTO userReqVO) {
        // 为了下面简单直接先copy一下
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userReqVO, user);

        if (!sysUserService.checkUserNameUnique(user)) {
            throw BusinessException.of(String.format("Create user %s error, the login account already exists ", user.getUserName()));
        } else if (StringUtils.isNotEmpty(user.getMobile()) && !sysUserService.checkPhoneUnique(user)) {
            throw BusinessException.of(String.format("Create user %s error, the mobile number already exists ", user.getUserName()));
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            throw BusinessException.of(String.format("Create user %s error, the email number already exists ", user.getUserName()));
        }
        user.setNickName(user.getUserName());
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(sysUserService.insertUser(user));
    }

    @PreAuthorize("@ss.hasPermission('system:user:edit')")
    @Log(title = "userInfo", businessType = BusinessType.UPDATE)
    @PutMapping("/user")
    public CommonResult<Object> edit(@Validated @RequestBody SysUser user) {
        SecurityUtils.checkUserAdmin(user.getUserId());
        sysUserService.checkUserDataScope(getUserId(), Collections.singletonList(user.getUserId()));
        if (!sysUserService.checkUserNameUnique(user)) {
            throw BusinessException.of(String.format("Update user %s error, the login account already exists ", user.getUserName()));
        } else if (StringUtils.isNotEmpty(user.getMobile()) && !sysUserService.checkPhoneUnique(user)) {
            throw BusinessException.of(String.format("Update user %s error, the mobile number already exists ", user.getUserName()));
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            throw BusinessException.of(String.format("Update user %s error, the email number already exists ", user.getUserName()));
        }
        user.setUpdateBy(getUsername());
        return toAjax(sysUserService.updateUser(user));
    }

    @PreAuthorize("@ss.hasPermission('system:user:remove')")
    @Log(title = "userInfo", businessType = BusinessType.DELETE)
    @DeleteMapping("/user/{userIds}")
    public CommonResult<Object> removeUser(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            throw BusinessException.of("The current user cannot be deleted");
        }
        sysUserService.checkUserDataScope(getUserId(), Arrays.asList(userIds));
        return toAjax(sysUserService.deleteUserByIds(userIds));
    }

    @PreAuthorize("@ss.hasPermission('system:user:resetPwd')")
    @Log(title = "userInfo", businessType = BusinessType.UPDATE)
    @PutMapping("/user/resetPwd")
    public CommonResult<Object> resetPwd(@RequestBody SysUser user) {
        SecurityUtils.checkUserAdmin(user.getUserId());
        sysUserService.checkUserDataScope(getUserId(), Collections.singletonList(user.getUserId()));
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(sysUserService.resetPwd(user));
    }

    @PreAuthorize("@ss.hasPermission('system:user:edit')")
    @Log(title = "userInfo", businessType = BusinessType.UPDATE)
    @PutMapping("/user/changeStatus")
    public CommonResult<Object> changeUserStatus(@RequestBody SysUser user) {
        SecurityUtils.checkUserAdmin(user.getUserId());
        sysUserService.checkUserDataScope(getUserId(), Collections.singletonList(user.getUserId()));
        user.setUpdateBy(getUsername());
        return toAjax(sysUserService.updateUserStatus(user));
    }

    @PreAuthorize("@ss.hasPermission('system:user:query')")
    @GetMapping("/user/authRole/{userId}")
    public CommonResult<Object> authRole(@PathVariable("userId") Long userId) {
        SecurityUtils.checkUserAdmin(userId);
        sysUserService.checkUserDataScope(getUserId(), Collections.singletonList(userId));
        SysUser user = sysUserService.selectUserById(userId);
        List<SysRole> roles = sysRoleService.selectRoles(userId);
        roles = SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList());
        return CommonResult.success(AuthRoleDTO.builder().user(user).roles(roles).build());
    }

    @PreAuthorize("@ss.hasPermission('system:user:edit')")
    @Log(title = "userInfo", businessType = BusinessType.GRANT)
    @PutMapping("/user/authRole")
    public CommonResult<Object> insertAuthRole(@RequestBody AuthRoleReqDTO reqDTO) {
        sysUserService.insertUserAuth(reqDTO.getUserId(), reqDTO.getRoleIds());
        return CommonResult.success();
    }


    @GetMapping("/profile")
    public CommonResult<Object> profile() {
        LoginUser loginUser = getLoginUser();
        String roleGroup = sysUserService.selectUserRoleGroup(loginUser.getUsername());
        return CommonResult.success(ProfileInfoDTO.builder().user(loginUser.getUser()).roleGroup(roleGroup).build());
    }

    @Log(title = "userInfo", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public CommonResult<Object> updateProfile(@RequestBody SysUser user) {
        LoginUser loginUser = getLoginUser();
        SysUser currentUser = loginUser.getUser();
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setMobile(user.getMobile());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getMobile()) && !sysUserService.checkPhoneUnique(currentUser)) {
            throw BusinessException.of("Failed to modify user, mobile number already exists");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(currentUser)) {
            throw BusinessException.of("Failed to modify user, email number already exists");
        }
        if (sysUserService.updateUserProfile(currentUser) > 0) {
            tokenService.setLoginUser(loginUser);
            return CommonResult.success();
        }
        throw BusinessException.of("If there is any abnormality in modifying personal information, please contact the administrator");
    }

    @Log(title = "userInfo", businessType = BusinessType.UPDATE)
    @PutMapping("/profile/updatePwd")
    public CommonResult<Object> updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            throw BusinessException.of("Failed to change password, old password is wrong");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            throw BusinessException.of("The new password cannot be the same as the old password");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (sysUserService.resetUserPwd(userName, newPassword) > 0) {
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return CommonResult.success();
        }
        return CommonResult.error("Password modification is abnormal, please contact the administrator");
    }

}
