package com.servauto.admin.controller.system;

import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.response.system.MenuTreeDTO;
import com.servauto.admin.model.dto.response.system.TreeSelectDTO;
import com.servauto.admin.model.entity.system.SysMenu;
import com.servauto.admin.service.system.SysMenuService;
import com.servauto.common.annotation.Log;
import com.servauto.common.constant.UserConstants;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin menu")
@RestController
@RequestMapping("/system/menu")
public class SysMenuController extends BaseController {

    @Resource
    private SysMenuService sysMenuService;

    /**
     * 获取菜单列表
     */
    @PreAuthorize("@ss.hasPermission('system:menu:list')")
    @GetMapping("/list")
    public CommonResult<List<SysMenu>> queryMenuList(SysMenu menu) {
        List<SysMenu> menus = sysMenuService.selectMenuList(menu, getUserId());
        return CommonResult.success(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermission('system:menu:query')")
    @GetMapping(value = "/{menuId}")
    public CommonResult<SysMenu> getMenuInfo(@PathVariable Long menuId) {
        return CommonResult.success(sysMenuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public CommonResult<List<TreeSelectDTO>> treeselect(SysMenu menu) {
        List<SysMenu> menus = sysMenuService.selectMenuList(menu, getUserId());
        return CommonResult.success(sysMenuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public CommonResult<Object> roleMenuTreeSelect(@PathVariable("roleId") Long roleId) {
        List<SysMenu> menus = sysMenuService.selectMenuList(getUserId());
        List<TreeSelectDTO> treeSelects = sysMenuService.buildMenuTreeSelect(menus);
        List<Long> menuIds = sysMenuService.selectMenuListByRoleId(roleId);
        return CommonResult.success(MenuTreeDTO.builder().checkedKeys(menuIds).menus(treeSelects).build());
    }

    /**
     * 新增菜单
     */
    @PreAuthorize("@ss.hasPermission('system:menu:add')")
    @Log(title = "menu", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Object> addMenu(@Validated @RequestBody SysMenu menu) {
        if (!sysMenuService.checkMenuNameUnique(menu)) {
            throw BusinessException.of(String.format("Add menu %s error, the menu already exists ", menu.getMenuName()));
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            throw BusinessException.of(String.format("Add menu %s error, the address must start with http(s)://", menu.getMenuName()));
        }
        menu.setCreateBy(getUsername());
        return toAjax(sysMenuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    @PreAuthorize("@ss.hasPermission('system:menu:edit')")
    @Log(title = "menu", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Object> editMenu(@Validated @RequestBody SysMenu menu) {
        if (!sysMenuService.checkMenuNameUnique(menu)) {
            throw BusinessException.of(String.format("Update menu %s error, the menu already exists ", menu.getMenuName()));
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            throw BusinessException.of(String.format("Update menu %s error, the address must start with http(s)://", menu.getMenuName()));
        } else if (menu.getMenuId().equals(menu.getParentId())) {
            throw BusinessException.of(String.format("Update menu %s error, Your superior cannot be yourself", menu.getMenuName()));
        }
        menu.setUpdateBy(getUsername());
        return toAjax(sysMenuService.updateMenu(menu));
    }

    /**
     * 删除菜单
     */
    @PreAuthorize("@ss.hasPermission('system:menu:remove')")
    @Log(title = "menu", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public CommonResult<Object> removeMenu(@PathVariable("menuId") Long menuId) {
        if (sysMenuService.hasChildByMenuId(menuId)) {
            throw BusinessException.of("There is a submenu, deletion is not allowed");
        }
        if (sysMenuService.checkMenuExistRole(menuId)) {
            throw BusinessException.of("The menu has been assigned, deletion is not allowed");
        }
        return toAjax(sysMenuService.deleteMenuById(menuId));
    }
}