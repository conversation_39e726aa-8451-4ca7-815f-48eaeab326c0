package com.servauto.admin.controller.system;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import com.servauto.common.exception.BusinessException;
import com.servauto.framework.dictionary.model.SysDictType;
import com.servauto.framework.dictionary.service.SysDictionaryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin dict-type")
@RestController
@RequestMapping("/system/dict")
public class SysDictTypeController extends BaseController {

    @Resource
    private SysDictionaryService sysDictionaryService;

    @PreAuthorize("@ss.hasPermission('system:dict:list')")
    @GetMapping("/type/list")
    public CommonResult<PageInfo<SysDictType>> queryDictTypeList(SysDictType dictType) {
        return CommonResult.success(sysDictionaryService.selectDictTypeList(dictType));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    @GetMapping(value = "/type/{dictId}")
    public CommonResult<Object> queryDictTypeInfo(@PathVariable Long dictId) {
        return CommonResult.success(sysDictionaryService.selectDictTypeById(dictId));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:add')")
    @Log(title = "dictionaryType", businessType = BusinessType.INSERT)
    @PostMapping("/type")
    public CommonResult<Object> addDictType(@Validated @RequestBody SysDictType dict) {
        if (!sysDictionaryService.checkDictTypeUnique(dict)) {
            throw BusinessException.of(String.format("Create dict %s error, the dict already exists ", dict.getDictName()));
        }
        dict.setCreateBy(getUsername());
        return toAjax(sysDictionaryService.insertDictType(dict));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:edit')")
    @Log(title = "dictionaryType", businessType = BusinessType.UPDATE)
    @PutMapping("/type")
    public CommonResult<Object> editDictType(@Validated @RequestBody SysDictType dict) {
        if (!sysDictionaryService.checkDictTypeUnique(dict)) {
            throw BusinessException.of(String.format("Update dict %s error, the dict already exists ", dict.getDictName()));
        }
        dict.setUpdateBy(getUsername());
        sysDictionaryService.updateDictType(dict);
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('system:dict:remove')")
    @Log(title = "dictionaryType", businessType = BusinessType.DELETE)
    @DeleteMapping("/type/{dictIds}")
    public CommonResult<Object> removeDictType(@PathVariable Long[] dictIds) {
        sysDictionaryService.deleteDictTypeByIds(dictIds);
        return CommonResult.success();
    }

    @GetMapping("/type/optionselect")
    public CommonResult<Object> optionSelect() {
        List<SysDictType> dictTypes = sysDictionaryService.selectDictTypeAll();
        return CommonResult.success(dictTypes);
    }
}
