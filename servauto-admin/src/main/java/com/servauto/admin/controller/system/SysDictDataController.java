package com.servauto.admin.controller.system;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import com.servauto.framework.dictionary.model.SysDictData;
import com.servauto.framework.dictionary.service.SysDictionaryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "admin dict-data")
@RestController
@RequestMapping("/system/dict")
public class SysDictDataController extends BaseController {

    @Resource
    private SysDictionaryService sysDictionaryService;

    @PreAuthorize("@ss.hasPermission('system:dict:list')")
    @GetMapping("/data/list")
    public CommonResult<PageInfo<SysDictData>> queryDictDataList(SysDictData dictData) {
        return CommonResult.success(sysDictionaryService.selectDictDataList(dictData));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    @GetMapping(value = "/data/{dictCode}")
    public CommonResult<Object> queryDictDataInfo(@PathVariable Long dictCode) {
        return CommonResult.success(sysDictionaryService.selectDictDataById(dictCode));
    }

    @GetMapping(value = "/data/type/{dictType}")
    public CommonResult<Object> queryDictType(@PathVariable String dictType) {
        return CommonResult.success(sysDictionaryService.selectDictDataByType(dictType));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:add')")
    @Log(title = "dictionary", businessType = BusinessType.INSERT)
    @PostMapping("/data")
    public CommonResult<Object> addDictData(@Validated @RequestBody SysDictData dict) {
        dict.setCreateBy(getUsername());
        return toAjax(sysDictionaryService.insertDictData(dict));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:edit')")
    @Log(title = "dictionary", businessType = BusinessType.UPDATE)
    @PutMapping("/data")
    public CommonResult<Object> editDictData(@Validated @RequestBody SysDictData dict) {
        dict.setUpdateBy(getUsername());
        return toAjax(sysDictionaryService.updateDictData(dict));
    }

    @PreAuthorize("@ss.hasPermission('system:dict:remove')")
    @Log(title = "dictionary", businessType = BusinessType.DELETE)
    @DeleteMapping("/data/{dictCodes}")
    public CommonResult<Object> removeDictData(@PathVariable Long[] dictCodes) {
        sysDictionaryService.deleteDictDataByIds(dictCodes);
        return CommonResult.success();
    }
}
