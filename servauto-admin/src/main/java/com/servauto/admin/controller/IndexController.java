package com.servauto.admin.controller;

import com.servauto.common.annotation.Anonymous;
import com.servauto.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Tag(name = "admin index")
@Slf4j
@RestController
public class IndexController {

    @GetMapping("/")
    public String index() {
        return StringUtils.format("Welcome to the backend management framework, please access through the front-end address");
    }

    @Anonymous
    @GetMapping("/date")
    public Date date(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        // String -> Date
        return date;
    }

    @Anonymous
    @GetMapping("/timestamp")
    public Date timestamp(@RequestParam(name = "timestamp") Long timestamp) {
        // Long -> Date
        return new Date(timestamp);
    }


}
