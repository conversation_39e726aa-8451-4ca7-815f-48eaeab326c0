package com.servauto.admin.controller.payment;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.payment.QueryPaymentDTO;
import com.servauto.admin.model.dto.response.payment.PaymentDTO;
import com.servauto.admin.service.payment.PaymentServices;
import com.servauto.admin.task.ScheduleInitCarLib;
import com.servauto.common.annotation.Anonymous;
import com.servauto.common.core.domain.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "admin payment")
@RestController
@RequestMapping("/payment")
@Validated
@Slf4j
public class PaymentController {

    @Resource
    private PaymentServices paymentServices;

    @Resource
    private ScheduleInitCarLib scheduleQueryPayRes;

    @GetMapping("/detail")
    @Operation(summary = "payment detail")
    @Anonymous
    public CommonResult<PaymentDTO> paymentDetail() {
        scheduleQueryPayRes.syncCarLibrary();
        return CommonResult.success("");
    }
    /**
     * 分页查询payment
     *
     * @param queryPaymentDTO queryPaymentDTO
     * @return CommonResult<PageInfo < CustomerInfoDTO>>
     */
    @GetMapping("/list")
    @Operation(summary = "payment list")
    @Anonymous
    public CommonResult<PageInfo<PaymentDTO>> queryCustomers(QueryPaymentDTO queryPaymentDTO) {
        PageInfo<PaymentDTO> pageInfo = paymentServices.pagePayments(queryPaymentDTO);
        return CommonResult.success(pageInfo);
    }
}
