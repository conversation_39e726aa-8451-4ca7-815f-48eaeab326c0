package com.servauto.admin.controller.car;

import com.servauto.admin.model.dto.response.car.*;
import com.servauto.admin.service.car.*;
import com.servauto.common.core.domain.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Tag(name = "admin car")
@RestController
@RequestMapping("/car")
@Validated
@Slf4j
public class CarSearchController {

    @Resource
    private CarBrandsService carBrandsService;

    @Resource
    private CarModelsService carModelsService;

    @Resource
    private CarYearsService carYearsService;

    @Resource
    private CarExtraService carExtraService;

    @GetMapping("/brandList")
    @Operation(summary = "brand list")
    public CommonResult<List<CarKeyValueDTO>> queryValidBrands() {
        List<CarKeyValueDTO> brandsDtoList = carBrandsService.selectAllValidBrandsKv();
        return CommonResult.success(brandsDtoList);
    }

    @GetMapping("/modelList")
    @Operation(summary = "model list")
    public CommonResult<List<CarKeyValueDTO>> queryValidModelsByBrandId(@RequestParam(name = "brandId") Long brandId) {
        List<CarKeyValueDTO> modelsDtoList = carModelsService.selectAllModelsByBrandIdKv(brandId);
        return CommonResult.success(modelsDtoList);
    }

    @GetMapping("/yearList")
    @Operation(summary = "year list")
    public CommonResult<List<CarKeyValueDTO>> queryValidYearByModelId(@RequestParam(name = "modelId") Long modelId) {
        List<CarKeyValueDTO> carYearsDtoList = carYearsService.getCarYearsByModelIdKv(modelId);
        return CommonResult.success(carYearsDtoList);
    }

    @GetMapping("/variantList")
    @Operation(summary = "variant list")
    public CommonResult<List<CarKeyValueDTO>> queryValidVariantByYearId(@RequestParam(name = "yearId") Long yearId) {
        List<CarKeyValueDTO> carYearsDtoList = carExtraService.getCarExtraByYearIdKv(yearId);
        return CommonResult.success(carYearsDtoList);
    }
}

