package com.servauto.admin.controller.order;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.enums.order.OrderDeliveryTypeEnum;
import com.servauto.admin.enums.order.OrderStatusEnum;
import com.servauto.admin.model.dto.request.order.*;
import com.servauto.admin.model.dto.response.order.OrderDTO;
import com.servauto.admin.model.dto.response.order.OrderProductDTO;
import com.servauto.admin.model.dto.response.order.OrderWorkshopDTO;
import com.servauto.admin.model.dto.response.order.PageOrderDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.service.order.OrderService;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.common.annotation.BizAopLog;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Tag(name = "admin order")
@RestController
@Validated
@Slf4j
public class OrderController extends BaseController {

    @Resource
    private OrderService orderService;

    @Resource
    private SysRoleService sysRoleService;


    @GetMapping("/h5/admin/orders")
    @PreAuthorize("@ss.hasPermission('order:list')")
    public CommonResult<PageInfo<PageOrderDTO>> h5listOrders(@RequestParam Integer pageNo,
                                                             @RequestParam Integer pageSize,
                                                             @RequestParam String deliveryType,
                                                             @RequestParam(required = false) String status,
                                                             @RequestParam(required = false) String licensePlate) {

        OrderDeliveryTypeEnum deliveryTypeEnum = OrderDeliveryTypeEnum.getByCode(deliveryType.toUpperCase());
        Preconditions.checkNotNull(deliveryTypeEnum);

        List<String> statuses;
        if (status.equals("PENDING_CONFIRM")) {
            statuses = Arrays.asList(OrderStatusEnum.PENDING_WORKSHOP_CONFIRM.getCode(), OrderStatusEnum.PENDING_HQOPS_CONFIRM.getCode());
        } else {
            statuses = Collections.singletonList(OrderStatusEnum.getByCode(status).getCode());
        }

        PageOrderReqDTO.PageOrderReqDTOBuilder builder = PageOrderReqDTO.builder()
                .deliveryType(deliveryType).statuses(statuses).licensePlate(licensePlate);

        if (OrderDeliveryTypeEnum.isInWorkshop(deliveryType) || OrderDeliveryTypeEnum.isPickup(deliveryType)) {
            if (!isAdmin()) {
                Long operatorId = getUserId();
                List<Long> workshopIds = sysRoleService.getWorkshopIdsByUserId(operatorId);
                if (CollectionUtils.isEmpty(workshopIds)) {
                    return CommonResult.success(PageInfo.emptyPageInfo());
                }
                if (isRoleAdmin()) {
                    workshopIds.add(0L);
                }
                builder.workShopIds(workshopIds);
            }
        }

        PageInfo<PageOrderDTO> orders = orderService.pageOrders(pageNo, pageSize, builder.build());
        return CommonResult.success(orders);
    }


    @GetMapping("/admin/orders")
    @PreAuthorize("@ss.hasPermission('order:list')")
    public CommonResult<PageInfo<PageOrderDTO>> listOrders(@RequestParam Integer pageNo,
                                                           @RequestParam Integer pageSize,
                                                           @RequestParam String deliveryType,
                                                           @RequestParam(required = false) Long workShopId,
                                                           @RequestParam(required = false) String status,
                                                           @RequestParam(required = false) Long createBeginTime,
                                                           @RequestParam(required = false) Long createEndTime,
                                                           @RequestParam(required = false) String inStoreMobile,
                                                           @RequestParam(required = false) String licensePlate

    ) {
        OrderDeliveryTypeEnum deliveryTypeEnum = OrderDeliveryTypeEnum.getByCode(deliveryType.toUpperCase());
        Preconditions.checkNotNull(deliveryTypeEnum);

        PageOrderReqDTO.PageOrderReqDTOBuilder builder = PageOrderReqDTO.builder()
                .deliveryType(deliveryType).status(status).inStoreMobile(inStoreMobile).licensePlate(licensePlate);

        if (createBeginTime != null && createBeginTime > 0) {
            builder.createBeginTime(new Date(createBeginTime));
        }

        if (createEndTime != null && createEndTime > 0) {
            builder.createEndTime(new Date(createEndTime));
        }

        if (OrderDeliveryTypeEnum.isInWorkshop(deliveryType) || OrderDeliveryTypeEnum.isPickup(deliveryType)) {
            if (!isAdmin()) {
                Long operatorId = getUserId();
                List<Long> workshopIds = sysRoleService.getWorkshopIdsByUserId(operatorId);
                if (CollectionUtils.isEmpty(workshopIds)) {
                    return CommonResult.success(PageInfo.emptyPageInfo());
                }

                if (workShopId != null && workShopId > 0) {
                    if (!workshopIds.contains(workShopId)) {
                        return CommonResult.success(PageInfo.emptyPageInfo());
                    }
                    builder.workShopIds(List.of(workShopId));
                } else {
                    if (isRoleAdmin()) {
                        workshopIds.add(0L);
                    }
                    builder.workShopIds(workshopIds);
                }
            } else if (workShopId != null && workShopId > 0) {
                builder.workShopIds(List.of(workShopId));
            }
        }

        PageInfo<PageOrderDTO> orders = orderService.pageOrders(pageNo, pageSize, builder.build());
        return CommonResult.success(orders);
    }

    @GetMapping("/admin/orders/export-excel")
    @BizAopLog(isPrint = false)
    @Log(title = "order", businessType = BusinessType.EXPORT)
    //@PreAuthorize("@ss.hasPermission('order:export')")
    public void exportOrderExcel(@RequestParam String deliveryType,
                                 @RequestParam(required = false) Long workShopId,
                                 @RequestParam(required = false) String status,
                                 @RequestParam(required = false) Long createBeginTime,
                                 @RequestParam(required = false) Long createEndTime, HttpServletResponse response) throws IOException {

        CommonResult<PageInfo<PageOrderDTO>> result = this.listOrders(1, Integer.MAX_VALUE, deliveryType, workShopId, status, createBeginTime, createEndTime, null, null);
        if (ResponseCode.SUCCESS.getCode().equals(result.getCode())) {

            List<OrderProductDTO> orderProducts = Lists.newArrayList();
            for (PageOrderDTO orderDTO : result.getData().getList()) {
                orderProducts.addAll(orderDTO.getProducts());
            }

            write(response, result.getData().getList(), orderProducts);
        }
    }

    private static void write(HttpServletResponse response, List<PageOrderDTO> pageOrders, List<OrderProductDTO> orderProducts) throws IOException {
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("order.csv", StandardCharsets.UTF_8));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(false).build()) {
            excelWriter.write(pageOrders, EasyExcel.writerSheet(0, "Order Data").head(PageOrderDTO.class).build());
            excelWriter.write(orderProducts, EasyExcel.writerSheet(1, "Product Data").head(OrderProductDTO.class).build());
        }
    }

    @GetMapping("/admin/orders/{orderNo}")
    @PreAuthorize("@ss.hasPermission('order:detail')")
    public CommonResult<OrderDTO> queryOrder(@PathVariable String orderNo) {
        OrderDTO orderDTO = orderService.queryOrderByOrderNo(orderNo);
        return CommonResult.success(orderDTO);
    }

    @PutMapping("/admin/orders/{orderNo}/cancel")
    @Log(title = "order-cancel", businessType = BusinessType.UPDATE)
    //@PreAuthorize("@ss.hasPermission('order:cancel')")
    public CommonResult<Boolean> cancelOrder(@PathVariable String orderNo, @Valid @RequestBody CancelOrderReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.cancelOrder(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/admin/orders/{orderNo}/delivery")
    //@PreAuthorize("@ss.hasPermission('order:delivery')")
    @Log(title = "order-delivery", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> deliveryOrder(@PathVariable String orderNo, @Valid @RequestBody DeliveryOrderReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.deliveryOrder(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/admin/orders/{orderNo}/shipping")
    //@PreAuthorize("@ss.hasPermission('order:shipping')")
    @Log(title = "order-shipping", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> shippingOrder(@PathVariable String orderNo, @Valid @RequestBody ShippingOrderReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.shippingOrder(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/admin/orders/{orderNo}/workshops")
    public CommonResult<List<IdNameDTO>> queryOrderWorkshops(@PathVariable String orderNo) {
        List<IdNameDTO> workshops = orderService.queryOrderWorkshops(orderNo);
        return CommonResult.success(workshops);
    }

    @PutMapping("/admin/orders/{orderNo}/reschedule")
    //@PreAuthorize("@ss.hasPermission('order:reschedule')")
    @Log(title = "order-reschedule", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> reschedule(@PathVariable String orderNo, @Valid @RequestBody RescheduleReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.reschedule(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/admin/orders/{orderNo}/pickup")
    //@PreAuthorize("@ss.hasPermission('order:pickup')")
    @Log(title = "order-pickup", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> pickupOrder(@PathVariable String orderNo, @Valid @RequestBody PickupOrderReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.pickup(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @PutMapping("/admin/orders/{orderNo}/confirm")
    //@PreAuthorize("@ss.hasPermission('order:confirm')")
    @Log(title = "order-confirm", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> confirm(@PathVariable String orderNo) {
        LoginUser operator = getLoginUser();
        orderService.confirm(operator, orderNo);
        return CommonResult.success(Boolean.TRUE);
    }


    @PutMapping("/admin/orders/{orderNo}/workshop-reschedule")
    //@PreAuthorize("@ss.hasPermission('order:confirm')")
    @Log(title = "order-confirm", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> workshopReschedule(@PathVariable String orderNo, @Valid @RequestBody WorkshopRescheduleReqDTO reqDTO) {
        LoginUser operator = getLoginUser();
        reqDTO.setOrderNo(orderNo);
        orderService.workshopReschedule(operator, reqDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/admin/orders/{orderNo}/workshops/{workshopId}/opening-hours")
    public CommonResult<OrderWorkshopDTO> queryOrderWorkshopOpeningHours(@PathVariable String orderNo, @PathVariable Long workshopId) {
        return CommonResult.success(orderService.queryOrderWorkshopOpeningHours(orderNo, workshopId));
    }

}
