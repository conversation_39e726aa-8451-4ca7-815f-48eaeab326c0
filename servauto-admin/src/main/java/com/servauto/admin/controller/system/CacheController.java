package com.servauto.admin.controller.system;

import com.servauto.admin.model.dto.response.system.CacheInfoDTO;
import com.servauto.admin.model.dto.response.system.SysCacheDTO;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.redis.connection.DefaultedRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Tag(name = "admin cache")
@RestController
@RequestMapping("/monitor/cache")
public class CacheController {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @SuppressWarnings("deprecation")
    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @GetMapping()
    public CommonResult<CacheInfoDTO> getCacheInfo() {
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) DefaultedRedisConnection::info);
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) DefaultedRedisConnection::dbSize);

        CacheInfoDTO.CacheInfoDTOBuilder builder = CacheInfoDTO.builder().info(info).dbSize(dbSize);

        List<Map<String, String>> pieList = new ArrayList<>();
        if (MapUtils.isNotEmpty(commandStats)) {
            commandStats.stringPropertyNames().forEach(key -> {
                Map<String, String> data = new HashMap<>(2);
                String property = commandStats.getProperty(key);
                data.put("name", StringUtils.removeStart(key, "cmdstat_"));
                data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
                pieList.add(data);
            });
            builder.commandStats(pieList);
        }
        return CommonResult.success(builder.build());
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @GetMapping("/getNames")
    public CommonResult<Object> cache() {
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @GetMapping("/getKeys/{cacheName}")
    public CommonResult<Object> getCacheKeys(@PathVariable String cacheName) {
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        return CommonResult.success(new TreeSet<>(cacheKeys));
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public CommonResult<Object> getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey) {
        String cacheValue = redisTemplate.opsForValue().get(cacheKey);
        SysCacheDTO sysCache = new SysCacheDTO(cacheName, cacheKey, cacheValue);
        return CommonResult.success(sysCache);
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @DeleteMapping("/clearCacheName/{cacheName}")
    public CommonResult<Object> clearCacheName(@PathVariable String cacheName) {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        if (CollectionUtils.isNotEmpty(cacheKeys)) {
            redisTemplate.delete(cacheKeys);
        }
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    public CommonResult<Object> clearCacheKey(@PathVariable String cacheKey) {
        redisTemplate.delete(cacheKey);
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('monitor:cache:list')")
    @DeleteMapping("/clearCacheAll")
    public CommonResult<Object> clearCacheAll() {
        Collection<String> cacheKeys = redisTemplate.keys("*");
        if (CollectionUtils.isNotEmpty(cacheKeys)) {
            redisTemplate.delete(cacheKeys);
        }
        return CommonResult.success();
    }
}
