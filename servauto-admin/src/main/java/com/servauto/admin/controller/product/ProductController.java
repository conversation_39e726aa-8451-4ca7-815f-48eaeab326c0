package com.servauto.admin.controller.product;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.dto.request.product.UpdateProductDTO;
import com.servauto.admin.model.dto.response.product.ProductAttributeDTO;
import com.servauto.admin.model.dto.response.product.ProductBaseDTO;
import com.servauto.admin.model.dto.response.product.ProductDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.service.product.ProductService;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.utils.PageSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin product")
@RestController
@Validated
@Slf4j
public class ProductController extends BaseController {

    @Resource
    private ProductService productService;

    /**
     * 查询 product detail
     *
     * @param productId productId
     * @return ProductDTO
     */
    @GetMapping("/admin/products/{productId}")
    @Operation(summary = "get product detail")
    public CommonResult<ProductDTO> queryProduct(@PathVariable(name = "productId") Long productId) {
        var info = productService.getProduct(productId);
        return CommonResult.success(info);
    }

    /**
     * 分页查询 product 基础信息列表
     *
     * @param reqDTO reqDTO
     * @return PageInfo<ProductBaseDTO>
     */
    @PostMapping("/admin/products/list")
    @Operation(summary = "get product list")
    public CommonResult<PageInfo<ProductBaseDTO>> queryProducts(@RequestBody QueryProductsDTO reqDTO) {
        reqDTO.setIsNeedAttributeValue(false);
        reqDTO.setIsNeedUpdaterName(true);
        var list = productService.pageProducts(reqDTO);
        return CommonResult.success(list);
    }

    /**
     * 查询 product 详细信息列表
     *
     * @param reqDTO reqDTO
     * @return List<ProductBaseDTO>
     */
    @PostMapping("/admin/products/detail-list")
    @Operation(summary = "get product detail list")
    public CommonResult<List<ProductBaseDTO>> queryProductDetails(@RequestBody QueryProductsDTO reqDTO) {
        reqDTO.setIsNeedAttributeValue(true);
        reqDTO.setIsNeedUpdaterName(false);
        var list = productService.getProducts(reqDTO);
        return CommonResult.success(list);
    }

    /**
     * 新增 product
     *
     * @param reqDTO reqDTO
     */
    @PostMapping("/admin/products")
    @Operation(summary = "insert product")
    public CommonResult<Object> insertProduct(@RequestBody UpdateProductDTO reqDTO) {
        reqDTO.setUpdatedBy(getUserId());
        productService.insertProduct(reqDTO);
        return CommonResult.success();
    }

    /**
     * 更新 product
     *
     * @param productId productId
     * @param reqDTO    reqDTO
     */
    @PutMapping("/admin/products/{productId}")
    @Operation(summary = "update product")
    public CommonResult<Object> updateProduct(@PathVariable(name = "productId") Long productId, @RequestBody UpdateProductDTO reqDTO) {
        reqDTO.setId(productId);
        reqDTO.setUpdatedBy(getUserId());
        productService.updateProduct(reqDTO);
        return CommonResult.success();
    }

    /**
     * 查询 product categories
     *
     * @return List<IdNameDTO>
     */
    @GetMapping("/admin/product-categories")
    @Operation(summary = "get product categories")
    public CommonResult<List<IdNameDTO>> queryProductCategories() {
        var list = productService.getProductCategories();
        return CommonResult.success(list);
    }

    /**
     * 查询 product attributes
     *
     * @param categoryId  categoryId
     * @param displayType displayType
     * @return List<ProductAttributeDTO>
     */
    @GetMapping("/admin/product-attributes")
    @Operation(summary = "get product attributes")
    public CommonResult<List<ProductAttributeDTO>> queryProductAttributes(
            @RequestParam(name = "categoryId") Long categoryId,
            @RequestParam(name = "displayType", required = false) String displayType) {
        var list = productService.getProductAttributes(displayType, categoryId);
        return CommonResult.success(list);
    }

    /**
     * 查询 product brand
     *
     * @return List<IdNameDTO>
     */
    @GetMapping("/admin/product-brands")
    @Operation(summary = "get product brands")
    public CommonResult<List<IdNameDTO>> queryProductBrands() {
        var list = productService.getProductBrands();
        return CommonResult.success(list);
    }
}
