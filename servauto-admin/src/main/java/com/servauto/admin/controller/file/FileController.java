package com.servauto.admin.controller.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.servauto.common.annotation.Anonymous;
import com.servauto.common.annotation.BizAopLog;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.file.api.FileApi;
import com.servauto.framework.file.model.dto.FileUploadReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static com.servauto.framework.file.utils.FileTypeUtils.getMineType;

@Tag(name = "admin file")
@RestController
@RequestMapping("/file/api")
@Validated
@Slf4j
public class FileController {

    @Resource
    private FileApi fileApi;

    @PostMapping("/upload")
    @BizAopLog(isPrint = false)
    @Operation(summary = "Upload file", description = "Upload file to backend")
    public CommonResult<String> uploadFile(FileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        String path = uploadReqVO.getPath();
        return CommonResult.success("Upload succeed", fileApi.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    @GetMapping("/{configId}/get/**")
    @Anonymous
    @BizAopLog(isPrint = false)
    @Operation(summary = "Read file")
    public void getFileContent(HttpServletRequest request, HttpServletResponse response, @PathVariable(value = "configId") Long configId) throws Exception {

        String path = StrUtil.subAfter(request.getRequestURI(), "/get/", false);
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("The path at the end must be passed");
        }

        path = URLUtil.decode(path);
        byte[] content = fileApi.getFile(configId, path);
        if (content == null) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }

        response.setContentType(getMineType(content, path));
        IoUtil.write(response.getOutputStream(), false, content);
    }

}
