package com.servauto.admin.controller.customer;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.model.dto.request.customer.EditCustomerReqDTO;
import com.servauto.admin.model.dto.request.customer.QueryCustomersDTO;
import com.servauto.admin.model.dto.response.customer.CustomerInfoDTO;
import com.servauto.admin.service.customer.CustomerService;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "admin customer")
@RestController
@Validated
@Slf4j
public class CustomerController {

    @Resource
    private CustomerService customerService;


    /**
     * 分页查询customer
     *
     * @param mobile mobile
     * @return CommonResult<PageInfo < CustomerInfoDTO>>
     */
    @GetMapping("/admin/customers")
    @Operation(summary = "customer list")
    @PreAuthorize("@ss.hasPermission('customer:list')")
    public CommonResult<PageInfo<CustomerInfoDTO>> queryCustomers(@RequestParam(name = "mobile", required = false) String mobile) {
        PageInfo<CustomerInfoDTO> customers = customerService.pageCustomers(QueryCustomersDTO.builder().mobile(mobile).build());
        return CommonResult.success(customers);
    }

    /**
     * 查询customer
     *
     * @param customerId customerId
     * @return CustomerInfoDTO
     */
    @GetMapping("/admin/customers/{customerId}")
    @Operation(summary = "customer detail")
    @PreAuthorize("@ss.hasPermission('customer:detail')")
    public CommonResult<CustomerInfoDTO> queryCustomerInfo(@PathVariable(name = "customerId") Long customerId) {
        CustomerInfoDTO customer = customerService.getCustomerById(customerId);
        return CommonResult.success(customer);
    }

    /**
     * 编辑customer
     *
     * @param customerId customerId
     */
    @PutMapping("/admin/customers/{customerId}")
    @Operation(summary = "edit customer")
    @PreAuthorize("@ss.hasPermission('customer:list:edit')")
    @Log(title = "customer", businessType = BusinessType.UPDATE)
    public CommonResult<Object> editCustomer(@PathVariable(name = "customerId") Long customerId, @RequestBody EditCustomerReqDTO reqDTO) {
        reqDTO.setCustomerId(customerId);
        customerService.updateCustomerInfo(reqDTO);
        return CommonResult.success();
    }

}
