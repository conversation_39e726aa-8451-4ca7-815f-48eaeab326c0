package com.servauto.admin.controller.system;

import com.github.pagehelper.PageInfo;
import com.servauto.admin.controller.BaseController;
import com.servauto.admin.model.dto.request.system.DataPermissionReqDTO;
import com.servauto.admin.model.dto.request.system.QueryRoleReqDTO;
import com.servauto.admin.model.dto.response.system.QueryRoleDTO;
import com.servauto.admin.model.entity.system.SysRole;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.model.entity.system.SysUserRole;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.security.service.SysPermissionService;
import com.servauto.admin.security.service.TokenService;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.annotation.Log;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.enums.BusinessType;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.PageSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "admin role")
@RestController
@RequestMapping("/system/role")
public class SysRoleController extends BaseController {

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private TokenService tokenService;

    @Resource
    private SysPermissionService sysPermissionService;

    @Resource
    private SysUserService sysUserService;

    @PreAuthorize("@ss.hasPermission('system:role:list')")
    @GetMapping("/list")
    public CommonResult<PageInfo<SysRole>> queryRoleList(QueryRoleReqDTO role) {
        PageSupport.startPage();
        List<SysRole> list = sysRoleService.selectRoleList(role);
        return CommonResult.success(PageInfo.of(list));
    }

    @PreAuthorize("@ss.hasPermission('system:role:query')")
    @GetMapping(value = "/{roleId}")
    public CommonResult<Object> queryRoleInfo(@PathVariable Long roleId) {
        SysRole role = sysRoleService.selectRoleById(roleId);
        QueryRoleDTO queryRoleDTO = QueryRoleDTO.builder().roleId(role.getRoleId()).roleName(role.getRoleName()).roleKey(role.getRoleKey()).
                workshopIds(sysRoleService.getWorkshopIdsByRoleId(roleId)).build();
        return CommonResult.success(queryRoleDTO);
    }

    @PreAuthorize("@ss.hasPermission('system:role:add')")
    @Log(title = "role", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Object> addRole(@Validated @RequestBody SysRole role) {
        if (!sysRoleService.checkRoleNameUnique(role)) {
            throw BusinessException.of(String.format("Create role %s error, the role name already exists ", role.getRoleName()));
        } else if (!sysRoleService.checkRoleKeyUnique(role)) {
            throw BusinessException.of(String.format("Create role %s error, the role permission already exists ", role.getRoleName()));
        }
        role.setCreateBy(getUsername());
        return toAjax(sysRoleService.insertRole(role));
    }

    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Object> editRole(@Validated @RequestBody SysRole role) {
        if (!sysRoleService.checkRoleNameUnique(role)) {
            throw BusinessException.of(String.format("Update role %s error, the role name already exists ", role.getRoleName()));
        } else if (!sysRoleService.checkRoleKeyUnique(role)) {
            throw BusinessException.of(String.format("Update role %s error, the role permission already exists ", role.getRoleName()));
        }
        role.setUpdateBy(getUsername());

        if (sysRoleService.updateRole(role) > 0) {
            LoginUser loginUser = getLoginUser();
            if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin()) {
                loginUser.setUser(sysUserService.selectUserByUserName(loginUser.getUser().getUserName()));
                loginUser.setPermissions(sysPermissionService.getMenuPermission(loginUser.getUser()));
                tokenService.setLoginUser(loginUser);
            }
            return CommonResult.success();
        }
        throw BusinessException.of(String.format("Update role %s error", role.getRoleName()));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Object> changeRoleStatus(@RequestBody SysRole role) {
        SecurityUtils.checkRoleAdmin(role.getRoleId());
        role.setUpdateBy(getUsername());
        return toAjax(sysRoleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @PreAuthorize("@ss.hasPermission('system:role:remove')")
    @Log(title = "role", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public CommonResult<Object> removeRole(@PathVariable Long[] roleIds) {
        return toAjax(sysRoleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    @PreAuthorize("@ss.hasPermission('system:role:query')")
    @GetMapping("/optionselect")
    public CommonResult<Object> roleOptionSelect() {
        return CommonResult.success(sysRoleService.selectRoleList(new QueryRoleReqDTO()));
    }

    /**
     * 查询已分配用户角色列表
     */
    @PreAuthorize("@ss.hasPermission('system:role:list')")
    @GetMapping("/authUser/allocatedList")
    public CommonResult<PageInfo<SysUser>> allocatedList(SysUser user) {
        PageSupport.startPage();
        List<SysUser> list = sysUserService.selectAllocatedList(user);
        return CommonResult.success(PageInfo.of(list));
    }

    /**
     * 查询未分配用户角色列表
     */
    @PreAuthorize("@ss.hasPermission('system:role:list')")
    @GetMapping("/authUser/unallocatedList")
    public CommonResult<PageInfo<SysUser>> unallocatedList(SysUser user) {
        PageSupport.startPage();
        List<SysUser> list = sysUserService.selectUnallocatedList(user);
        return CommonResult.success(PageInfo.of(list));
    }

    /**
     * 取消授权用户
     */
    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public CommonResult<Object> cancelAuthUser(@RequestBody SysUserRole userRole) {
        return toAjax(sysRoleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public CommonResult<Object> cancelAuthUserAll(Long roleId, Long[] userIds) {
        return toAjax(sysRoleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权
     */
    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public CommonResult<Object> selectAuthUserAll(Long roleId, Long[] userIds) {
        return toAjax(sysRoleService.insertAuthUsers(roleId, userIds));
    }


    @PreAuthorize("@ss.hasPermission('system:role:edit')")
    @Log(title = "role", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public CommonResult<Object> dataScope(@RequestBody DataPermissionReqDTO reqDTO) {
        sysRoleService.authDataScope(reqDTO);
        return CommonResult.success();
    }


}
