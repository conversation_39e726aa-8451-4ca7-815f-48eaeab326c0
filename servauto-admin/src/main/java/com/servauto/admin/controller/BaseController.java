package com.servauto.admin.controller;

import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.common.core.domain.CommonResult;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public class BaseController {

    protected CommonResult<Object> toAjax(int rows) {
        return rows > 0 ? CommonResult.success() : CommonResult.error();
    }

    public LoginUser getLoginUser() {
        return SecurityUtils.getLoginUser();
    }

    public Long getUserId() {
        return getLoginUser().getUserId();
    }

    public String getUsername() {
        return getLoginUser().getUsername();
    }

    protected boolean isAdmin() {
        return SecurityUtils.isAdmin(getUserId());
    }

    protected boolean isRoleAdmin() {
       return SecurityUtils.isLoginRoleAdmin();
    }

}
