package com.servauto.admin.controller.system;

import com.servauto.admin.model.dto.request.system.LoginReqDTO;
import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.system.LoginDTO;
import com.servauto.admin.model.dto.response.system.UserInfoDTO;
import com.servauto.admin.model.entity.system.SysMenu;
import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.security.service.SysLoginService;
import com.servauto.admin.security.service.SysPermissionService;
import com.servauto.admin.service.system.SysMenuService;
import com.servauto.admin.service.system.SysOperaLogService;
import com.servauto.admin.service.system.SysRoleService;
import com.servauto.admin.service.workshop.WorkshopService;
import com.servauto.common.constant.Constants;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.framework.utils.MessageUtils;
import com.servauto.framework.utils.ServletUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "admin login")
@RestController
public class SysLoginController {

    @Resource
    private SysLoginService sysLoginService;

    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private SysPermissionService sysPermissionService;

    @Resource
    private SysOperaLogService sysOperaLogService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private WorkshopService workshopService;


    @PostMapping("/login")
    public CommonResult<LoginDTO> login(@RequestBody LoginReqDTO loginReqDTO) {
        String token;
        try {
            token = sysLoginService.login(loginReqDTO.getUsername(), loginReqDTO.getPassword());
        } catch (Exception e) {
            sysOperaLogService.recordLoginInfo(ServletUtils.getRequest(), loginReqDTO.getUsername(), Constants.LOGIN_FAIL, e.getMessage());
            throw e;
        }
        sysOperaLogService.recordLoginInfo(ServletUtils.getRequest(), loginReqDTO.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return CommonResult.success(LoginDTO.builder().token(token).build());
    }

    @GetMapping("/getInfo")
    public CommonResult<Object> getUserInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        Set<String> permissions = sysPermissionService.getMenuPermission(user);

        List<IdNameDTO> workshopNames = workshopService.getWorkshopNames();
        List<Long> workshopIds = sysRoleService.getWorkshopIdsByUserId(user.getUserId());
        workshopNames = workshopNames.stream().filter(e -> workshopIds.contains(e.getId())).collect(Collectors.toList());
        return CommonResult.success(UserInfoDTO.builder().user(user).roles(roles).permissions(permissions).workshopIds(workshopNames).build());
    }

    @GetMapping("/getRouters")
    public CommonResult<Object> getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = sysMenuService.selectMenuTreeByUserId(userId);
        return CommonResult.success(sysMenuService.buildMenus(menus));
    }
}
