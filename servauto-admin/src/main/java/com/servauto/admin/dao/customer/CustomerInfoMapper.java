package com.servauto.admin.dao.customer;

import com.servauto.admin.model.dto.request.customer.QueryCustomersDTO;
import com.servauto.admin.model.entity.customer.CustomerInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CustomerInfo record);

    int insertSelective(CustomerInfo record);

    CustomerInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomerInfo record);

    int updateByPrimaryKey(CustomerInfo record);

    List<CustomerInfo> selectByConditions(@Param("conditions") QueryCustomersDTO queryCustomersDTO);
}