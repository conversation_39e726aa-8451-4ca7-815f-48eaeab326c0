package com.servauto.admin.dao.car;

import com.servauto.admin.model.entity.car.CarModels;

import java.util.List;

public interface CarModelsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CarModels record);

    int insertSelective(CarModels record);

    CarModels selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CarModels record);

    int updateByPrimaryKey(CarModels record);

    // 新增方法：查询所有有效的models
    List<CarModels> selectAllValidModels();

    // 新增方法：根据brandId查询所有有效的models
    List<CarModels> selectAllModelsByBrandId(Long brandId);

    // 新增方法：根据brandId新增model，如果已存在则跳过
    void insertCarModelsIfNotExists(CarModels carModels);

    // 新增方法：批量插入models
    int batchInsertCarModels(List<CarModels> carModelsList);

    List<CarModels> selectAllModelsByBrandIdAndModels(Long brandId, List<String> modelsNames);

    List<CarModels> selectByPrimaryKeys(List<Long> modelIds);
}