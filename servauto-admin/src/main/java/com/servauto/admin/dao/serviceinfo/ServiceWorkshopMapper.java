package com.servauto.admin.dao.serviceinfo;

import com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop;

import java.util.List;

public interface ServiceWorkshopMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByServiceId(Long serviceId);

    int insert(ServiceWorkshop record);

    int insertSelective(ServiceWorkshop record);

    ServiceWorkshop selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceWorkshop record);

    int updateByPrimaryKey(ServiceWorkshop record);

    List<ServiceWorkshop> selectByServiceId(Long serviceId);

    List<ServiceWorkshop> selectByWorkshopId(Long workshopId);
}