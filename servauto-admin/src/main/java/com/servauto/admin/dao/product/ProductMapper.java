package com.servauto.admin.dao.product;

import com.servauto.admin.model.dto.request.product.QueryProductsDTO;
import com.servauto.admin.model.entity.product.Product;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Product record);

    int insertSelective(Product record);

    Product selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Product record);

    int updateByPrimaryKeyWithBLOBs(Product record);

    int updateByPrimaryKey(Product record);

    List<Product> selectByConditions(@Param("conditions") QueryProductsDTO queryProductsDTO);

    int updateServiceIdByIds(List<Long> ids, Long serviceId);

    int updateServiceIdByServiceId(Long srcServiceId, Long dstServiceId);
}