package com.servauto.admin.dao.order;

import com.servauto.admin.model.dto.request.order.PageOrderReqDTO;
import com.servauto.admin.model.entity.order.Order;
import com.servauto.admin.model.entity.order.OrderExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderMapper {
    List<Order> selectByExample(OrderExample example);

    List<Order> selectByPage(@Param("req") PageOrderReqDTO reqDTO);

    Order selectByOrderNo(String orderNo);

    int casByOrderNoAndStatusList(@Param("orderNo") String orderNo,
                                  @Param("o") Order order,
                                  @Param("statusList") List<String> statusList);

    List<Order> selectOrdersBeforeOneDay(@Param("date") Date date);

    List<Order> selectOrdersBeforeTwoHours(@Param("date") Date date);

    int markReminderOneDay(String orderNo);

    int markReminderTwoHour(String orderNo);
}