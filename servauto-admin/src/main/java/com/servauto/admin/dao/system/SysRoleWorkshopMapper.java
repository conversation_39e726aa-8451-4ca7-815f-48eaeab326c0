package com.servauto.admin.dao.system;

import com.servauto.admin.model.entity.system.SysRoleWorkshop;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysRoleWorkshopMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SysRoleWorkshop record);

    int insertSelective(SysRoleWorkshop record);

    SysRoleWorkshop selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysRoleWorkshop record);

    int updateByPrimaryKey(SysRoleWorkshop record);

    int deleteByRoleIds(@Param("roleIds") List<Long> roleIds);

    List<SysRoleWorkshop> selectByUserId(@Param("userId") Long userId);

    List<SysRoleWorkshop> selectByRoleId(@Param("roleId") Long roleId);

    List<SysRoleWorkshop> selectByRoleIds(@Param("roleIds") List<Long> roleIds);
}