package com.servauto.admin.dao.packageinfo;

import com.servauto.admin.model.dto.request.packageinfo.QueryPackagesDTO;
import com.servauto.admin.model.entity.packageinfo.PackageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PackageInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PackageInfo record);

    int insertSelective(PackageInfo record);

    PackageInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PackageInfo record);

    int updateByPrimaryKey(PackageInfo record);

    List<PackageInfo> selectByConditions(@Param("conditions") QueryPackagesDTO conditions);

    int updateServiceIdByIds(List<Long> ids, Long serviceId);

    int updateServiceIdByServiceId(Long srcServiceId, Long dstServiceId);
}