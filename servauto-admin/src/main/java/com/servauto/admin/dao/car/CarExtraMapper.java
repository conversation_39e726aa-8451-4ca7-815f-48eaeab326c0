package com.servauto.admin.dao.car;

import com.servauto.admin.model.entity.car.CarExtra;

import java.util.List;

public interface CarExtraMapper {
    // 修改返回类型为 List<CarExtra>
    List<CarExtra> selectByCarYearId(Long id);

    // 修改 insertCarExtraIfNotExists 方法
    void insertCarExtraIfNotExists(CarExtra carExtra);

    void batchInsertCarExtra(List<CarExtra> carExtraList);

    List<CarExtra> selectByYearId(Long id);

    List<CarExtra> selectCarExtraByIds(List<Long> id);

    List<CarExtra> selectByPrimaryKeys(List<Long> variantIds);
}