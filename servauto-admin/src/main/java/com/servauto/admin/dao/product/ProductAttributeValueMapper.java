package com.servauto.admin.dao.product;

import com.servauto.admin.model.entity.product.ProductAttributeValue;

import java.util.List;

public interface ProductAttributeValueMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAttributeValue record);

    int insertSelective(ProductAttributeValue record);

    ProductAttributeValue selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAttributeValue record);

    int updateByPrimaryKey(ProductAttributeValue record);

    List<ProductAttributeValue> selectByProductId(Long productId);

    List<ProductAttributeValue> selectByProductIds(List<Long> productIds);

    int deleteByProductId(Long productId);
}