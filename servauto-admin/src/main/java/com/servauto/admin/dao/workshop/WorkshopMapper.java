package com.servauto.admin.dao.workshop;

import com.servauto.admin.model.dto.request.workshop.QueryWorkshopsDTO;
import com.servauto.admin.model.entity.workshop.Workshop;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkshopMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Workshop record);

    int insertSelective(Workshop record);

    Workshop selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Workshop record);

    int updateByPrimaryKeyWithBLOBs(Workshop record);

    int updateByPrimaryKey(Workshop record);

    List<Workshop> selectByConditions(@Param("conditions") QueryWorkshopsDTO queryWorkshopsDTO);
}