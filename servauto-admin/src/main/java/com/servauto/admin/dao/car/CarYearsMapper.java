package com.servauto.admin.dao.car;

import com.servauto.admin.model.entity.car.CarYears;

import java.util.List;

public interface CarYearsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CarYears record);

    int insertSelective(CarYears record);

    CarYears selectByPrimaryKey(Integer id);

    List<CarYears> selectByModelId(Long modelId);

    void insertCarYearsIfNotExists(CarYears carYears);

    void batchInsertCarYears(List<CarYears> collect);

    List<CarYears> selectByModelIdAndYears(Long modelId, List<String> years);

    List<CarYears> selectByPrimaryKeys(List<Long> yearsIds);
}