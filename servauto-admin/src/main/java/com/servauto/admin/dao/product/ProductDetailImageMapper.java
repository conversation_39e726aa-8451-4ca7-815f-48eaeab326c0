package com.servauto.admin.dao.product;

import com.servauto.admin.model.entity.product.ProductDetailImage;

import java.util.List;

public interface ProductDetailImageMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductDetailImage record);

    int insertSelective(ProductDetailImage record);

    ProductDetailImage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductDetailImage record);

    int updateByPrimaryKey(ProductDetailImage record);

    List<ProductDetailImage> selectByProductId(Long productId);

    int deleteByProductId(Long productId);
}