package com.servauto.admin.dao.serviceinfo;

import com.servauto.admin.model.dto.request.serviceinfo.QueryServicesDTO;
import com.servauto.admin.model.entity.serviceinfo.ServiceInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ServiceInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ServiceInfo record);

    int insertSelective(ServiceInfo record);

    ServiceInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceInfo record);

    int updateByPrimaryKey(ServiceInfo record);

    List<ServiceInfo> selectByIds(List<Long> ids);

    List<ServiceInfo> selectByConditions(@Param("conditions") QueryServicesDTO conditions);
}