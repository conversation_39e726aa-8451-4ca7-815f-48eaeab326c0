package com.servauto.admin.dao.product;

import com.servauto.admin.model.entity.product.ProductAttributeDisplay;

public interface ProductAttributeDisplayMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAttributeDisplay record);

    int insertSelective(ProductAttributeDisplay record);

    ProductAttributeDisplay selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAttributeDisplay record);

    int updateByPrimaryKey(ProductAttributeDisplay record);

    ProductAttributeDisplay selectByTypeAndCategoryId(String type, Long categoryId);
}