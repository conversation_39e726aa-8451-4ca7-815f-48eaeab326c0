package com.servauto.admin.dao.customer;

import com.servauto.admin.model.entity.customer.CustomerShippingAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerShippingAddressMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CustomerShippingAddress record);

    int insertSelective(CustomerShippingAddress record);

    CustomerShippingAddress selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomerShippingAddress record);

    int updateByPrimaryKey(CustomerShippingAddress record);

    List<CustomerShippingAddress> selectByCustomerId(@Param("customerId") Long customerId);

    List<CustomerShippingAddress> selectByCustomerIds(@Param("customerIds") List<Long> customerIds);

    int deleteByCustomerId(@Param("customerId") Long customerId);

    void batchInsert(@Param("addresses") List<CustomerShippingAddress> addresses);
}