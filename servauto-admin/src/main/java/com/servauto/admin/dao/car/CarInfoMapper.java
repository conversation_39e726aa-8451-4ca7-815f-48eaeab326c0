package com.servauto.admin.dao.car;

import com.servauto.admin.model.entity.car.CarInfo;
import com.servauto.admin.model.entity.car.CarInfoWithBLOBs;
import com.servauto.admin.model.entity.customer.CustomerShippingAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CarInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CarInfoWithBLOBs record);

    int insertSelective(CarInfoWithBLOBs record);

    CarInfoWithBLOBs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarInfoWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(CarInfoWithBLOBs record);

    int updateByPrimaryKey(CarInfo record);

    List<CarInfo> selectByCustomerId(Long customerId);

    int deleteByCustomerId(Long customerId);

    void batchInsert(List<CarInfo> carInfoList);
}