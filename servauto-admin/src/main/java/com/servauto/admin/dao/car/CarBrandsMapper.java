package com.servauto.admin.dao.car;

import com.servauto.admin.model.dto.response.car.CarBrandsDTO;
import com.servauto.admin.model.entity.car.CarBrands;

import java.util.List;

public interface CarBrandsMapper {
    // 新增方法：查询所有品牌
    List<CarBrands> selectAll();

    // 新增方法：根据brandName查询品牌
    CarBrands selectByBrandName(String brandName);

    // 新增方法：查询所有有效的brands
    List<CarBrands> selectAllValidBrands();

    // 新增方法：批量插入CarBrands
    int batchInsertCarBrands(List<CarBrands> carBrandsList);

    List<CarBrandsDTO> selectBrandsByBrandNames(List<String> brandNames);

    List<CarBrands> selectByPrimaryKeys(List<Long> brandIds);

}