package com.servauto.admin.dao.workshop;

import com.servauto.admin.model.entity.workshop.WorkshopServiceTime;

import java.util.List;

public interface WorkshopServiceTimeMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByWorkshopId(Long workshopId);

    int insert(WorkshopServiceTime record);

    int insertSelective(WorkshopServiceTime record);

    WorkshopServiceTime selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WorkshopServiceTime record);

    int updateByPrimaryKey(WorkshopServiceTime record);

    List<WorkshopServiceTime> selectByWorkshopIds(List<Long> workshopIds);
}