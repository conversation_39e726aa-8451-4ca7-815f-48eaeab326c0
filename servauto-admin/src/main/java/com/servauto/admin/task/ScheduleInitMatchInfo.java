package com.servauto.admin.task;

import com.servauto.admin.model.entity.car.CarLibrary;
import com.servauto.admin.service.car.*;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@EnableScheduling
public class ScheduleInitMatchInfo {
    @Resource
    private CarLibraryService carLibraryService;

    public void syncCarLibrary() {
        /*
         * init brand
         */
        List<CarLibrary> carLibraries = this.selectWaitSyncCars();

        if (carLibraries.isEmpty()) {
            return;
        }
        //获取到carLibraries中所有的LibraryID信息
        List<Integer> libraryIds = carLibraries.stream().map(CarLibrary::getId).distinct().sorted(Comparator.naturalOrder()).toList();
        //根据brand获取到carLibraries中所有的CarModel去重后信息放到不同的map中
        Map<String, CarLibrary> carLibraryMap = new HashMap<>();

        carLibraries.forEach(carLibrary -> {
            //添加到carLibraryMap中





        });


    }


    private List<CarLibrary> selectWaitSyncCars() {
        return carLibraryService.selectWaitSyncCars();
    }


    public static String formatString(String input) {
        // 处理 null 和空字符串
        if (input == null || input.isEmpty()) {
            return input;
        }

        try {
            // 使用正则表达式插入空格
            // 正则表达式解释：
            // (\\d+/\\d+)([RZ][RFBDGHLMQRSTU]?\\d+) 匹配形如 "12/34R123" 的部分
            // (\\d+)([RZ][RFBDGHLMQRSTU]\\d+) 匹配形如 "195R15" 的部分
            // (\\d+/\\d+)([RZ][RFBDGHLMQRSTU]?\\d+)(\\s*\\d+[A-Z]?)? 匹配形如 "215/65R16 98H" 的部分
            String formatted = input.replaceAll("(\\d+/\\d+)([RFBDGHLMQRSTUZ][RFT]?\\d+)(\\s*\\d+[A-Z]?)?", "$1 $2$3");
            formatted = formatted.replaceAll("(\\d+)([RFBDGHLMQRSTUZ][RFT]\\d+)(\\s*\\d+[A-Z]?)?", "$1 $2$3");
            formatted = formatted.replaceAll("(\\d+)([RFBDGHLMQRSTUZ]\\d+)", "$1 $2");
            return formatted.trim();
        } catch (Exception e) {
            // 记录异常日志（如果有日志系统）
            System.err.println("Error occurred while formatting string: " + e.getMessage());
            return input;
        }
    }

   /* public static void main(String[] args) {
        String input1 = "255/40R19";
        String input2 = "255/40ZR19";
        String input3 = "195R15";
        String input4 = "255/40RF19";
        String input5 = "255/40Z19";
        String input6 = "215/65R16 98H";
        String input7 = "215/65ZR16 98H";

        String output1 = formatString(input1);
        String output2 = formatString(input2);
        String output3 = formatString(input3);
        String output4 = formatString(input4);
        String output5 = formatString(input5);
        String output6 = formatString(input6);
        String output7 = formatString(input7);

        System.out.println(output1);  // 输出: 255/40 R19
        System.out.println(output2);  // 输出: 255/40 ZR19
        System.out.println(output3);  // 输出: 195 R15
        System.out.println(output4);  // 输出: 255/40 RF19
        System.out.println(output5);  // 输出: 255/40 B19
        System.out.println(output6);  // 输出: 215/65 R16 98H
        System.out.println(output7);  // 输出: 215/65 ZR16 98H
    }*/

}
