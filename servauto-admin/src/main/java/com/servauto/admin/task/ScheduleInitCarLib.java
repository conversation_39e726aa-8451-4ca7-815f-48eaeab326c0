package com.servauto.admin.task;

import com.servauto.admin.model.dto.response.car.*;
import com.servauto.admin.model.entity.car.*;
import com.servauto.admin.service.car.*;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.*;
import java.util.stream.Collectors;

@Configuration
@EnableScheduling
public class ScheduleInitCarLib {
    @Resource
    private CarLibraryService carLibraryService;

    @Resource
    private CarBrandsService carBrandsService;

    @Resource
    private CarModelsService carModelsService;

    @Resource
    private CarYearsService carYearsService;

    @Resource
    private CarExtraService carExtraService;

    public void syncCarLibrary() {
        /*
         * init brand
         */
        List<CarLibrary> carLibraries = this.getWaitSyncDistinctBrands();

        if (carLibraries.isEmpty()) {
            return;
        }
        //获取到carLibraries中所有的LibraryID信息
        List<Integer> libraryIds = carLibraries.stream().map(CarLibrary::getId).distinct().sorted(Comparator.naturalOrder()).toList();
        //根据brand获取到carLibraries中所有的CarModel去重后信息放到不同的map中
        Map<String, List<String>> brandToModelsMap = new HashMap<>();
        Map<String, Map<String, Set<String>>> brandToModelsToYearsMap = new HashMap<>();

        carLibraries.forEach(carLibrary -> {
            String brand = carLibrary.getBrand();
            String model = carLibrary.getModel();
            if (brand != null && model != null) {
                brandToModelsMap.computeIfAbsent(brand.trim(), k -> new ArrayList<>());
                Set<String> modelsSet = new HashSet<>(brandToModelsMap.get(brand.trim()));
                modelsSet.add(model.trim());
                brandToModelsMap.put(brand.trim(), new ArrayList<>(modelsSet));
                brandToModelsToYearsMap.computeIfAbsent(brand.trim(), k -> new HashMap<>()).computeIfAbsent(model.trim(), k -> new HashSet<>()).add(carLibrary.getModelYear());
            }
        });


        //获取到carLibraries中所有的CarBrand信息
        List<String> brands = carLibraries.stream().map(CarLibrary::getBrand).distinct().sorted(String::compareToIgnoreCase).toList();
        //去除非null值的前后空格，保留null值
        brands = brands.stream().map(brand -> brand != null ? brand.trim() : null).toList();
        //将brands插入到car_brands
        //1.拿到所有有效的brands
        List<CarBrandsDTO> carBrandsDTOList = carBrandsService.selectAllValidBrands();
        //2.对比CarLibrary中的brands拿到新增的
        List<CarBrandsDTO> finalCarBrandsDTOList = carBrandsDTOList;
        List<String> newBrands = brands.stream().filter(brand -> !finalCarBrandsDTOList.stream().map(CarBrandsDTO::getBrandName).toList().contains(brand)).toList();
        //将brands批量插入到car_brands
        if (!newBrands.isEmpty()) {
            carBrandsService.batchInsertCarBrands(newBrands.stream().map(brand -> {
                CarBrands carBrands = new CarBrands();
                carBrands.setBrandName(brand);
                carBrands.setStatus(1);
                return carBrands;
            }).collect(Collectors.toList()));
        }

        /*
         * init models
         */
        carBrandsDTOList = carBrandsService.selectBrandsByBrandNames(brands);
        List<CarModels> waitInsertModels = new ArrayList<>();
        for (CarBrandsDTO brand : carBrandsDTOList) {
            //获取到需要同步的CarModel信息
            List<String> waitSyncModels = brandToModelsMap.get(brand.getBrandName());
            //拿到所有的CarModels信息
            List<CarModelsDTO> waitSyncModelsDTOList = this.getWaitSyncModels(brand.getId(), waitSyncModels);
            //2.对比brand中的models拿到新增的
            List<String> newModels = waitSyncModels.stream().filter(model -> !waitSyncModelsDTOList.stream().map(CarModelsDTO::getModelName).toList().contains(model)).toList();
            if (!newModels.isEmpty()) {
                //将models批量插入到car_models
                waitInsertModels.addAll(newModels.stream().map(modelName -> {
                    CarModels model = new CarModels();
                    model.setBrandId(brand.getId());
                    model.setModelName(modelName);
                    model.setStatus(1);
                    return model;
                }).toList());


            }
        }
        carModelsService.batchInsertCarModels(waitInsertModels);
        /*
         * init years
         */
        List<CarYears> waitInsertYears = new ArrayList<>();

        for (CarBrandsDTO brand : carBrandsDTOList) {
            //获取到需要同步的CarModel信息
            List<String> waitSyncModels = brandToModelsMap.get(brand.getBrandName());
            //拿到所有的CarModels信息
            List<CarModelsDTO> waitSyncModelsDTOList = this.getWaitSyncModels(brand.getId(), waitSyncModels);
            for (CarModelsDTO model : waitSyncModelsDTOList) {
                List<String> waitSyncYears = brandToModelsToYearsMap.get(brand.getBrandName()).get(model.getModelName()).stream().toList();
                List<CarYearsDTO> carYearsDtoListCarYearsDTO = this.getWaitSyncYears(model.getId(), waitSyncYears);
                //2.对比carLibYears中拿到新增
                List<String> newYears = waitSyncYears.stream().filter(year -> !carYearsDtoListCarYearsDTO.stream().map(CarYearsDTO::getYearValue).toList().contains(year)).toList();
                if (!newYears.isEmpty()) {
                    //将years批量插入到car_years
                    waitInsertYears.addAll(newYears.stream().map(year -> {
                        CarYears carYears = new CarYears();
                        carYears.setBrandId(brand.getId());
                        carYears.setModelId(model.getId());
                        carYears.setYearValue(year);
                        carYears.setStatus(1);
                        return carYears;
                    }).toList());
                }
            }
        }
        carYearsService.batchInsertCarYears(waitInsertYears);

        /*
         * init CarExtra
         */
        //获取到CarLibrary中所有的CarExtra信息
        List<CarExtra> waitInsertCarExtras = new ArrayList<>();

        for (CarBrandsDTO brand : carBrandsDTOList) {
            //获取到需要同步的CarModel信息
            List<String> waitSyncModels = brandToModelsMap.get(brand.getBrandName());
            //拿到所有的CarModels信息
            List<CarModelsDTO> waitSyncModelsDTOList = this.getWaitSyncModels(brand.getId(), waitSyncModels);
            for (CarModelsDTO model : waitSyncModelsDTOList) {
                //根据modelid 查询所有的有效modelyears
                List<String> waitSyncYears = brandToModelsToYearsMap.get(brand.getBrandName()).get(model.getModelName()).stream().toList();
                List<CarYearsDTO> carYearsDtoListCarYearsDTO = this.getWaitSyncYears(model.getId(), waitSyncYears);
                for (CarYearsDTO year : carYearsDtoListCarYearsDTO) {

                    List<CarLibrary> carLibExtra = this.getCarLibrariesByBrandModelYearAndGrade(brand.getBrandName(), model.getModelName(), year.getYearValue());
                    //根据variant 查询所有的有效car_extra
                    List<CarExtraDTO> carExtraDTOList = carExtraService.getCarExtraByYearId(year.getId());
                    //根据carLibId拿到新增CarLibrary
                    List<CarLibrary> carLibExtraNew = carLibExtra.stream().filter(carLib -> !carExtraDTOList.stream().map(CarExtraDTO::getCarLibId).toList().contains(carLib.getCarLibId().trim())).toList();
                    if (!carLibExtraNew.isEmpty()) {
                        //将years批量插入到car_years
                        waitInsertCarExtras.addAll(carLibExtraNew.stream().map(carLib -> {
                            CarExtra carExtra = new CarExtra();
                            carExtra.setCarLibId(carLib.getCarLibId());
                            carExtra.setBrandId(brand.getId());
                            carExtra.setModelId(model.getId());
                            carExtra.setYearId(year.getId());
                            carExtra.setVariantName(carLib.getGrade() + " " + carLib.getTransmissionType() + " " + carLib.getDisplacement());
                            carExtra.setTransmissionType(carLib.getTransmissionType());
                            carExtra.setDisplacementValue(carLib.getDisplacement());
                            carExtra.setCarType(carLib.getVehicleType());
                            carExtra.setTireFront(carLib.getTireFront());
                            carExtra.setTireRear(carLib.getTireRear());
                            carExtra.setStatus(1);
                            return carExtra;

                        }).toList());
                    }

                }
            }
        }
        carExtraService.batchInsertCarExtra(waitInsertCarExtras);
        //library更新为已同步
        carLibraryService.updateCarLibraryStatusByIds(libraryIds);
    }


    private List<CarLibrary> getWaitSyncDistinctBrands() {
        return carLibraryService.selectWaitSyncDistinctBrands();
    }

    private List<CarLibrary> getCarLibrariesByBrandModelYearAndGrade(String brandName, String modelName, String yearValue) {
        return carLibraryService.selectCarLibrariesByBrandModelYearAndGrade(brandName, modelName, yearValue).stream().sorted(Comparator.comparing(CarLibrary::getCarLibId)).collect(Collectors.toList());

    }


    private List<CarModelsDTO> getWaitSyncModels(Long brandId, List<String> models) {
        //去重
        models = models.stream().distinct().sorted(String::compareToIgnoreCase).map(String::trim).collect(Collectors.toList());
        //拿到所有的CarModels信息
        return carModelsService.selectAllModelsByBrandIdAndModels(brandId, models);

    }


    private List<CarYearsDTO> getWaitSyncYears(Long modelId, List<String> years) {
        //去重
        years = years.stream().map(year -> year != null ? year.trim() : null).toList();
        //拿到所有的CarModels信息
        return carYearsService.selectAllYearsByModelIdAndModels(modelId, years);

    }
}
