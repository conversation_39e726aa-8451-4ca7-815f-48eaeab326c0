package com.servauto.admin.task;

import com.servauto.admin.dao.order.OrderMapper;
import com.servauto.admin.model.entity.order.Order;
import com.servauto.admin.service.notice.NoticeService;
import com.servauto.common.utils.DateUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * <p>CustomerAdvanceReminder</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/16 19:53
 */
@Slf4j
@Configuration
@EnableScheduling
public class CustomerAdvanceReminder {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private NoticeService noticeService;


    @Scheduled(cron = "0 0,30 * * * *")
    public void advanceReminder() {
        LocalDateTime localDateTime = LocalDateTime.now();
        oneDayAdvanceReminder(localDateTime);
        twoHourAdvanceReminder(localDateTime);
    }

    @Scheduled(cron = "0 5,35 * * * *")
    public void compensationAdvanceReminder() {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(5);
        oneDayAdvanceReminder(localDateTime);
        twoHourAdvanceReminder(localDateTime);
    }

    /**
     * This is a 1-day advance reminder from ServAuto! Your [Service Name] appointment isscheduled for tomorrow at [Appointment Time]at [Store Name] ([Store Address]).
     * Please confirm your availability and prepare vehicle.
     * Need to adjust? Reply to reschedule or contact us at [Customer Service Number] by today. We’ll ensure a seamless experience for you!
     * 简单实现一个查询标记
     */
    private void oneDayAdvanceReminder(LocalDateTime localDateTime) {
        LocalDateTime targetTime = localDateTime.plusDays(1).withSecond(0).withNano(0);
        log.info("OneDayAdvanceReminder start {}", targetTime.format(DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
        Date date = Date.from(targetTime.atZone(ZoneId.systemDefault()).toInstant());
        List<Order> orders = orderMapper.selectOrdersBeforeOneDay(date);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (Order order : orders) {
            try {
                noticeService.advanceReminderOneDay(order);
                orderMapper.markReminderOneDay(order.getOrderNo());
            } catch (Exception e) {
                log.error("OneDayAdvanceReminder error", e);
            }
        }
    }

    /**
     * Here's a friendly reminder from ServAuto! Your appointment for [Service Name] at [Store Name, Address] is scheduled in 2 hours (at [Appointment Time]).
     * Please arrive on time and bring your vehicle for a smooth service experience. Can't make it? Reply to reschedule or contact our team at [Customer Service Number].
     * We're ready to assist!
     * 简单实现一个查询标记
     */
    private void twoHourAdvanceReminder(LocalDateTime localDateTime) {
        LocalDateTime targetTime = localDateTime.plusHours(2).withSecond(0).withNano(0);
        log.info("TwoHourAdvanceReminder start {}", targetTime.format(DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
        Date date = Date.from(targetTime.atZone(ZoneId.systemDefault()).toInstant());
        List<Order> orders = orderMapper.selectOrdersBeforeTwoHours(date);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (Order order : orders) {
            try {
                noticeService.advanceReminderTwoHour(order);
                orderMapper.markReminderTwoHour(order.getOrderNo());
            } catch (Exception e) {
                log.error("TwoHourAdvanceReminder error", e);
            }
        }
    }

}
