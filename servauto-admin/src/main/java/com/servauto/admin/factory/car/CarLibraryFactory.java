package com.servauto.admin.factory.car;

import com.servauto.admin.model.dto.response.car.CarLibraryDTO;
import com.servauto.admin.model.entity.car.CarLibrary;

public class CarLibraryFactory {
    public static CarLibraryDTO convert(CarLibrary info) {
        if (info == null) {
            return null;
        }
        return CarLibraryDTO.builder()
                .id(info.getId())
                .carLibId(info.getCarLibId())
                .manufacturer(info.getManufacturer())
                .brand(info.getBrand())
                .series(info.getSeries())
                .model(info.getModel())
                .modelYear(info.getModelYear())
                .chassisCode(info.getChassisCode())
                .transmissionType(info.getTransmissionType())
                .driverPosition(info.getDriverPosition())
                .vehicleType(info.getVehicleType())
                .doors(info.getDoors())
                .engineModel(info.getEngineModel())
                .displacement(info.getDisplacement())
                .powerKw(info.getPowerKw())
                .fuelType(info.getFuelType())
                .driveModel(info.getDriveModel())
                .countryOfSale(info.getCountryOfSale())
                .areaOfSale(info.getAreaOfSale())
                .tireFront(info.getTireFront())
                .tireRear(info.getTireRear())
                .status(info.getStatus())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}