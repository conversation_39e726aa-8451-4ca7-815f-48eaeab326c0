package com.servauto.admin.factory.product;


import com.servauto.admin.enums.SaleStatus;
import com.servauto.admin.model.dto.response.product.ProductBaseDTO;
import com.servauto.admin.model.dto.response.product.ProductDTO;
import com.servauto.admin.model.entity.product.Product;
import com.servauto.common.utils.BigDecimalUtils;

public class ProductFactory {
    public static ProductDTO convert(Product info) {
        return ProductDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .categoryId(info.getCategoryId())
                .brandId(info.getBrandId())
                .status(info.getStatus().toString())
                .statusName(SaleStatus.getByCode(info.getStatus()).getDesc())
                .description(info.getDescription())
                .serviceId(info.getServiceId().equals(0L)?  null :info.getServiceId())
                .netContent(info.getNetContent())
                .contentUnit(info.getContentUnit())
                .price(info.getPrice())
                .mainImage(info.getMainImage())
                .build();
    }

    public static ProductBaseDTO convertBase(Product info) {
        return ProductBaseDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .categoryId(info.getCategoryId())
                .brandId(info.getBrandId())
                .status(info.getStatus())
                .statusName(SaleStatus.getByCode(info.getStatus()).getDesc())
                .serviceId(info.getServiceId().equals(0L)?  null :info.getServiceId())
                .netContent(info.getNetContent())
                .contentUnit(info.getContentUnit())
                .price(info.getPrice())
                .updatedBy(info.getUpdatedBy())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}
