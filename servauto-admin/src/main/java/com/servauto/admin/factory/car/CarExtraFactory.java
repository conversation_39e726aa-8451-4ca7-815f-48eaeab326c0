package com.servauto.admin.factory.car;

import com.servauto.admin.model.dto.response.car.CarExtraDTO;
import com.servauto.admin.model.entity.car.CarExtra;

public class CarExtraFactory {
    public static CarExtraDTO convert(CarExtra info) {
        return CarExtraDTO.builder()
                .id(info.getId())
                .carLibId(info.getCarLibId())
                .transmissionType(info.getTransmissionType())
                .displacementValue(info.getDisplacementValue())
                .carType(info.getCarType())
                .tireFront(info.getTireFront())
                .tireRear(info.getTireRear())
                .status(info.getStatus())
                .order(info.getOrder())
                .build();
    }
}