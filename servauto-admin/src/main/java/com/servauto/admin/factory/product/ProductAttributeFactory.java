package com.servauto.admin.factory.product;

import com.servauto.admin.model.dto.response.product.ProductAttributeDTO;
import com.servauto.admin.model.entity.product.ProductAttribute;

public class ProductAttributeFactory {
    public static ProductAttributeDTO convert(ProductAttribute info) {
        return  ProductAttributeDTO.builder()
                .id(info.getId())
                .type(info.getType())
                .name(info.getName())
                .suffix(info.getSuffix())
                .categoryId(info.getCategoryId())
                .isRequired(info.getIsRequired() == 1)
                .order(info.getOrder())
                .build();
    }
}
