package com.servauto.admin.factory.payment;

import com.servauto.admin.model.dto.response.payment.PaymentDTO;
import com.servauto.admin.model.entity.payment.Payment;

public class PaymentFactory {

    public static PaymentDTO convert(Payment payment) {
        return PaymentDTO.builder().payNo(payment.getPayNo()).channel(payment.getChannel()).payType(payment.getPayType()).amount(payment.getAmount()).sourceId(payment.getSourceId()).build();
    }

}
