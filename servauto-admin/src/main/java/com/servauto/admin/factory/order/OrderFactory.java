package com.servauto.admin.factory.order;


import com.google.common.base.Preconditions;
import com.servauto.admin.enums.order.OrderDeliveryTypeEnum;
import com.servauto.admin.enums.order.OrderStatusEnum;
import com.servauto.admin.model.dto.response.order.*;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.model.entity.order.Order;
import com.servauto.admin.model.entity.order.OrderDelivery;
import com.servauto.admin.model.entity.order.OrderPay;
import com.servauto.admin.model.entity.order.OrderProduct;

import java.util.Date;
import java.util.List;

public class OrderFactory {

    private static String PRINT_TEMPLATE = "^XA\n" +
            "^FO50,50^GFA,952,952,28,003FEgI0FFCFFC,01IF8gH0FF8FF8,07IFEgH0FF8FF98,0KFgG01FF9FF98O01FE,1KF8g01FF1FF18O01FE,3KFCg01FF1FF3CO01FE,3KFCg03FF3FF3CO01FE,7FE03FEg03FE3FE3CO01FE,7FC01FEI07M0CN03FE3FE7EO01FEJ078,7F801FE007FF001FE3F3FC00FF07FE7FE7E01FE00FF1IFE007FF8,7F8K01IFC01FE7F3FE01FF07FC7FC7E01FF00FF1IFE01IFE,7FCK03JF01FEFF1FE01FE07FC7FCFF01FE00FF1IFE03JF,7FFK07JF01FEFF1FE01FE0FFCFFCFF01FE00FF1IFE07JF8,7FFEJ0KF81JF1FF01FE0FF8FF8FF01FE00FF1IFE07JFC,3IFEI0FF87FC1JF0FF03FC0FF9FF9FF81FF00FF0IFE0FFC7FE,1JFC01FF03FC1FFE00FF03FC1FF9FF8FF81FE00FF01FE01FF03FE,0KF01FE01FC1FF800FF03FC1FF1FF0FF81FE00FF01FE01FF01FE,07JF81FC00FE1FFI07F87F81FF3FF0FFC1FF00FF01FE01FE01FF,01JFC3FF0DFE1FFI07F87F83FF3FE07FC1FF00FF01FE03FE01FF,003IFE3KFE1FEI07F87F83FE3FE07FE1FF00FF01FE03FE00FF,I01FFE3KFE1FEI03F87F07FE7FE07FE1FF00FF01FE03FE00FF,J03FE3KFE1FEI03FCFF07FE7FC03FE1FF00FF01FE03FE00FF,5C001FE3KFE1FEI01FCFF07FC7FC03FF1FF00FF01FE03FE00FF,FF001FF3FCJ01FEI01FCFE0FFCFFC03FF1FF00FF01FE03FE00FF,FF001FF1FCJ01FEI01FEFE0FF8FF801FF1FF00FF01FE01FE01FF,FF801FF1FEJ01FEJ0IFE0FF8FF801FF9FF01FF01FE01FE01FE,7FC03FE1FE01FE1FEJ0IFC1FF9FF801FF8FF83FF01FE01FF03FE,7KFE1FF03FE1FEJ0IFC1FF1FFI0FF8LF01FF00FF87FE,3KFC0KFC1FEJ07FF81FF1FFI0FFCLF01FFE0KFC,3KFC07JFC1FEJ07FF83FF3FFI0FFCLF01FFE07JF8,1KF807JF81FEJ07FF83FE3FEI07FC7FFEFF00FFE03JF8,0KF001JF01FEJ03FF03FE3FEI07FE3FFCFF00IF01IFE,03IFCI0IFC01FEJ03FF07FE7FEI03FE1FF8FF007FF00IFC,007FEJ01FEI07CJ01FE07FC7FCI03FE07E07EI0FE001FE,^FS\n" +
            "^FO480,30^GB380,45,45^FS \n" +
            "^FO520,40^A0N,30,30^FR^FD%s^FS\n" +
            "^FO50,120^A0N,35,30^FB700,2,10,L,0^FD%s^FS\n" +
            "^FO50,220^A0N,25,25^FDOrder ID : %s^FS  \n" +
            "^FO50,260^A0N,25,25^FDPlate No : %s^FS\n" +
            "^FO50,300^A0N,25,25^FDCustomer Name : %s^FS\n" +
            "^FO50,340^A0N,25,25^FDService Workshop : %s^FS \n" +
            "^XZ";

    public static String formatTemplate(Order order) {
        OrderDeliveryTypeEnum deliveryTypeEnum = OrderDeliveryTypeEnum.getByCode(order.getDeliveryType());
        Preconditions.checkNotNull(deliveryTypeEnum);
        return String.format(PRINT_TEMPLATE,
                deliveryTypeEnum.getPrintMsg(), formatCarInfo(order), order.getOrderNo(), order.getLicensePlate(), order.getCustomerName(), order.getWorkshopName()
        );
    }

    public static OrderDTO buildOrderDTO(Order order, OrderPay pay, List<OrderProduct> products, OrderDelivery delivery) {
        List<OrderProductDTO> orderProducts = products.stream().map(OrderProductFactory::convert).toList();
        OrderPayDTO orderPay = OrderPayFactory.convert(pay);

        OrderDeliveryDTO orderDelivery = new OrderDeliveryDTO();
        if (OrderDeliveryTypeEnum.isShipping(order.getDeliveryType())) {
            orderDelivery = OrderDeliveryFactory.convert(delivery);
        }

        return OrderDTO.builder()
                .orderNo(order.getOrderNo())
                .licensePlate(order.getLicensePlate())
                .carInfo(formatCarInfo(order))
                .status(order.getStatus())
                .statusName(OrderStatusEnum.getByCode(order.getStatus()).getMsg())
                .customerId(order.getCustomerId())
                .customerMobile(order.getCustomerMobile())
                .customerName(order.getCustomerName())
                .inStoreName(order.getInStoreName())
                .inStoreMobile(order.getInStoreMobile())
                .creatorName(order.getCreateBy())
                .updaterName(order.getUpdateBy())
                .originalAmount(order.getOriginalAmount())
                .discountAmount(order.getDiscountAmount())
                .subtotal(order.getSubtotal())
                .shippingFee(order.getShippingFee())
                .grandTotal(order.getGrandTotal())
                .type(order.getType())
                .packageId(order.getPackageId())
                .packageName(order.getPackageName())
                .serviceId(order.getServiceId())
                .serviceName(order.getServiceName())
                .serviceHour(order.getServiceHour())
                .serviceFee(order.getServiceFee())
                .workshopId(order.getWorkshopId())
                .workshopName(order.getWorkshopName())
                .deliveryType(order.getDeliveryType())
                .couponAmount(order.getCouponAmount())
                .orderTime(order.getOrderTime())
                .createTime(order.getCreateTime())
                .reservationTime(formatDate(order.getReservationTime()))
                .paidTime(formatDate(order.getPaidTime()))
                .finishedTime(formatDate(order.getFinishedTime()))
                .refundedTime(formatDate(order.getRefundedTime()))
                .updateTime(order.getUpdateTime())
                .products(orderProducts)
                .delivery(orderDelivery)
                .orderPay(orderPay)
                .build();
    }

    public static boolean isValidDate(Date date) {
        return date != null && date.getTime() > 0;
    }

    public static Date formatDate(Date date) {
        return isValidDate(date) ? date : null;
    }

    public static String formatCarInfo(Order order) {
        return String.format("%s %s %s %s", order.getBrand(), order.getModel(), order.getYear(), order.getVariant());
    }

    public static OrderWorkshopDTO buildWorkshop(WorkshopDetailDTO e, List<OrderWorkshopDTO.ServiceTimeDTO> serviceTimes) {
        return OrderWorkshopDTO.builder()
                .id(e.getId())
                .name(e.getName())
                .stateCode(e.getStateCode())
                .stateName(e.getStateName())
                .cityCode(e.getCityCode())
                .cityName(e.getCityName())
                .address(e.getAddress())
                .latitude(e.getLatitude())
                .longitude(e.getLongitude())
                .contactNumber(e.getContactNumber())
                .logo(e.getLogo())
                .locationUrl(e.getLocationUrl())
                .serviceTimes(serviceTimes)
                .build();
    }
}
