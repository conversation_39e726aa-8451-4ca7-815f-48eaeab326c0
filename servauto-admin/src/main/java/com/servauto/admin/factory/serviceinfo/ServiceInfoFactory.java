package com.servauto.admin.factory.serviceinfo;

import com.servauto.admin.enums.ServiceSupportedOn;
import com.servauto.admin.enums.YesOrNo;
import com.servauto.admin.model.dto.response.serviceinfo.ServiceInfoDTO;
import com.servauto.admin.model.entity.serviceinfo.ServiceInfo;
import com.servauto.common.utils.BigDecimalUtils;

public class ServiceInfoFactory {
    public static ServiceInfoDTO convert(ServiceInfo info) {
        return ServiceInfoDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .hours(info.getHours())
                .fee(info.getFee())
                .supportedOn(info.getSupportedOn().toString())
                .supportedOnName(ServiceSupportedOn.getByCode(info.getSupportedOn()).getDesc())
                .isRequired(info.getIsRequired().toString())
                .isRequiredName(YesOrNo.getByCode(info.getIsRequired()).getDesc())
                .updatedBy(info.getUpdatedBy())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}
