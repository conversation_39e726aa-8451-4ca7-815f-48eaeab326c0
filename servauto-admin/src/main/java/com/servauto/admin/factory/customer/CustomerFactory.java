package com.servauto.admin.factory.customer;

import com.servauto.admin.enums.GenderEnum;
import com.servauto.admin.model.dto.response.customer.CustomerAddressDTO;
import com.servauto.admin.model.dto.response.customer.CustomerInfoDTO;
import com.servauto.admin.model.entity.customer.CustomerInfo;
import com.servauto.admin.model.entity.customer.CustomerShippingAddress;
import com.servauto.framework.location.model.dto.AreaDTO;
import com.servauto.framework.location.model.dto.StateDTO;

import java.util.Map;

public class CustomerFactory {

    public static CustomerInfoDTO convert(CustomerInfo customerInfo) {
        return CustomerInfoDTO.builder()
                .customerId(customerInfo.getId())
                .realName(customerInfo.getRealName())
                .mobile(customerInfo.getMobile())
                .birthday(customerInfo.getBirthday())
                .gender(customerInfo.getGender())
                .genderName(GenderEnum.getByCode(customerInfo.getGender()).getDesc())
                .build();
    }

    public static CustomerAddressDTO convert(CustomerShippingAddress address, Map<String, StateDTO> stateMap, Map<String, AreaDTO> areaMap) {
        AreaDTO areaDTO = areaMap.get(address.getCityCode());
        StateDTO stateDTO = stateMap.get(address.getStateCode());
        String areaName = areaDTO == null ? "" : areaDTO.getName();
        String stateName = stateDTO == null ? "" : stateDTO.getName();

        return CustomerAddressDTO.builder()
                .customerId(address.getCustomerId())
                .name(address.getName())
                .mobile(address.getMobile())
                .isDefault(address.getIsDefault())
                .stateCode(address.getStateCode())
                .cityName(areaName)
                .stateName(stateName)
                .cityCode(address.getCityCode())
                .address(address.getAddress())
                .shippingAddress(address.getAddress() + "," + stateName + "," + areaName)
                .build();
    }

}
