package com.servauto.admin.factory.order;


import com.servauto.admin.model.dto.response.order.OrderDeliveryDTO;
import com.servauto.admin.model.entity.order.OrderDelivery;

/**
 * <p>OrderDeliveryFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/8 11:18
 */
public class OrderDeliveryFactory {

    public static OrderDeliveryDTO convert(OrderDelivery delivery) {
        return OrderDeliveryDTO.builder()
                .orderNo(delivery.getOrderNo())
                .name(delivery.getName())
                .mobile(delivery.getMobile())
                .stateCode(delivery.getStateCode())
                .stateName(delivery.getStateName())
                .cityCode(delivery.getCityCode())
                .cityName(delivery.getCityName())
                .address(delivery.getAddress())
                .build();
    }

    public static String formatAddress(OrderDelivery delivery) {
        return String.format("%s %s %s ", delivery.getCityName(), delivery.getStateName(), delivery.getAddress());
    }
}
