package com.servauto.admin.factory.packageinfo;

import com.servauto.admin.enums.SaleStatus;
import com.servauto.admin.enums.YesOrNo;
import com.servauto.admin.model.dto.response.packageinfo.PackageInfoDTO;
import com.servauto.admin.model.entity.packageinfo.PackageInfo;

public class PackageInfoFactory {
    public static PackageInfoDTO convert(PackageInfo info) {
        return PackageInfoDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .status(info.getStatus().toString())
                .statusName(SaleStatus.getByCode(info.getStatus()).getDesc())
                .price(YesOrNo.yes(info.getIsFixedPrice()) ? info.getPrice() : null)
                .serviceId(info.getServiceId())
                .updatedBy(info.getUpdatedBy())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .build();
    }
}
