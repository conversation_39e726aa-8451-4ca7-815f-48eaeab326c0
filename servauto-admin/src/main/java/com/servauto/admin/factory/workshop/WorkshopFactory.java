package com.servauto.admin.factory.workshop;

import com.servauto.admin.enums.WorkshopStatus;
import com.servauto.admin.enums.WorkshopType;
import com.servauto.admin.model.dto.response.workshop.WorkshopDTO;
import com.servauto.admin.model.dto.response.workshop.WorkshopDetailDTO;
import com.servauto.admin.model.entity.workshop.Workshop;

import java.util.List;

public class WorkshopFactory {
    public static WorkshopDTO convert(Workshop info) {
        return  WorkshopDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .type(info.getType())
                .typeName(WorkshopType.getByCode(info.getType()).getDesc())
                .status(info.getStatus())
                .statusName(WorkshopStatus.getByCode(info.getStatus()).getDesc())
                .stateCode(info.getStateCode())
                .cityCode(info.getCityCode())
                .address(info.getAddress())
                .contactNumber(info.getPhoneNumber())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .operatorId(info.getOperatorId())
                .longitude(info.getLongitude())
                .latitude(info.getLatitude())
                .locationUrl(info.getLocationUrl())
                .build();
    }

    public static WorkshopDetailDTO convertDetail(Workshop info) {
        return  WorkshopDetailDTO.builder()
                .id(info.getId())
                .name(info.getName())
                .type(info.getType().toString())
                .typeName(WorkshopType.getByCode(info.getType()).getDesc())
                .status(info.getStatus().toString())
                .statusName(WorkshopStatus.getByCode(info.getStatus()).getDesc())
                .stateCode(info.getStateCode())
                .cityCode(info.getCityCode())
                .address(info.getAddress())
                .contactNumber(info.getPhoneNumber())
                .createTime(info.getCreateTime())
                .updateTime(info.getUpdateTime())
                .operatorId(info.getOperatorId())
                .remark(info.getRemark())
                .logo(info.getLogo())
                .longitude(info.getLongitude())
                .latitude(info.getLatitude())
                .locationUrl(info.getLocationUrl())
                .photo(List.of(info.getPhoto().split(",")))
                .build();
    }
}
