package com.servauto.admin.factory.order;

import com.fasterxml.jackson.core.type.TypeReference;
import com.servauto.admin.enums.order.OrderStatusEnum;
import com.servauto.admin.model.dto.response.order.OrderStatusLogDTO;
import com.servauto.admin.model.entity.order.OrderDelivery;
import com.servauto.admin.model.entity.order.OrderStatusLog;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>OrderStatusLogFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 18:31
 */
public class OrderStatusLogFactory {

    public static OrderStatusLog build(String orderNo,
                                       OrderStatusEnum enums, String updateBy,
                                       List<String> images, String remark, Long deliveryTime) {
        OrderStatusLog orderStatusLog = new OrderStatusLog();
        orderStatusLog.setOrderNo(orderNo);
        orderStatusLog.setStatus(enums.getCode());
        orderStatusLog.setRemark(remark);
        orderStatusLog.setUpdateBy(updateBy);
        if (CollectionUtils.isNotEmpty(images)) {
            orderStatusLog.setImages(JacksonSerializer.serialize(images));
        }
        if (deliveryTime != null && deliveryTime > 0) {
            orderStatusLog.setDeliveryTime(new Date(deliveryTime));
        }
        return orderStatusLog;
    }

    public static OrderStatusLog build(String orderNo, OrderStatusEnum enums, String updateBy,
                                       List<String> images, String remark) {
        return build(orderNo, enums, updateBy, images, remark, null);
    }

    public static OrderStatusLog build(String orderNo, OrderStatusEnum enums, String updateBy) {
        return build(orderNo, enums, updateBy, null, null, null);
    }


    public static OrderStatusLogDTO convert(OrderStatusLog e, OrderDelivery delivery) {
        OrderStatusLogDTO o = OrderStatusLogDTO.builder()
                .id(e.getId())
                .orderNo(e.getOrderNo())
                .status(e.getStatus())
                .statusName(OrderStatusEnum.getByCode(e.getStatus()).getMsg())
                .remark(e.getRemark())
                .updateBy(e.getUpdateBy())
                .createTime(e.getCreateTime())
                .updateTime(e.getUpdateTime())
                .deliveryTime(OrderFactory.formatDate(e.getDeliveryTime()))
                .build();
        if (StringUtils.isNotBlank(e.getImages())) {
            o.setImages(JacksonSerializer.deSerialize(e.getImages(), new TypeReference<>() {
            }));
        }
        if (OrderStatusEnum.isDespatched(e.getStatus())) {
            o.setTrackingNo(delivery != null ? delivery.getTrackingNo() : null);
        }
        return o;
    }


}
