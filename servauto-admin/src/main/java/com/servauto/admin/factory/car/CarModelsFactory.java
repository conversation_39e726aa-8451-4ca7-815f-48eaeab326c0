package com.servauto.admin.factory.car;

import com.servauto.admin.model.dto.response.car.CarKeyValueDTO;
import com.servauto.admin.model.dto.response.car.CarModelsDTO;
import com.servauto.admin.model.entity.car.CarBrands;
import com.servauto.admin.model.entity.car.CarModels;

public class CarModelsFactory {
    public static CarModelsDTO convert(CarModels info) {
        return CarModelsDTO.builder()
                .id(info.getId())
                .brandId(info.getBrandId())
                .modelName(info.getModelName())
                .order(info.getOrder())
                .build();
    }

    public static CarKeyValueDTO convertKv(CarModels info) {
        return CarKeyValueDTO.builder()
                .id(info.getId())
                .value(info.getModelName())
                .order(info.getOrder())
                .build();
    }
}