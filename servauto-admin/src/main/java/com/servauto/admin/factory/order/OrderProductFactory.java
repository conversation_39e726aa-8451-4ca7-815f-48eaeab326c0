package com.servauto.admin.factory.order;


import com.fasterxml.jackson.core.type.TypeReference;
import com.servauto.admin.model.dto.response.order.OrderProductDTO;
import com.servauto.admin.model.entity.order.OrderProduct;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;

import java.util.List;

public class OrderProductFactory {

    public static OrderProductDTO convert(OrderProduct product) {
        return OrderProductDTO.builder()
                .orderNo(product.getOrderNo())
                .productId(product.getProductId())
                .productName(product.getProductName())
                .productAttribute(StringUtils.isNotBlank(product.getProductAttribute()) ? JacksonSerializer.deSerialize(product.getProductAttribute(), new TypeReference<List<Object>>() {
                }) : null)
                .brandId(product.getBrandId())
                .brandName(product.getBrandName())
                .categoryId(product.getCategoryId())
                .categoryName(product.getCategoryName())
                .image(product.getImage())
                .reservePrice(product.getReservePrice())
                .actualPrice(product.getActualPrice())
                .discountPrice(product.getDiscountPrice())
                .discountAmount(product.getDiscountAmount())
                .quantity(product.getQuantity())
                .build();
    }
}
