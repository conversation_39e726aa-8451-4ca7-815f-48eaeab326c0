package com.servauto.admin.factory.order;


import com.servauto.admin.model.dto.response.order.OrderPayDTO;
import com.servauto.admin.model.entity.order.OrderPay;

/**
 * <p>OrderPayFactory</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 16:56
 */
public class OrderPayFactory {

    public static OrderPayDTO convert(OrderPay orderPay) {
        return OrderPayDTO.builder()
                .orderNo(orderPay.getOrderNo())
                .payNo(orderPay.getPayNo())
                .customerId(orderPay.getCustomerId())
                .grandTotal(orderPay.getGrandTotal())
                .paidAmount(orderPay.getPaidAmount())
                .errorCode(orderPay.getErrorCode())
                .errorMsg(orderPay.getErrorMsg())
                .build();
    }
}
