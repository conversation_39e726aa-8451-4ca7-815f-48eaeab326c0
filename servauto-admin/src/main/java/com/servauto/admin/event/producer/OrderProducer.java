package com.servauto.admin.event.producer;

import com.servauto.admin.dao.order.OrderMapper;
import com.servauto.admin.event.dto.OrderEvent;
import com.servauto.admin.model.entity.order.Order;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <p>OrderProducer</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/28 16:09
 */
@Slf4j
@Component
public class OrderProducer {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private OrderMapper orderMapper;

    public void sendRescheduleEvent(String orderNo) {
        log.info("Send order reschedule message, orderNo: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        applicationContext.publishEvent(new OrderEvent.OrderRescheduleEvent<>(this, orderNo, order));
    }

    public void sendWorkshopRescheduleEvent(String orderNo) {
        log.info("Send order workshop-reschedule message, orderNo: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        applicationContext.publishEvent(new OrderEvent.OrderWorkshopRescheduleEvent<>(this, orderNo, order));
    }

}
