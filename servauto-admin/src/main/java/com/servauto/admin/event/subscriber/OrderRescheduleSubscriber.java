package com.servauto.admin.event.subscriber;


import com.servauto.admin.event.dto.OrderEvent;
import com.servauto.admin.model.entity.order.Order;
import com.servauto.admin.service.notice.NoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
public class OrderRescheduleSubscriber {

    @Resource
    private NoticeService noticeService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerHqOPSReschedulePushWhatsappSubscriber(OrderEvent.OrderRescheduleEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reschedule event orderNo {} {} {} push whatsapp message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onRescheduleSendWhatsapp(order);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerHqOPSReschedulePushCustomerWhatsappSubscriber(OrderEvent.OrderRescheduleEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order reschedule event orderNo {} {} {} push customer whatsapp message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onRescheduleSendWhatsappToCustomer(order);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION)
    public void handlerWorkshopReschedulePushWhatsappSubscriber(OrderEvent.OrderWorkshopRescheduleEvent<Order> event) {
        Order order = event.getT();
        log.info("receive order workshop-reschedule event orderNo {} {} {} push whatsapp message",
                event.getOrderNo(), order.getWorkshopName(), order.getReservationTime());
        noticeService.onWorkshopRescheduleSendLark(order);
    }


    
}
