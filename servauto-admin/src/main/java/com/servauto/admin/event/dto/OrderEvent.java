package com.servauto.admin.event.dto;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

public class OrderEvent {

    @Getter
    public static class AbstractOrderEvent<T> extends ApplicationEvent {

        public String orderNo;

        public T t;

        public AbstractOrderEvent(Object source, String orderNo, T t) {
            super(source);
            this.orderNo = orderNo;
            this.t = t;
        }
    }

    public static class OrderCanceledEvent<T> extends AbstractOrderEvent<T> {
        public OrderCanceledEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderRescheduleEvent<T> extends AbstractOrderEvent<T> {
        public OrderRescheduleEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }

    public static class OrderWorkshopRescheduleEvent<T> extends AbstractOrderEvent<T> {
        public OrderWorkshopRescheduleEvent(Object source, String orderNo, T t) {
            super(source, orderNo, t);
        }
    }


}
