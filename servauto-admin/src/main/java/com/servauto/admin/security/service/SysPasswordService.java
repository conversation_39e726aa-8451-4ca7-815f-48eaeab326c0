package com.servauto.admin.security.service;

import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.SecurityUtils;
import com.servauto.admin.security.context.AuthenticationContextHolder;
import com.servauto.common.constant.CacheConstants;
import com.servauto.common.exception.BusinessException;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Component
public class SysPasswordService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    private String getCacheKey(String username) {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    public void validate(SysUser user) {
        Authentication authentication = AuthenticationContextHolder.getContext();
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        int retryCount = 0;
        Object retryCountObj = redisTemplate.opsForValue().get(getCacheKey(username));
        if (Objects.nonNull(retryCountObj)) {
            retryCount = (Integer) retryCountObj;
        }

        if (retryCount >= maxRetryCount) {
            throw BusinessException.ofLocale("user.password.retry.limit.exceed", new Object[]{maxRetryCount, lockTime});
        }

        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            retryCount = retryCount + 1;
            redisTemplate.opsForValue().set(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            throw BusinessException.ofLocale("user.password.not.match");
        } else {
            clearLoginRecordCache(username);
        }
    }

    public void clearLoginRecordCache(String loginName) {

        if (redisTemplate.hasKey(getCacheKey(loginName))) {
            redisTemplate.delete(getCacheKey(loginName));
        }
    }
}
