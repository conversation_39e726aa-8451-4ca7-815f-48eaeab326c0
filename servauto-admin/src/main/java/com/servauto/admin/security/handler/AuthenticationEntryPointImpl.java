package com.servauto.admin.security.handler;

import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

@Slf4j
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable {

    @Serial
    private static final long serialVersionUID = -8970718410437077606L;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        String msg = StringUtils.format("Request access: {}, authentication failed, unable to access system resources", request.getRequestURI());
        log.warn(msg);
        ServletUtils.renderString(response, JacksonSerializer.serialize(CommonResult.error(ResponseCode.UNAUTHORIZED.getCode(), msg)));
    }
}
