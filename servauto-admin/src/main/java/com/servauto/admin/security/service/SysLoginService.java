package com.servauto.admin.security.service;

import com.servauto.admin.model.entity.system.SysUser;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.context.AuthenticationContextHolder;
import com.servauto.admin.service.system.SysConfigService;
import com.servauto.admin.service.system.SysUserService;
import com.servauto.common.constant.UserConstants;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import com.servauto.common.utils.DateUtils;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.ip.IpUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SysLoginService {

    @Resource
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysConfigService sysConfigService;

    public String login(String username, String password) {
        Authentication authentication;
        try {

            loginPreCheck(username, password);

            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw BusinessException.ofLocale("user.password.not.match");
            } else if (e instanceof InternalAuthenticationServiceException) {
                InternalAuthenticationServiceException iae = (InternalAuthenticationServiceException) e;
                if (iae.getCause() instanceof BusinessException businessException) {
                    if (businessException.isConvertI18n()) {
                        throw BusinessException.ofLocale(businessException.getCode(), businessException.getArgs());
                    } else {
                        throw BusinessException.of(businessException.getCode(), businessException.getMsg());
                    }
                } else {
                    throw BusinessException.of(ResponseCode.ERROR);
                }
            } else {
                throw BusinessException.of(StringUtils.isBlank(e.getMessage()) ? ResponseCode.ERROR.getMsg() : e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        return tokenService.createToken(loginUser);
    }

    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            throw BusinessException.ofLocale("user.not.exists");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw BusinessException.ofLocale("user.password.not.match");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw BusinessException.ofLocale("user.password.not.match");
        }
        // IP黑名单校验
        String blackStr = sysConfigService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            throw BusinessException.ofLocale("login.blocked");
        }
    }

    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUserService.updateUserProfile(sysUser);
    }
}
