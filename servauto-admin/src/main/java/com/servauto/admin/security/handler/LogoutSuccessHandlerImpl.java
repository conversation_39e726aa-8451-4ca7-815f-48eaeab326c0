package com.servauto.admin.security.handler;

import com.alibaba.fastjson2.JSON;
import com.servauto.admin.security.LoginUser;
import com.servauto.admin.security.service.TokenService;
import com.servauto.admin.service.system.SysOperaLogService;
import com.servauto.common.constant.Constants;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.utils.MessageUtils;
import com.servauto.framework.utils.ServletUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;


@Component
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Resource
    private TokenService tokenService;

    @Resource
    private SysOperaLogService sysOperaLogService;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            tokenService.delLoginUser(loginUser.getToken());
            sysOperaLogService.recordLoginInfo(request, loginUser.getUsername(), Constants.LOGOUT, MessageUtils.message("user.logout.success"));
        }
        ServletUtils.renderString(response, JSON.toJSONString(CommonResult.success(MessageUtils.message("user.logout.success"))));
    }
}
