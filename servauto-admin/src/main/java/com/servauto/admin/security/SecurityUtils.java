package com.servauto.admin.security;

import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.exception.BusinessException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Arrays;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    public static Long getUserId() {
        return getLoginUser().getUserId();
    }

    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw BusinessException.of("Exception in obtaining user information" + ResponseCode.UNAUTHORIZED.getMsg());
        }
    }

    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    public static void checkUserAdmin(Long userId) {
        if (isAdmin(userId)) {
            throw BusinessException.of("Super administrator role is not allowed");
        }
    }

    public static boolean isRoleAdmin(Long roleId) {
        return roleId != null && 1L == roleId;
    }

    public static boolean isLoginRoleAdmin() {
        Long[] roleIds = getLoginUser().getUser().getRoleIds();
        return roleIds != null && Arrays.stream(getLoginUser().getUser().getRoleIds()).anyMatch(SecurityUtils::isRoleAdmin);
    }

    public static void checkRoleAdmin(Long roleId) {
        if (isRoleAdmin(roleId)) {
            throw BusinessException.of("Super administrator role is not allowed");
        }
    }
}
