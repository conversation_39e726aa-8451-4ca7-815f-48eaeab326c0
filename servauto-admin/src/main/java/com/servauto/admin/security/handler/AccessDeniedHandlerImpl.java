package com.servauto.admin.security.handler;


import com.servauto.admin.security.SecurityUtils;
import com.servauto.common.core.domain.CommonResult;
import com.servauto.common.core.domain.ResponseCode;
import com.servauto.common.utils.StringUtils;
import com.servauto.framework.parse.JacksonSerializer;
import com.servauto.framework.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) {
        String msg = StringUtils.format("Request access: {}, Insufficient permissions for user {}", request.getRequestURI(), SecurityUtils.getUserId());
        log.warn(msg);
        ServletUtils.renderString(response, JacksonSerializer.serialize(CommonResult.of(ResponseCode.FORBIDDEN)));
    }

}
