package com.servauto.admin.model.dto.response.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Properties;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CacheInfoDTO {

    private Properties info;
    private Object dbSize;
    private List<Map<String, String>> commandStats;


}
