package com.servauto.admin.model.dto.request.system;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Data
public class LoginReqDTO {

    @Schema(description = "username", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    @NotBlank(message = "username cannot null")
    private String username;

    @Schema(description = "password", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    @NotBlank(message = "password cannot null")
    private String password;
}
