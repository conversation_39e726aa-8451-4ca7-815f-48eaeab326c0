package com.servauto.admin.model.dto.request.order;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Builder
@Data
public class PageOrderReqDTO {

    private String deliveryType;

    private List<Long> workShopIds;

    private String status;

    private List<String> statuses;

    private Date createBeginTime;

    private Date createEndTime;

    private String inStoreMobile;

    private String licensePlate;
}
