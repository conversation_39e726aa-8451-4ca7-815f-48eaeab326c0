package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Car Years Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarYearsDTO {

    @Schema(description = "Car Years ID", example = "1")
    private Long id;

    @Schema(description = "Model ID", example = "1")
    private Long modelId;

    @Schema(description = "Year Value", example = "2023.0")
    private String yearValue;

    @Schema(description = "Car Years Order", example = "1")
    private Integer order;

}