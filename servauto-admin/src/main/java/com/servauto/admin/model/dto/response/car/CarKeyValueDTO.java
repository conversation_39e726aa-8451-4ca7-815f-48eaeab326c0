package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Car Key Value Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarKeyValueDTO {

    @Schema(description = "Car Brand ID", example = "1")
    private Long id;

    @Schema(description = "Value", example = "Toyota")
    private String value;

    @Schema(description = "Car Brand Order", example = "1")
    private Integer order;

}