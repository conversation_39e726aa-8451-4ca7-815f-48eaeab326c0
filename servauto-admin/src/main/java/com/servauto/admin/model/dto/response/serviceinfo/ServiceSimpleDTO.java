package com.servauto.admin.model.dto.response.serviceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Service Info - Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceSimpleDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "supportedOn", example = "1")
    private String supportedOn;
}
