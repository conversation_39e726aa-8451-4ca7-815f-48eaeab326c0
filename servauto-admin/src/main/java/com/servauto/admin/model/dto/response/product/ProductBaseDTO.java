package com.servauto.admin.model.dto.response.product;

import com.servauto.admin.model.dto.response.system.TypeNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Schema(description = "Product - base Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductBaseDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "oil")
    private String name;

    @Schema(description = "categoryId", example = "1")
    private Long categoryId;

    @Schema(description = "categoryName", example = "categoryName")
    private String categoryName;

    @Schema(description = "brandId", example = "1")
    private Long brandId;

    @Schema(description = "brandName", example = "brandName")
    private String brandName;

    @Schema(description = "status, 1: Listed, 2: Unlisted", example = "1")
    private Integer status;

    @Schema(description = "statusName", example = "statusName")
    private String statusName;

    @Schema(description = "serviceId", example = "1")
    private Long serviceId;

    @Schema(description = "serviceName", example = "serviceName")
    private String serviceName;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "price", example = "10000.00")
    private BigDecimal price;

    @Schema(description = "featuredTags", example = "[{}]")
    private List<String> featuredTags;

    @Schema(description = "deliveryModes", example = "[1]")
    private List<TypeNameDTO> deliveryModes;

    @Schema(description = "attributeValueMap", example = "{id: []}")
    private HashMap<Long, List<String>> attributeValueMap;

    @Schema(description = "updatedBy", example = "1")
    private Long updatedBy;

    @Schema(description = "updaterName", example = "updaterName")
    private String updaterName;

    @Schema(description = "createTime", example = "1")
    private Date createTime;

    @Schema(description = "updateTime", example = "1")
    private Date updateTime;
}
