package com.servauto.admin.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Order - reschedule order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkshopRescheduleReqDTO {

    @Schema(hidden = true)
    private String orderNo;

    @NotNull(message = "reservation time cannot be null")
    @Schema(description = "images", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long reservationTime;

}
