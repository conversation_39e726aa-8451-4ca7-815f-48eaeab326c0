package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Car Extra Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarExtraDTO {

    @Schema(description = "Car Extra ID", example = "1")
    private Long id;

    @Schema(description = "Car Library ID", example = "CL001")
    private String carLibId;

    @Schema(description = "Variant ID", example = "1")
    private Long variantId;

    @Schema(description = "Displacement Value", example = "1800")
    private String displacementValue;

    @Schema(description = "TransmissionType Value", example = "8AT")
    private String transmissionType;

    @Schema(description = "VariantName Value", example = "TFSI")
    private String variantName;

    @Schema(description = "Car Type", example = "Sedan")
    private String carType;

    @Schema(description = "Tire Front", example = "225/45 R18")
    private String tireFront;

    @Schema(description = "Tire Rear", example = "225/45 R18")
    private String tireRear;

    @Schema(description = "Car Extra Status", example = "1")
    private Integer status;

    @Schema(description = "Car Extra Order", example = "1")
    private Integer order;

}