package com.servauto.admin.model.dto.response.product;

import com.servauto.admin.model.dto.response.system.TypeNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Product - detail Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "oil")
    private String name;

    @Schema(description = "categoryId", example = "1")
    private Long categoryId;

    @Schema(description = "categoryName", example = "categoryName")
    private String categoryName;

    @Schema(description = "brandId", example = "1")
    private Long brandId;

    @Schema(description = "brandName", example = "brandName")
    private String brandName;

    @Schema(description = "status, 1: Listed, 2: Unlisted", example = "1")
    private String status;

    @Schema(description = "statusName", example = "statusName")
    private String statusName;

    @Schema(description = "description", example = "oil")
    private String description;

    @Schema(description = "serviceId", example = "1")
    private Long serviceId;

    @Schema(description = "serviceName", example = "serviceName")
    private String serviceName;

    @Schema(description = "netContent", example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", example = "L")
    private String contentUnit;

    @Schema(description = "price", example = "10000.00")
    private BigDecimal price;

    @Schema(description = "mainImage", example = "https://www.toyota.com.cn/")
    private String mainImage;

    @Schema(description = "featuredTags", example = "[{}]")
    private List<String> featuredTags;

    @Schema(description = "deliveryModes", example = "[1]")
    private List<String> deliveryModes;

    @Schema(description = "attributeValues", example = "[{attributeId: 1, attributeName: name, value: value, order: 1. isRequired: true}]")
    private List<ProductAttributeValueDTO> attributeValues;

    @Schema(description = "coverImages", example = "[https://www.toyota.com.cn]")
    private List<String> coverImages;

    @Schema(description = "h5DetailImages", example = "[https://www.toyota.com.cn]")
    private List<String> h5DetailImages;

    @Schema(description = "isAssociatePackage", example = "true")
    private boolean isAssociatePackage;

}
