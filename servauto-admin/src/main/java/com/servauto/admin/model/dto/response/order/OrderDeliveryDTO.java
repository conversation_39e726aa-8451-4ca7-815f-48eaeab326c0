package com.servauto.admin.model.dto.response.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>OrderDeliveryDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/8 10:41
 */
@Schema(description = "Order - order delivery response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDeliveryDTO {

    @Schema(description = "order no")
    private String orderNo;

    @Schema(description = "consignee customer name")
    private String name;

    @Schema(description = "consignee customer mobile")
    private String mobile;

    @Schema(description = "consignee state code")
    private String stateCode;

    @Schema(description = "consignee state name")
    private String stateName;

    @Schema(description = "consignee city code")
    private String cityCode;

    @Schema(description = "consignee city name")
    private String cityName;

    @Schema(description = "consignee address")
    private String address;

}
