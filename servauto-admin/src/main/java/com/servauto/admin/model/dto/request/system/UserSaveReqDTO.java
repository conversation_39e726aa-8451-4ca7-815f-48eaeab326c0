package com.servauto.admin.model.dto.request.system;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "SYS - Create User Request VO")
@Data
public class UserSaveReqDTO {

    @Schema(description = "user name", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton")
    @NotBlank(message = "user name cannot null")
    private String userName;

    @Schema(description = "user emil", example = "<EMAIL>")
    private String email;

    @Schema(description = "phone number", example = "***********")
    private String mobile;

    @Schema(description = "sex", example = "1")
    private Integer sex;

    @Schema(description = "password", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String password;

    @Schema(description = "remark", example = "test")
    private String remark;

    @Schema(description = "role", example = "1")
    private Long[] roleIds;

    @Schema(description = "account status", example = "0/1")
    private String status;
}
