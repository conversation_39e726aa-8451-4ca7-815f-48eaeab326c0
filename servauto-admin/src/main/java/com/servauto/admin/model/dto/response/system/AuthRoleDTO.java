package com.servauto.admin.model.dto.response.system;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.servauto.admin.model.entity.system.SysRole;
import com.servauto.admin.model.entity.system.SysUser;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthRoleDTO {

    private SysUser user;

    private List<SysRole> roles;

}
