package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Car Models Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarModelsDTO {

    @Schema(description = "Car Models ID", example = "1")
    private Long id;

    @Schema(description = "Brand ID", example = "1")
    private Long brandId;

    @Schema(description = "Model Name", example = "Model A")
    private String modelName;

    @Schema(description = "Car Models Order", example = "1")
    private Integer order;
}