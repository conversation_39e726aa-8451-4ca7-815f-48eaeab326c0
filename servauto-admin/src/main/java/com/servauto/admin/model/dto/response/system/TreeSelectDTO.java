package com.servauto.admin.model.dto.response.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.servauto.admin.model.entity.system.SysMenu;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TreeSelect 树结构实体类
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class TreeSelectDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelectDTO> children;

    public TreeSelectDTO() {

    }

    public TreeSelectDTO(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelectDTO::new).collect(Collectors.toList());
    }

}
