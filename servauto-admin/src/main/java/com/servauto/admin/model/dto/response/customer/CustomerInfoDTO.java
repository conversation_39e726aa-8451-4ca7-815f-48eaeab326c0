package com.servauto.admin.model.dto.response.customer;

import com.servauto.admin.model.dto.response.car.CarInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Schema(description = "Customer - list/detail Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerInfoDTO {

    @Schema(description = "customerId", example = "1024")
    private Long customerId;

    @Schema(description = "realName", example = "realName")
    private String realName;

    @Schema(description = "mobile", example = "mobile")
    private String mobile;

    @Schema(description = "gender", example = "0")
    private String gender;

    @Schema(description = "genderName", example = "genderName")
    private String genderName;

    @Schema(description = "birthday", example = "123456789000")
    private Date birthday;

    @Schema(description = "addresses", example = "customer address list")
    List<CustomerAddressDTO> addresses;

    @Schema(description = "carInfos", example = "customer carInfo list")
    List<CarInfoDTO> carInfos;
}
