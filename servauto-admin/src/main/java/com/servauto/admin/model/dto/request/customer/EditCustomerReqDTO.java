package com.servauto.admin.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Customer - edit Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EditCustomerReqDTO {

    @Schema(description = "customerId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long customerId;

    @Schema(description = "realName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "realName")
    private String realName;

    @Schema(description = "gender", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0/1")
    private String gender;

    @Schema(description = "birthday", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456789")
    private Long birthday;

    @Schema(description = "addresses", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "addresses")
    List<EditCustomerAddressReqDTO> addresses;
    
    @Schema(description = "carInfo", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "carInfo")
    List<EditCustomerCarInfoReqDTO> carInfo;
}
