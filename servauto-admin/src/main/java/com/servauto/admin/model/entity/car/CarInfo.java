package com.servauto.admin.model.entity.car;

import java.util.Date;

public class CarInfo {
    private Long id;

    private String carLibId;

    private Long customerId;

    private Byte carSource;

    private Long brandId;

    private Long modelId;

    private Long yearId;

    private Long variantId;

    private Integer carTypeId;

    private String carVariant;

    private String carEngine;

    private Long carMileage;

    private Integer carTransmission;

    private Date registrationDate;

    private Integer registrationType;

    private Integer seat;

    private Integer srcSeat;

    private Integer color;

    private Integer fuelType;

    private String vinCode;

    private String engineNo;

    private Boolean carType;

    private Byte serviceBook;

    private String licensePlate;

    private String image;

    private String source;

    private String location;

    private String locationAddress;

    private String locationId;

    private Date nextMaintenanceDate;

    private Byte status;

    private Date updateTime;

    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCarLibId() {
        return carLibId;
    }

    public void setCarLibId(String carLibId) {
        this.carLibId = carLibId == null ? null : carLibId.trim();
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Byte getCarSource() {
        return carSource;
    }

    public void setCarSource(Byte carSource) {
        this.carSource = carSource;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public Long getYearId() {
        return yearId;
    }

    public void setYearId(Long yearId) {
        this.yearId = yearId;
    }

    public Long getVariantId() {
        return variantId;
    }

    public void setVariantId(Long variantId) {
        this.variantId = variantId;
    }

    public Integer getCarTypeId() {
        return carTypeId;
    }

    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }

    public String getCarVariant() {
        return carVariant;
    }

    public void setCarVariant(String carVariant) {
        this.carVariant = carVariant == null ? null : carVariant.trim();
    }

    public String getCarEngine() {
        return carEngine;
    }

    public void setCarEngine(String carEngine) {
        this.carEngine = carEngine == null ? null : carEngine.trim();
    }

    public Long getCarMileage() {
        return carMileage;
    }

    public void setCarMileage(Long carMileage) {
        this.carMileage = carMileage;
    }

    public Integer getCarTransmission() {
        return carTransmission;
    }

    public void setCarTransmission(Integer carTransmission) {
        this.carTransmission = carTransmission;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public Integer getRegistrationType() {
        return registrationType;
    }

    public void setRegistrationType(Integer registrationType) {
        this.registrationType = registrationType;
    }

    public Integer getSeat() {
        return seat;
    }

    public void setSeat(Integer seat) {
        this.seat = seat;
    }

    public Integer getSrcSeat() {
        return srcSeat;
    }

    public void setSrcSeat(Integer srcSeat) {
        this.srcSeat = srcSeat;
    }

    public Integer getColor() {
        return color;
    }

    public void setColor(Integer color) {
        this.color = color;
    }

    public Integer getFuelType() {
        return fuelType;
    }

    public void setFuelType(Integer fuelType) {
        this.fuelType = fuelType;
    }

    public String getVinCode() {
        return vinCode;
    }

    public void setVinCode(String vinCode) {
        this.vinCode = vinCode == null ? null : vinCode.trim();
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo == null ? null : engineNo.trim();
    }

    public Boolean getCarType() {
        return carType;
    }

    public void setCarType(Boolean carType) {
        this.carType = carType;
    }

    public Byte getServiceBook() {
        return serviceBook;
    }

    public void setServiceBook(Byte serviceBook) {
        this.serviceBook = serviceBook;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image == null ? null : image.trim();
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public String getLocationAddress() {
        return locationAddress;
    }

    public void setLocationAddress(String locationAddress) {
        this.locationAddress = locationAddress == null ? null : locationAddress.trim();
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId == null ? null : locationId.trim();
    }

    public Date getNextMaintenanceDate() {
        return nextMaintenanceDate;
    }

    public void setNextMaintenanceDate(Date nextMaintenanceDate) {
        this.nextMaintenanceDate = nextMaintenanceDate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}