package com.servauto.admin.model.dto.request.workshop;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Workshop - edit/insert Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateWorkshopDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    private String name;

    @Schema(description = "type", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "stateCode", requiredMode = Schema.RequiredMode.REQUIRED, example = "stateCode")
    private String stateCode;

    @Schema(description = "cityCode", requiredMode = Schema.RequiredMode.REQUIRED, example = "cityCode")
    private String cityCode;

    @Schema(description = "address", requiredMode = Schema.RequiredMode.REQUIRED, example = "address")
    private String address;

    @Schema(description = "contactNumber", requiredMode = Schema.RequiredMode.REQUIRED, example = "1234")
    private String contactNumber;

    @Schema(description = "operatorId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long operatorId;

    @Schema(description = "remark", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "remark")
    private String remark;

    @Schema(description = "logo", requiredMode = Schema.RequiredMode.REQUIRED, example = "logo")
    private String logo;

    @Schema(description = "latitude", requiredMode = Schema.RequiredMode.REQUIRED, example = "1123.123")
    private BigDecimal latitude;

    @Schema(description = "longitude", requiredMode = Schema.RequiredMode.REQUIRED, example = "234.5675")
    private BigDecimal longitude;

    @Schema(description = "locationUrl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.google.com/maps/place/1")
    private String locationUrl;

    @Schema(description = "photo", requiredMode = Schema.RequiredMode.REQUIRED, example = "photo1,photo2,photo3")
    private List<String> photo;

    @Schema(description = "featuredTags", requiredMode = Schema.RequiredMode.REQUIRED, example = "featuredTags")
    private List<String> featuredTags;

    @Schema(description = "serviceTimeList", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "serviceTimeList")
    private List<WorkshopServiceTimeDTO> serviceTimeList;

    @Schema(description = "whatsAppNumber", requiredMode = Schema.RequiredMode.REQUIRED, example = "phone1;phone2;phone3;")
    private String  whatsAppNumber;
}
