package com.servauto.admin.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Order - pickup order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PickupOrderReqDTO {

    @Schema(hidden = true)
    private String orderNo;

    @Schema(description = "pickupCode", requiredMode = Schema.RequiredMode.REQUIRED, example = "pickup code")
    private String pickupCode;

    @Schema(description = "images", requiredMode = Schema.RequiredMode.REQUIRED, example = "[https://,https://]")
    private List<String> images;

    @Schema(description = "remark", requiredMode = Schema.RequiredMode.REQUIRED, example = "pickup remark")
    private String remark;
}
