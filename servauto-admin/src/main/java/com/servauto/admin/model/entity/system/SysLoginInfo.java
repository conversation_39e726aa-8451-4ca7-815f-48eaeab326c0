package com.servauto.admin.model.entity.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.servauto.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 系统访问记录表 sys_logininfor
 *
 * <AUTHOR>
 */

public class SysLoginInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long infoId;

    /**
     * 用户账号
     */
    private String userName;

    public Long getInfoId() {
        return infoId;
    }

    public String getUserName() {
        return userName;
    }

    public String getStatus() {
        return status;
    }

    public String getIpaddr() {
        return ipaddr;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public String getBrowser() {
        return browser;
    }

    public String getOs() {
        return os;
    }

    public String getMsg() {
        return msg;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    /**
     * 登录状态 0成功 1失败
     */
    private String status;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 提示消息
     */
    private String msg;

    /**
     * 访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }
}
