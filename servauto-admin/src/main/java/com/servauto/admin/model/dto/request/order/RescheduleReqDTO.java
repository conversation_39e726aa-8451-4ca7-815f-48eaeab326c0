package com.servauto.admin.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Order - reschedule order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RescheduleReqDTO {

    @Schema(hidden = true)
    private String orderNo;

    @NotNull(message = "workshop id cannot be null")
    @Schema(description = "workshop id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    private Long workshopId;

    @NotNull(message = "reservation time cannot be null")
    @Schema(description = "images", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long reservationTime;

}
