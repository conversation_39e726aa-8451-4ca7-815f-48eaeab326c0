package com.servauto.admin.model.entity.payment;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class Payment {
    private Long id;

    private String payNo;

    private String sourceId;

    private int channel;

    private int payMethod;

    private BigDecimal amount;

    private String currency;

    private Date expiresAt;

    private Date payTime;

    private Date refundTime;

    private String payType;

    private String url;

    private String status;

    private Date createTime;

    private Date updateTime;

}