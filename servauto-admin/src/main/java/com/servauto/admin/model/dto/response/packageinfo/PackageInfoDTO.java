package com.servauto.admin.model.dto.response.packageinfo;

import com.servauto.admin.model.dto.response.system.IdNameDTO;
import com.servauto.admin.model.dto.response.system.TypeNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Package Info - Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageInfoDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "status", example = "1")
    private String status;

    @Schema(description = "statusName", example = "statusName")
    private String statusName;

    @Schema(description = "price", example = "100.00")
    private BigDecimal price;

    @Schema(description = "serviceId", example = "1")
    private Long serviceId;

    @Schema(description = "serviceName", example = "serviceName")
    private String serviceName;

    @Schema(description = "productIds", example = "[1]")
    private List<Long> productIds;

    @Schema(description = "deliveryModes", example = "[1]")
    private List<String> deliveryModes;

    @Schema(description = "updatedBy", example = "1")
    private Long updatedBy;

    @Schema(description = "updaterName", example = "updaterName")
    private String updaterName;

    @Schema(description = "createTime", example = "1")
    private Date createTime;

    @Schema(description = "updateTime", example = "1")
    private Date updateTime;
}
