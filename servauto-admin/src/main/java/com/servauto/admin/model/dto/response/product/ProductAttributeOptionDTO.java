package com.servauto.admin.model.dto.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Product Attribute Option Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductAttributeOptionDTO {
    @Schema(description = "value", example = "value")
    private String value;

    @Schema(description = "label", example = "label")
    private String label;

    @Schema(description = "order", example = "1")
    private Integer order;
}
