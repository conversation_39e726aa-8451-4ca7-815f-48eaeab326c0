package com.servauto.admin.model.dto.response.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>OrderPayDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 16:48
 */

@Schema(description = "Order - order pay response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderPayDTO {

    @Schema(description = "order number", example = "1")
    private String orderNo;

    @Schema(description = "order payNo", example = "1")
    private String payNo;

    @Schema(description = "customer id", example = "1")
    private Long customerId;

    @Schema(description = "order grand total", example = "1")
    private BigDecimal grandTotal;

    @Schema(description = "paid amount", example = "1")
    private BigDecimal paidAmount;

    @Schema(description = "error code", example = "1")
    private String errorCode;

    @Schema(description = "error message", example = "1")
    private String errorMsg;
    
    @Schema(description = "pay method", example = "1")
    private String payMethod;

    @Schema(description = "paid time", example = "1")
    private Date paidTime;

    @Schema(description = "canceled time", example = "1")
    private Date canceledTime;
}
