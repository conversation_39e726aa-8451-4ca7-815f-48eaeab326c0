package com.servauto.admin.model.dto.request.system;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "SYS - Role DataPermission Request VO")
@Data
public class DataPermissionReqDTO {

    @Schema(description = "role id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotBlank(message = "role id cannot null")
    private Long roleId;

    @Schema(description = "workshop ids", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "workshop ids cannot null")
    private List<Long> workshopIds;
}
