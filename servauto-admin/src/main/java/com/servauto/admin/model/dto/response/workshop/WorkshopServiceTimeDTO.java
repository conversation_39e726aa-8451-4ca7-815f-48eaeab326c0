package com.servauto.admin.model.dto.response.workshop;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description = "Workshop - service time Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkshopServiceTimeDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "day, 0 1 2 3 4 5 6", example = "0")
    private Integer day;

    @Schema(description = "startTime", example = "1735664461000")
    private Date startTime;

    @Schema(description = "endTime", example = "1735664461000")
    private Date endTime;
}
