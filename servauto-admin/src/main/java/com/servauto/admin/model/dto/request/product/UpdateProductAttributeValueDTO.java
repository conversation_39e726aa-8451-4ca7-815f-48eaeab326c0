package com.servauto.admin.model.dto.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product Attribute Value Request V0")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateProductAttributeValueDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "values", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    private List<String> values;
}
