package com.servauto.admin.model.dto.response.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.servauto.admin.model.entity.system.SysUser;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;


@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserInfoDTO {
    private SysUser user;
    private Set<String> roles;
    private Set<String> permissions;
    private List<IdNameDTO> workshopIds;
}
