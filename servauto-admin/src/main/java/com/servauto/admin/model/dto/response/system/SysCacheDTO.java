package com.servauto.admin.model.dto.response.system;

import com.servauto.common.utils.StringUtils;
import lombok.Data;

/**
 * 缓存信息
 *
 * <AUTHOR>
 */
@Data
public class SysCacheDTO {

    /**
     * 缓存名称
     */
    private String cacheName = "";

    /**
     * 缓存键名
     */
    private String cacheKey = "";

    /**
     * 缓存内容
     */
    private String cacheValue = "";

    /**
     * 备注
     */
    private String remark = "";

    public SysCacheDTO() {

    }

    public SysCacheDTO(String cacheName, String remark) {
        this.cacheName = cacheName;
        this.remark = remark;
    }

    public SysCacheDTO(String cacheName, String cacheKey, String cacheValue) {
        this.cacheName = StringUtils.replace(cacheName, ":", "");
        this.cacheKey = StringUtils.replace(cacheKey, cacheName, "");
        this.cacheValue = cacheValue;
    }

}
