package com.servauto.admin.model.dto.response.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>OrderWorkshopDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/24 14:49
 */
@Schema(description = "Order - order workshops response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderWorkshopDTO {

    @Schema(description = "workshop id", example = "1")
    private Long id;

    @Schema(description = "workshop name", example = "name")
    private String name;

    @Schema(description = "stateCode", example = "stateCode")
    private String stateCode;

    @Schema(description = "stateName", example = "stateName")
    private String stateName;

    @Schema(description = "cityCode", example = "cityCode")
    private String cityCode;

    @Schema(description = "cityName", example = "cityName")
    private String cityName;

    @Schema(description = "address", example = "address")
    private String address;

    @Schema(description = "contactNumber", example = "contactNumber")
    private String contactNumber;

    @Schema(description = "logo", example = "logo")
    private String logo;

    @Schema(description = "latitude", example = "latitude")
    private BigDecimal latitude;

    @Schema(description = "longitude", example = "longitude")
    private BigDecimal longitude;

    @Schema(description = "locationUrl", example = "locationUrl")
    private String locationUrl;

    @Schema(description = "serviceTime", example = "1")
    private List<ServiceTimeDTO> serviceTimes;

    public record ServiceTimeDTO(Date date, List<DayOfTimeDTO> times) {

        @Override
        public String toString() {
            return "ServiceTimeDTO{" + "date=" + date + ", times=" + times + '}';
        }
    }

    public record DayOfTimeDTO(boolean visible, String time) {

        @Override
        public String toString() {
            return "DayOfTimeDTO{" + "visible=" + visible + ", time='" + time + '\'' + '}';
        }
    }
}
