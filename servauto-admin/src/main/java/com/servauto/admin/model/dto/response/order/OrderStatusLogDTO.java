package com.servauto.admin.model.dto.response.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <p>OrderStatusLogDTO</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 15:10
 */
@Schema(description = "Order - order status response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderStatusLogDTO {

    @Schema(description = "LOG Id", example = "")
    private Long id;

    @Schema(description = "LOG orderNo", example = "")
    private String orderNo;

    @Schema(description = "LOG status", example = "")
    private String status;

    @Schema(description = "LOG statusName", example = "")
    private String statusName;

    @Schema(description = "LOG images", example = "")
    private List<String> images;

    @Schema(description = "LOG remark", example = "")
    private String remark;

    @Schema(description = "LOG updateBy user", example = "")
    private String updateBy;

    @Schema(description = "LOG deliveryTime", example = "")
    private Date deliveryTime;

    @Schema(description = "LOG createTime", example = "")
    private Date createTime;

    @Schema(description = "LOG updateTime", example = "")
    private Date updateTime;

    @Schema(description = "LOG trackingNo", example = "")
    private String trackingNo;
}
