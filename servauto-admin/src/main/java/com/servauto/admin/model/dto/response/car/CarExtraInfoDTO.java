package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Car Extra Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarExtraInfoDTO {

    @Schema(description = "Car Extra ID", example = "1")
    private Integer id;

    @Schema(description = "Car Variant", example = "Sedan")
    private String variant;

    @Schema(description = "Car Extra Order", example = "1")
    private Integer order;

}