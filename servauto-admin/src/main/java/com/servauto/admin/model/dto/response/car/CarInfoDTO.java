package com.servauto.admin.model.dto.response.car;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Car Info Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarInfoDTO {
    @Schema(description = "carId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long carId;

    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long brandId;

    @Schema(description = "modelId", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long modelId;

    @Schema(description = "yearId", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Long yearId;

    @Schema(description = "variantId", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    private Long variantId;

    @Schema(description = "license Plate", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "XA1024")
    private String licensePlate;

    @Schema(description = "carMileage", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "25")
    private Long carMileage;

    @Schema(description = "carName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "25")
    private String carName;

}