package com.servauto.admin.model.dto.request.serviceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Service Info - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryServicesDTO {
    @Schema(description = "name, fuzzy query", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "supportedOn", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer supportedOn;

    @Schema(description = "workshopId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long workshopId;

    @Schema(description = "ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> ids;

    @Schema(description = "isNeedUpdaterName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "false")
    private boolean isNeedUpdaterName;
}
