package com.servauto.admin.model.dto.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryProductsDTO {
    @Schema(description = "pageNo", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @NotBlank(message = "pageNo")
    private Integer pageNo;

    @Schema(description = "pageSize", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "20")
    @NotBlank(message = "pageSize")
    private Integer pageSize;

    @Schema(description = "name, fuzzy query", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "name")
    private String name;

    @Schema(description = "ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> ids;

    @Schema(description = "categoryId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long categoryId;

    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long brandId;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long serviceId;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "categoryIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> categoryIds;

    @Schema(description = "brandIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> brandIds;

    @Schema(description = "isNeedAttributeValue", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "true")
    private Boolean isNeedAttributeValue;

    @Schema(description = "isNeedUpdaterName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "true")
    private Boolean isNeedUpdaterName;
}
