package com.servauto.admin.model.dto.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product - update/insert Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateProductDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED, example = "oil")
    private String name;

    @Schema(description = "categoryId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long categoryId;

    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long brandId;

    @Schema(description = "status, 1: Listed, 2: Unlisted", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "description", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "oil")
    private String description;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long serviceId;

    @Schema(description = "netContent", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer netContent;

    @Schema(description = "contentUnit", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "L")
    private String contentUnit;

    @Schema(description = "price", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000.00")
    private String price;

    @Schema(description = "mainImage", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.toyota.com.cn/")
    private String mainImage;

    @Schema(description = "featuredTags", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1]")
    private List<String> featuredTags;

    @Schema(description = "coverImages", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[https://www.toyota.com.cn]")
    private List<String> coverImages;

    @Schema(description = "h5DetailImages", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[https://www.toyota.com.cn]")
    private List<String> h5DetailImages;

    @Schema(description = "deliveryModes", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Integer> deliveryModes;

    @Schema(description = "attributeValues", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[{id: 1, value: value}]")
    private List<UpdateProductAttributeValueDTO> attributeValues;

    @Schema(description = "updatedBy", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long updatedBy;
}
