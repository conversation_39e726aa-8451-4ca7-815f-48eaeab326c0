package com.servauto.admin.model.dto.request.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "SYS - Query Role Request VO")
@Data
public class QueryRoleReqDTO {

    @Schema(hidden = true)
    private List<Long> roleIds;

    @Schema(description = "role name", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton")
    private String roleName;

}
