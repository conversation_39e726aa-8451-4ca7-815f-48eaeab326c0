package com.servauto.admin.model.dto.response.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "Payment - list/detail Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentDTO {

    @Schema(description = "Payment Number", example = "123456789000")
    private String payNo;

    @Schema(description = "Source ID", example = "0987654321")
    private String sourceId;

    @Schema(description = "Channel", example = "0987654321")
    private int channel;

    @Schema(description = "Whether a specific payment method is used", example = "true")
    private int isPayMethod;

    @Schema(description = "Payment Amount", example = "100.00")
    private BigDecimal amount;

    @Schema(description = "Currency Type", example = "CNY")
    private String currency;

    @Schema(description = "Expiration Time", example = "2023-10-01T12:00:00")
    private Date expiresAt;

    @Schema(description = "Payment Time", example = "2023-10-01T12:00:00")
    private Date payTime;

    @Schema(description = "Refund Time", example = "2023-10-01T12:00:00")
    private Date refundTime;

    @Schema(description = "Payment Type", example = "ONLINE")
    private String payType;

    @Schema(description = "Payment URL", example = "https://example.com/payment")
    private String url;

    @Schema(description = "Payment Status", example = "SUCCESS")
    private String status;

    @Schema(description = "Creation Time", example = "2023-10-01T12:00:00")
    private Date createTime;

    @Schema(description = "Update Time", example = "2023-10-01T12:00:00")
    private Date updateTime;
}
