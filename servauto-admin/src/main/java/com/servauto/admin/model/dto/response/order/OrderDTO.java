package com.servauto.admin.model.dto.response.order;

import com.servauto.admin.model.entity.order.OrderStatusLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Order - order response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDTO {

    @Schema(description = "orderNo", example = "123123123123123")
    private String orderNo;

    @Schema(description = "license plate", example = "")
    private String licensePlate;

    @Schema(description = "car info", example = "")
    private String carInfo;

    @Schema(description = "order status", example = "")
    private String status;

    @Schema(description = "order status name", example = "")
    private String statusName;

    @Schema(description = "customer id = create order customer", example = "")
    private Long customerId;

    @Schema(description = "customer name  = create order customer", example = "")
    private String customerName;

    @Schema(description = "contact no.  = create order customer", example = "")
    private String customerMobile;

    @Schema(description = "in-store customer name  = actual in-store customer name", example = "")
    private String inStoreName;

    @Schema(description = "in-store customer contact no. = actual in-store customer no", example = "")
    private String inStoreMobile;

    @Schema(description = "creator name", example = "")
    private String creatorName;

    @Schema(description = "create order time", example = "123123123123")
    private Date orderTime;

    @Schema(description = "create order time", example = "123123123123")
    private Date createTime;

    @Schema(description = "is fixed price", example = "true")
    private Boolean forceFixedPrice;

    @Schema(description = "order original amount", example = "120")
    private BigDecimal originalAmount;

    @Schema(description = "order discount amount", example = "25")
    private BigDecimal discountAmount;

    @Schema(description = "order subtotal = Total price of products", example = "95")
    private BigDecimal subtotal;

    @Schema(description = "order shipping fee", example = "10")
    private BigDecimal shippingFee;

    @Schema(description = "order grand total = Actual total price paid", example = "105")
    private BigDecimal grandTotal;

    @Schema(description = "order type", example = "todo")
    private String type;

    @Schema(description = "order product package id", example = "")
    private Long packageId;

    @Schema(description = "order product package name", example = "")
    private String packageName;

    @Schema(description = "service id", example = "")
    private Long serviceId;

    @Schema(description = "service name", example = "")
    private String serviceName;

    @Schema(description = "service Hour", example = "")
    private Integer serviceHour;

    @Schema(description = "service fee", example = "")
    private BigDecimal serviceFee;

    @Schema(description = "workshop id", example = "")
    private Long workshopId;

    @Schema(description = "workshop name", example = "")
    private String workshopName;

    @Schema(description = "delivery type", example = "OrderDeliveryTypeEnum")
    private String deliveryType;

    @Schema(description = "coupon amount", example = "")
    private BigDecimal couponAmount;

    @Schema(description = "reservation time", example = "")
    private Date reservationTime;

    @Schema(description = "paid time", example = "")
    private Date paidTime;

    @Schema(description = "order finished time", example = "")
    private Date finishedTime;

    @Schema(description = "order refunded time", example = "")
    private Date refundedTime;

    @Schema(description = "update order time", example = "")
    private Date updateTime;

    @Schema(description = "order remark", example = "")
    private String remark;

    @Schema(description = "updater name", example = "")
    private String updaterName;

    @Schema(description = "order product details", example = "[]")
    List<OrderProductDTO> products;

    @Schema(description = "order delivery", example = "")
    private OrderDeliveryDTO delivery;

    @Schema(description = "order status logs", example = "")
    private List<OrderStatusLogDTO> orderStatusLogs;

    @Schema(description = "order pay", example = "")
    private OrderPayDTO orderPay;

    @Schema(description = "workshop feedback name", example = "")
    private String suggestionName;

    @Schema(description = "workshop feedback time", example = "")
    private Date suggestionTime;
}
