package com.servauto.admin.model.dto.request.serviceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Service Info - edit/insert Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateServiceInfoDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    private String name;

    @Schema(description = "hours", requiredMode = Schema.RequiredMode.REQUIRED, example = "hours")
    private Integer hours;

    @Schema(description = "fee", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private String fee;

    @Schema(description = "supportedOn", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer supportedOn;

    @Schema(description = "isRequired", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer isRequired;

    @Schema(description = "workshopIds", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1]")
    private List<Long> workshopIds;

    @Schema(description = "productIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> productIds;

    @Schema(description = "packageIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Long> packageIds;

    @Schema(description = "updatedBy", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long updatedBy;
}
