package com.servauto.admin.model.dto.response.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - address Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerAddressDTO {

    @Schema(description = "customerId", example = "1024")
    private Long customerId;

    @Schema(description = "shipping name", example = "name")
    private String name;

    @Schema(description = "shipping mobile", example = "mobile")
    private String mobile;

    @Schema(description = "shipping state code", example = "state code")
    private String stateCode;

    @Schema(description = "shipping state name", example = "state name")
    private String stateName;

    @Schema(description = "shipping city code", example = "city code")
    private String cityCode;

    @Schema(description = "shipping city name", example = "city name")
    private String cityName;

    @Schema(description = "shipping address", example = "state name + city name + address")
    private String shippingAddress;

    @Schema(description = "shipping address", example = "address")
    private String address;

    @Schema(description = "is default address", example = "true/false")
    private Boolean isDefault;
}
