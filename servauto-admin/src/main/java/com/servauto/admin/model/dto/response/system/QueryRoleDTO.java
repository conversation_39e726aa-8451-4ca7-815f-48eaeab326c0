package com.servauto.admin.model.dto.response.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Schema(description = "SYS - Query Role Response VO")
@Data
@Builder
public class QueryRoleDTO {

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限
     */
    private String roleKey;


    private List<Long> workshopIds;
}
