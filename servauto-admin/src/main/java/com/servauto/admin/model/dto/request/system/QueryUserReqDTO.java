package com.servauto.admin.model.dto.request.system;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Schema(description = "SYS - Query User Request VO")
@Data
public class QueryUserReqDTO {

    @Schema(description = "current", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotBlank(message = "current")
    private Integer pageNo;

    @Schema(description = "pageSize", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    @NotBlank(message = "pageSize")
    private Integer pageSize;

    @Schema(description = "user name", requiredMode = Schema.RequiredMode.REQUIRED, example = "orton")
    @NotBlank(message = "user name cannot null")
    private String userName;

    @Schema(description = "user mobile", example = "mobile")
    private String mobile;

    @Schema(description = "work shops", example = "[1,2,3]")
    private List<Long> workshopIds;

}
