package com.servauto.admin.model.dto.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Product Attribute Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductAttributeDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "type", example = "input/select/number/date/multiple")
    private String type;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "suffix", example = "mm")
    private String suffix;

    @Schema(description = "categoryId", example = "1")
    private Long categoryId;

    @Schema(description = "isRequired", example = "true")
    private Boolean isRequired;

    @Schema(description = "order", example = "1")
    private Integer order;

    @Schema(description = "options", example = "[{}]")
    private List<ProductAttributeOptionDTO> options;
}
