package com.servauto.admin.model.dto.response.workshop;

import com.servauto.admin.model.dto.response.system.TypeNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Workshop - detail Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkshopDetailDTO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "name", example = "name")
    private String name;

    @Schema(description = "type", example = "1")
    private String type;

    @Schema(description = "typeName", example = "typeName")
    private String typeName;

    @Schema(description = "status", example = "status")
    private String status;

    @Schema(description = "statusName", example = "statusName")
    private String statusName;

    @Schema(description = "stateCode", example = "stateCode")
    private String stateCode;

    @Schema(description = "stateName", example = "stateName")
    private String stateName;

    @Schema(description = "cityCode", example = "cityCode")
    private String cityCode;

    @Schema(description = "cityName", example = "cityName")
    private String cityName;

    @Schema(description = "address", example = "address")
    private String address;

    @Schema(description = "contactNumber", example = "contactNumber")
    private String contactNumber;

    @Schema(description = "createTime", example = "1735664461000")
    private Date createTime;

    @Schema(description = "updateTime", example = "1735664461000")
    private Date updateTime;

    @Schema(description = "operatorId", example = "1")
    private Long operatorId;

    @Schema(description = "operatorName", example = "operatorName")
    private String operatorName;

    @Schema(description = "remark", example = "remark")
    private String remark;

    @Schema(description = "logo", example = "logo")
    private String logo;

    @Schema(description = "latitude", example = "100.11234")
    private BigDecimal latitude;

    @Schema(description = "longitude", example = "124.45645")
    private BigDecimal longitude;

    @Schema(description = "locationUrl", example = "https://www.google.com/maps/place/1")
    private String locationUrl;

    @Schema(description = "photo", example = "photo")
    private List<String> photo;

    @Schema(description = "featuredTags", example = "featuredTags")
    private List<String> featuredTags;

    @Schema(description = "serviceTimeList", example = "serviceTimeList")
    private List<WorkshopServiceTimeDTO> serviceTimeList;

    @Schema(description = "whatsAppNumber", example = "phone1,phone2,phone3")
    private String whatsAppNumber;
}