package com.servauto.admin.model.dto.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Order - shipping order request vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShippingOrderReqDTO {

    @Schema(hidden = true)
    private String orderNo;

    @Schema(description = "trackingNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "tracking number")
    private String trackingNo;

    @Schema(description = "remark", requiredMode = Schema.RequiredMode.REQUIRED, example = "shipping remark")
    private String remark;
}
