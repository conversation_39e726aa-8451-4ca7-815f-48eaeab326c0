package com.servauto.admin.model.dto.request.workshop;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Workshop - service time Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkshopServiceTimeDTO {
    @Schema(description = "day, 0 1 2 3 4 5 6", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0")
    private Integer day;

    @Schema(description = "startTime", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456")
    private Long startTime;

    @Schema(description = "startTime", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456")
    private Long endTime;
}
