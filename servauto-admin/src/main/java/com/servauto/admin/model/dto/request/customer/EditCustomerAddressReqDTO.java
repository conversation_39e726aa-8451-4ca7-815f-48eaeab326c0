package com.servauto.admin.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - address Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EditCustomerAddressReqDTO {

    @Schema(description = "shipping name", example = "name")
    private String name;

    @Schema(description = "shipping mobile", example = "mobile")
    private String mobile;

    @Schema(description = "shipping state code", example = "state code")
    private String stateCode;

    @Schema(description = "shipping city code", example = "city code")
    private String cityCode;

    @Schema(description = "shipping address", example = "address")
    private String address;

    @Schema(description = "is default address", example = "true/false")
    private Boolean isDefault;
}
