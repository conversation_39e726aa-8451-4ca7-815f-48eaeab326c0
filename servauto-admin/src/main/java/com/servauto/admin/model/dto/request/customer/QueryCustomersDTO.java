package com.servauto.admin.model.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - query Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryCustomersDTO {

    @Schema(description = "mobile", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "mobile")
    private String mobile;
}
