package com.servauto.admin.model.dto.response.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Type Name Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TypeNameDTO {
    @Schema(description = "type", example = "1")
    private Integer type;

    @Schema(description = "name", example = "name")
    private String name;
}
