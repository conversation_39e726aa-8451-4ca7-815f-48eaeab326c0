package com.servauto.admin.model.dto.response.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(description = "Order - page order response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@ExcelIgnoreUnannotated
public class PageOrderDTO {

    @Schema(description = "orderNo", example = "")
    @ExcelProperty(index = 1, value = "Order ID")
    private String orderNo;

    @Schema(description = "order status", example = "")
    private String status;

    @Schema(description = "order status name", example = "")
    @ExcelProperty(index = 2, value = "Order Status")
    private String statusName;

    @Schema(description = "creator name", example = "")
    @ExcelProperty(index = 3, value = "Creator Name")
    private String creatorName;

    @Schema(description = "create order time", example = "")
    @ExcelProperty(index = 4, value = "Creator Time")
    private Date orderTime;

    @Schema(description = "customer name", example = "")
    @ExcelProperty(index = 5, value = "Customer Name")
    private String customerName;

    @Schema(description = "contact name", example = "")
    @ExcelProperty(index = 6, value = "Contact No.")
    private String customerMobile;

    @Schema(description = "license plate", example = "")
    @ExcelProperty(index = 7, value = "Plate Number")
    private String licensePlate;

    @Schema(description = "car info", example = "")
    @ExcelProperty(index = 8, value = "Vehicle Info")
    private String carInfo;

    @Schema(description = "order product package id", example = "")
    private Long packageId;

    @Schema(description = "order product package name", example = "")
    @ExcelProperty(index = 9, value = "Package Name")
    private String packageName;

    @Schema(description = "workshop id", example = "")
    private Long workShopId;

    @Schema(description = "workshop name", example = "")
    @ExcelProperty(index = 10, value = "Pickup Workshop")
    private String workShopName;

    @Schema(description = "order original amount", example = "")
    private BigDecimal originalAmount;

    @Schema(description = "order grand total", example = "")
    @ExcelProperty(index = 11, value = "Payment Amount")
    private BigDecimal grandTotal;

    @Schema(description = "order remark", example = "")
    private String remark;

    @Schema(description = "updater name", example = "")
    @ExcelProperty(index = 12, value = "Update By")
    private String updaterName;

    @Schema(description = "update order time", example = "")
    @ExcelProperty(index = 13, value = "Update Time")
    private Date updateTime;

    @Schema(description = "order reservation time", example = "")
    private Date reservationTime;

    @Schema(description = "order product details", example = "[]")
    List<OrderProductDTO> products;

    @Schema(description = "delivery address", example = "")
    private String deliveryAddress;

    @Schema(description = "logistics tracking number", example = "")
    private String trackingNo;

    @Schema(description = "is show cancel button", example = "true/false")
    private Boolean allowCancel;

    @Schema(description = "is show shipping button", example = "true/false")
    private Boolean allowShipping;

    @Schema(description = "is show delivery button", example = "true/false")
    private Boolean allowDelivery;

    @Schema(description = "is show reschedule button", example = "true/false")
    private Boolean allowReschedule;

    @Schema(description = "is show workshop reschedule button", example = "true/false")
    private Boolean allowWorkshopReschedule;

    @Schema(description = "is show confirm button", example = "true/false")
    private Boolean allowConfirm;

    @Schema(description = "print info", example = "xxx")
    private String printInfo;

    @Schema(description = "order suggestion time", example = "xxx")
    private Date suggestionTime;
}
