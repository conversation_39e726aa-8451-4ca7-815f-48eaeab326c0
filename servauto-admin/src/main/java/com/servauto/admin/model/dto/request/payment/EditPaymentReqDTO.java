package com.servauto.admin.model.dto.request.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "Customer - edit Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EditPaymentReqDTO {

    @Schema(description = "customerId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long customerId;

    @Schema(description = "realName", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "realName")
    private String realName;

    @Schema(description = "gender", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "0/1")
    private String gender;

    @Schema(description = "birthday", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "")
    private Long birthday;

    // todo  car info and shipping address

}
