package com.servauto.admin.model.dto.request.packageinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "Package Info - edit/insert Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdatePackageInfoDTO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long id;

    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    private String name;

    @Schema(description = "status", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "price", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "100.00")
    private String price;

    @Schema(description = "serviceId", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long serviceId;

    @Schema(description = "deliveryModes", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "[1]")
    private List<Integer> deliveryModes;

    @Schema(description = "productIds", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1]")
    private List<Long> productIds;

    @Schema(description = "updatedBy", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Long updatedBy;
}
