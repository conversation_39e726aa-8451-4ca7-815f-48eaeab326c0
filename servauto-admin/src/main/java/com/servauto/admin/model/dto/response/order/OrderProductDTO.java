package com.servauto.admin.model.dto.response.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "Order - order product response vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ExcelIgnoreUnannotated
public class OrderProductDTO {

    @Schema(description = "order no", example = "")
    @ExcelProperty(index = 1, value = "Order ID")
    private String orderNo;

    @Schema(description = "product id", example = "1")
    @ExcelProperty(index = 2, value = "Product ID")
    private Long productId;

    @Schema(description = "product name", example = "name/title")
    @ExcelProperty(index = 3, value = "Product Name")
    private String productName;

    @Schema(description = "product attribute", example = "xxx 5L")
    private List<Object> productAttribute;

    @Schema(description = "product brand id", example = "1")
    private Long brandId;

    @Schema(description = "product brand name", example = "1")
    @ExcelProperty(index = 4, value = "Product Brand")
    private String brandName;

    @Schema(description = "product category id", example = "1")
    private Long categoryId;

    @Schema(description = "product category name", example = "oil")
    @ExcelProperty(index = 5, value = "Product Category")
    private String categoryName;

    @Schema(description = "product image", example = "https://xxxx.com")
    private String image;

    @Schema(description = "product reserve price", example = "1")
    private BigDecimal reservePrice;

    @Schema(description = "product actual price", example = "1")
    private BigDecimal actualPrice;

    @Schema(description = "product discount price", example = "1")
    private BigDecimal discountPrice;

    @Schema(description = "product discount amount", example = "1")
    private BigDecimal discountAmount;

    @Schema(description = "product quantity", example = "1")
    @ExcelProperty(index = 6, value = "Quantity")
    private Integer quantity;

}
