package com.servauto.admin.enums;

public enum GenderEnum {

    MALE("0", "Male"),

    FEMALE("1", "Female"),

    UNKNOWN("2", "Unknown");

    private final String code;

    private final String desc;

    GenderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static GenderEnum getByCode(String code) {
        for (GenderEnum e : GenderEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }


}
