package com.servauto.admin.enums.order;

import lombok.Getter;

@Getter
public enum OrderDeliveryTypeEnum {

    SHIPPING("SHIPPING", "Shipping delivery", "Mailing"),
    PICKUP("PIC<PERSON><PERSON>", "Pickup delivery", "Self Pickup"),
    W<PERSON><PERSON>HOP("IN-WORKSHOP", "In workshop install", "In Store Installation"),
    ;

    private final String code;

    private final String msg;

    private final String printMsg;

    OrderDeliveryTypeEnum(String code, String msg, String printMsg) {
        this.code = code;
        this.msg = msg;
        this.printMsg = printMsg;
    }

    public static boolean isShipping(String code) {
        return SHIPPING.getCode().equals(code);
    }

    public static boolean isPickup(String code) {
        return PICKUP.getCode().equals(code);
    }

    public static boolean isInWorkshop(String code) {
        return WORKSHOP.getCode().equals(code);
    }

    public static OrderDeliveryTypeEnum getByCode(String code) {
        for (OrderDeliveryTypeEnum e : OrderDeliveryTypeEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }


}
