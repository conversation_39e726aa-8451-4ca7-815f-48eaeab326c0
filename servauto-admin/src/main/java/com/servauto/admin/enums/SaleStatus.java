package com.servauto.admin.enums;

public enum SaleStatus {
    UNKNOWN(0, "Unknown"),

    ACTIVE(1, "Listed"),

    INACTIVE(2, "Unlisted");

    private final Integer code;

    private final String desc;

    SaleStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean active(Integer code) {
        return ACTIVE.getCode().equals(code);
    }

    public static SaleStatus getByCode(Integer code) {
        for (SaleStatus e : SaleStatus.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
