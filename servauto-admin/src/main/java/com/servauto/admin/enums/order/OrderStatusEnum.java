package com.servauto.admin.enums.order;

import cn.hutool.core.util.ArrayUtil;
import lombok.Getter;

@Getter
public enum OrderStatusEnum {

    PENDING_PAY("PENDING_PAY", "Pending Payment"),
    PAYMENT_SUCCESS("PAYMENT_SUCCESS", "Payment Completed"),
    PENDING_WORKSHOP_CONFIRM("PENDING_WORKSHOP_CONFIRM", "Pending Workshop Confirm"),
    PENDING_HQOPS_CONFIRM("PENDING_HQOPS_CONFIRM", "Pending HQ Ops Confirm"),
    PENDING_DELIVERY("PENDING_DELIVERY", "Pending Delivery"),
    COMPLETED("COMPLETED", "Order Completed"),
    DESPATCHED("DESPATCHED", "Despatched"),
    CANCELED("CANCELED", "Cancelled"),
    CANCELED_TIMEOUT("CANCELED_TIMEOUT", "Cancelled"),
    FINISHED("FINISHED", "Order Closed"),
    ;

    private final String code;

    private final String msg;

    OrderStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isUnpaid(String code) {
        return PENDING_PAY.code.equals(code);
    }

    public static boolean hasPaid(String code) {
        return !PENDING_PAY.code.equals(code) && !CANCELED.code.equals(code) && !CANCELED_TIMEOUT.code.equals(code);
    }

    public static boolean isCanceled(String code) {
        return CANCELED.code.equals(code) || CANCELED_TIMEOUT.code.equals(code);
    }

    public static boolean isDespatched(String code) {
        return DESPATCHED.code.equals(code);
    }

    public static boolean canCancel(String code) {
        return PENDING_PAY.code.equals(code) || PENDING_DELIVERY.code.equals(code);
    }

    public static boolean canDelivery(String deliveryType, String code) {
        if (OrderDeliveryTypeEnum.isShipping(deliveryType)) {
            return DESPATCHED.code.equals(code);
        }
        return PENDING_DELIVERY.code.equals(code);
    }

    public static boolean canReschedule(String code) {
        return PENDING_HQOPS_CONFIRM.code.equals(code) || PENDING_DELIVERY.code.equals(code);
    }

    public static boolean canWorkshopReschedule(String code) {
        return PENDING_WORKSHOP_CONFIRM.code.equals(code);
    }

    public static boolean canShipping(String code) {
        return PENDING_DELIVERY.code.equals(code);
    }

    public static OrderStatusEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }

}
