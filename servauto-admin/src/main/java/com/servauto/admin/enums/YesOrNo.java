package com.servauto.admin.enums;

public enum YesOrNo {
    UNKNOWN(0, ""),

    YES(1, "Yes"),

    NO(2, "No");

    private final Integer code;
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    YesOrNo(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean yes(Integer code) {
        return YES.code.equals(code);
    }

    public static YesOrNo getByCode(Integer code) {
        for (YesOrNo item : values()) {
            if(item.getCode().equals(code)) {
                return item;
            }
        }
        return UNKNOWN;
    }
}
