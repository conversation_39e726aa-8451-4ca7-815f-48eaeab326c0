package com.servauto.admin.enums;

public enum ServiceSupportedOn {
    UNKNOWN(0, "unknown"),

    PRODUCT(1, "Product"),

    PACKAGE(2, "Package");

    private final Integer code;

    private final String desc;

    ServiceSupportedOn(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ServiceSupportedOn getByCode(Integer code) {
        for (ServiceSupportedOn e : ServiceSupportedOn.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
