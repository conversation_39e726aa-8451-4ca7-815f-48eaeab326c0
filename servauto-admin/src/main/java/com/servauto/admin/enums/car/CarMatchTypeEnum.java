package com.servauto.admin.enums.car;

import lombok.Getter;

@Getter
public enum CarMatchTypeEnum {

    ENGINE_OIL("ENGINE_OIL", "ENGINE_OIL"),
    OIL_FILTER("OIL_FILTER", "O<PERSON>_FILTER"),
    TIRE("TIRE", "TIRE"),
    PPF("PPF", "PAINT PROTECTION FILM (PPF)"),
    WRAP_FILM("WRAP_FILM", "WRAP_FILM"),
    WINDOW_FILM("WINDOW_FILM", "WINDOW_FILM"),
    FOOT_PAD("FOOT_PAD", "FOOT_PAD"),
    ;

    private final String code;

    private final String desc;


    CarMatchTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarMatchTypeEnum getByCode(String code) {
        for (CarMatchTypeEnum e : CarMatchTypeEnum.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }


}
