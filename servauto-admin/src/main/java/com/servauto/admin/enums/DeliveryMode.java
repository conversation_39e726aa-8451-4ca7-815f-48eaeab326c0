package com.servauto.admin.enums;

public enum DeliveryMode {
    UNKNOWN(0, "Unknown"),

    SELF_PICKUP(1, "Self Pickup"),

    MAILING(2, "Mailing"),

    MAX_ENUM(4, "Max ENUM");

    private final Integer code;

    private final String desc;

    DeliveryMode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DeliveryMode getByCode(Integer code) {
        for (DeliveryMode e : DeliveryMode.values()) {
            if (code.equals(e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
