<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.order.OrderMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.order.Order">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="customer_email" jdbcType="VARCHAR" property="customerEmail" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_mobile" jdbcType="VARCHAR" property="inStoreMobile" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType" />
    <result column="variant" jdbcType="VARCHAR" property="variant" />
    <result column="car_icon" jdbcType="VARCHAR" property="carIcon" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="service_hour" jdbcType="INTEGER" property="serviceHour" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="package_id" jdbcType="BIGINT" property="packageId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
    <result column="delivery_type" jdbcType="VARCHAR" property="deliveryType" />
    <result column="pickup_code" jdbcType="VARCHAR" property="pickupCode" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="shipping_fee" jdbcType="DECIMAL" property="shippingFee" />
    <result column="original_amount" jdbcType="DECIMAL" property="originalAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="subtotal" jdbcType="DECIMAL" property="subtotal" />
    <result column="applied_coupon" jdbcType="BIT" property="appliedCoupon" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="grand_total" jdbcType="DECIMAL" property="grandTotal" />
    <result column="force_fixed_price" jdbcType="BIT" property="forceFixedPrice" />
    <result column="refund_tag" jdbcType="INTEGER" property="refundTag" />
    <result column="reservation_time" jdbcType="TIMESTAMP" property="reservationTime" />
    <result column="suggestion_time" jdbcType="TIMESTAMP" property="suggestionTime" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="completed_time" jdbcType="TIMESTAMP" property="completedTime" />
    <result column="finished_time" jdbcType="TIMESTAMP" property="finishedTime" />
    <result column="refunded_time" jdbcType="TIMESTAMP" property="refundedTime" />
    <result column="reminder_one_day" jdbcType="BIT" property="reminderOneDay" />
    <result column="reminder_two_hour" jdbcType="BIT" property="reminderTwoHour" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, type, customer_id, customer_name, customer_mobile, customer_email, in_store_name,
    in_store_mobile, license_plate, brand, model, year, transmission_type, variant,car_icon,
    service_id, service_name, service_hour, service_fee, package_id, package_name, workshop_id,
    workshop_name, delivery_type, pickup_code, status, shipping_fee, original_amount, 
    discount_amount, subtotal, applied_coupon, coupon_amount, grand_total, force_fixed_price, 
    refund_tag, reservation_time, suggestion_time, confirm_time, order_time, paid_time, completed_time, finished_time,
    refunded_time, remark, create_by, update_by, create_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.servauto.admin.model.entity.order.OrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from `order`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <select id="selectByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where deleted = 0
    <if test="req.deliveryType != null">
      and delivery_type = #{req.deliveryType}
    </if>
    <if test="req.status != null">
      and status = #{req.status}
    </if>
    <if test="req.createBeginTime != null and req.createEndTime != null">
      and order_time between #{req.createBeginTime} and #{req.createEndTime}
    </if>
    <if test="req.workShopIds != null and req.workShopIds.size() > 0">
      and workshop_id IN
      <foreach close=")" collection="req.workShopIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="req.inStoreMobile != null">
      and in_store_mobile #{req.inStoreMobile}
    </if>
    <if test="req.licensePlate != null">
      and license_plate = #{req.licensePlate}
    </if>
    <if test="req.statuses != null and req.statuses.size() > 0">
      and `status` IN
      <foreach close=")" collection="req.statuses" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by id desc
  </select>

  <select id="selectByOrderNo" resultType="com.servauto.admin.model.entity.order.Order">
    select
    <include refid="Base_Column_List" />
    from `order`
    where order_no = #{orderNo} and deleted = 0
  </select>

  <select id="selectOrdersBeforeOneDay" parameterType="date" resultType="com.servauto.admin.model.entity.order.Order">
    select
    <include refid="Base_Column_List" />
        from `order`
    where reservation_time = #{date}
      and `status` not in (
      'PENDING_PAY','CANCELED','COMPLETED','FINISHED','REFUNDED'
    )  and delivery_type = 'IN-WORKSHOP' and deleted = 0 and reminder_one_day = 0
    and workshop_id > 0 and reservation_time  != '1970-01-01 00:00:00'
  </select>

  <select id="selectOrdersBeforeTwoHours" parameterType="date" resultType="com.servauto.admin.model.entity.order.Order">
    select
    <include refid="Base_Column_List" />
    from `order`
    where reservation_time = #{date}
    and `status` not in (
    'PENDING_PAY','CANCELED','COMPLETED','FINISHED','REFUNDED'
    )  and delivery_type = 'IN-WORKSHOP' and deleted = 0 and reminder_two_hour = 0
    and workshop_id > 0 and reservation_time  != '1970-01-01 00:00:00'
  </select>

  <update id="casByOrderNoAndStatusList">
    update `order`
    <set>
      <if test="o.status != null">
        `status` = #{o.status,jdbcType=VARCHAR},
      </if>
      <if test="o.refundTag != null">
        refund_tag = #{o.refundTag,jdbcType=INTEGER},
      </if>
      <if test="o.completedTime != null">
        completed_time = #{o.completedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.finishedTime != null">
        finished_time = #{o.finishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.refundedTime != null">
        refunded_time = #{o.refundedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.remark != null">
        remark = #{o.remark,jdbcType=VARCHAR},
      </if>
      <if test="o.updateBy != null">
        update_by = #{o.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="o.confirmTime != null">
        confirm_time = #{o.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.suggestionTime != null">
        suggestion_time = #{o.suggestionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="o.workshopId != null">
        workshop_id = #{o.workshopId,jdbcType=BIGINT},
      </if>
      <if test="o.workshopName != null">
        workshop_name = #{o.workshopName,jdbcType=VARCHAR},
      </if>
      <if test="o.reservationTime != null">
        reservation_time = #{o.reservationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where order_no = #{orderNo} and deleted = 0 and
    `status` IN
    <foreach close=")" collection="statusList" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </update>

  <update id="markReminderOneDay">
    update `order` set reminder_one_day = 1 where order_no = #{orderNo} and reminder_one_day = 0 and deleted = 0
  </update>

  <update id="markReminderTwoHour">
    update `order` set reminder_two_hour = 1 where order_no = #{orderNo} and reminder_two_hour = 0 and deleted = 0
  </update>

</mapper>