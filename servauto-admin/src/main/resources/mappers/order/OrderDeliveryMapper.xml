<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.order.OrderDeliveryMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.order.OrderDelivery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="tracking_no" jdbcType="VARCHAR" property="trackingNo" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="state_name" jdbcType="VARCHAR" property="stateName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, name, mobile, tracking_no, state_code, state_name, city_code, city_name, 
    address, create_time, update_time, deleted
  </sql>

    <select id="selectByExample" parameterType="com.servauto.admin.model.entity.order.OrderDeliveryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from order_delivery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <update id="updateTrackingNoByOrderNo">
    update order_delivery
    <set>
      tracking_no = #{trackingNo,jdbcType=VARCHAR}
    </set>
    where order_no = #{orderNo}
  </update>


</mapper>