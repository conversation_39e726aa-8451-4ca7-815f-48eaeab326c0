<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.order.OrderProductMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.order.OrderProduct">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sub_order_no" jdbcType="VARCHAR" property="subOrderNo" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="net_content" jdbcType="INTEGER" property="netContent" />
    <result column="content_unit" jdbcType="VARCHAR" property="contentUnit" />
    <result column="product_attribute" jdbcType="VARCHAR" property="productAttribute" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="reserve_price" jdbcType="DECIMAL" property="reservePrice" />
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
    <result column="discount_price" jdbcType="DECIMAL" property="discountPrice" />
    <result column="promotion_discount" jdbcType="DECIMAL" property="promotionDiscount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, sub_order_no, product_id, product_name, brand_id, brand_name, net_content, 
    content_unit, product_attribute, category_id, category_name, image, reserve_price, 
    actual_price, discount_price, promotion_discount, discount_amount, quantity, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.servauto.admin.model.entity.order.OrderProductExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from order_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>