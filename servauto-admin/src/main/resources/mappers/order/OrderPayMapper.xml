<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.order.OrderPayMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.order.OrderPay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="grand_total" jdbcType="DECIMAL" property="grandTotal" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="canceled_time" jdbcType="TIMESTAMP" property="canceledTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, pay_no, customer_id, grand_total, paid_amount, error_code, error_msg, 
    pay_method, paid_time, canceled_time, create_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.servauto.admin.model.entity.order.OrderPayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>