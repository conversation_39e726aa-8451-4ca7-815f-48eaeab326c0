<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.payment.PaymentLogMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.payment.PaymentLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pay_id" jdbcType="BIGINT" property="payId" />
    <result column="src_status" jdbcType="INTEGER" property="srcStatus" />
    <result column="target_status" jdbcType="INTEGER" property="targetStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.admin.model.entity.payment.PaymentLog">
    <result column="info" jdbcType="LONGVARCHAR" property="info" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pay_id, src_status, target_status, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from payment_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from payment_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.payment.PaymentLog">
    insert into payment_log (id, pay_id, src_status, 
      target_status, create_time, update_time, 
      info)
    values (#{id,jdbcType=BIGINT}, #{payId,jdbcType=BIGINT}, #{srcStatus,jdbcType=INTEGER}, 
      #{targetStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{info,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.payment.PaymentLog">
    insert into payment_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payId != null">
        pay_id,
      </if>
      <if test="srcStatus != null">
        src_status,
      </if>
      <if test="targetStatus != null">
        target_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="info != null">
        info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="payId != null">
        #{payId,jdbcType=BIGINT},
      </if>
      <if test="srcStatus != null">
        #{srcStatus,jdbcType=INTEGER},
      </if>
      <if test="targetStatus != null">
        #{targetStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="info != null">
        #{info,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.payment.PaymentLog">
    update payment_log
    <set>
      <if test="payId != null">
        pay_id = #{payId,jdbcType=BIGINT},
      </if>
      <if test="srcStatus != null">
        src_status = #{srcStatus,jdbcType=INTEGER},
      </if>
      <if test="targetStatus != null">
        target_status = #{targetStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="info != null">
        info = #{info,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.servauto.admin.model.entity.payment.PaymentLog">
    update payment_log
    set pay_id = #{payId,jdbcType=BIGINT},
      src_status = #{srcStatus,jdbcType=INTEGER},
      target_status = #{targetStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      info = #{info,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.payment.PaymentLog">
    update payment_log
    set pay_id = #{payId,jdbcType=BIGINT},
      src_status = #{srcStatus,jdbcType=INTEGER},
      target_status = #{targetStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>