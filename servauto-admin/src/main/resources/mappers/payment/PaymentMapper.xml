<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.payment.PaymentMapper">
    <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.payment.Payment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pay_no" jdbcType="VARCHAR" property="payNo"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="pay_method" jdbcType="INTEGER" property="payMethod"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="expires_at" jdbcType="TIMESTAMP" property="expiresAt"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , pay_no, source_id, channel, pay_method, amount, currency, expires_at, pay_time,
    refund_time, pay_type, url, status, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from payment
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditions" resultType="com.servauto.admin.model.entity.payment.Payment">
        select
        <include refid="Base_Column_List"/>
        from payment
        <where>
            <if test="payNo != null and payNo != ''">
                and pay_no = #{payNo,jdbcType=VARCHAR}
            </if>
            <if test="sourceId != null and sourceId != ''">
                and source_id = #{sourceId,jdbcType=VARCHAR}
            </if>
            <if test="payTime != null">
                and pay_time = #{payTime,jdbcType=TIMESTAMP}
            </if>
            <if test="refundTime != null">
                and refund_time = #{refundTime,jdbcType=TIMESTAMP}
            </if>
            <if test="payType != null and payType != ''">
                and pay_type = #{payType,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbc}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="(payNo != null and payNo != '') or (sourceId != null and sourceId != '') or (payTime != null) or (refundTime != null) or (payType != null and payType != '') or (status != null and status != '') or (createTime != null) or (updateTime != null)">
                order by
                <if test="payNo != null and payNo != ''">
                    pay_no,
                </if>
                <if test="sourceId != null and sourceId != ''">
                    source_id,
                </if>
            </if>

        </where>
    </select>
    <select id="selectByPaymentIds" resultType="com.servauto.admin.model.entity.payment.Payment">
        select
        <include refid="Base_Column_List"/>
        from payment
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from payment
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.servauto.admin.model.entity.payment.Payment">
        insert into payment (id, pay_no, source_id,
                             channel, pay_method, amount,
                             currency, expires_at, pay_time,
                             refund_time, pay_type, url,
                             status, create_time, update_time)
        values (#{id,jdbcType=BIGINT}, #{payNo,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR},
                #{channel,jdbcType=INTEGER}, #{payMethod,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL},
                #{currency,jdbcType=VARCHAR}, #{expiresAt,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP},
                #{refundTime,jdbcType=TIMESTAMP}, #{payType,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.payment.Payment">
        insert into payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="payNo != null">
                pay_no,
            </if>
            <if test="sourceId != null">
                source_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="payMethod != null">
                pay_method,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="currency != null">
                currency,
            </if>
            <if test="expiresAt != null">
                expires_at,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="refundTime != null">
                refund_time,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="payNo != null">
                #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null">
                #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=INTEGER},
            </if>
            <if test="payMethod != null">
                #{payMethod,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currency != null">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="expiresAt != null">
                #{expiresAt,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundTime != null">
                #{refundTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.payment.Payment">
        update payment
        <set>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=INTEGER},
            </if>
            <if test="payMethod != null">
                pay_method = #{payMethod,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currency != null">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="expiresAt != null">
                expires_at = #{expiresAt,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.payment.Payment">
        update payment
        set pay_no      = #{payNo,jdbcType=VARCHAR},
            source_id   = #{sourceId,jdbcType=VARCHAR},
            channel     = #{channel,jdbcType=INTEGER},
            pay_method  = #{payMethod,jdbcType=INTEGER},
            amount      = #{amount,jdbcType=DECIMAL},
            currency    = #{currency,jdbcType=VARCHAR},
            expires_at  = #{expiresAt,jdbcType=TIMESTAMP},
            pay_time    = #{payTime,jdbcType=TIMESTAMP},
            refund_time = #{refundTime,jdbcType=TIMESTAMP},
            pay_type    = #{payType,jdbcType=VARCHAR},
            url         = #{url,jdbcType=VARCHAR},
            status      = #{status,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStatusByPayNo">
        update payment
        set status = #{status,jdbcType=VARCHAR}
        where pay_no = #{payNo,jdbcType=NUMERIC}
    </update>
</mapper>