<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.car.CarYearsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.car.CarYears">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="brand_id" jdbcType="BIGINT" property="brandId" />
        <result column="model_id" jdbcType="BIGINT" property="modelId" />
        <result column="year_value" jdbcType="VARCHAR" property="yearValue" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="order" jdbcType="INTEGER" property="order" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_id, model_id, `year_value`, `status`, `order`, created_time, updated_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByModelId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where model_id = #{modelId,jdbcType=BIGINT}
        and status = 1
        order by year_value desc
    </select>
    <select id="selectByModelIdAndYears" resultType="com.servauto.admin.model.entity.car.CarYears">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where model_id = #{modelId,jdbcType=BIGINT} and year_value in
        <foreach collection="years" item="year" index="index" open="(" close=")" separator=",">
            #{year}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from car_years
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.servauto.admin.model.entity.car.CarYears">
        insert into car_years (id, brand_id, model_id, year_value,
                               status, order, created_time,
                               updated_time)
        values (#{id,jdbcType=BIGINT}, #{brandId,jdbcType=BIGINT}, #{modelId,jdbcType=BIGINT},
                #{yearValue,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{order,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP},
                #{updatedTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.car.CarYears">
        insert into car_years
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="modelId != null">
                model_id,
            </if>
            <if test="yearValue != null">
                year_value,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="order != null">
                order,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=BIGINT},
            </if>
            <if test="modelId != null">
                #{modelId,jdbcType=BIGINT},
            </if>

            <if test="yearValue != null">
                #{yearValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="order != null">
                #{order,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 新增 SQL 插入语句 -->
    <insert id="insertCarYearsIfNotExists" parameterType="com.servauto.admin.model.entity.car.CarYears">
        insert into car_years (brand_id, model_id, year_value, status, `order`, created_time, updated_time)
        select #{brandId,jdbcType=BIGINT},
               #{modelId,jdbcType=BIGINT},
               #{yearValue,jdbcType=VARCHAR},
               #{status,jdbcType=INTEGER},
               #{order,jdbcType=INTEGER},
               #{createdTime,jdbcType=TIMESTAMP},
               #{updatedTime,jdbcType=TIMESTAMP}
        where not exists (select 1
                          from car_years
                          where model_id = #{modelId,jdbcType=BIGINT} and year_value = #{yearValue,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsertCarYears">
        insert into car_years (brand_id, model_id, year_value, status)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.brandId,jdbcType=BIGINT},
             #{item.modelId,jdbcType=BIGINT},
             #{item.yearValue,jdbcType=VARCHAR},
             #{item.status,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <select id="selectByPrimaryKeys" resultType="com.servauto.admin.model.entity.car.CarYears">
        select 'true' as QUERYID,
        <include refid="Base_Column_List" />
        from car_years
        where id in
        <foreach close=")" collection="yearsIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>