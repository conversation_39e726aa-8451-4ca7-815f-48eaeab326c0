<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.car.CarBrandsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.car.CarBrands">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_name, `status`, `order`, created_time, updated_time
    </sql>
    <!-- 新增 SQL 查询语句 -->
    <select id="selectByBrandName" parameterType="String" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where brand_name = #{brandName,jdbcType=VARCHAR}
    </select>

    <!-- 新增 SQL 查询语句 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
    </select>

    <!-- 新增 SQL 查询语句 -->
    <select id="selectAllValidBrands" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where status = 1
        order by brand_name
    </select>

    <!-- 新增 SQL 批量插入语句 -->
    <insert id="batchInsertCarBrands" parameterType="java.util.List">
        insert into car_brands (brand_name, status)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.brandName,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="selectBrandsByBrandNames" resultType="com.servauto.admin.model.dto.response.car.CarBrandsDTO">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where brand_name in
        <foreach collection="brandNames" item="brandName" open="(" separator="," close=")">
            #{brandName}
        </foreach>
    </select>


    <select id="selectByPrimaryKeys" resultType="com.servauto.admin.model.entity.car.CarBrands">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_brands
        where id in
        <foreach close=")" collection="brandIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>