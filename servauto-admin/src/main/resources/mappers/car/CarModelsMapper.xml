<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.car.CarModelsMapper">
    <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.car.CarModels">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, brand_id, model_name, `status`, `order`, created_time, updated_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from car_models
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.servauto.admin.model.entity.car.CarModels">
        insert into car_models (id, brand_id, model_name,
                                status, order, created_time,
                                updated_time)
        values (#{id,jdbcType=BIGINT}, #{brandId,jdbcType=BIGINT}, #{modelName,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{order,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP},
                #{updatedTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.car.CarModels">
        insert into car_models
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="modelName != null">
                model_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="order != null">
                order,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=BIGINT},
            </if>
            <if test="modelName != null">
                #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="order != null">
                #{order,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.car.CarModels">
        update car_models
        <set>
            <if test="brandId != null">
                brand_id = #{brandId,jdbcType=BIGINT},
            </if>
            <if test="modelName != null">
                model_name = #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="order != null">
                order = #{order,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.car.CarModels">
        update car_models
        set brand_id     = #{brandId,jdbcType=BIGINT},
            model_name   = #{modelName,jdbcType=VARCHAR},
            status       = #{status,jdbcType=INTEGER},
            order        = #{order,jdbcType=INTEGER},
            created_time = #{createdTime,jdbcType=TIMESTAMP},
            updated_time = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <!-- 新增 SQL 插入语句 -->
    <insert id="insertCarModelsIfNotExists" parameterType="com.servauto.admin.model.entity.car.CarModels">
        insert into car_models (brand_id, model_name, status, `order`, created_time, updated_time)
        select #{brandId,jdbcType=BIGINT},
               #{modelName,jdbcType=VARCHAR},
               #{status,jdbcType=INTEGER},
               #{order,jdbcType=INTEGER},
               #{createdTime,jdbcType=TIMESTAMP},
               #{updatedTime,jdbcType=TIMESTAMP}
        where not exists (select 1
                          from car_models
                          where brand_id = #{brandId,jdbcType=BIGINT}
                            and model_name = #{modelName,jdbcType=VARCHAR})
    </insert>
    <!-- 新增 SQL 查询语句 -->
    <select id="selectByBrandIdAndModelName" parameterType="map" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where brand_id = #{brandId,jdbcType=BIGINT}
        and model_name = #{modelName,jdbcType=VARCHAR}
    </select>
    <select id="selectAllModelsByBrandId" parameterType="Long" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where brand_id = #{brandId,jdbcType=BIGINT}
        and status = 1
        order by model_name
    </select>
    <!-- 新增 SQL 查询语句 -->
    <select id="selectAllValidModels" resultMap="BaseResultMap">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where status = 1
    </select>
    <select id="selectAllModelsByBrandIdAndModels" resultType="com.servauto.admin.model.entity.car.CarModels">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where brand_id = #{brandId,jdbcType=BIGINT}
        and model_name in
        <foreach collection="modelsNames" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <insert id="batchInsertCarModels" parameterType="java.util.List">
        insert into car_models (brand_id, model_name, status)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.brandId,jdbcType=BIGINT}, #{item.modelName,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByPrimaryKeys" resultType="com.servauto.admin.model.entity.car.CarModels">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_models
        where id in
        <foreach collection="modelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>