<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.car.CarExtraMapper">
    <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.car.CarExtra">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="car_lib_id" jdbcType="VARCHAR" property="carLibId"/>
        <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="year_id" jdbcType="BIGINT" property="yearId"/>
        <result column="variant_name" jdbcType="VARCHAR" property="variantName"/>
        <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType"/>
        <result column="displacement_value" jdbcType="VARCHAR" property="displacementValue"/>
        <result column="car_type" jdbcType="VARCHAR" property="carType"/>
        <result column="tire_front" jdbcType="VARCHAR" property="tireFront"/>
        <result column="tire_rear" jdbcType="VARCHAR" property="tireRear"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, car_lib_id, brand_id,model_id,year_id,variant_name,transmission_type,displacement_value, car_type, tire_front, tire_rear,
    `status`, `order`, created_time, updated_time
    </sql>

    <select id="selectByCarYearId" resultType="com.servauto.admin.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where year_id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 新增 SQL 插入语句 -->
    <insert id="insertCarExtraIfNotExists" parameterType="com.servauto.admin.model.entity.car.CarExtra">
        insert into car_extra (car_lib_id, brand_id, model_id, year_id, variant_name, transmission_type,
                               displacement_value, car_type,
                               tire_front, tire_rear, status,
                               `order`, created_time, updated_time)
        select #{carLibId,jdbcType=VARCHAR},
               #{brandId,jdbcType=BIGINT},
               #{modelId,jdbcType=BIGINT},
               #{yearId,jdbcType=BIGINT},
               #{variantName,jdbcType=VARCHAR},
               #{transmissionType,jdbcType=VARCHAR},
               #{displacementValue,jdbcType=VARCHAR},
               #{carType,jdbcType=VARCHAR},
               #{tireFront,jdbcType=VARCHAR},
               #{tireRear,jdbcType=VARCHAR},
               #{status,jdbcType=INTEGER},
               #{order,jdbcType=INTEGER},
               #{createdTime,jdbcType=TIMESTAMP},
               #{updatedTime,jdbcType=TIMESTAMP}
        where not exists (select 1
                          from car_extra
                          where variant_id = #{variantId,jdbcType=BIGINT}
                            and car_lib_id = #{carLibId,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsertCarExtra">
        insert into car_extra (car_lib_id, brand_id, model_id, year_id, variant_name, transmission_type,
        displacement_value, car_type,
        tire_front, tire_rear, status)
        values
        <foreach collection="carExtraList" item="item" index="index" separator=",">
            (#{item.carLibId,jdbcType=VARCHAR},
            #{item.brandId,jdbcType=BIGINT},
            #{item.modelId,jdbcType=BIGINT},
            #{item.yearId,jdbcType=BIGINT},
            #{item.variantName,jdbcType=VARCHAR},
            #{item.transmissionType,jdbcType=VARCHAR},
            #{item.displacementValue,jdbcType=VARCHAR},
            #{item.carType,jdbcType=VARCHAR},
            #{item.tireFront,jdbcType=VARCHAR},
            #{item.tireRear,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}
            )
        </foreach>
    </insert>


    <select id="selectByYearId" resultType="com.servauto.admin.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where year_id = #{id,jdbcType=BIGINT}
        and status = 1
        order by variant_name
    </select>
    <select id="selectCarExtraByIds" resultType="com.servauto.admin.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectByPrimaryKeys" resultType="com.servauto.admin.model.entity.car.CarExtra">
        select 'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from car_extra
        where id in
        <foreach collection="variantIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>