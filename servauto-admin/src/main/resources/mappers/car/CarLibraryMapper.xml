<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.car.CarLibraryMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.car.CarLibrary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_lib_id" jdbcType="VARCHAR" property="carLibId" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="series" jdbcType="VARCHAR" property="series" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="model_year" jdbcType="VARCHAR" property="modelYear" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="chassis_code" jdbcType="VARCHAR" property="chassisCode" />
    <result column="trn" jdbcType="VARCHAR" property="trn" />
    <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType" />
    <result column="driver_position" jdbcType="VARCHAR" property="driverPosition" />
    <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType" />
    <result column="doors" jdbcType="TINYINT" property="doors" />
    <result column="engine_model" jdbcType="VARCHAR" property="engineModel" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="power_kw" jdbcType="VARCHAR" property="powerKw" />
    <result column="fuel_type" jdbcType="VARCHAR" property="fuelType" />
    <result column="drive_model" jdbcType="VARCHAR" property="driveModel" />
    <result column="country_of_sale" jdbcType="VARCHAR" property="countryOfSale" />
    <result column="area_of_sale" jdbcType="VARCHAR" property="areaOfSale" />
    <result column="tire_front" jdbcType="VARCHAR" property="tireFront" />
    <result column="tire_rear" jdbcType="VARCHAR" property="tireRear" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, car_lib_id, manufacturer, brand, series, model, model_year, grade, chassis_code, 
    trn, transmission_type, driver_position, vehicle_type, doors, engine_model, displacement, 
    power_kw, fuel_type, drive_model, country_of_sale, area_of_sale, tire_front, tire_rear, 
    status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from car_library
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from car_library
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.car.CarLibrary">
    insert into car_library (id, car_lib_id, manufacturer, 
      brand, series, model, 
      model_year, grade, chassis_code, 
      trn, transmission_type, driver_position, 
      vehicle_type, doors, engine_model, 
      displacement, power_kw, fuel_type, 
      drive_model, country_of_sale, area_of_sale, 
      tire_front, tire_rear, status, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{carLibId,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{brand,jdbcType=VARCHAR}, #{series,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{modelYear,jdbcType=VARCHAR}, #{grade,jdbcType=VARCHAR}, #{chassisCode,jdbcType=VARCHAR}, 
      #{trn,jdbcType=VARCHAR}, #{transmissionType,jdbcType=VARCHAR}, #{driverPosition,jdbcType=VARCHAR}, 
      #{vehicleType,jdbcType=VARCHAR}, #{doors,jdbcType=TINYINT}, #{engineModel,jdbcType=VARCHAR}, 
      #{displacement,jdbcType=VARCHAR}, #{powerKw,jdbcType=VARCHAR}, #{fuelType,jdbcType=VARCHAR}, 
      #{driveModel,jdbcType=VARCHAR}, #{countryOfSale,jdbcType=VARCHAR}, #{areaOfSale,jdbcType=VARCHAR}, 
      #{tireFront,jdbcType=VARCHAR}, #{tireRear,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.car.CarLibrary">
    insert into car_library
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carLibId != null">
        car_lib_id,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="series != null">
        series,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="modelYear != null">
        model_year,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="chassisCode != null">
        chassis_code,
      </if>
      <if test="trn != null">
        trn,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="driverPosition != null">
        driver_position,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="doors != null">
        doors,
      </if>
      <if test="engineModel != null">
        engine_model,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="powerKw != null">
        power_kw,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="driveModel != null">
        drive_model,
      </if>
      <if test="countryOfSale != null">
        country_of_sale,
      </if>
      <if test="areaOfSale != null">
        area_of_sale,
      </if>
      <if test="tireFront != null">
        tire_front,
      </if>
      <if test="tireRear != null">
        tire_rear,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carLibId != null">
        #{carLibId,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="series != null">
        #{series,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="modelYear != null">
        #{modelYear,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=VARCHAR},
      </if>
      <if test="chassisCode != null">
        #{chassisCode,jdbcType=VARCHAR},
      </if>
      <if test="trn != null">
        #{trn,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="driverPosition != null">
        #{driverPosition,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="doors != null">
        #{doors,jdbcType=TINYINT},
      </if>
      <if test="engineModel != null">
        #{engineModel,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="powerKw != null">
        #{powerKw,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="driveModel != null">
        #{driveModel,jdbcType=VARCHAR},
      </if>
      <if test="countryOfSale != null">
        #{countryOfSale,jdbcType=VARCHAR},
      </if>
      <if test="areaOfSale != null">
        #{areaOfSale,jdbcType=VARCHAR},
      </if>
      <if test="tireFront != null">
        #{tireFront,jdbcType=VARCHAR},
      </if>
      <if test="tireRear != null">
        #{tireRear,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.car.CarLibrary">
    update car_library
    <set>
      <if test="carLibId != null">
        car_lib_id = #{carLibId,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="series != null">
        series = #{series,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="modelYear != null">
        model_year = #{modelYear,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        grade = #{grade,jdbcType=VARCHAR},
      </if>
      <if test="chassisCode != null">
        chassis_code = #{chassisCode,jdbcType=VARCHAR},
      </if>
      <if test="trn != null">
        trn = #{trn,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        transmission_type = #{transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="driverPosition != null">
        driver_position = #{driverPosition,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="doors != null">
        doors = #{doors,jdbcType=TINYINT},
      </if>
      <if test="engineModel != null">
        engine_model = #{engineModel,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="powerKw != null">
        power_kw = #{powerKw,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="driveModel != null">
        drive_model = #{driveModel,jdbcType=VARCHAR},
      </if>
      <if test="countryOfSale != null">
        country_of_sale = #{countryOfSale,jdbcType=VARCHAR},
      </if>
      <if test="areaOfSale != null">
        area_of_sale = #{areaOfSale,jdbcType=VARCHAR},
      </if>
      <if test="tireFront != null">
        tire_front = #{tireFront,jdbcType=VARCHAR},
      </if>
      <if test="tireRear != null">
        tire_rear = #{tireRear,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.car.CarLibrary">
    update car_library
    set car_lib_id = #{carLibId,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      series = #{series,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      model_year = #{modelYear,jdbcType=VARCHAR},
      grade = #{grade,jdbcType=VARCHAR},
      chassis_code = #{chassisCode,jdbcType=VARCHAR},
      trn = #{trn,jdbcType=VARCHAR},
      transmission_type = #{transmissionType,jdbcType=VARCHAR},
      driver_position = #{driverPosition,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      doors = #{doors,jdbcType=TINYINT},
      engine_model = #{engineModel,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      power_kw = #{powerKw,jdbcType=VARCHAR},
      fuel_type = #{fuelType,jdbcType=VARCHAR},
      drive_model = #{driveModel,jdbcType=VARCHAR},
      country_of_sale = #{countryOfSale,jdbcType=VARCHAR},
      area_of_sale = #{areaOfSale,jdbcType=VARCHAR},
      tire_front = #{tireFront,jdbcType=VARCHAR},
      tire_rear = #{tireRear,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 新增 SQL 查询语句 -->
  <select id="selectModelsByBrandDistinct" parameterType="String" resultType="String">
    select distinct model as modelName
    from car_library
    where brand = #{brand,jdbcType=VARCHAR}
  </select>
  <select id="selectModelYearsByBrandAndModelDistinct" parameterType="map" resultType="String">
    select distinct model_year
    from car_library
    where brand = #{brand,jdbcType=VARCHAR}
      and model = #{model,jdbcType=VARCHAR}
  </select>
  <!-- 根据brand、model和model_year获取去重后的grade集合 -->
  <select id="selectVariantByBrandModelAndYearDistinct" parameterType="map" resultType="String">
      select distinct grade
      from car_library
      where brand = #{brand,jdbcType=VARCHAR}
      and model = #{model,jdbcType=VARCHAR}
      <if test="modelYear != null">
          and model_year = #{modelYear,jdbcType=VARCHAR}
      </if>
      <if test="modelYear == null">
          and model_year is null
      </if>
  </select>

  <!-- 根据brand、model、model_year和grade获取carlibrary的集合 -->
  <select id="selectCarLibrariesByBrandModelYearAndGrade" parameterType="map" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from car_library
    where brand = #{brand,jdbcType=VARCHAR}
      and model = #{model,jdbcType=VARCHAR}
    <if test="modelYear != null">
      and model_year = #{modelYear,jdbcType=VARCHAR}
    </if>
    <if test="modelYear == null">
      and model_year is null
    </if>
  </select>

  <select id="selectAllBrandsDistinct" resultType="String">
    select distinct brand as brandName
    from car_library
  </select>
  <select id="selectWaitSyncDistinctBrands" resultType="com.servauto.admin.model.entity.car.CarLibrary">
    select *
    from car_library
    where status = 1
  </select>
  <select id="selectWaitSyncCars" resultType="com.servauto.admin.model.entity.car.CarLibrary">
    select *
    from car_library
    where status = 2
  </select>

  <update id="updateCarLibraryStatusByIds">
    update car_library
    set status = 2
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>