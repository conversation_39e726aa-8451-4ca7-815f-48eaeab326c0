<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.workshop.WorkshopMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.workshop.Workshop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="state_code" jdbcType="VARCHAR" property="stateCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="featured_tags" jdbcType="VARCHAR" property="featuredTags" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="location_url" jdbcType="VARCHAR" property="locationUrl" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="whats_app_number" jdbcType="LONGVARCHAR" property="whatsAppNumber" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.servauto.admin.model.entity.workshop.Workshop">
    <result column="photo" jdbcType="LONGVARCHAR" property="photo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, type, status, state_code, city_code, address, country_code, phone_number,
    featured_tags, latitude, longitude, location_url, logo, operator_id, remark, create_time,
    update_time, deleted, whats_app_number
  </sql>
  <sql id="Blob_Column_List">
    photo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from workshop
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from workshop
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.workshop.Workshop">
    insert into workshop (id, name, type,
      status, state_code, city_code,
      address, country_code, phone_number,
      featured_tags, latitude, longitude,
      location_url, logo, operator_id,
      remark, create_time, update_time,
      deleted, photo,whats_app_number)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{status,jdbcType=INTEGER}, #{stateCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
      #{address,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR},
      #{featuredTags,jdbcType=VARCHAR}, #{latitude,jdbcType=DECIMAL}, #{longitude,jdbcType=DECIMAL},
      #{locationUrl,jdbcType=VARCHAR}, #{logo,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT},
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{deleted,jdbcType=BIT}, #{photo,jdbcType=LONGVARCHAR},#{whatsAppNumber,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.workshop.Workshop" useGeneratedKeys="true" keyProperty="id">
    insert into workshop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="stateCode != null">
        state_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="countryCode != null">
        country_code,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="featuredTags != null">
        featured_tags,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="locationUrl != null">
        location_url,
      </if>
      <if test="logo != null">
        logo,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="photo != null">
        photo,
      </if>
      <if test="whatsAppNumber != null">
        whats_app_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="stateCode != null">
        #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="featuredTags != null">
        #{featuredTags,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="locationUrl != null">
        #{locationUrl,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="photo != null">
        #{photo,jdbcType=LONGVARCHAR},
      </if>
      <if test="whatsAppNumber != null">
        #{whatsAppNumber,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.workshop.Workshop">
    update workshop
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="stateCode != null">
        state_code = #{stateCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="featuredTags != null">
        featured_tags = #{featuredTags,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="locationUrl != null">
        location_url = #{locationUrl,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="photo != null">
        photo = #{photo,jdbcType=LONGVARCHAR},
      </if>
       <if test="whatsAppNumber != null">
           whats_app_number = #{whatsAppNumber,jdbcType=LONGVARCHAR},
       </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.servauto.admin.model.entity.workshop.Workshop">
    update workshop
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      state_code = #{stateCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      featured_tags = #{featuredTags,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=DECIMAL},
      longitude = #{longitude,jdbcType=DECIMAL},
      location_url = #{locationUrl,jdbcType=VARCHAR},
      logo = #{logo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      photo = #{photo,jdbcType=LONGVARCHAR},
      whats_app_number = #{whatsAppNumber,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.workshop.Workshop">
    update workshop
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      state_code = #{stateCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      featured_tags = #{featuredTags,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=DECIMAL},
      longitude = #{longitude,jdbcType=DECIMAL},
      location_url = #{locationUrl,jdbcType=VARCHAR},
      logo = #{logo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      photo = #{photo,jdbcType=LONGVARCHAR},
      whats_app_number = #{whatsAppNumber,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from workshop
    <where>
      <if test="conditions.id != null and conditions.id != 0">
        and id = #{conditions.id,jdbcType=BIGINT}
      </if>
      <if test="conditions.name != null and conditions.name != ''">
        and name LIKE CONCAT(#{conditions.name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="conditions.type != null and conditions.type != 0">
        and type = #{conditions.type,jdbcType=INTEGER}
      </if>
      <if test="conditions.ids != null and conditions.ids.size() > 0">
        and id IN
        <foreach item="id" collection="conditions.ids" open="(" separator="," close=")">
          #{id,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
</mapper>