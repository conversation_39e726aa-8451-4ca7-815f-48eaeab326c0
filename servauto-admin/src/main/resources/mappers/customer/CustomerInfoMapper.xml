<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.customer.CustomerInfoMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.customer.CustomerInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="gender" jdbcType="CHAR" property="gender" />
    <result column="source" jdbcType="CHAR" property="source" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, real_name, mobile, gender, `source`, birthday, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from customer_info
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from customer_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.customer.CustomerInfo">
    insert into customer_info (id, real_name, mobile, 
      gender, `source`, birthday, create_time,
      update_time, deleted)
    values (#{id,jdbcType=BIGINT}, #{realName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{gender,jdbcType=CHAR},#{source}, #{birthday,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.customer.CustomerInfo">
    insert into customer_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=CHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.customer.CustomerInfo">
    update customer_info
    <set>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=CHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.customer.CustomerInfo">
    update customer_info
    set real_name = #{realName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=CHAR},
      gender = #{gender,jdbcType=CHAR},
      birthday = #{birthday,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
        from customer_info
    <where>
      <if test="conditions.mobile != null and conditions.mobile != ''">
        and mobile = #{conditions.mobile,jdbcType=VARCHAR}
      </if>
    </where>
  </select>


</mapper>