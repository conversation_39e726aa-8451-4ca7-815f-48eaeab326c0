<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.product.ProductAttributeDisplayMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.product.ProductAttributeDisplay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="attribute_ids" jdbcType="VARCHAR" property="attributeIds" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, category_id, attribute_ids, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute_display
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_attribute_display
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.product.ProductAttributeDisplay">
    insert into product_attribute_display (id, type, category_id, 
      attribute_ids, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{categoryId,jdbcType=BIGINT}, 
      #{attributeIds,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.product.ProductAttributeDisplay">
    insert into product_attribute_display
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="attributeIds != null">
        attribute_ids,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="attributeIds != null">
        #{attributeIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.product.ProductAttributeDisplay">
    update product_attribute_display
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="attributeIds != null">
        attribute_ids = #{attributeIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.product.ProductAttributeDisplay">
    update product_attribute_display
    set type = #{type,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=BIGINT},
      attribute_ids = #{attributeIds,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTypeAndCategoryId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute_display
    <where>
      <if test="type != null">
        and type = #{type,jdbcType=VARCHAR}
      </if>
      <if test="categoryId != null and categoryId != 0">
        and category_id = #{categoryId,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>