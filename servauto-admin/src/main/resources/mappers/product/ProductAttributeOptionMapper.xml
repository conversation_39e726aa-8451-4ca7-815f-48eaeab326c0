<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.product.ProductAttributeOptionMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.product.ProductAttributeOption">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attribute_id" jdbcType="BIGINT" property="attributeId" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, attribute_id, value, label, image, sort_order, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute_option
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_attribute_option
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.product.ProductAttributeOption">
    insert into product_attribute_option (id, attribute_id, value, 
      label, image, sort_order, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{attributeId,jdbcType=BIGINT}, #{value,jdbcType=VARCHAR}, 
      #{label,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR}, #{sortOrder,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.product.ProductAttributeOption">
    insert into product_attribute_option
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attributeId != null">
        attribute_id,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attributeId != null">
        #{attributeId,jdbcType=BIGINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.product.ProductAttributeOption">
    update product_attribute_option
    <set>
      <if test="attributeId != null">
        attribute_id = #{attributeId,jdbcType=BIGINT},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.product.ProductAttributeOption">
    update product_attribute_option
    set attribute_id = #{attributeId,jdbcType=BIGINT},
      value = #{value,jdbcType=VARCHAR},
      label = #{label,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAttributeIds" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute_option
    where attribute_id in
    <foreach collection="attributeIds" item="attributeId" open="(" separator="," close=")">
      #{attributeId,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>