<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.product.ProductAttributeMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.product.ProductAttribute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="suffix" jdbcType="VARCHAR" property="suffix" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="is_required" jdbcType="INTEGER" property="isRequired" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, suffix, type, category_id, is_required, `order`, create_time, update_time,
    deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByCategoryId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute
    where category_id = #{categoryId,jdbcType=BIGINT}
  </select>

  <select id="selectByCategoryIds" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_attribute
    where category_id in
    <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
      #{categoryId,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>