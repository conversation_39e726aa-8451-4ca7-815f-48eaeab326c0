<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.serviceinfo.ServiceWorkshopMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, service_id, workshop_id, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_workshop
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from service_workshop
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop">
    insert into service_workshop (id, service_id, workshop_id, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{serviceId,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop">
    insert into service_workshop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="workshopId != null">
        workshop_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null">
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop">
    update service_workshop
    <set>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null">
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceWorkshop">
    update service_workshop
    set service_id = #{serviceId,jdbcType=BIGINT},
      workshop_id = #{workshopId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByServiceId" parameterType="java.lang.Long">
    delete from service_workshop
    where service_id = #{serviceId,jdbcType=BIGINT}
  </delete>

  <select id="selectByServiceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_workshop
    where service_id = #{serviceId,jdbcType=BIGINT}
  </select>

  <select id="selectByWorkshopId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_workshop
    where workshop_id = #{workshopId,jdbcType=BIGINT}
  </select>
</mapper>