<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.serviceinfo.ServiceInfoMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.serviceinfo.ServiceInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="hours" jdbcType="INTEGER" property="hours" />
    <result column="fee" jdbcType="DECIMAL" property="fee" />
    <result column="supported_on" jdbcType="INTEGER" property="supportedOn" />
    <result column="is_required" jdbcType="INTEGER" property="isRequired" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, hours, fee, supported_on, is_required, updated_by, create_time, update_time, 
    deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from service_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceInfo">
    insert into service_info (id, name, hours, 
      fee, supported_on, is_required, 
      updated_by, create_time, update_time, 
      deleted)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{hours,jdbcType=INTEGER}, 
      #{fee,jdbcType=DECIMAL}, #{supportedOn,jdbcType=INTEGER}, #{isRequired,jdbcType=INTEGER}, 
      #{updatedBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceInfo" useGeneratedKeys="true" keyProperty="id">
    insert into service_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="hours != null">
        hours,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="supportedOn != null">
        supported_on,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="hours != null">
        #{hours,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="supportedOn != null">
        #{supportedOn,jdbcType=INTEGER},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceInfo">
    update service_info
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="hours != null">
        hours = #{hours,jdbcType=INTEGER},
      </if>
      <if test="fee != null">
        fee = #{fee,jdbcType=DECIMAL},
      </if>
      <if test="supportedOn != null">
        supported_on = #{supportedOn,jdbcType=INTEGER},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.serviceinfo.ServiceInfo">
    update service_info
    set name = #{name,jdbcType=VARCHAR},
      hours = #{hours,jdbcType=INTEGER},
      fee = #{fee,jdbcType=DECIMAL},
      supported_on = #{supportedOn,jdbcType=INTEGER},
      is_required = #{isRequired,jdbcType=INTEGER},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_info
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from service_info
    where deleted = 0
    <if test="conditions.name != null and conditions.name != ''">
      and name LIKE CONCAT(#{conditions.name,jdbcType=VARCHAR}, '%')
    </if>
    <if test="conditions.supportedOn != null and conditions.supportedOn > 0">
      and supported_on = #{conditions.supportedOn}
    </if>
    <if test="conditions.ids != null and conditions.ids.size() > 0">
      and id in
      <foreach collection="conditions.ids" item="id" open="(" separator="," close=")">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>
</mapper>