<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.servauto.admin.dao.system.SysRoleWorkshopMapper">
  <resultMap id="BaseResultMap" type="com.servauto.admin.model.entity.system.SysRoleWorkshop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, role_id, workshop_id, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'true' as QUERYID,
    <include refid="Base_Column_List" />
    from sys_role_workshop
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_role_workshop
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <insert id="insert" parameterType="com.servauto.admin.model.entity.system.SysRoleWorkshop">
    insert into sys_role_workshop (id, role_id, workshop_id, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.servauto.admin.model.entity.system.SysRoleWorkshop">
    insert into sys_role_workshop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="workshopId != null">
        workshop_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null">
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.servauto.admin.model.entity.system.SysRoleWorkshop">
    update sys_role_workshop
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null">
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.servauto.admin.model.entity.system.SysRoleWorkshop">
    update sys_role_workshop
    set role_id = #{roleId,jdbcType=BIGINT},
      workshop_id = #{workshopId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByRoleIds">
    delete from sys_role_workshop where role_id in
    <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
      #{roleId}
    </foreach>
  </delete>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select
      srw.id, srw.role_id, srw.workshop_id,srw.create_time, srw.update_time, srw.deleted
    from sys_role_workshop srw
    inner join sys_user_role sur on srw.role_id = sur.role_id
    where sur.user_id = #{userId,jdbcType=BIGINT}
  </select>

  <select id="selectByRoleId" resultMap="BaseResultMap">
    select
      srw.id, srw.role_id, srw.workshop_id, srw.create_time, srw.update_time, srw.deleted
    from sys_role_workshop srw
    where srw.role_id = #{roleId,jdbcType=BIGINT}
  </select>


  <select id="selectByRoleIds" resultMap="BaseResultMap">
    select
      srw.id, srw.role_id, srw.workshop_id, srw.create_time, srw.update_time, srw.deleted
    from sys_role_workshop srw
    where srw.role_id in
    <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
      #{roleId}
    </foreach>
  </select>

</mapper>