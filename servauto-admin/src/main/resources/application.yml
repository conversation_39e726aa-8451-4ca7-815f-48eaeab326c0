server:
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100
    max-swallow-size: 50MB

  shutdown: graceful

spring:
  application:
    name: servauto-admin
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  main:
    allow-circular-references: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      slave:
        enabled: false
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
  messages:
    basename: i18n/messages
  jackson:
    serialization:
      write-dates-as-timestamps: true
      write-date-timestamps-as-nanoseconds: false
      write-durations-as-timestamps: true
      fail-on-empty-beans: false
  cache:
    type: REDIS
  data:
    redis:
      client-type: lettuce
      lettuce:
        pool:
          enabled: true
          min-idle: 0
          max-active: 8
          max-idle: 8
          max-wait: -1ms
      connect-timeout: 10s


mybatis:
  typeAliasesPackage: com.servauto.admin.**.entity
  mapperLocations: classpath*:mappers/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml

pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: never
