INSERT INTO sys_user
(user_id, user_name, nick_name, user_type, email, mobile, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark)
VALUES(1, 'admin', 'ServAuto', '00', '<EMAIL>', '18888888888', '1', '',
       '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '',
       now(), 'admin', now(), '', now(), 'administrator');

INSERT INTO sys_role
(role_id, role_name, role_key, role_sort, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)
VALUES(1, 'SuperAdmin', 'admin', '1', 1, 1, '0', '0', 'admin', '2025-02-28 19:43:11', 'admin', '2025-03-07 15:48:11', 'super administrator');

INSERT INTO sys_user_role (user_id, role_id) VALUES(1, 1);

INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1, 'System Management', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'SettingOutlined', 'admin', '2025-02-28 19:43:11', '', NULL, 'System Management Directory');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(100, 'User Management', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-02-28 19:43:13', '', NULL, 'User Management Menu');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(101, 'Role Management', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-02-28 19:43:13', '', NULL, 'Role Management Menu');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(102, 'Menu Management', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-02-28 19:43:13', '', NULL, 'Menu Management Menu');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(105, 'Dictionary Management', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-02-28 19:43:14', '', NULL, 'Dictionary Management Menu');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1000, 'User Query', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-02-28 19:43:18', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1001, 'User Addition', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-02-28 19:43:18', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1002, 'User Modification', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-02-28 19:43:18', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1006, 'Reset Password', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-02-28 19:43:19', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1007, 'Role Query', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-02-28 19:43:19', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1008, 'Role Addition', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-02-28 19:43:20', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1009, 'Role Modification', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-02-28 19:43:20', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1010, 'Role Deletion', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-02-28 19:43:20', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1012, 'Menu Query', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-02-28 19:43:21', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1013, 'Menu Addition', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-02-28 19:43:21', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1014, 'Menu Modification', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-02-28 19:43:21', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1015, 'Menu Deletion', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-02-28 19:43:21', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1025, 'Dictionary Query', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-02-28 19:43:24', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1026, 'Dictionary Addition', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-02-28 19:43:24', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1027, 'Dictionary Modification', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-02-28 19:43:24', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(1028, 'Dictionary Deletion', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-02-28 19:43:24', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2000, 'Customer Management', 0, 11, 'customer', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'SolutionOutlined', 'admin', '2025-03-03 15:08:02', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2001, 'Customer List', 2000, 11, 'list', 'customer/list/index', NULL, '', 1, 1, 'C', '0', '0', 'customer:list', '#', 'admin', '2025-03-03 15:14:20', 'admin', '2025-03-03 15:16:38', '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2002, 'Customer Detail', 2000, 11, 'detail/:id', 'customer/detail/index', NULL, '', 1, 1, 'C', '1', '0', 'customer:detail', '#', 'admin', '2025-03-04 14:17:25', 'admin', '2025-03-04 14:27:46', '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2003, 'Workshop Management', 0, 11, 'workshop', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'ShopOutlined', 'admin', '2025-03-04 17:37:07', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2004, 'Workshop List', 2003, 11, 'list', 'workshop/list/index', 'list', '', 1, 1, 'C', '0', '0', 'workshop:list', '#', 'admin', '2025-03-04 17:39:22', '', NULL, '');
INSERT INTO sys_menu
(menu_id, menu_name, parent_id, order_num, `path`, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2008, 'workshop detail', 2003, 1212, 'detail', 'workshop/detail/index', 'id', '', 1, 1, 'C', '1', '0', 'workshop:detail', '#', 'admin', '2025-03-06 18:23:32', 'admin', '2025-03-07 11:15:40', '');


INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 100);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 101);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 102);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 105);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1000);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1001);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1002);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1006);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1007);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1008);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1009);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1010);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1012);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1013);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1014);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1015);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1025);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1026);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1027);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 1028);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2000);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2001);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2002);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2003);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2004);
INSERT INTO sys_role_menu
(role_id, menu_id)
VALUES(1, 2008);


INSERT INTO state
(id, code, name, `order`)
VALUES(1, 'melaka', 'Melaka', 1);
INSERT INTO state
(id, code, name, `order`)
VALUES(2, 'putrajaya', 'Putrajaya', 2);
INSERT INTO state
(id, code, name, `order`)
VALUES(3, 'kedah', 'Kedah', 3);
INSERT INTO state
(id, code, name, `order`)
VALUES(4, 'labuan', 'Labuan', 4);
INSERT INTO state
(id, code, name, `order`)
VALUES(5, 'sarawak', 'Sarawak', 5);
INSERT INTO state
(id, code, name, `order`)
VALUES(6, 'pahang', 'Pahang', 6);
INSERT INTO state
(id, code, name, `order`)
VALUES(7, 'kelantan', 'Kelantan', 7);
INSERT INTO state
(id, code, name, `order`)
VALUES(8, 'johor', 'Johor', 8);
INSERT INTO state
(id, code, name, `order`)
VALUES(9, 'terengganu', 'Terengganu', 9);
INSERT INTO state
(id, code, name, `order`)
VALUES(10, 'negeri-sembilan', 'Negeri Sembilan', 10);
INSERT INTO state
(id, code, name, `order`)
VALUES(11, 'selangor', 'Selangor', 11);
INSERT INTO state
(id, code, name, `order`)
VALUES(12, 'pulau-pinang', 'Pulau Pinang', 12);
INSERT INTO state
(id, code, name, `order`)
VALUES(13, 'sabah', 'Sabah', 13);
INSERT INTO state
(id, code, name, `order`)
VALUES(14, 'perlis', 'Perlis', 14);
INSERT INTO state
(id, code, name, `order`)
VALUES(15, 'kuala-lumpur', 'Kuala Lumpur', 15);
INSERT INTO state
(id, code, name, `order`)
VALUES(16, 'perak', 'Perak', 16);

INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lubok-china', 'Lubok China', 'melaka', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('alor-gajah', 'Alor Gajah', 'melaka', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('melaka', 'Melaka', 'melaka', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('putrajaya', 'Putrajaya', 'putrajaya', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('yan', 'Yan', 'kedah', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-pegang', 'Kuala Pegang', 'kedah', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kupang', 'Kupang', 'kedah', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pokok-sena', 'Pokok Sena', 'kedah', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kulim', 'Kulim', 'kedah', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('universiti-utara-malaysia', 'Universiti Utara Malaysia', 'kedah', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('langgar', 'Langgar', 'kedah', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-kayu-hitam', 'Bukit Kayu Hitam', 'kedah', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-ketil', 'Kuala Ketil', 'kedah', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bedong', 'Bedong', 'kedah', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-petani', 'Sungai Petani', 'kedah', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-hitam', 'Ayer Hitam', 'johor', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jeniang', 'Jeniang', 'kedah', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kodiang', 'Kodiang', 'kedah', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-nerang', 'Kuala Nerang', 'kedah', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('changloon', 'Changloon', 'kedah', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sik', 'Sik', 'kedah', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lunas', 'Lunas', 'kedah', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-empat', 'Simpang Empat', 'kedah', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pendang', 'Pendang', 'kedah', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-bahru', 'Bandar Bahru', 'kedah', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gurun', 'Gurun', 'kedah', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-baharu', 'Bandar Baharu', 'kedah', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('merbok', 'Merbok', 'kedah', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('padang-serai', 'Padang Serai', 'kedah', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('langkawi', 'Langkawi', 'kedah', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jitra', 'Jitra', 'kedah', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('alor-setar', 'Alor Setar', 'kedah', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('baling', 'Baling', 'kedah', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('serdang', 'Serdang', 'selangor', 52, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('labuan', 'Labuan', 'labuan', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('debak', 'Debak', 'sarawak', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('dalat', 'Dalat', 'sarawak', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('julau', 'Julau', 'sarawak', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('siburan', 'Siburan', 'sarawak', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sebauh', 'Sebauh', 'sarawak', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bekenu', 'Bekenu', 'sarawak', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lingga', 'Lingga', 'sarawak', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kabong', 'Kabong', 'sarawak', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('mukah', 'Mukah', 'sarawak', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sibu', 'Sibu', 'sarawak', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sundar', 'Sundar', 'sarawak', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('song', 'Song', 'sarawak', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sri-aman', 'Sri Aman', 'sarawak', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('belaga', 'Belaga', 'sarawak', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lundu', 'Lundu', 'sarawak', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tatau', 'Tatau', 'sarawak', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lutong', 'Lutong', 'sarawak', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('betong', 'Betong', 'sarawak', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('baram', 'Baram', 'sarawak', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('spaoh', 'Spaoh', 'sarawak', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('miri', 'Miri', 'sarawak', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bintangor', 'Bintangor', 'sarawak', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('serian', 'Serian', 'sarawak', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sarikei', 'Sarikei', 'sarawak', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('saratok', 'Saratok', 'sarawak', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('belawai', 'Belawai', 'sarawak', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('balingian', 'Balingian', 'sarawak', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('asajaya', 'Asajaya', 'sarawak', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuching', 'Kuching', 'sarawak', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bau', 'Bau', 'sarawak', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lanchang', 'Lanchang', 'pahang', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('damak', 'Damak', 'pahang', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ringlet', 'Ringlet', 'pahang', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bentong', 'Bentong', 'pahang', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-lipis', 'Kuala Lipis', 'pahang', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pekan', 'Pekan', 'pahang', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('muadzam-shah', 'Muadzam Shah', 'pahang', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-krau', 'Kuala Krau', 'pahang', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-fraser', 'Bukit Fraser', 'pahang', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sega', 'Sega', 'pahang', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-ruan', 'Sungai Ruan', 'pahang', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-tun-abdul-razak', 'Bandar Tun Abdul Razak', 'pahang', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-koyan', 'Sungai Koyan', 'pahang', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('brinchang', 'Brinchang', 'pahang', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('balok', 'Balok', 'pahang', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-bera', 'Bandar Bera', 'pahang', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('dong', 'Dong', 'pahang', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gambang', 'Gambang', 'pahang', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-goh', 'Bukit Goh', 'pahang', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('maran', 'Maran', 'pahang', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('triang', 'Triang', 'pahang', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('mentakab', 'Mentakab', 'pahang', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chini', 'Chini', 'pahang', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lurah-bilut', 'Lurah Bilut', 'pahang', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jerantut', 'Jerantut', 'selangor', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('genting-highlands', 'Genting Highlands', 'pahang', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-lembing', 'Sungai Lembing', 'pahang', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuantan', 'Kuantan', 'selangor', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('padang-tengku', 'Padang Tengku', 'pahang', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jaya-gading', 'Jaya Gading', 'pahang', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pasir-mas', 'Pasir Mas', 'kelantan', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tumpat', 'Tumpat', 'kelantan', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-bharu', 'Kota Bharu', 'kelantan', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanah-merah', 'Tanah Merah', 'kelantan', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kem-desa-pahlawan', 'Kem Desa Pahlawan', 'kelantan', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gua-musang', 'Gua Musang', 'kelantan', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-bahru', 'Kota Bahru', 'kelantan', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('selising', 'Selising', 'kelantan', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('dabong', 'Dabong', 'kelantan', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bachok', 'Bachok', 'kelantan', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('temangan', 'Temangan', 'kelantan', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rantau-panjang', 'Rantau Panjang', 'perak', 38, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('cherang-ruku', 'Cherang Ruku', 'kelantan', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pasir-puteh', 'Pasir Puteh', 'kelantan', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ketereh', 'Ketereh', 'kelantan', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pulai-chondong', 'Pulai Chondong', 'kelantan', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('melor', 'Melor', 'kelantan', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-krai', 'Kuala Krai', 'kelantan', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jeli', 'Jeli', 'kelantan', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('machang', 'Machang', 'kelantan', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-balah', 'Kuala Balah', 'kelantan', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-lanas', 'Ayer Lanas', 'kelantan', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('wakaf-bharu', 'Wakaf Bharu', 'kelantan', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('muar', 'Muar', 'johor', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-gambir', 'Bukit Gambir', 'johor', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-tinggi', 'Kota Tinggi', 'johor', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('masai', 'Masai', 'johor', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pontian', 'Pontian', 'johor', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('parit-raja', 'Parit Raja', 'johor', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rengam', 'Rengam', 'johor', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gugusan-taib-andak', 'Gugusan Taib Andak', 'johor', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('senai', 'Senai', 'johor', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-mati', 'Sungai Mati', 'johor', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-tawar-2', 'Ayer Tawar 2', 'johor', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-tawar-5', 'Ayer Tawar 5', 'johor', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-tawar-3', 'Ayer Tawar 3', 'johor', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('benut', 'Benut', 'johor', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-tawar-4', 'Ayer Tawar 4', 'johor', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seri-gading', 'Seri Gading', 'johor', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-penawar', 'Bandar Penawar', 'johor', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('nusajaya', 'Nusajaya', 'johor', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seri-medan', 'Seri Medan', 'johor', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pagoh', 'Pagoh', 'johor', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('endau', 'Endau', 'johor', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kahang', 'Kahang', 'johor', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('semerah', 'Semerah', 'johor', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('senggarang', 'Senggarang', 'johor', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('layang-layang', 'Layang-Layang', 'johor', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-baloi', 'Ayer Baloi', 'johor', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('johor-bharu', 'Johor Bharu', 'johor', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ulu-tiram', 'Ulu Tiram', 'johor', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('panchor', 'Panchor', 'johor', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sri-medan', 'Sri Medan', 'johor', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kerteh', 'Kerteh', 'terengganu', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('paka', 'Paka', 'terengganu', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ajil', 'Ajil', 'terengganu', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-besi', 'Bukit Besi', 'terengganu', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jerteh', 'Jerteh', 'terengganu', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-berang', 'Kuala Berang', 'terengganu', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-tong', 'Sungai Tong', 'terengganu', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kijal', 'Kijal', 'terengganu', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('permaisuri', 'Permaisuri', 'terengganu', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-besut', 'Kuala Besut', 'terengganu', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('cukai', 'Cukai', 'terengganu', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ketengah-jaya', 'Ketengah Jaya', 'terengganu', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('dungun', 'Dungun', 'terengganu', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-terengganu', 'Kuala Terengganu', 'terengganu', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-puteh', 'Ayer Puteh', 'terengganu', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-payong', 'Bukit Payong', 'terengganu', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('al-muktatfi-billah-shah', 'Al Muktatfi Billah Shah', 'terengganu', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('marang', 'Marang', 'terengganu', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chalok', 'Chalok', 'terengganu', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ceneh', 'Ceneh', 'terengganu', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kampung-raja', 'Kampung Raja', 'terengganu', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kemasek', 'Kemasek', 'terengganu', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bahau', 'Bahau', 'negeri-sembilan', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seremban', 'Seremban', 'negeri-sembilan', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gemencheh', 'Gemencheh', 'negeri-sembilan', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rembau', 'Rembau', 'negeri-sembilan', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('linggi', 'Linggi', 'negeri-sembilan', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-kikir', 'Batu Kikir', 'negeri-sembilan', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pusat-bandar-palong', 'Pusat Bandar Palong', 'negeri-sembilan', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('si-rusa', 'Si Rusa', 'negeri-sembilan', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-pertang', 'Simpang Pertang', 'negeri-sembilan', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('port-dickson', 'Port Dickson', 'negeri-sembilan', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rantau', 'Rantau', 'negeri-sembilan', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rompin', 'Rompin', 'negeri-sembilan', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('johol', 'Johol', 'negeri-sembilan', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-durian', 'Simpang Durian', 'negeri-sembilan', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-ipoh', 'Tanjong Ipoh', 'negeri-sembilan', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tampin', 'Tampin', 'negeri-sembilan', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota', 'Kota', 'negeri-sembilan', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gemas', 'Gemas', 'negeri-sembilan', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-seri-jempol', 'Bandar Seri Jempol', 'negeri-sembilan', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-pilah', 'Kuala Pilah', 'negeri-sembilan', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('mantin', 'Mantin', 'negeri-sembilan', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-baru-enstek', 'Bandar Baru Enstek', 'negeri-sembilan', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-klawang', 'Kuala Klawang', 'negeri-sembilan', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('nilai', 'Nilai', 'negeri-sembilan', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('labu', 'Labu', 'negeri-sembilan', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rasa', 'Rasa', 'selangor', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rawang', 'Rawang', 'selangor', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('serendah', 'Serendah', 'selangor', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-karang', 'Tanjong Karang', 'selangor', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('telok-panglima-garang', 'Telok Panglima Garang', 'selangor', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seri-kembangan', 'Seri Kembangan', 'selangor', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('subang-jaya', 'Subang Jaya', 'selangor', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-pelek', 'Sungai Pelek', 'selangor', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('beranang', 'Beranang', 'selangor', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('cheras', 'Cheras', 'kuala-lumpur', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-baru-bangi', 'Bandar Baru Bangi', 'selangor', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pandan', 'Pandan', 'selangor', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batang-kali', 'Batang Kali', 'selangor', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('hulu-langat', 'Hulu Langat', 'selangor', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('banting', 'Banting', 'selangor', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kajang', 'Kajang', 'selangor', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bangi', 'Bangi', 'selangor', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-puncak-alam', 'Bandar Puncak Alam', 'selangor', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sekinchan', 'Sekinchan', 'selangor', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jeram', 'Jeram', 'perak', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('dengkil', 'Dengkil', 'selangor', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('semenyih', 'Semenyih', 'selangor', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gombak', 'Gombak', 'selangor', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-kubu-baru', 'Kuala Kubu Baru', 'selangor', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pulau-indah', 'Pulau Indah', 'selangor', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('klang', 'Klang', 'selangor', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pulau-ketam', 'Pulau Ketam', 'selangor', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pulau-carey', 'Pulau Carey', 'selangor', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjung-bungah', 'Tanjung Bungah', 'pulau-pinang', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('usm-pulau-pinang', 'Usm Pulau Pinang', 'pulau-pinang', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('butterworth', 'Butterworth', 'pulau-pinang', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bayan-lepas', 'Bayan Lepas', 'pulau-pinang', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-ampat', 'Simpang Ampat', 'perlis', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tasek-gelugur', 'Tasek Gelugur', 'pulau-pinang', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kepala-batas', 'Kepala Batas', 'pulau-pinang', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('penaga', 'Penaga', 'pulau-pinang', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-maung', 'Batu Maung', 'pulau-pinang', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tasek-gelugor', 'Tasek Gelugor', 'pulau-pinang', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('penang-hill', 'Penang Hill', 'pulau-pinang', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('permatang-pauh', 'Permatang Pauh', 'pulau-pinang', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-mertajam', 'Bukit Mertajam', 'pulau-pinang', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('nibong-tebal', 'Nibong Tebal', 'pulau-pinang', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kubang-semang', 'Kubang Semang', 'pulau-pinang', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-jawi', 'Sungai Jawi', 'pulau-pinang', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pulau-pinang', 'Pulau Pinang', 'pulau-pinang', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sandakan', 'Sandakan', 'sabah', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('membakut', 'Membakut', 'sabah', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tuaran', 'Tuaran', 'sabah', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kunak', 'Kunak', 'sabah', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('likas', 'Likas', 'sabah', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('keningau', 'Keningau', 'sabah', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ranau', 'Ranau', 'sabah', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjung-aru', 'Tanjung Aru', 'sabah', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-marudu', 'Kota Marudu', 'sabah', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('inanam', 'Inanam', 'sabah', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lahad-datu', 'Lahad Datu', 'sabah', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('semporna', 'Semporna', 'sabah', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('papar', 'Papar', 'sabah', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-kinabatangan', 'Kota Kinabatangan', 'sabah', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bongawan', 'Bongawan', 'sabah', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kudat', 'Kudat', 'sabah', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tawau', 'Tawau', 'sabah', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('beaufort', 'Beaufort', 'sabah', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('menumbok', 'Menumbok', 'sabah', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('penampang', 'Penampang', 'sabah', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tenghilan', 'Tenghilan', 'sabah', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-kinabalu', 'Kota Kinabalu', 'sabah', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tenom', 'Tenom', 'sabah', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-belud', 'Kota Belud', 'sabah', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-penyu', 'Kuala Penyu', 'sabah', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tambunan', 'Tambunan', 'sabah', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('beverly', 'Beverly', 'sabah', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sipitang', 'Sipitang', 'sabah', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('nabawan', 'Nabawan', 'sabah', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pamol', 'Pamol', 'sabah', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('arau', 'Arau', 'perlis', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('padang-besar', 'Padang Besar', 'perlis', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kaki-bukit', 'Kaki Bukit', 'perlis', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-perlis', 'Kuala Perlis', 'perlis', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kangar', 'Kangar', 'perlis', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-caves', 'Batu Caves', 'kuala-lumpur', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('setapak', 'Setapak', 'kuala-lumpur', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-lumpur', 'Kuala Lumpur', 'kuala-lumpur', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chikus', 'Chikus', 'perak', 1, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-sumun', 'Sungai Sumun', 'perak', 2, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-tualang', 'Tanjong Tualang', 'perak', 3, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('intan', 'Intan', 'perak', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('selekoh', 'Selekoh', 'perak', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chenderong-balai', 'Chenderong Balai', 'perak', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bidor', 'Bidor', 'perak', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-gajah', 'Batu Gajah', 'perak', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kampung-gajah', 'Kampung Gajah', 'perak', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seri-manjung', 'Seri Manjung', 'perak', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('padang-rengas', 'Padang Rengas', 'perak', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tapah-road', 'Tapah Road', 'perak', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanah-rata', 'Tanah Rata', 'perak', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gerik', 'Gerik', 'perak', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungkai', 'Sungkai', 'perak', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('malim-nawar', 'Malim Nawar', 'perak', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('manong', 'Manong', 'perak', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('taiping', 'Taiping', 'perak', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-siput', 'Sungai Siput', 'perak', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-kurau', 'Batu Kurau', 'perak', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('hutan-melintang', 'Hutan Melintang', 'perak', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bagan-datoh', 'Bagan Datoh', 'perak', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ulu-kinta', 'Ulu Kinta', 'perak', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('teluk-intan', 'Teluk Intan', 'perak', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-rambutan', 'Tanjong Rambutan', 'perak', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('trolak', 'Trolak', 'perak', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pantai-remis', 'Pantai Remis', 'perak', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-tawar', 'Ayer Tawar', 'perak', 29, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bruas', 'Bruas', 'perak', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('merlimau', 'Merlimau', 'melaka', 4, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-sungai-baru', 'Kuala Sungai Baru', 'melaka', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bemban', 'Bemban', 'melaka', 6, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('durian-tunggal', 'Durian Tunggal', 'melaka', 7, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('asahan', 'Asahan', 'melaka', 8, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('selandar', 'Selandar', 'melaka', 9, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('masjid-tanah', 'Masjid Tanah', 'melaka', 10, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-rambai', 'Sungai Rambai', 'melaka', 11, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kem-trendak', 'Kem Trendak', 'melaka', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-udang', 'Sungai Udang', 'melaka', 13, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-keroh', 'Ayer Keroh', 'melaka', 14, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jasin', 'Jasin', 'melaka', 15, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-kling', 'Tanjong Kling', 'melaka', 16, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('air-keroh', 'Air Keroh', 'melaka', 17, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kanowit', 'Kanowit', 'sarawak', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lubok-antu', 'Lubok Antu', 'sarawak', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('niah', 'Niah', 'sarawak', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('matu', 'Matu', 'sarawak', 34, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pusa', 'Pusa', 'sarawak', 35, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lawas', 'Lawas', 'sarawak', 36, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simunjan', 'Simunjan', 'sarawak', 37, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('daro', 'Daro', 'sarawak', 38, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('nanga-medamit', 'Nanga Medamit', 'sarawak', 39, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sebuyau', 'Sebuyau', 'sarawak', 40, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kota-samarahan', 'Kota Samarahan', 'sarawak', 41, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kapit', 'Kapit', 'sarawak', 42, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('engkilili', 'Engkilili', 'sarawak', 43, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('long-lama', 'Long Lama', 'sarawak', 44, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('limbang', 'Limbang', 'sarawak', 45, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bintulu', 'Bintulu', 'sarawak', 46, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('roban', 'Roban', 'sarawak', 47, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-pusat-jengka', 'Bandar Pusat Jengka', 'pahang', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-rompin', 'Kuala Rompin', 'pahang', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('raub', 'Raub', 'pahang', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chenor', 'Chenor', 'pahang', 34, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('karak', 'Karak', 'pahang', 35, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kemayan', 'Kemayan', 'pahang', 36, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('benta', 'Benta', 'pahang', 38, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('temerloh', 'Temerloh', 'pahang', 39, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chaah', 'Chaah', 'johor', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tangkak', 'Tangkak', 'johor', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('segamat', 'Segamat', 'johor', 34, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pekan-nenas', 'Pekan Nenas', 'johor', 35, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-pahat', 'Batu Pahat', 'johor', 36, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kluang', 'Kluang', 'johor', 37, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('yong-peng', 'Yong Peng', 'johor', 38, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('labis', 'Labis', 'johor', 39, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-tenggara', 'Bandar Tenggara', 'johor', 40, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sri-gading', 'Sri Gading', 'johor', 41, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pengerang', 'Pengerang', 'johor', 42, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gerisek', 'Gerisek', 'johor', 43, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('paloh', 'Paloh', 'johor', 44, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kulai', 'Kulai', 'johor', 45, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pasir-gudang', 'Pasir Gudang', 'johor', 46, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('mersing', 'Mersing', 'johor', 47, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-anam', 'Batu Anam', 'johor', 48, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gelang-patah', 'Gelang Patah', 'johor', 49, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bekok', 'Bekok', 'johor', 50, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('parit-sulong', 'Parit Sulong', 'johor', 51, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kukup', 'Kukup', 'johor', 52, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rengit', 'Rengit', 'johor', 53, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-pasir', 'Bukit Pasir', 'johor', 54, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jementah', 'Jementah', 'johor', 55, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-rengam', 'Simpang Rengam', 'johor', 56, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('parit-jawa', 'Parit Jawa', 'johor', 57, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-selangor', 'Kuala Selangor', 'selangor', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('puchong', 'Puchong', 'selangor', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('klia', 'Klia', 'selangor', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batang-berjuntai', 'Batang Berjuntai', 'selangor', 34, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bukit-rotan', 'Bukit Rotan', 'selangor', 35, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jenjarom', 'Jenjarom', 'selangor', 36, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sepang', 'Sepang', 'selangor', 37, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-buloh', 'Sungai Buloh', 'selangor', 38, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-arang', 'Batu Arang', 'selangor', 39, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('petaling-jaya', 'Petaling Jaya', 'selangor', 40, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kapar', 'Kapar', 'selangor', 41, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('cyberjaya', 'Cyberjaya', 'selangor', 42, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sabak-bernam', 'Sabak Bernam', 'selangor', 44, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-besar', 'Sungai Besar', 'selangor', 45, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('shah-alam', 'Shah Alam', 'selangor', 46, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-sepat', 'Tanjong Sepat', 'selangor', 47, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ampang', 'Ampang', 'selangor', 48, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('subang-airport', 'Subang Airport', 'selangor', 49, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kerling', 'Kerling', 'selangor', 50, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pelabuhan-klang', 'Pelabuhan Klang', 'selangor', 51, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sungai-ayer-tawar', 'Sungai Ayer Tawar', 'selangor', 53, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('balik-pulau', 'Balik Pulau', 'pulau-pinang', 18, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gelugor', 'Gelugor', 'pulau-pinang', 19, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-bungah', 'Tanjong Bungah', 'pulau-pinang', 20, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-itam', 'Ayer Itam', 'pulau-pinang', 21, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-ferringhi', 'Batu Ferringhi', 'pulau-pinang', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jelutong', 'Jelutong', 'pulau-pinang', 23, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('perai', 'Perai', 'pulau-pinang', 24, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('beluran', 'Beluran', 'sabah', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('putatan', 'Putatan', 'sabah', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tamparuli', 'Tamparuli', 'sabah', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-ampat-semanggol', 'Simpang Ampat Semanggol', 'perak', 31, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kamunting', 'Kamunting', 'perak', 32, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ulu-bernam', 'Ulu Bernam', 'perak', 33, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lambor-kanan', 'Lambor Kanan', 'perak', 34, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pusing', 'Pusing', 'perak', 35, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('langkap', 'Langkap', 'perak', 36, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sitiawan', 'Sitiawan', 'perak', 37, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-kangsar', 'Kuala Kangsar', 'perak', 39, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('behrang-stesen', 'Behrang Stesen', 'perak', 40, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-sepetang', 'Kuala Sepetang', 'perak', 41, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chemor', 'Chemor', 'perak', 42, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('trong', 'Trong', 'perak', 43, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('chenderiang', 'Chenderiang', 'perak', 44, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ipoh', 'Ipoh', 'perak', 45, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tapah', 'Tapah', 'perak', 46, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pengkalan-hulu', 'Pengkalan Hulu', 'perak', 47, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-piandang', 'Tanjong Piandang', 'perak', 48, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('sauk', 'Sauk', 'perak', 49, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang', 'Simpang', 'perak', 50, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuala-kurau', 'Kuala Kurau', 'perak', 51, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('changkat-jering', 'Changkat Jering', 'perak', 52, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kampung-kepayang', 'Kampung Kepayang', 'perak', 53, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('temoh', 'Temoh', 'perak', 54, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('enggor', 'Enggor', 'perak', 55, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kampar', 'Kampar', 'perak', 56, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bota', 'Bota', 'perak', 57, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('gopeng', 'Gopeng', 'perak', 58, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('parit', 'Parit', 'perak', 59, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('pangkor', 'Pangkor', 'perak', 60, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('parit-buntar', 'Parit Buntar', 'perak', 61, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lumut', 'Lumut', 'perak', 62, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('mambang-di-awan', 'Mambang Di Awan', 'perak', 63, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tronoh', 'Tronoh', 'perak', 64, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('selama', 'Selama', 'perak', 65, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('matang', 'Matang', 'perak', 66, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lenggong', 'Lenggong', 'perak', 67, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('slim-river', 'Slim River', 'perak', 68, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tldm-lumut', 'Tldm Lumut', 'perak', 69, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('changkat-keruing', 'Changkat Keruing', 'perak', 70, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bandar-seri-iskandar', 'Bandar Seri Iskandar', 'perak', 71, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('bagan-serai', 'Bagan Serai', 'perak', 72, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanjong-malim', 'Tanjong Malim', 'perak', 73, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('seri-manjong', 'Seri Manjong', 'perak', 74, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('ayer-hitam', 'Ayer Hitam', 'kedah', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('serdang', 'Serdang', 'kedah', 30, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jerantut', 'Jerantut', 'pahang', 25, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('kuantan', 'Kuantan', 'pahang', 28, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('tanah-rata', 'Tanah Rata', 'pahang', 37, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('rantau-panjang', 'Rantau Panjang', 'kelantan', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('cheras', 'Cheras', 'selangor', 12, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('jeram', 'Jeram', 'selangor', 22, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('batu-caves', 'Batu Caves', 'selangor', 43, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('simpang-ampat', 'Simpang Ampat', 'pulau-pinang', 5, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('lukut', 'Lukut', 'negeri-sembilan', 26, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
INSERT INTO area
(code, name, parent_code, `order`, create_time, update_time)
VALUES('johor-bahru', 'Johor Bahru', 'johor', 27, '2025-05-21 18:39:29', '2025-05-21 18:39:29');
-- Product Related
delete from product;
delete from product_category;
delete from product_attribute;
delete from product_attribute_value;
delete from product_attribute_display;
delete from product_attribute_option;
delete from product_detail_image;
delete from package_product;

-- insert category
insert into product_category (id, name) values (1, 'Engine Oil');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1, 'SAE Grade', 'select', 1, 1, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (2, 'API', 'select', 1, 1, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (3, 'ACEA', 'input', 1, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (4, 'OEM Approvals', 'input', 1, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (5, 'ILSAC', 'input', 1, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (6, 'Oil Type', 'select', 1, 1, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (7, 'Expiration Date', 'date', 1, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (8, 'Model', 'select', 1, 1, 1);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '1', '0W-20', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '2', '0W-30', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '3', '0W-40', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '4', '5W-20', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '5', '5W-30', 5);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '6', '5W-40', 6);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '7', '10W-30', 7);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '8', '10W-40', 8);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '9', '15W-40', 9);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1, '10', '20W-50', 10);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '1', 'SL', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '2', 'SM', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '3', 'SN', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '4', 'SO', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '5', 'SP', 5);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (2, '6', 'SQ', 6);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (6, '1', 'Mineral', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (6, '2', 'Semi-Synthetic', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (6, '3', 'Full Synthetic', 3);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '1', 'PREMIUM X7 0W20 SP', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '2', 'MAGNETEC STOP-START 0W20 SN', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '3', 'MEGA X6 10W30 SN', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '4', 'MEGA X6 10W40 SP', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '5', 'SHELL HELIX HX7 10W40 SN', 5);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '6', 'GTX PROFESSIONAL 10W40 SN', 6);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '7', 'MAGNATEC 10W40 SN', 7);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '8', 'SHELL HELIX HX5 15W40 SN', 8);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '9', 'GTX ULTRACLEAN 15W40 SN/CF', 9);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '10', 'PREMIUM X7 5W30 SP', 10);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '11', 'SHELL HELIX HX7 5W30 SN', 11);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '12', 'GTX ULTRACLEAN 5W30 SN/CF', 12);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '13', 'MAGNATEC STOP-START 5W30 SN', 13);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '14', 'SHELL HELIX ULTRA 5W30 SP', 14);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '15', 'PREMIUM X7 5W40 SN', 15);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '16', 'SHELL HELIX HX7 5W40 SN', 16);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '17', 'SHELL HELIX ULTRA 5W40 SP', 17);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (8, '18', 'MAGNATEC 5W40 SP', 18);


-- insert category
insert into product_category (id, name) values (2, 'Oil Filter');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (100, 'Material', 'input', 2, 2, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (101, 'Product Grade', 'input', 2, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (102, 'Dimensions', 'input', 2, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (103, 'Thread Size', 'input', 2, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (104, 'Sealing Ring Diameter', 'input', 2, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (105, 'Filter Length', 'input', 2, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (106, 'Item Weight', 'input', 2, 2, 9);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (107, 'Original Catalog Number', 'input', 2, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (108, 'Model', 'input', 2, 1, 2);


-- insert category
insert into product_category (id, name) values (3, 'Tire');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (200, 'Tire Size', 'input', 3, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (201, 'Load Index', 'input', 3, 2, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (202, 'Speed Rating', 'input', 3, 2, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (203, 'Tire Type', 'input', 3, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (204, 'Tread Pattern', 'input', 3, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (205, 'Treadwear', 'input', 3, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (206, 'Traction', 'input', 3, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (207, 'Temperature', 'input', 3, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (208, 'Production Date', 'date', 3, 2, 9);


-- insert category
insert into product_category (id, name) values (4, 'Paint Protection Film (PPF)');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (300, 'Size', 'select', 4, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (301, 'Color', 'input', 4, 1, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (302, 'Thickness', 'select', 4, 1, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (303, 'Materials', 'select', 4, 1, 4);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (300, '1', 'S', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (300, '2', 'M', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (300, '3', 'L', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (300, '4', 'MPV', 4);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (302, '1', '7.5 μ', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (302, '2', '8.5 μ', 2);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (303, '1', 'TPU', 1);


-- insert category
insert into product_category (id, name) values (5, 'Wrap Film');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (400, 'Size', 'select', 5, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (401, 'Color', 'input', 5, 1, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (402, 'Thickness', 'select', 5, 1, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (403, 'Materials', 'select', 5, 1, 4);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (400, '1', 'S', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (400, '2', 'M', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (400, '3', 'L', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (400, '4', 'MPV', 4);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (402, '1', '7.5 μ', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (402, '2', '8.5 μ', 2);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (403, '1', 'TPU', 1);


-- insert category
insert into product_category (id, name) values (6, 'Window Film');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (500, 'Size', 'select', 6, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (501, 'Front IRR', 'input', 6, 2, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (502, 'Front VLT', 'select', 6, 2, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (503, 'Front Material', 'input', 6, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (504, 'front side IRR', 'input', 6, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (505, 'front side VLT', 'input', 6, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (506, 'front side ', 'input', 6, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (507, 'Side And Rear Insulation Rate', 'input', 6, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (508, 'Side Rear Light Transmittance', 'input', 6, 2, 9);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (509, 'Side Rear Guard Material', 'input', 6, 2, 10);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (510, 'Ultraviolet Light Protection', 'input', 6, 2, 11);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (511, 'TSER', 'input', 6, 2, 12);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (500, '1', 'S', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (500, '2', 'M', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (500, '3', 'L', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (500, '4', 'MPV', 4);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (502, '1', '70%', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (502, '2', '50%', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (502, '3', '35%', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (502, '4', '15%', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (502, '5', '5%', 5);



-- insert category
insert into product_category (id, name) values (7, 'Dash Cam');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (600, 'Installation Type', 'input', 7, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (601, 'Power Supply', 'input', 7, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (602, 'Wide Angle', 'input', 7, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (603, 'Resolution', 'select', 7, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (604, 'Night Vision Function', 'input', 7, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (605, 'Orientation', 'select', 7, 1, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (606, 'Built-in Storage ', 'input', 7, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (607, 'Support Expansion (SD card)', 'select', 7, 2, 9);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (608, 'Emergency Lock Sensitivity', 'input', 7, 2, 10);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (609, 'ADAS Driving Assistance', 'input', 7, 2, 11);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (610, 'Parking Nonitoring (voltage reduction line)', 'input', 7, 1, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (611, 'GPS Track Record', 'input', 7, 2, 12);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (612, 'Production Date', 'date', 7, 2, 13);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (603, '1', '720P', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (603, '2', '1080P', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (603, '3', '2K', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (603, '4', '4K', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (603, '5', '8K', 5);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (605, '1', 'Front', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (605, '2', 'Front and Rear', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (605, '3', 'Front and Rear and Inside', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (605, '4', '360 Degree', 4);

insert into product_attribute_option (attribute_id, value, label, sort_order) values (607, '1', '16GB', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (607, '2', '32GB', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (607, '3', '64GB', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (607, '4', '128GB', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (607, '5', '256GB', 5);


-- insert category
insert into product_category (id, name) values (8, 'Dash Cam SD Card');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (700, 'Color', 'input', 8, 2, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (701, 'Read Speed', 'input', 8, 2, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (702, 'Hardware Interface', 'input', 8, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (703, 'Secure Digital Association Speed Class', 'input', 8, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (704, 'Product Dimensions', 'input', 8, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (705, 'RAM', 'select', 8, 1, 1);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (705, '1', '16GB', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (705, '2', '32GB', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (705, '3', '64GB', 3);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (705, '4', '128GB', 4);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (705, '5', '256GB', 5);


-- insert category
insert into product_category (id, name) values (9, 'Dash Cam Data Cable');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (800, 'Connector Type', 'select', 9, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (801, 'Maximum Voltage', 'input', 9, 2, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (802, 'Length', 'input', 9, 2, 3);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (800, '1', 'MINI USB', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (800, '2', 'MICRO USB', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (800, '3', 'TYPE C', 3);


-- insert category
insert into product_category (id, name) values (10, 'Foot Pad');
-- insert attribute
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (900, 'Parts', 'select', 10, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (901, 'Model', 'input', 10, 2, 2);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (902, 'Material', 'input', 10, 2, 3);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (903, 'Fixing Method', 'input', 10, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (904, 'Production Date', 'date', 10, 2, 5);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, image, sort_order) values (900, '1', 'Front+Rear', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/d1ce09df8fddbb2712b9e896f8a088a9c0faaf10d9edf59251700d455ce6d41b.svg', 1);
insert into product_attribute_option (attribute_id, value, label, image, sort_order) values (900, '2', 'All included', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/c8b1e23e530c194fb07af626445c467c831caea010b5278795ee9b8561d37031.svg', 2);
insert into product_attribute_option (attribute_id, value, label, image, sort_order) values (900, '3', 'Driver', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/9bb1e6d062488e854050de5b4c701bc5a242c5390646ecdbbc48652079e4139a.svg', 3);
insert into product_attribute_option (attribute_id, value, label, image, sort_order) values (900, '4', 'Back+Rear', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/6bbf014a5d490af9a3f85526be5f78de134eb4ee999abed755e8920b8ecb5566.svg', 4);


-- insert category
insert into product_category (id, name) values (11, 'Wiper');
-- insert attribute
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1000, 'Length (main driver)', 'mm', 'number',11, 2, 2);
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1001, 'Length (front passenger)', 'mm', 'number', 11, 2, 3);
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1002, 'Length (rear wiper)', 'mm', 'number', 11, 2, 4);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1003, 'Type', 'select', 11, 1, 1);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1004, 'Mounting System', 'input', 11, 2, 5);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1005, 'Beam/Frame', 'input', 11, 2, 6);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1006, 'Rubber Compound', 'input', 11, 2, 7);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1007, 'Pressure Point Distribution', 'input', 11, 2, 8);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1008, 'Spring Tension', 'input', 11, 2, 9);
insert into product_attribute (id, name, type, category_id, is_required, `order`)
values (1009, 'Production Date', 'date', 11, 2, 10);
-- insert attribute option
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1003, '1', 'main driver/front driver', 1);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1003, '2', 'rear wiper', 2);
insert into product_attribute_option (attribute_id, value, label, sort_order) values (1003, '3', 'main driver/front driver/rear wiper', 3);


-- insert category
insert into product_category (id, name) values (12, 'Windshield Fluid');

-- insert category
insert into product_category (id, name) values (13, 'Cleaning Cloths');
-- insert attribute
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1200, 'Color', '', 'input', 13, 2, 1);
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1201, 'Dimensions', '', 'input', 13, 2, 2);
insert into product_attribute (id, name, suffix, type, category_id, is_required, `order`)
values (1202, 'Material', '', 'input', 13, 2, 3);


-- insert attribute display
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 1, '1,6');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 2, '100,101,102');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 3, '200,201');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 4, '300,301');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 5, '400,401');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 6, '500,501');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 7, '600,601');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 8, '700,701');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 9, '800,801');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 10, '900,901');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('service', 11, '1000,1001');

insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 1, '1,6');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 2, '100,101,102');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 3, '200,201');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 4, '300,301');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 5, '400,401');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 6, '500,501');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 7, '600,601');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 8, '700,701');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 9, '800,801');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 10, '900,901');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('package', 11, '1000,1001');

-- 商品顶部 filter
insert into product_attribute_display (type, category_id, attribute_ids)
values ('customer/product-top-filter', 10, '900');

-- 商品 filter
insert into product_attribute_display (type, category_id, attribute_ids)
values ('customer/product-filter', 1, '1,2,6');
insert into product_attribute_display (type, category_id, attribute_ids)
values ('customer/product-filter', 7, '603,605');

-- 商品主标签
insert into product_attribute_display (type, category_id, attribute_ids)
values ('customer/products/label', 3, '200');

-- 商品 SKU
insert into product_attribute_display (type, category_id, attribute_ids)
values ('customer/products/sku-attr', 5, '401');


-- 模板

INSERT INTO content_template
(id, code, title, enabled, create_time, update_time, deleted)
VALUES(645, 'HOME_PAGE', 'Home Page', 1, '2025-03-31 14:51:42', '2025-03-31 14:52:01', 0);


INSERT INTO content_layout
(id, template_code, title, seq, enabled, `type`, create_time, update_time, deleted)
VALUES(645, 'HOME_PAGE', 'home page banners', 1, 1, 'BANNER', '2025-03-31 14:53:22', '2025-03-31 14:53:22', 0);
INSERT INTO content_layout
(id, template_code, title, seq, enabled, `type`, create_time, update_time, deleted)
VALUES(646, 'HOME_PAGE', 'home page channels', 2, 1, 'CHANNEL', '2025-03-31 17:45:25', '2025-03-31 17:45:25', 0);


INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(645, 645, 645, 1, 1, '2025-03-31 14:54:21', '2025-03-31 14:54:21', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(646, 646, 646, 1, 1, '2025-03-31 17:46:15', '2025-04-08 11:27:17', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(647, 646, 647, 2, 1, '2025-04-08 11:26:28', '2025-04-08 11:27:43', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(648, 646, 648, 3, 1, '2025-04-08 11:26:28', '2025-04-08 11:27:43', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(649, 646, 649, 4, 1, '2025-04-08 11:26:28', '2025-04-08 11:27:43', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(650, 646, 650, 5, 1, '2025-04-08 11:26:28', '2025-04-08 11:27:43', 0);
INSERT INTO content_layout_resource
(id, layout_id, resource_id, resource_seq, enabled, create_time, update_time, deleted)
VALUES(651, 646, 651, 6, 1, '2025-04-08 11:26:28', '2025-04-08 11:27:43', 0);

INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(645, 'banner', 'BANNER', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/c33899e0739ef63520b446d4e4d340c405b3ae561b83a7f7244e325789b482f5.png', '', 'WEB', '{"title":"Car Care Expert with <b>ServAuto</b>","subTitle":["Transparent Pricing","AfterSales Guarantee"]}', '2025-03-31 14:54:05', '2025-05-27 18:41:10', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(646, 'Maintenance', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/35767305ed9dbe7e3129a5350c8b5d5640e648af62cd9626f644bd01cc7000ec.svg', '/packages', 'WEB', '', '2025-03-31 17:45:57', '2025-05-20 12:01:57', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(647, 'Tires', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/fec80a64295aa86e3114a77191bd4db435e75659d9055767fdd2ba29c0b779f9.svg', 'products/3', 'WEB', '', '2025-04-08 11:25:12', '2025-05-20 12:01:57', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(648, 'Solar Film', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/5ddf4c641322b725acf1a457a73f01f3b4a6a2c7e9fcff08d96eef5ea6f86a00.svg', 'products/4', 'WEB', '{"extLabel": true, "values": [{"id":"4","name":"Paint Protection Film (PPF)"},{"id":"5","name":"Wrap Film"},{"id":"6","name":"Window Film"}]} ', '2025-04-08 11:25:12', '2025-05-20 12:01:57', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(649, 'Dashcam', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/b6d294710df8d5046c926950870dc6e8b97b6a0c5d4dd32cac148e7f53c3cc36.svg', 'products/7', 'WEB', '', '2025-04-08 11:25:12', '2025-05-20 12:01:57', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(650, 'Foot Pad', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/2fd0e10383996c1d2e1dafb1041f1d8e3c4073e44fda4ae73a9f7de205106e87.svg', 'products/10', 'WEB', '', '2025-04-08 11:25:12', '2025-05-20 12:01:57', 0);
INSERT INTO content_resource
(id, title, `type`, pict_uri, entry_uri, entry_type, ext, create_time, update_time, deleted)
VALUES(651, 'Wiper', 'CHANNEL', 'https://servauto-dev.oss-cn-zhangjiakou.aliyuncs.com/368cd8ebb0836d9ac39733e7c886a176a734a20fb5e407c9e3b4b6258b1ba19c.svg', 'products/11', 'WEB', '', '2025-04-08 11:25:12', '2025-05-20 12:01:57', 0);



INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(1, 1, 'Male', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-02-28 19:43:56', '', NULL, 'Male gender');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(2, 2, 'Female', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-02-28 19:43:57', '', NULL, 'Female gender');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(3, 3, 'Unknown', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-02-28 19:43:57', '', NULL, 'Unknown gender');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(4, 1, 'Show', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-02-28 19:43:57', '', NULL, 'Show menu');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(5, 2, 'Hide', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:43:57', '', NULL, 'Hide menu');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(6, 1, 'Normal', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2025-02-28 19:43:58', '', NULL, 'Normal status');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(7, 2, 'Disabled', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:43:58', '', NULL, 'Disabled status');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(12, 1, 'Yes', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-02-28 19:43:59', '', NULL, 'System default yes');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(13, 2, 'No', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:43:59', '', NULL, 'System default no');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(18, 99, 'Other', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-02-28 19:44:01', '', NULL, 'Other operations');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(19, 1, 'Add', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-02-28 19:44:01', '', NULL, 'Add operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(20, 2, 'Modify', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-02-28 19:44:01', '', NULL, 'Modify operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(21, 3, 'Delete', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:44:01', '', NULL, 'Delete operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(22, 4, 'Authorize', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-02-28 19:44:02', '', NULL, 'Authorize operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(23, 5, 'Export', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-02-28 19:44:02', '', NULL, 'Export operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(24, 6, 'Import', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-02-28 19:44:02', '', NULL, 'Import operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(25, 7, 'Force logout', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:44:02', '', NULL, 'Force logout operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(26, 8, 'Generate code', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-02-28 19:44:02', '', NULL, 'Generate code operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(27, 9, 'Clear data', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:44:03', '', NULL, 'Clear data operation');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(28, 1, 'Success', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-02-28 19:44:03', '', NULL, 'Normal status');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(29, 2, 'Failure', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-02-28 19:44:03', '', NULL, 'Failure status');
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(103, 0, 'Product', '1', 'service_support_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-31 16:43:05', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(104, 0, 'Package', '2', 'service_support_type', NULL, NULL, 'N', '0', 'admin', '2025-03-31 16:44:02', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(105, 0, 'Yes', '1', 'services_must_buy', NULL, NULL, 'N', '0', 'admin', '2025-03-31 16:45:00', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(106, 0, 'No', '2', 'services_must_buy', NULL, NULL, 'N', '0', 'admin', '2025-03-31 16:45:08', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(107, 0, 'Listed', '1', 'package_status', NULL, NULL, 'N', '0', 'admin', '2025-04-01 22:31:01', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(108, 0, 'Unlisted', '2', 'package_status', NULL, NULL, 'N', '0', 'admin', '2025-04-01 22:31:28', 'admin', '2025-04-01 23:53:43', NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(109, 0, 'Self Pickup', '1', 'package_delivery_type', NULL, NULL, 'N', '0', 'admin', '2025-04-01 23:51:00', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(110, 0, 'Mailing', '2', 'package_delivery_type', NULL, NULL, 'N', '0', 'admin', '2025-04-01 23:51:08', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(111, 0, 'Tag1', '1', 'product_tags', NULL, NULL, 'N', '0', 'admin', '2025-04-02 17:15:47', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(112, 0, 'Tag2', '2', 'product_tags', NULL, NULL, 'N', '0', 'admin', '2025-04-02 17:15:58', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(113, 0, 'Tag3', '3', 'product_tags', NULL, NULL, 'N', '0', 'admin', '2025-04-02 17:16:05', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(114, 0, 'ServAuto', '1', 'workshop_type', NULL, NULL, 'N', '0', 'admin', '2025-04-02 19:51:30', 'admin', '2025-05-26 15:37:05', NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(115, 0, 'Partner', '2', 'workshop_type', NULL, NULL, 'N', '0', 'admin', '2025-04-02 19:51:38', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(116, 0, 'Franchise', '3', 'workshop_type', NULL, NULL, 'N', '0', 'admin', '2025-04-02 19:51:46', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(117, 0, 'Active', '1', 'workshop_status', NULL, NULL, 'N', '0', 'admin', '2025-04-02 20:15:34', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(118, 0, 'Inactive', '2', 'workshop_status', NULL, NULL, 'N', '0', 'admin', '2025-04-02 20:15:42', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(119, 0, 'Tags1', '1', 'workshop_featured_tags', NULL, NULL, 'N', '0', 'admin', '2025-04-02 20:16:44', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(120, 0, 'Tags2', '2', 'workshop_featured_tags', NULL, NULL, 'N', '0', 'admin', '2025-04-02 20:16:51', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(121, 0, 'Pending Payment', 'PENDING_PAY', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:22:01', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(122, 0, 'Pending Delivery', 'PENDING_DELIVERY', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:22:14', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(123, 0, 'Order Completed', 'COMPLETED', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:22:29', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(124, 0, 'Despatched', 'DESPATCHED', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:22:38', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(125, 0, 'Canceled', 'CANCELED', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:22:53', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(126, 0, 'Order Closed', 'FINISHED', 'order_status', NULL, NULL, 'N', '0', 'admin', '2025-04-23 21:23:05', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(127, 0, 'label test1', 'label value1', 'product_featured_tag', NULL, NULL, 'N', '0', 'admin', '2025-05-14 11:06:12', '', NULL, NULL);
INSERT INTO sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(129, 0, 'label test2', 'value test2', 'product_featured_tag', NULL, NULL, 'N', '0', 'admin', '2025-05-14 11:07:06', '', NULL, NULL);


INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(1, 'User Gender', 'sys_user_sex', '0', 'admin', '2025-02-28 19:43:54', '', NULL, 'User Gender List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(2, 'Menu Status', 'sys_show_hide', '0', 'admin', '2025-02-28 19:43:54', '', NULL, 'Menu Status List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(3, 'System Switch', 'sys_normal_disable', '0', 'admin', '2025-02-28 19:43:55', '', NULL, 'System Switch List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(6, 'System Yes/No', 'sys_yes_no', '0', 'admin', '2025-02-28 19:43:55', '', NULL, 'System Yes/No List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(9, 'Operation Type', 'sys_oper_type', '0', 'admin', '2025-02-28 19:43:56', '', NULL, 'Operation Type List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(10, 'System Status', 'sys_common_status', '0', 'admin', '2025-02-28 19:43:56', 'admin', '2025-03-07 16:45:25', 'Login Status List');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(102, 'Service Supported Type', 'service_support_type', '0', 'admin', '2025-03-31 16:38:01', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(103, 'Services Must Buy', 'services_must_buy', '0', 'admin', '2025-03-31 16:44:46', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(104, 'Package Status', 'package_status', '0', 'admin', '2025-04-01 22:30:33', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(105, 'Package Delivery Type', 'package_delivery_type', '0', 'admin', '2025-04-01 23:50:21', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(106, 'Product Tag', 'product_tags', '0', 'admin', '2025-04-02 17:15:25', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(107, 'Workshop Type', 'workshop_type', '0', 'admin', '2025-04-02 19:51:18', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(108, 'Workshop Status', 'workshop_status', '0', 'admin', '2025-04-02 20:15:06', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(109, 'Workshop Featured Tags', 'workshop_featured_tags', '0', 'admin', '2025-04-02 20:16:26', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(110, 'Order status', 'order_status', '0', 'admin', '2025-04-23 21:21:21', '', NULL, NULL);
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(111, 'Product Featured Tag', 'product_featured_tag', '0', 'admin', '2025-05-14 11:04:38', '', NULL, NULL);



INSERT INTO system_file_config
(id, name, storage, remark, master, config, create_time, update_time, deleted)
VALUES(29, 'db store', 1, '', 1, '{"@class":"com.servauto.framework.file.factory.db.DBFileClientConfig","domain":"http://39.99.196.38:8080"}', '2025-03-04 11:07:17', '2025-04-24 16:02:30', 0);
INSERT INTO system_file_config
(id, name, storage, remark, master, config, create_time, update_time, deleted)
VALUES(30, 'aliyun', 20, NULL, 0, '{"@class":"com.servauto.framework.file.factory.s3.S3FileClientConfig","endpoint":"oss-cn-zhangjiakou.aliyuncs.com","domain":"","bucket":"servauto-dev","accessKey":"LTAI5tEhJdj6PrCuiFAF8J3R","accessSecret":"******************************"}', '2025-04-24 16:02:30', '2025-04-24 16:02:30', 0);


INSERT INTO system_sms_channel
(id, signature, code, status, remark, api_key, api_secret, callback_url, create_time, update_time, deleted)
VALUES(1, '', 'YCloud-SMS', 1, '', 'cf44a44c6aee9ee3aaf608d751880e34', '', 'http://39.98.70.150:8080/sys/sms/callback/ycloud-sms', '2025-03-04 11:16:55', '2025-05-23 11:19:16', 0);
INSERT INTO system_sms_channel
(id, signature, code, status, remark, api_key, api_secret, callback_url, create_time, update_time, deleted)
VALUES(2, '', 'YCloud_WHATSAPP', 1, '+601128407382', 'cf44a44c6aee9ee3aaf608d751880e34', '', NULL, '2025-03-04 11:16:55', '2025-05-23 11:19:16', 0);


INSERT INTO system_sms_template
(id, `type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code, create_time, update_time, deleted)
VALUES(20, 1, 0, 'user-mobile-login', 'customer mobile login', 'Hi! Your ServAuto registration code is {code}. Use it within 10 minutes to complete signup. Safe shopping awaits!', '["code"]', NULL, '', 1, 'YCloud-SMS', '2025-03-04 11:16:55', '2025-05-14 14:53:55', 0);
INSERT INTO system_sms_template
(id, `type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code, create_time, update_time, deleted)
VALUES(21, 1, 0, 'user-whatsapp-login', 'customer whatsapp login', 'Hi! Your ServAuto registration code is {code}. Use it within 10 minutes to complete signup. Safe shopping awaits!', '["code"]', NULL, 'template_whatsapp_login', 2, 'YCloud_WHATSAPP', '2025-03-04 11:16:55', '2025-05-14 15:26:03', 0);
INSERT INTO system_sms_template
(id, `type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code, create_time, update_time, deleted)
VALUES(22, 2, 0, 'HQ-OPS', 'HQ-OPS', '', '["orderNo","orderTime","licensePlate","workshopName","reservationTime","serviceName"]', NULL, 'order_created_hqops', 2, 'YCloud_WHATSAPP', '2025-05-12 14:55:21', '2025-05-14 16:23:58', 0);











