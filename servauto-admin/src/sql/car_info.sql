create table car_info
(
    id                    bigint unsigned auto_increment
        primary key,
    car_lib_id            varchar(20)                                not null comment '车辆编码',
    customer_id           bigint unsigned                            not null,
    car_source            tinyint                                    null,
    brand_id              int                                        not null,
    model_id              int                                        not null,
    year_id               int                                        not null,
    variant_id            int                                        not null,
    car_type_id           int                                        null,
    car_variant           varchar(100)                               null comment '车辆型号',
    car_engine            varchar(22)                                null comment '排量',
    car_mileage           int unsigned                               null,
    car_transmission      int                                        null,
    registration_date     datetime                                   null comment '注册日期',
    registration_type     int unsigned                               null,
    description           text collate utf8mb4_unicode_ci            null comment '第三方车描述',
    seat                  int unsigned                               null,
    src_seat              int unsigned                               null,
    color                 int unsigned                               null,
    fuel_type             int unsigned                               null,
    vin_code              varchar(45)                                null comment 'vin码',
    engine_no             varchar(64)                                null comment 'Engine No || Engine No',
    car_type              tinyint(1)       default 0                 null comment 'car type || 车辆类型',
    service_book          tinyint unsigned                           null,
    license_plate         varchar(45)                                null comment '车架号',
    image                 varchar(200)                               null comment '头图',
    source                varchar(255)                               null comment '来源',
    location              varchar(500)                               null comment '城市地区',
    location_address      varchar(500)                               null comment '详细地址，如：地下二层B105停车位',
    location_id           varchar(100)                               null comment '地址id',
    next_maintenance_date datetime                                   null comment '下次保养日期',
    status                tinyint unsigned default '1'               null,
    default_car           tinyint                                    null,
    source_info           text                                       null,
    update_time           timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    create_time           timestamp        default CURRENT_TIMESTAMP not null comment '创建时间'
)
    collate = utf8_unicode_ci;

create index car_info_customer_id_index
    on car_info (customer_id);
