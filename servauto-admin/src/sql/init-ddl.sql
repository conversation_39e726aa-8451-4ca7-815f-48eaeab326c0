-- ----------------------------
-- 1、User Information Table
-- ----------------------------
DROP TABLE IF EXISTS sys_user;

CREATE TABLE sys_user
(
    user_id     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'User ID',
    user_name   varchar(30) NOT NULL COMMENT 'User Account',
    nick_name   varchar(30) NOT NULL COMMENT 'User Nickname',
    user_type   varchar(2)   DEFAULT '00' COMMENT 'User Type (00: System User)',
    email       varchar(50)  DEFAULT '' COMMENT 'User Email',
    mobile      varchar(11)  DEFAULT '' COMMENT 'Mobile Phone Number',
    sex         char(1)      DEFAULT '0' COMMENT 'User Gender (0: Male, 1: Female, 2: Unknown)',
    avatar      varchar(100) DEFAULT '' COMMENT 'Avatar Address',
    password    varchar(100) DEFAULT '' COMMENT 'Password',
    status      char(1)      DEFAULT '0' COMMENT 'Account Status (0: Normal, 1: Disabled)',
    del_flag    char(1)      DEFAULT '0' COMMENT 'Deletion Flag (0: Exists, 2: Deleted)',
    login_ip    varchar(128) DEFAULT '' COMMENT 'Last Login IP',
    login_date  datetime COMMENT 'Last Login Time',
    create_by   varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time datetime COMMENT 'Creation Time',
    update_by   varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time datetime COMMENT 'Update Time',
    remark      varchar(500) DEFAULT NULL COMMENT 'Remarks',
    PRIMARY KEY (user_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'User Information Table';

-- ----------------------------
-- 2、Role Information Table
-- ----------------------------
DROP TABLE IF EXISTS sys_role;

CREATE TABLE sys_role
(
    role_id     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'Role ID',
    role_name   varchar(30)  NOT NULL COMMENT 'Role Name',
    role_key    varchar(100) NOT NULL COMMENT 'Role Permission String',
    role_sort   int(4)       NOT NULL COMMENT 'Display Order',
    status      char(1)      NOT NULL COMMENT 'Role Status (0: Normal, 1: Disabled)',
    del_flag    char(1)      DEFAULT '0' COMMENT 'Deletion Flag (0: Exists, 2: Deleted)',
    create_by   varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time datetime COMMENT 'Creation Time',
    update_by   varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time datetime COMMENT 'Update Time',
    remark      varchar(500) DEFAULT NULL COMMENT 'Remarks',
    PRIMARY KEY (role_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'Role Information Table';

-- ----------------------------
-- 2、Menu Permission Table
-- ----------------------------
DROP TABLE IF EXISTS sys_menu;

CREATE TABLE sys_menu
(
    menu_id     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'Menu ID',
    menu_name   varchar(50) NOT NULL COMMENT 'Menu Name',
    parent_id   bigint(20)   DEFAULT 0 COMMENT 'Parent Menu ID',
    order_num   int(4)       DEFAULT 0 COMMENT 'Display Order',
    path        varchar(200) DEFAULT '' COMMENT 'Routing Address',
    component   varchar(255) DEFAULT NULL COMMENT 'Component Path',
    query       varchar(255) DEFAULT NULL COMMENT 'Routing Parameters',
    route_name  varchar(50)  DEFAULT '' COMMENT 'Routing Name',
    is_frame    int(1)       DEFAULT 1 COMMENT 'Whether it is an external link (0: Yes, 1: No)',
    is_cache    int(1)       DEFAULT 0 COMMENT 'Whether to cache (0: Cache, 1: Do not cache)',
    menu_type   char(1)      DEFAULT '' COMMENT 'Menu Type (M: Directory, C: Menu, F: Button)',
    visible     char(1)      DEFAULT '0' COMMENT 'Menu Visibility (0: Display, 1: Hide)',
    status      char(1)      DEFAULT '0' COMMENT 'Menu Status (0: Normal, 1: Disabled)',
    perms       varchar(100) DEFAULT NULL COMMENT 'Permission Identifier',
    icon        varchar(100) DEFAULT '#' COMMENT 'Menu Icon',
    create_by   varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time datetime COMMENT 'Creation Time',
    update_by   varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time datetime COMMENT 'Update Time',
    remark      varchar(500) DEFAULT '' COMMENT 'Remarks',
    PRIMARY KEY (menu_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2000 COMMENT 'Menu Table';

-- ----------------------------
-- 3、User - Role Association Table  User N - 1 Role
-- ----------------------------
DROP TABLE IF EXISTS sys_user_role;

CREATE TABLE sys_user_role
(
    user_id bigint(20) NOT NULL COMMENT 'User ID',
    role_id bigint(20) NOT NULL COMMENT 'Role ID',
    PRIMARY KEY (user_id, role_id)
) ENGINE = InnoDB COMMENT 'User - Role Association Table';

-- ----------------------------
-- 7、Role - Menu Association Table  Role 1 - N Menu
-- ----------------------------
DROP TABLE IF EXISTS sys_role_menu;

CREATE TABLE sys_role_menu
(
    role_id bigint(20) NOT NULL COMMENT 'Role ID',
    menu_id bigint(20) NOT NULL COMMENT 'Menu ID',
    PRIMARY KEY (role_id, menu_id)
) ENGINE = InnoDB COMMENT 'Role - Menu Association Table';

-- ----------------------------
-- 10、Operation Log Record
-- ----------------------------
DROP TABLE IF EXISTS sys_oper_log;

CREATE TABLE sys_oper_log
(
    oper_id        bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Log Primary Key',
    title          varchar(50)   DEFAULT '' COMMENT 'Module Title',
    business_type  int(2)        DEFAULT 0 COMMENT 'Business Type (0: Other, 1: Add, 2: Modify, 3: Delete)',
    method         varchar(200)  DEFAULT '' COMMENT 'Method Name',
    request_method varchar(10)   DEFAULT '' COMMENT 'Request Method',
    operator_type  int(1)        DEFAULT 0 COMMENT 'Operation Category (0: Other, 1: Back - end User, 2: Mobile User)',
    oper_name      varchar(50)   DEFAULT '' COMMENT 'Operator Name',
    oper_url       varchar(255)  DEFAULT '' COMMENT 'Request URL',
    oper_ip        varchar(128)  DEFAULT '' COMMENT 'Host Address',
    oper_location  varchar(255)  DEFAULT '' COMMENT 'Operation Location',
    oper_param     varchar(2000) DEFAULT '' COMMENT 'Request Parameters',
    json_result    varchar(2000) DEFAULT '' COMMENT 'Return Parameters',
    status         int(1)        DEFAULT 0 COMMENT 'Operation Status (0: Normal, 1: Abnormal)',
    error_msg      varchar(2000) DEFAULT '' COMMENT 'Error Message',
    oper_time      datetime COMMENT 'Operation Time',
    cost_time      bigint(20)    DEFAULT 0 COMMENT 'Elapsed Time',
    PRIMARY KEY (oper_id),
    KEY            idx_sys_oper_log_bt (business_type),
    KEY            idx_sys_oper_log_s (status),
    KEY            idx_sys_oper_log_ot (oper_time)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'Operation Log Record';

-- ----------------------------
-- 11、Dictionary Type Table
-- ----------------------------
DROP TABLE IF EXISTS sys_dict_type;

CREATE TABLE sys_dict_type
(
    dict_id     bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Dictionary Primary Key',
    dict_name   varchar(100) DEFAULT '' COMMENT 'Dictionary Name',
    dict_type   varchar(100) DEFAULT '' COMMENT 'Dictionary Type',
    status      char(1)      DEFAULT '0' COMMENT 'Status (0: Normal, 1: Disabled)',
    create_by   varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time datetime COMMENT 'Creation Time',
    update_by   varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time datetime COMMENT 'Update Time',
    remark      varchar(500) DEFAULT NULL COMMENT 'Remarks',
    PRIMARY KEY (dict_id),
    UNIQUE (dict_type)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'Dictionary Type Table';

-- ----------------------------
-- 12、Dictionary Data Table
-- ----------------------------
DROP TABLE IF EXISTS sys_dict_data;

CREATE TABLE sys_dict_data
(
    dict_code   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Dictionary Code',
    dict_sort   int(4)       DEFAULT 0 COMMENT 'Dictionary Sorting',
    dict_label  varchar(100) DEFAULT '' COMMENT 'Dictionary Label',
    dict_value  varchar(100) DEFAULT '' COMMENT 'Dictionary Key - Value',
    dict_type   varchar(100) DEFAULT '' COMMENT 'Dictionary Type',
    css_class   varchar(100) DEFAULT NULL COMMENT 'Style Attributes (Other Style Extensions)',
    list_class  varchar(100) DEFAULT NULL COMMENT 'Table Echo Style',
    is_default  char(1)      DEFAULT 'N' COMMENT 'Whether it is the default (Y: Yes, N: No)',
    status      char(1)      DEFAULT '0' COMMENT 'Status (0: Normal, 1: Disabled)',
    create_by   varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time datetime COMMENT 'Creation Time',
    update_by   varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time datetime COMMENT 'Update Time',
    remark      varchar(500) DEFAULT NULL COMMENT 'Remarks',
    PRIMARY KEY (dict_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'Dictionary Data Table';

-- ----------------------------
-- 13、Parameter Configuration Table
-- ----------------------------
DROP TABLE IF EXISTS sys_config;

CREATE TABLE sys_config
(
    config_id    int(5) NOT NULL AUTO_INCREMENT COMMENT 'Parameter Primary Key',
    config_name  varchar(100) DEFAULT '' COMMENT 'Parameter Name',
    config_key   varchar(100) DEFAULT '' COMMENT 'Parameter Key Name',
    config_value varchar(500) DEFAULT '' COMMENT 'Parameter Key - Value',
    config_type  char(1)      DEFAULT 'N' COMMENT 'System Built - in (Y: Yes, N: No)',
    create_by    varchar(64)  DEFAULT '' COMMENT 'Creator',
    create_time  datetime COMMENT 'Creation Time',
    update_by    varchar(64)  DEFAULT '' COMMENT 'Updater',
    update_time  datetime COMMENT 'Update Time',
    remark       varchar(500) DEFAULT NULL COMMENT 'Remarks',
    PRIMARY KEY (config_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'Parameter Configuration Table';

-- ----------------------------
-- 14、System Access Record
-- ----------------------------
DROP TABLE IF EXISTS sys_logininfor;

CREATE TABLE sys_logininfor
(
    info_id        bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Access ID',
    user_name      varchar(50)  DEFAULT '' COMMENT 'User Account',
    ipaddr         varchar(128) DEFAULT '' COMMENT 'Login IP Address',
    login_location varchar(255) DEFAULT '' COMMENT 'Login Location',
    browser        varchar(50)  DEFAULT '' COMMENT 'Browser Type',
    os             varchar(50)  DEFAULT '' COMMENT 'Operating System',
    status         char(1)      DEFAULT '0' COMMENT 'Login Status (0: Success, 1: Failure)',
    msg            varchar(255) DEFAULT '' COMMENT 'Prompt Message',
    login_time     datetime COMMENT 'Access Time',
    PRIMARY KEY (info_id),
    KEY            idx_sys_logininfor_s (status),
    KEY            idx_sys_logininfor_lt (login_time)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT 'System Access Record';


DROP TABLE IF EXISTS customer_info;
CREATE TABLE `customer_info`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT 'primary key | 主键',
    `real_name`   varchar(64) NOT NULL DEFAULT '' COMMENT 'real name | 客户姓名',
    `mobile`      varchar(16) NOT NULL DEFAULT '' COMMENT 'mobile | 手机号',
    `gender`      char(1)              DEFAULT '2' COMMENT 'User Gender (0: Male, 1: Female, 2: Unknown)',
    `source`      varchar(64)          DEFAULT 'DEFAULT' COMMENT 'User source',
    `birthday`    datetime             DEFAULT NULL COMMENT 'birthday | 生日',
    `create_time` timestamp NULL     DEFAULT CURRENT_TIMESTAMP COMMENT 'created time | 创建时间',
    `update_time` timestamp NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'last updated time | 最后修改时间',
    `deleted`     bit(1)      NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_mobile` (`mobile`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='customer info | 客户信息';

CREATE TABLE `customer_shipping_address`
(
    `id`          bigint        NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_id` bigint        NOT NULL DEFAULT '0' COMMENT 'customer id',
    `name`        varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee name',
    `mobile`      varchar(16)   NOT NULL DEFAULT '' COMMENT 'consignee mobile',
    `state_code`  varchar(255)  NOT NULL DEFAULT '' COMMENT 'state address',
    `city_code`   varchar(255)  NOT NULL DEFAULT '' COMMENT 'City Code',
    `address`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'Address',
    `is_default`  bit(1)        NOT NULL DEFAULT b'1' COMMENT 'is default 0=default 1=',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)        NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`),
    KEY           `idx_customer_id` (`customer_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='customer shipping address';

DROP TABLE IF EXISTS `system_sms_channel`;
CREATE TABLE `system_sms_channel`
(
    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `signature`    varchar(12)  NOT NULL COMMENT 'SMS signature',
    `code`         varchar(63)  NOT NULL COMMENT 'Channel code',
    `status`       tinyint      NOT NULL COMMENT 'Enabled status',
    `remark`       varchar(255) NULL     DEFAULT NULL COMMENT 'Remarks',
    `api_key`      varchar(128) NOT NULL COMMENT 'SMS API account',
    `api_secret`   varchar(128) NULL     DEFAULT NULL COMMENT 'SMS API secret key',
    `callback_url` varchar(255) NULL     DEFAULT NULL COMMENT 'SMS sending callback URL',
    `create_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`      bit(1)       NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 8
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'SMS channel';

DROP TABLE IF EXISTS `system_sms_template`;
CREATE TABLE `system_sms_template`
(
    `id`              bigint        NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `type`            tinyint       NOT NULL COMMENT 'Template type',
    `status`          tinyint       NOT NULL COMMENT 'Enabled status',
    `code`            varchar(63)   NOT NULL COMMENT 'Template code',
    `name`            varchar(63)   NOT NULL COMMENT 'Template name',
    `content`         varchar(1024) NOT NULL COMMENT 'Template content',
    `params`          varchar(1024) NOT NULL COMMENT 'Parameter array',
    `remark`          varchar(1024) NULL     DEFAULT NULL COMMENT 'Remarks',
    `api_template_id` varchar(63)   NOT NULL COMMENT 'SMS API template ID',
    `channel_id`      bigint        NOT NULL COMMENT 'SMS channel ID',
    `channel_code`    varchar(63)   NOT NULL COMMENT 'SMS channel code',
    `create_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`         bit(1)        NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 18
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'SMS template';


DROP TABLE IF EXISTS `system_sms_code`;
CREATE TABLE `system_sms_code`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `mobile`      varchar(16) NOT NULL COMMENT 'Mobile number',
    `code`        varchar(9)  NOT NULL COMMENT 'Verification code',
    `create_ip`   varchar(15) NOT NULL COMMENT 'Creation IP',
    `scene`       tinyint     NOT NULL COMMENT 'Sending scenario',
    `today_index` tinyint     NOT NULL COMMENT 'The number of the SMS sent today',
    `used`        tinyint     NOT NULL COMMENT 'Whether used',
    `used_time`   datetime NULL     DEFAULT NULL COMMENT 'Usage time',
    `used_ip`     varchar(255) NULL     DEFAULT NULL COMMENT 'Usage IP',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)      NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_mobile` (`mobile` ASC) USING BTREE COMMENT 'Mobile number'
) ENGINE = InnoDB
  AUTO_INCREMENT = 645
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'Mobile verification code';


DROP TABLE IF EXISTS `system_sms_log`;
CREATE TABLE `system_sms_log`
(
    `id`               bigint        NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `channel_id`       bigint        NOT NULL COMMENT 'SMS channel ID',
    `channel_code`     varchar(63)   NOT NULL COMMENT 'SMS channel code',
    `template_id`      bigint        NOT NULL COMMENT 'Template ID',
    `template_code`    varchar(63)   NOT NULL COMMENT 'Template code',
    `template_type`    tinyint       NOT NULL COMMENT 'SMS type',
    `template_content` varchar(1024) NOT NULL COMMENT 'SMS content',
    `template_params`  varchar(1024) NOT NULL COMMENT 'SMS parameters',
    `api_template_id`  varchar(63)   NOT NULL COMMENT 'SMS API template ID',
    `mobile`           varchar(16)   NOT NULL COMMENT 'Mobile number',
    `user_id`          bigint NULL     DEFAULT NULL COMMENT 'User ID',
    `user_type`        tinyint NULL     DEFAULT NULL COMMENT 'User type',
    `send_status`      tinyint       NOT NULL DEFAULT 0 COMMENT 'Sending status',
    `send_time`        datetime NULL     DEFAULT NULL COMMENT 'Sending time',
    `api_send_code`    varchar(63) NULL     DEFAULT NULL COMMENT 'SMS API sending result code',
    `api_send_msg`     text(1024)    NULL     DEFAULT NULL COMMENT 'SMS API sending failure prompt',
    `api_request_id`   varchar(255) NULL     DEFAULT NULL COMMENT 'Unique request ID returned by SMS API sending',
    `api_serial_no`    varchar(255) NULL     DEFAULT NULL COMMENT 'Serial number returned by SMS API sending',
    `receive_status`   tinyint       NOT NULL DEFAULT 0 COMMENT 'Receiving status',
    `receive_time`     datetime NULL     DEFAULT NULL COMMENT 'Receiving time',
    `api_receive_code` varchar(63) NULL     DEFAULT NULL COMMENT 'API receiving result code',
    `api_receive_msg`  text                   DEFAULT NULL COMMENT 'API receiving result description',
    `create_time`      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`          bit(1)        NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1241
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'SMS log';



DROP TABLE IF EXISTS `system_file_config`;
CREATE TABLE `system_file_config`
(
    `id`          bigint        NOT NULL AUTO_INCREMENT COMMENT '编号',
    `name`        varchar(63)   NOT NULL COMMENT '配置名',
    `storage`     int           NOT NULL COMMENT '存储器',
    `remark`      varchar(255) NULL     DEFAULT NULL COMMENT '备注',
    `master`      bit(1)        NOT NULL COMMENT '是否为主配置',
    `config`      varchar(4096) NOT NULL COMMENT '存储配置',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 29 COMMENT = '文件配置表';

DROP TABLE IF EXISTS `system_file`;
CREATE TABLE `system_file`
(
    `id`          bigint        NOT NULL AUTO_INCREMENT COMMENT '文件编号',
    `config_id`   bigint                 DEFAULT NULL COMMENT '配置编号',
    `name`        varchar(256)           DEFAULT NULL COMMENT '文件名',
    `path`        varchar(512)  NOT NULL COMMENT '文件路径',
    `url`         varchar(1024) NOT NULL COMMENT '文件 URL',
    `type`        varchar(128) NULL     DEFAULT NULL COMMENT '文件类型',
    `size`        int           NOT NULL COMMENT '文件大小',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1577 COMMENT = '文件表';



DROP TABLE IF EXISTS `system_file_content`;
CREATE TABLE `system_file_content`
(
    `id`          bigint       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `config_id`   bigint       NOT NULL COMMENT 'Configuration ID',
    `path`        varchar(512) NOT NULL COMMENT 'File path',
    `content`     mediumblob   NOT NULL COMMENT 'File content',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)       NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 283 COMMENT = 'File table';


-- ----------------------------
-- Workshop Related
-- ----------------------------

DROP TABLE IF EXISTS workshop;
CREATE TABLE workshop
(
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Name',
    `type` int NOT NULL DEFAULT '0' COMMENT 'Type',
    `status` int NOT NULL DEFAULT '1' COMMENT 'Status, 1: active, 2: inactive',
    `state_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'State Code',
    `city_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'City Code',
    `address` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Address',
    `country_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Country Code',
    `phone_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Phone Number',
    `featured_tags` varchar(1024) NOT NULL DEFAULT '' COMMENT 'Featured Tags',
    `latitude` decimal(10,6) DEFAULT NULL COMMENT 'Latitude',
    `longitude` decimal(11,6) DEFAULT NULL COMMENT 'Longitude',
    `location_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Location Url',
    `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Logo',
    `photo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'Photo List',
    `operator_id` bigint NOT NULL DEFAULT '0' COMMENT 'Operator ID',
    `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Remark',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_name` (`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT 'Workshop Table';


DROP TABLE IF EXISTS workshop_service_time;
CREATE TABLE workshop_service_time
(
    id          bigint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    workshop_id bigint(4) NOT NULL DEFAULT 0 COMMENT 'Workshop ID',
    day         int      NOT NULL DEFAULT 0 COMMENT 'Day',
    start_time  datetime COMMENT 'Start Time',
    end_time    datetime COMMENT 'End Time',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)   NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Workshop Service Time Table';

create index idx_workshop_id on workshop_service_time (workshop_id);

-- ----------------------------
-- Product Related
-- ----------------------------

DROP TABLE IF EXISTS product;
CREATE TABLE product
(
    id             bigint(4)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name           varchar(255)   NOT NULL DEFAULT '' COMMENT 'Name',
    status         int(4)         NOT NULL DEFAULT 1 COMMENT 'Status, 1: Listed, 2: Unlisted',
    category_id    bigint(4)      NOT NULL DEFAULT 0 COMMENT 'Category ID',
    brand_id       bigint(4)      NOT NULL DEFAULT 0 COMMENT 'Brand ID',
    featured_tags  varchar(1024)  NOT NULL DEFAULT '' COMMENT 'Featured Tags',
    delivery_modes int(4)         NOT NULL DEFAULT 0 COMMENT 'Delivery Modes, 1: In-store Installation, 2: Self Pickup, 4: Mailing',
    price          DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Price, RM',
    service_id     bigint(4)      NOT NULL DEFAULT 0 COMMENT 'Service ID',
    net_content    int            NOT NULL DEFAULT 0 COMMENT 'net content',
    content_unit   VARCHAR(10)    NOT NULL DEFAULT '' COMMENT 'content unit (L/mL/kg/g)',
    main_image     varchar(1024)  NOT NULL DEFAULT '' COMMENT 'main image url',
    description    TEXT COMMENT 'Description',
    updated_by     bigint         NOT NULL DEFAULT 0 COMMENT 'Updated By',
    create_time    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted        bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Table';

create index idx_name on product (name);
create index idx_category_id_brand_id on product (category_id, brand_id);
create index idx_service_id on product (service_id);

DROP TABLE IF EXISTS product_detail_image;
CREATE TABLE product_detail_image
(
    id          bigint(4)     NOT NULL AUTO_INCREMENT COMMENT 'ID',
    type        int           NOT NULL DEFAULT 0 COMMENT 'Type, 1: Cover, 2: Web Detail, 2: H5 Detail',
    product_id  bigint(4)     NOT NULL DEFAULT 0 COMMENT 'Product ID',
    image       varchar(1024) NOT NULL DEFAULT '' COMMENT 'image',
    create_time datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)        NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Detail Photo';

create index idx_product_id on product_detail_image (product_id);

DROP TABLE IF EXISTS product_brand;
CREATE TABLE product_brand
(
    id          bigint(4)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name        varchar(255) NOT NULL DEFAULT '' COMMENT 'Name',
    create_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Brand Table';

DROP TABLE IF EXISTS product_category;
CREATE TABLE product_category
(
    id          bigint(4)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name        varchar(255) NOT NULL DEFAULT '' COMMENT 'Name',
    parent_id   bigint       NOT NULL DEFAULT 0 COMMENT 'Parent ID',
    create_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Category Table';

DROP TABLE IF EXISTS product_attribute;
CREATE TABLE product_attribute
(
    id          bigint(4)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name        varchar(255) NOT NULL DEFAULT '' COMMENT 'Name',
    suffix      varchar(255) NOT NULL DEFAULT '' COMMENT 'Suffix',
    category_id bigint       NOT NULL DEFAULT 0 COMMENT 'Category ID',
    type        varchar(255) NOT NULL DEFAULT '' COMMENT 'Type, select/number/date/input',
    is_required int(4)       NOT NULL DEFAULT 1 COMMENT 'Is Required, 1: Yes, 2: No',
    `order`     int(4)       NOT NULL DEFAULT 1 COMMENT 'Order',
    create_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Attribute Table';

create index idx_category_id on product_attribute (category_id);

DROP TABLE IF EXISTS product_attribute_option;
CREATE TABLE product_attribute_option
(
    id           bigint(4)     NOT NULL AUTO_INCREMENT COMMENT 'ID',
    attribute_id bigint        NOT NULL DEFAULT 0 COMMENT 'Attribute ID',
    value        varchar(255)  NOT NULL DEFAULT '' COMMENT 'Value',
    label        varchar(255)  NOT NULL DEFAULT '' COMMENT 'Label',
    image        varchar(1024) NOT NULL DEFAULT '' COMMENT 'Image',
    sort_order   int(4)        NOT NULL DEFAULT 1 COMMENT 'Order',
    create_time  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted      bit(1)        NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Attribute Option Table';

create unique index uk_attribute_id_value on product_attribute_option (attribute_id, value);

DROP TABLE IF EXISTS product_attribute_display;
CREATE TABLE product_attribute_display
(
    id            bigint(4)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    type          varchar(255) NOT NULL DEFAULT '' COMMENT 'Type, Associate with page',
    category_id   bigint       NOT NULL DEFAULT 0 COMMENT 'Category ID',
    attribute_ids varchar(255) NOT NULL DEFAULT '' COMMENT 'Attribute ID List',
    create_time   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted       bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Attribute Display Table';

create index idx_type_category_id on product_attribute_display (type, category_id);

DROP TABLE IF EXISTS product_attribute_value;
CREATE TABLE product_attribute_value
(
    id           bigint(4)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    product_id   bigint       NOT NULL DEFAULT 0 COMMENT 'Product ID',
    attribute_id bigint       NOT NULL DEFAULT 0 COMMENT 'Attribute ID',
    value        varchar(255) NOT NULL DEFAULT '' COMMENT 'Value',
    create_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted      bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Product Attribute Value Table';

create index idx_product_id on product_attribute_value (product_id);
create index idx_attribute_id_value_product_id on product_attribute_value (attribute_id, value, product_id);

-- ----------------------------
-- Service Related
-- ----------------------------

DROP TABLE IF EXISTS service_info;
CREATE TABLE service_info
(
    id           bigint(4)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name         varchar(255)   NOT NULL DEFAULT '' COMMENT 'Name',
    hours        int(4)         NOT NULL DEFAULT 0 COMMENT 'Service Hours',
    fee          DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Service Fee, RM',
    supported_on int(1)         NOT NULL DEFAULT 0 COMMENT 'Supported On',
    is_required  int(1)         NOT NULL DEFAULT 0 COMMENT 'Is Required',
    updated_by   bigint         NOT NULL DEFAULT 0 COMMENT 'Updated By',
    create_time  datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time  datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted      bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Service Table';

DROP TABLE IF EXISTS service_workshop;
CREATE TABLE service_workshop
(
    id          bigint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    service_id  bigint(4) NOT NULL DEFAULT 0 COMMENT 'Service ID',
    workshop_id bigint(4) NOT NULL DEFAULT 0 COMMENT 'Workshop ID',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)   NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Service Workshop Table';

create index idx_service_id on service_workshop (service_id);
create index idx_workshop_id on service_workshop (workshop_id);


-- ----------------------------
-- Package Related
-- ----------------------------

DROP TABLE IF EXISTS package_info;
CREATE TABLE package_info
(
    id             bigint(4)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    name           varchar(255)   NOT NULL DEFAULT '' COMMENT 'Name',
    code           varchar(255)   NOT NULL DEFAULT '' COMMENT 'Code',
    service_id     bigint(4)      NOT NULL DEFAULT 0 COMMENT 'Service ID',
    status         int(1)         NOT NULL DEFAULT 0 COMMENT 'Status, 1: Listed, 2: Unlisted',
    delivery_modes int(4)         NOT NULL DEFAULT 0 COMMENT 'Delivery Modes, 1: Self Pickup, 2: Mailing',
    is_fixed_price int            NOT NULL DEFAULT 1 COMMENT 'Price, 1: True, 2: False',
    price          DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Price, RM',
    updated_by     bigint         NOT NULL DEFAULT 0 COMMENT 'Updated By',
    create_time    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted        bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Package Table';

create index idx_name on package_info (name);
create index idx_service_id on package_info (service_id);
create index idx_price on package_info (price);

DROP TABLE IF EXISTS package_product;
CREATE TABLE package_product
(
    id          bigint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    package_id  bigint(4) NOT NULL DEFAULT 0 COMMENT 'Package ID',
    product_id  bigint(4) NOT NULL DEFAULT 0 COMMENT 'Product ID',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted     bit(1)   NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT 'Package Product Table';

create index idx_package_id on package_product (package_id);
create index idx_product_id on package_product (product_id);

-- ----------------------------
-- Area Related
-- ----------------------------

CREATE TABLE `area`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `code`        varchar(32)           DEFAULT NULL COMMENT 'Location code',
    `name`        varchar(100) NOT NULL DEFAULT '' COMMENT 'Location name',
    `parent_code` varchar(32)  NOT NULL DEFAULT '0' COMMENT 'Parent node ID',
    `order`       int(11)                      DEFAULT NULL COMMENT 'Sorting',
    `create_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `code` (`code`, `parent_code`) USING BTREE,
    KEY           `parent_code` (`parent_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 10426 COMMENT = 'Area code';


CREATE TABLE `state`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `code`        varchar(32)           DEFAULT NULL COMMENT 'Location code',
    `name`        varchar(100) NOT NULL DEFAULT '' COMMENT 'Location name',
    `order`       int(11)                      DEFAULT NULL COMMENT 'Sorting',
    `create_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `code` (`code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 122 COMMENT = 'State code';


CREATE TABLE `sys_role_workshop`
(
    `id`          bigint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_id`     bigint   NOT NULL COMMENT 'Role ID',
    `workshop_id` bigint   NOT NULL COMMENT 'Workshop ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)   NOT NULL DEFAULT b'0' COMMENT 'is deleted | 是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY uk_role_workshop (`role_id`, `workshop_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 107 COMMENT ='Role Workshop Table';


DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_no`          varchar(64)    NOT NULL DEFAULT '' COMMENT 'order no',
    `type`              varchar(10)    NOT NULL DEFAULT 'PHYSICAL' COMMENT 'order type [PHYSICAL,SERVICE,PACKAGE]',
    `customer_id`       bigint         NOT NULL DEFAULT '0' COMMENT 'customer id',
    `customer_name`     varchar(64)    NOT NULL DEFAULT '' COMMENT 'customer name',
    `customer_mobile`   varchar(20)    NOT NULL DEFAULT '' COMMENT 'customer mobile',
    `customer_email`    varchar(64)    NOT NULL DEFAULT '' COMMENT 'customer email',
    `in_store_name`     varchar(64)    NOT NULL DEFAULT '' COMMENT 'actual in-store name',
    `in_store_mobile`   varchar(20)    NOT NULL DEFAULT '' COMMENT 'actual in-store mobile',
    `license_plate`     varchar(20)    NOT NULL DEFAULT '' COMMENT 'customer car license plate',
    `brand`             varchar(255)   NOT NULL DEFAULT '' COMMENT 'brand name',
    `model`             varchar(255)   NOT NULL DEFAULT '' COMMENT 'model name',
    `year`              varchar(32)    NOT NULL DEFAULT '' COMMENT 'model year',
    `transmission_type` varchar(20)    NOT NULL DEFAULT '' COMMENT 'car transmission',
    `variant`           varchar(255)   NOT NULL DEFAULT '' COMMENT 'car variant',
    `car_icon`          varchar(255)   NOT NULL DEFAULT '' COMMENT 'car icon',
    `service_id`        bigint(10)          NOT NULL DEFAULT '0' COMMENT 'service id',
    `service_name`      varchar(255)    NOT NULL DEFAULT '' COMMENT 'service name',
    `service_hour`      int(4)              NOT NULL DEFAULT 0 COMMENT 'service hours',
    `service_fee`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'service fee',
    `package_id`        bigint(10)          NOT NULL DEFAULT '0' COMMENT 'package id',
    `package_name`      varchar(20)    NOT NULL DEFAULT '' COMMENT 'package name',
    `workshop_id`       bigint(10)          NOT NULL DEFAULT '0' COMMENT 'workshop id',
    `workshop_name`     varchar(255)    NOT NULL DEFAULT '' COMMENT 'workshop name',
    `delivery_type`     varchar(20)    NOT NULL DEFAULT '' COMMENT 'order delivery type [SHIPPING,IN-WORKSHOP,PICKUP]',
    `pickup_code`       varchar(6)     NOT NULL DEFAULT '' COMMENT 'pickup code',
    `status`            varchar(255)    NOT NULL DEFAULT '' COMMENT 'order status [PENDING_PAY,xxx,FINISHED,CANCELED,REFUNDED]',
    `shipping_fee`      decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'shipping fee',
    `original_amount`   decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'product original amount ',
    `discount_amount`   decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'product discount amount',
    `subtotal`          decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'product subtotal',
    `applied_coupon`    bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is use coupon',
    `coupon_amount`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'use coupon amount',
    `grand_total`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'order pay total',
    `force_fixed_price` bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is force fixed price',
    `refund_tag`        int(2)              NOT NULL DEFAULT '0' COMMENT 'refund tag',
    `reservation_time`  datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'reservation time',
    `order_time`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create order time',
    `paid_time`         datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'paid time',
    `completed_time`    datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'completed time',
    `finished_time`     datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'finished time',
    `refunded_time`     datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'refunded time',
    `remark`            varchar(255)   NOT NULL DEFAULT '' COMMENT 'order remark',
    `create_by`         varchar(64)    NOT NULL DEFAULT '' COMMENT 'create by name',
    `update_by`         varchar(64)    NOT NULL DEFAULT '' COMMENT 'update by name',
    `create_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'creation time',
    `update_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    `deleted`           bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_order_no` (`order_no`) USING BTREE,
    KEY                 `idx_order_time` (`order_time`) USING BTREE,
    KEY                 `idx_pickup_code` (`pickup_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT ='order table';

DROP TABLE IF EXISTS `order_status_log`;
CREATE TABLE `order_status_log`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_no`      varchar(64)  NOT NULL DEFAULT '' COMMENT 'order no',
    `status`        varchar(20)  NOT NULL DEFAULT '' COMMENT 'order status',
    `images`        varchar(255) NOT NULL DEFAULT '' COMMENT 'order image',
    `remark`        varchar(255) NOT NULL DEFAULT '' COMMENT 'order remark',
    `update_by`     varchar(64)  NOT NULL DEFAULT '' COMMENT 'update by name',
    `delivery_time` datetime     NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'order delivery time',
    `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'creation time',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    `deleted`       bit(1)       NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `idx_order_no` (`order_no`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1 COMMENT ='order status table';

DROP TABLE IF EXISTS `order_product`;
CREATE TABLE `order_product`
(
    `id`                 bigint(4)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_no`           varchar(64)    NOT NULL DEFAULT '' COMMENT 'order no',
    `sub_order_no`       varchar(64)    NOT NULL DEFAULT '' COMMENT 'sub order no',
    `product_id`         bigint(4)      NOT NULL DEFAULT '0' COMMENT 'product id',
    `product_name`       varchar(64)    NOT NULL DEFAULT '' COMMENT 'product name',
    `brand_id`           bigint(4)      NOT NULL DEFAULT '0' COMMENT 'product brand id',
    `brand_name`         varchar(64)    NOT NULL DEFAULT '' COMMENT 'product brand name',
    `net_content`        int            NOT NULL DEFAULT 0 COMMENT 'net content',
    `content_unit`       VARCHAR(10)    NOT NULL DEFAULT '' COMMENT 'content unit (L/mL/kg/g)',
    `product_attribute`  varchar(2048)           DEFAULT '' COMMENT 'product attribute',
    `category_id`        bigint(4)      NOT NULL DEFAULT '0' COMMENT 'category id',
    `category_name`      varchar(64)    NOT NULL DEFAULT '' COMMENT 'category name',
    `image`              varchar(512)   NOT NULL DEFAULT '' COMMENT 'product main image',
    `reserve_price`      decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'original amount',
    `actual_price`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'product actual price',
    `discount_price`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'discount price',
    `promotion_discount` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'promotion discount',
    `discount_amount`    decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'discount amount',
    `quantity`           int(4)         NOT NULL DEFAULT '1' COMMENT 'product quantity',
    `create_time`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`            bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`),
    KEY                  `idx_order_no` (`order_no`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 107 COMMENT ='order product table';

DROP TABLE IF EXISTS `order_delivery`;
CREATE TABLE `order_delivery`
(
    `id`          bigint(4)     NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_no`    varchar(64)   NOT NULL DEFAULT '' COMMENT 'order no',
    `name`        varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee name',
    `mobile`      varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee mobile',
    `tracking_no` varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee tracking no',
    `state_code`  varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee state address',
    `state_name`  varchar(255)  NOT NULL DEFAULT '' COMMENT 'consignee state address',
    `city_code`   varchar(64)   NOT NULL DEFAULT '' COMMENT 'consignee city address',
    `city_name`   varchar(255)  NOT NULL DEFAULT '' COMMENT 'consignee city address',
    `address`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'consignee address',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)        NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 107 COMMENT ='order delivery table';


DROP TABLE IF EXISTS `order_pay`;
CREATE TABLE `order_pay`
(
    `id`            bigint(4)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_no`      varchar(64)    NOT NULL DEFAULT '' COMMENT 'order no',
    `pay_no`        varchar(64)    NOT NULL COMMENT 'payment no',
    `customer_id`   bigint         NOT NULL DEFAULT '0' COMMENT 'customer id',
    `grand_total`   decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'order pay total',
    `paid_amount`   decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT 'paid total',
    `error_code`    varchar(255)   NOT NULL DEFAULT '' COMMENT 'error code',
    `error_msg`     varchar(255)   NOT NULL DEFAULT '' COMMENT 'error msg',
    `pay_method`    varchar(64)    NOT NULL DEFAULT '' COMMENT 'payment methods',
    `paid_time`     datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'paid time',
    `canceled_time` datetime       NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'canceled time',
    `create_time`   datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`   datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`       bit(1)         NOT NULL DEFAULT b'0' COMMENT 'is deleted',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
    UNIQUE KEY `idx_pay_no` (`pay_no`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 107 COMMENT ='order pay table';


DROP TABLE IF EXISTS `content_template`;
CREATE TABLE `content_template`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `code`        varchar(64) NOT NULL DEFAULT '' COMMENT 'layout code',
    `title`       varchar(64) NOT NULL DEFAULT '' COMMENT 'layout title',
    `enabled`     bit(1)      NOT NULL DEFAULT b'0' COMMENT 'Whether enabled',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)      NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `uk_code` (`code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 645
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'content template';


DROP TABLE IF EXISTS `content_layout`;
CREATE TABLE `content_layout`
(
    `id`            bigint      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `template_code` varchar(64) NOT NULL DEFAULT '' COMMENT 'Template Code',
    `title`         varchar(64) NOT NULL DEFAULT '' COMMENT 'layout title',
    `seq`           bigint      NOT NULL DEFAULT 0 COMMENT 'layout sequence',
    `enabled`       bit(1)      NOT NULL DEFAULT b'0' COMMENT 'Whether enabled',
    `type`          varchar(64) NOT NULL DEFAULT '' COMMENT 'layout type',
    `create_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`       bit(1)      NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX           `idx_template_code` (`template_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 645
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'content layout';


DROP TABLE IF EXISTS `content_resource`;
CREATE TABLE `content_resource`
(
    `id`          bigint        NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `title`       varchar(64)   NOT NULL DEFAULT '' COMMENT 'resource title',
    `type`        varchar(64)   NOT NULL DEFAULT '' COMMENT 'resource type',
    `pict_uri`    varchar(255)  NOT NULL DEFAULT '' COMMENT 'resource picture uri ',
    `entry_uri`   varchar(255)  NOT NULL DEFAULT '' COMMENT 'resource entry uri',
    `entry_type`  varchar(64)   NOT NULL DEFAULT '' COMMENT 'resource entry type',
    `ext`         varchar(1024) NOT NULL DEFAULT '' COMMENT 'resource extension fields',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`     bit(1)        NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 645
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'content resource';


DROP TABLE IF EXISTS `content_layout_resource`;
CREATE TABLE `content_layout_resource`
(
    `id`           bigint   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `layout_id`    bigint   NOT NULL DEFAULT 0 COMMENT 'layout id',
    `resource_id`  bigint   NOT NULL DEFAULT 0 COMMENT 'resource id',
    `resource_seq` bigint   NOT NULL DEFAULT 0 COMMENT 'resource sequence',
    `enabled`      bit(1)   NOT NULL DEFAULT b'0' COMMENT 'Whether enabled',
    `create_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    `update_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    `deleted`      bit(1)   NOT NULL DEFAULT b'0' COMMENT 'Whether deleted',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX          `idx_layout_resource` (`layout_id`, `resource_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 645
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'content layout resource mapping';

