create table if not exists payment
(
    id           bigint unsigned auto_increment
        primary key,
    pay_no       varchar(64)                         not null comment 'pay no',
    source_id    varchar(100)                        null comment 'source id',
    third_pay_no varchar(100)                        null,
    channel      int                                 not null comment 'channel',
    pay_method   int       default 0                 not null comment '1:card 2:others, eg: fpx',
    amount       decimal(20, 2)                      null comment 'amount',
    currency     varchar(10)                         null comment 'currency',
    expires_at   datetime                            null comment 'expires time',
    pay_time     datetime                            null comment 'pay time',
    refund_time  datetime                            null comment 'refund time',
    pay_type     varchar(100)                        not null comment 'pay type',
    url          varchar(500)                        not null comment 'pay call back url',
    request_data text                                null,
    status       varchar(2000)                       not null comment '1 pending pay',
    create_time  timestamp default CURRENT_TIMESTAMP not null comment 'create time',
    update_time  timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'update time'
)
    comment 'Payment' auto_increment = 29;

create table if not exists payment_log
(
    id            bigint unsigned auto_increment
        primary key,
    pay_id        bigint unsigned                     not null comment 'payment id',
    src_status    varchar(64)                         null comment 'source status',
    target_status varchar(64)                         not null comment 'target status',
    info          text                                null,
    create_time   timestamp default CURRENT_TIMESTAMP not null comment 'create time',
    update_time   timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'update time'
)
    comment 'payment log' auto_increment = 50;

create index idx_pay_id
    on payment_log (pay_id);

