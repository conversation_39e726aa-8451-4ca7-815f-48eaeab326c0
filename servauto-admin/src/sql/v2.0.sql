

ALTER TABLE `order`
    ADD COLUMN  `suggestion_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'suggestion reservation time' AFTER `reservation_time`,
    ADD COLUMN  `confirm_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT 'workshop confirm time' AFTER `reservation_time`,
    ADD COLUMN  `reminder_one_day` bit(1) NOT NULL DEFAULT b'0' COMMENT 'reminder one day' AFTER `remark`,
    ADD COLUMN  `reminder_two_hour` bit(1) NOT NULL DEFAULT b'0' COMMENT 'reminder two hour' AFTER `remark`;

ALTER TABLE workshop ADD COLUMN `whats_app_number` text NOT NULL COMMENT 'WhatsApp account' AFTER phone_number;

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, 'payment-completed', 'payment-completed', '', '["orderType","orderNo","orderType1","licensePlate","serviceName","workshopName","time"]',NULL, 'order_paid', 2, 'YCloud_WHATSAPP');

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, 'book-service', 'book-service', '', '["orderNo","orderType","licensePlate","serviceName","workshopName","time"]',NULL, 'order_book_service', 2, 'YCloud_WHATSAPP');

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, 'reschedule', 'reschedule', '', '["orderNo","orderType","licensePlate","serviceName","workshopName","time"]',NULL, 'order_reschedule', 2, 'YCloud_WHATSAPP');

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, '1day-advance-reminder', '1day-advance-reminder', '', '["serviceName","time","workshopName","address","contactNumber"]',NULL, '1day_advance_reminder', 2, 'YCloud_WHATSAPP');

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, '2hour-advance-reminder', '2hour-advance-reminder', '', '["serviceName","workshopName","address","time","contactNumber"]',NULL, '2hour_advance_reminder', 2, 'YCloud_WHATSAPP');

INSERT INTO system_sms_template
(`type`, status, code, name, content, params, remark, api_template_id, channel_id, channel_code)
VALUES(2, 0, 'reschedule-customer', 'reschedule-customer', '', '["date","time","workshopName","address"]',NULL, 'order_reschedule_customer', 2, 'YCloud_WHATSAPP');


